#!/bin/bash
set -e

# Check if database exists
DB_EXIST=$(psql -U "$POSTGRES_USER" -tAc "SELECT 1 FROM pg_database WHERE datname='insyde_division'")

if [ "$DB_EXIST" != "1" ]; then
  echo "Creating database: insyde_division"
  createdb -U "$POSTGRES_USER" insyde_division
  psql -U "$POSTGRES_USER" -c "GRANT ALL PRIVILEGES ON DATABASE insyde_division TO $POSTGRES_USER;"
else
  echo "Database insyde_division already exists"
fi

# Check if database exists
DB_EXIST=$(psql -U "$POSTGRES_USER" -tAc "SELECT 1 FROM pg_database WHERE datname='insyde'")

if [ "$DB_EXIST" != "1" ]; then
  echo "Creating database: insyde"
  createdb -U "$POSTGRES_USER" insyde
  psql -U "$POSTGRES_USER" -c "GRANT ALL PRIVILEGES ON DATABASE insyde TO $POSTGRES_USER;"
else
  echo "Database insyde already exists"
fi
