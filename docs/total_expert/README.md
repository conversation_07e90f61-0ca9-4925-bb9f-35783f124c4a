# TotalExpert Integration

This documentation provides an overview of the TotalExpert integration within the system, detailing the endpoints, flow, and processes used in the application.

## Overview

TotalExpert is a tool used to manage relationships and marketing campaigns. This integration facilitates syncing of data such as insights, contacts, journeys, and activities between our system and TotalExpert.

## Endpoints ### 1. Contact Management

- **Fetch Contact Groups**
  - **Endpoint:** `GET /contact-group/`
  - **Description:** Fetch contact groups associated with the authenticated user.
  - **Parameters:**
    - `assistant_id`: The ID of the assistant.

- **Update Contact**
  - **Endpoint:** `PATCH /contact/`
  - **Description:** Update a contact's details.
  - **Parameters:**
    - `assistant_id`: The ID of the assistant.
  - **Request Body:** `ContactUpdate`

- **Add Contact**
  - **Endpoint:** `POST /contact/`
  - **Description:** Add a new contact to the TotalExpert system.
  - **Parameters:**
    - `assistant_id`: The ID of the assistant.
  - **Request Body:** `ContactAdd`

- **Fetch Contacts**
  - **Endpoint:** `GET /contact/`
  - **Description:** Fetch contacts from Total Expert.
  - **Parameters:**
    - `assistant_id`: The ID of the assistant.
    - `filter`: Optional filter criteria.


### 2. Note Management

- **Add Note**
  - **Endpoint:** `POST /contact-note/`
  - **Description:** Add a note to a contact.
  - **Parameters:**
    - `assistant_id`: The ID of the assistant.
    - `contact_id`: ID of the contact.

- **Update Note**
  - **Endpoint:** `PATCH /contact-note/{id}`
  - **Description:** Update a contact note.
  - **Parameters:**
    - `id`: Note ID.
    - `assistant_id`: The ID of the assistant.

- **Delete Note**
  - **Endpoint:** `DELETE /contact-note/{id}/`
  - **Description:** Delete a note from a contact.
  - **Parameters:**
    - `assistant_id`: The ID of the assistant.
    - `id`: Note ID.

### 3. Insight Management

- **Fetch Insights**
  - **Endpoint:** `GET /insights/`
  - **Description:** Fetches insights for the authenticated user.
  - **Parameters:**
    - `assistant_id`: The ID of the assistant.

- **Mark Insight as Read**
  - **Endpoint:** `PATCH /insights/{id}/read/`
  - **Description:** Mark an insight as read.
  - **Parameters:**
    - `id`: Insight ID.

- **Sync Insight Types**
  - **Endpoint:** `GET /insight-types/sync/`
  - **Description:** Synchronizes insight types from Total Expert to the database.


### 4. Journey Management

- **Sync Journeys**
  - **Endpoint:** `PATCH /journey/sync/`
  - **Description:** Synchronizes journeys data.
  - **Parameters:**
    - `assistant_id`: The ID of the assistant.

- **List Journeys**
  - **Endpoint:** `GET /journeys/`
  - **Description:** List journeys associated with an assistant.
  - **Parameters:**
    - `assistant_id`: The ID of the assistant.

- **Trigger Journey**
  - **Endpoint:** Function call: `trigger_journey`
  - **Description:** Trigger a journey for a contact based on certain input fields or loan conditions.
  - **Parameters:**
    - `journey_name`: Name of the journey to be triggered.
    - `input_fields`: Optional fields used for filtering contacts in the journey.


## Flow and Process

### Initialization
The integration initializes by setting up connections to TotalExpert through the `total_expert_utils` module. Authentication tokens are managed to communicate with the TotalExpert API.

### Sync Operations
- The integration supports automated tasks to synchronize insights and journey data.
- **Insights synchronization** ensures that insights are up-to-date in both the system and TotalExpert.
- **Journeys synchronization** updates journey data from TotalExpert and reflects changes when new journeys are created or existing ones are updated or deleted.

### Journey Triggering
- Journeys, also known as contact groups, can be manually triggered via the `trigger_journey` function.
- When triggered, the system checks for published journeys and verifies the journey type. If conditions (such as loan details) are met, the journey is initiated.
- If input fields for loans are provided, the system first fetches loan details to filter the contacts associated with the journey.

### Error Handling
- Proper logging is implemented using `loguru` to track the processes and handle errors gracefully. HTTP exceptions are managed to ensure that any unauthorized or failed attempts are communicated back to the user.

### Task Management
- Celery tasks (e.g., `sync_insights`) are used for managing background processes for data sync operations.


## Schema Definitions

### ContactAdd
Represents the structure used to add a new contact:
- `source`: A string indicating the source of the contact, required.
- `first_name`, `last_name`, `email`, `phone_cell`: Details of the contact.

### ContactUpdate
Used to update existing contact details:
- Contains fields that may be updated including names and contact information.


### Multiple User Impersonation

A user can now impersonate multiple users, provided those users have previously logged into the system.

#### 1. Endpoint: `/loan-officers/`
-  An endpoint to fetch **loan officers**, i.e., users who have a `TotalExpertToken` associated with them.

#### 2. Endpoint: `/v2/{id}/impersonation/users/`
-  An endpoint to fetch the list of users that the current user has impersonated.

#### 3. Active Impersonation Tracking
- Introduced a new column `active` in the `CustomLoanOfficerMapping` model to track which impersonation is currently active.
- Only one impersonation can be active at a time for a given user.

#### 4. Chat Streaming Update
- Added an optional argument `impersonation_id` to the `/{id}/stream` endpoint.
- Workflow:
  - Fetch the impersonated user's email using the given `user_id`.
  - Check if an impersonation mapping exists for the current assistant and email.
  - If it exists and is not already active:
    - Mark all other impersonations as `active = false`.
    - Activate the selected impersonation by setting `active = true`.

#### 5. Token Selection for Resource Access
- Updated logic to always use the token of the **currently active impersonation** when accessing TotalExpert-related resources , if None active we use the current user's id to fetch the user token.

#### 6. Auth URL Handling
- Modified the auth URL fetching logic to include a `WHERE active = true` clause, ensuring it respects the active impersonation context.
#### 7. Current Active Impersonation
- Returns current active impersonation.If no active impersonation, returns self
## Conclusion
This document outlines the functions and endpoints related to TotalExpert integration, providing the necessary details for interacting with various elements like contacts, insights, notes, and journeys. Successfully using this integration requires understanding the defined endpoints and potential responses, as well as handling background tasks efficiently.
