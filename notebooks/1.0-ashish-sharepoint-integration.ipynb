{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import requests\n", "from urllib.parse import urlparse"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class SharePointClient:\n", "    def __init__(self, tenant_id, client_id, client_secret, resource_url):\n", "        self.tenant_id = tenant_id\n", "        self.client_id = client_id\n", "        self.client_secret = client_secret\n", "        self.resource_url = resource_url\n", "        self.base_url = f\"https://login.microsoftonline.com/{tenant_id}/oauth2/v2.0/token\"\n", "        self.headers = {\"Content-Type\": \"application/x-www-form-urlencoded\"}\n", "        self.access_token = self.get_access_token()  # Initialize and store the access token upon instantiation\n", "\n", "    def get_access_token(self):\n", "        body = {\n", "            \"grant_type\": \"client_credentials\",\n", "            \"client_id\": self.client_id,\n", "            \"client_secret\": self.client_secret,\n", "            \"scope\": self.resource_url + \".default\",\n", "        }\n", "        response = requests.post(self.base_url, headers=self.headers, data=body)\n", "        return response.json().get(\"access_token\")\n", "\n", "    def convert_sharepoint_url(self, url):\n", "        parsed_url = urlparse(url)\n", "\n", "        # Remove the protocol part and construct the new site URL\n", "        netloc = parsed_url.netloc\n", "        path_parts = parsed_url.path.split(\"/\")\n", "        try:\n", "            sites_index = path_parts.index(\"sites\")\n", "        except ValueError:\n", "            return None  # 'sites' not found in the URL path\n", "\n", "        # Construct the path up to and including the site name\n", "        base_path = \"/\".join(path_parts[: sites_index + 2])\n", "        return f\"{netloc}:{base_path}\"\n", "\n", "    def get_site_id(self, site_url):\n", "        \"\"\"Get site id from site URL\"\"\"\n", "        site_url = self.convert_sharepoint_url(site_url)\n", "        full_url = f\"https://graph.microsoft.com/v1.0/sites/{site_url}\"\n", "        response = requests.get(full_url, headers={\"Authorization\": f\"Bearer {self.access_token}\"})\n", "        return response.json().get(\"id\")\n", "\n", "    def get_drive_id(self, site_id):\n", "        \"\"\"Retrieve drive IDs and names associated with a site\"\"\"\n", "        drives_url = f\"https://graph.microsoft.com/v1.0/sites/{site_id}/drives\"\n", "        response = requests.get(drives_url, headers={\"Authorization\": f\"Bearer {self.access_token}\"})\n", "        drives = response.json().get(\"value\", [])\n", "        return [(drive[\"id\"], drive[\"name\"]) for drive in drives]\n", "\n", "    def list_folder_contents(self, site_id, drive_id, folder_id):\n", "        \"\"\"List folder contents recursively\"\"\"\n", "        folder_contents_url = (\n", "            f\"https://graph.microsoft.com/v1.0/sites/{site_id}/drives/{drive_id}/items/{folder_id}/children\"\n", "        )\n", "        contents_response = requests.get(folder_contents_url, headers={\"Authorization\": f\"Bearer {self.access_token}\"})\n", "        folder_contents = contents_response.json()\n", "        folder_hierarchy = []\n", "\n", "        for item in folder_contents.get(\"value\", []):\n", "            if \"folder\" in item:\n", "                folder_hierarchy.append(\n", "                    {\n", "                        \"id\": item[\"id\"],\n", "                        \"name\": item[\"name\"],\n", "                        \"type\": \"Folder\",\n", "                        \"createdBy\": item.get(\"createdBy\", {}),\n", "                        \"createdDateTime\": item.get(\"createdDateTime\", \"\"),\n", "                        \"lastModifiedBy\": item.get(\"lastModifiedBy\", {}),\n", "                        \"lastModifiedDateTime\": item.get(\"lastModifiedDateTime\", \"\"),\n", "                        \"children\": self.list_folder_contents(site_id, drive_id, item[\"id\"]),\n", "                    }\n", "                )\n", "            elif \"file\" in item:\n", "                path_parts = item[\"parentReference\"][\"path\"].split(\"root:\")\n", "                path = path_parts[1] if len(path_parts) > 1 else \"\"\n", "                full_path = f\"{path}/{item['name']}\" if path else item[\"name\"]\n", "                folder_hierarchy.append(\n", "                    {\n", "                        \"id\": item[\"id\"],\n", "                        \"name\": item[\"name\"],\n", "                        \"type\": \"File\",\n", "                        \"createdBy\": item.get(\"createdBy\", {}),\n", "                        \"createdDateTime\": item.get(\"createdDateTime\", \"\"),\n", "                        \"lastModifiedBy\": item.get(\"lastModifiedBy\", {}),\n", "                        \"lastModifiedDateTime\": item.get(\"lastModifiedDateTime\", \"\"),\n", "                        \"mimeType\": item[\"file\"][\"mimeType\"],\n", "                        \"uri\": item.get(\"@microsoft.graph.downloadUrl\", \"\"),\n", "                        \"fullpath\": full_path,\n", "                    }\n", "                )\n", "\n", "        return folder_hierarchy"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tenant_id = \"<your_tenant_id>\"\n", "client_id = \"<your_client_id>\"\n", "client_secret = \"<your_client_secret>\"\n", "site_url = \"https://insydeai.sharepoint.com/sites/InsydeDemoSite\"\n", "resource = \"https://graph.microsoft.com/\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["client = SharePointClient(tenant_id, client_id, client_secret, resource)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["site_id = client.get_site_id(site_url)\n", "print(\"Site ID:\", site_id)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["drive_info = client.get_drive_id(site_id)\n", "print(\"Root folder:\", drive_info)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["drive_id = drive_info[0][0]\n", "contents = client.list_folder_contents(site_id, drive_id, \"root\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["contents"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 2}