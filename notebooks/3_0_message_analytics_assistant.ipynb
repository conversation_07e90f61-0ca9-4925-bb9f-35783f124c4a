{"cells": [{"cell_type": "code", "execution_count": null, "id": "07a55d52-cb4e-4a0e-8cf6-e903c6eaed9e", "metadata": {}, "outputs": [], "source": ["OPENAI_API_KEY=\"openai_key\"\n", "OPENAI_ORG_ID=\"org_id\""]}, {"cell_type": "code", "execution_count": null, "id": "af160857-6dda-45d1-8e4d-f92aefbc895d", "metadata": {}, "outputs": [], "source": ["\n", "import hdbscan\n", "import numpy as np\n", "import pandas as pd\n", "import plotly.express as px\n", "from dotenv import load_dotenv\n", "from umap import UMAP\n", "\n", "load_dotenv()\n"]}, {"cell_type": "code", "execution_count": null, "id": "c572ca6d-9e75-4006-a56b-579011c80e0b", "metadata": {}, "outputs": [], "source": ["df = pd.read_excel(\"filepath\")"]}, {"cell_type": "code", "execution_count": null, "id": "d6e7a8ad", "metadata": {}, "outputs": [], "source": ["df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "28879d2d", "metadata": {}, "outputs": [], "source": ["df = df[df['Role'] == 'user']\n"]}, {"cell_type": "code", "execution_count": null, "id": "8a365f66-85b7-4568-8af7-b6f74b175133", "metadata": {}, "outputs": [], "source": ["df = df[[\"Assistant\", \"Message\", \"Conversation Title\"]]"]}, {"cell_type": "code", "execution_count": null, "id": "0db7133f-410b-4e36-bde6-3ccc6a7dc28c", "metadata": {}, "outputs": [], "source": ["df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "3e89c800-0c20-46e5-a077-7a8b7a8407ce", "metadata": {}, "outputs": [], "source": ["docs = df[\"Message\"].to_list()\n", "docs = [str(doc) for doc in docs]\n", "\n", "docs = [string for string in docs if string.strip()]\n", "\n", "print(len(docs))"]}, {"cell_type": "code", "execution_count": null, "id": "34cedbf8-ea9f-47aa-91c5-b921ef2ca99c", "metadata": {}, "outputs": [], "source": ["import voyageai\n", "\n", "class VoyageEmbedder:\n", "    def __init__(self):\n", "        self.client = voyageai.AsyncClient(api_key=\"api_key\")\n", "\n", "    async def get_embedding(self, texts: str | list[str], input_type: str) -> list[list[float]]:\n", "        if isinstance(texts, str):\n", "            texts = [texts]\n", "\n", "        response = await self.client.embed(texts, model=\"model_name\", input_type=input_type)\n", "        return response.embeddings\n", "\n", "    async def get_reranked_context(self, query, contexts, top_k):\n", "        top = await self.client.rerank(query, contexts, model=\"rerank_model_name\", top_k=top_k)\n", "\n", "        return top\n", "\n", "\n", "voyage_embedder = VoyageEmbedder()"]}, {"cell_type": "code", "execution_count": null, "id": "e0f89b2b", "metadata": {}, "outputs": [], "source": ["def chunk_docs(docs, chunk_size=128):\n", "    for i in range(0, len(docs), chunk_size):\n", "        yield docs[i:i + chunk_size]\n", "\n", "all_embeddings = []\n", "for chunk in chunk_docs(docs):\n", "    embeddings = await voyage_embedder.get_embedding(chunk, input_type='query')\n", "    all_embeddings.extend(embeddings)\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "61373952-34a3-4242-92b3-b9fcfc2af7dc", "metadata": {}, "outputs": [], "source": ["from sklearn.cluster import KMeans\n", "\n", "kmeans = KMeans(n_clusters=10, random_state=0)  \n", "labels = kmeans.fit_predict(all_embeddings)    \n"]}, {"cell_type": "code", "execution_count": null, "id": "3cc609eb-1916-4bd0-b8e3-a031dd5326ab", "metadata": {}, "outputs": [], "source": ["umap = UMAP(n_components=2, random_state=42, n_neighbors=80, min_dist=0.05)\n", "\n", "df_umap = (\n", "    pd.DataFrame(umap.fit_transform(np.array(all_embeddings)), columns=['x', 'y'])\n", "    .assign(cluster=lambda df: labels.astype(str))\n", "    .query('cluster != \"-1\"')\n", "    .sort_values(by='cluster')\n", ")\n", "\n", "fig = px.scatter(df_umap, x='x', y='y', color='cluster')\n", "fig.show()\n"]}, {"cell_type": "code", "execution_count": null, "id": "f1e6bfb0-1057-4072-80ac-e968170eae67", "metadata": {}, "outputs": [], "source": ["df[\"cluster\"] = labels.astype(str)"]}, {"cell_type": "code", "execution_count": null, "id": "82677fa5-eef6-441b-b29d-3d2208758b86", "metadata": {}, "outputs": [], "source": ["df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "c3bf605a-9e18-44fd-9489-f7532b72ee37", "metadata": {}, "outputs": [], "source": ["df.groupby([\"cluster\"]).size().sort_values(ascending=False)"]}, {"cell_type": "code", "execution_count": null, "id": "59d3f0e4-7217-4daf-8cf7-29c9070cb37f", "metadata": {}, "outputs": [], "source": ["df[df[\"cluster\"]==\"4\"]"]}, {"cell_type": "code", "execution_count": null, "id": "fb2b4805-c0a2-4902-adfa-dfff018dd79f", "metadata": {}, "outputs": [], "source": ["for c in df.cluster.unique():\n", "    sample_queries = df.query(f\"cluster == '{c}'\").to_dict(orient=\"records\")\n", "    queries_str = \"\\n\\n\".join(\n", "        [\n", "            f\"[{i}] {query['Message']}\"\n", "            for i, query in enumerate(\n", "                sample_queries, start=1\n", "            )\n", "        ]\n", "    )\n", "    print(queries_str)\n", "    break\n"]}, {"cell_type": "code", "execution_count": null, "id": "93f6fe57-b7fa-4e9b-b40b-c76ab996a61e", "metadata": {}, "outputs": [], "source": ["from openai import OpenAI\n", "\n", "client = OpenAI()\n", "\n", "df[\"cluster_name\"] = \"Uncategorized\" \n", "\n", "def generate_topic_titles():\n", "    system_message = (\n", "        \"You are a helpful assistant skilled in analyzing and categorizing message clusters. \"\n", "        \"Your task is to summarize each cluster with a short, descriptive topic title. \"\n", "        \"Focus on the central theme or intent of each group, ignoring personal details or names. \"\n", "        \"Keep titles concise and specific, ideally as a 2-4 word noun phrase.\"\n", "    )\n", "    user_template = (\n", "        \"Please provide a 2-4 word title that best describes the common theme \"\n", "        \"or intent in the following group of queries.\\n\\nQUERIES:\\n\\n{}\\n\\nTOPIC TITLE:\"\n", "    )\n", "\n", "    for c in df.cluster.unique():\n", "        sample_queries = df.query(f\"cluster == '{c}'\").head(20).to_dict(orient=\"records\") \n", "        queries_str = \"\\n\\n\".join(\n", "            [\n", "                f\"[{i}] {query['Message']}\"\n", "                for i, query in enumerate(\n", "                    sample_queries, start=1\n", "                )\n", "            ]\n", "        )\n", "        messages = [\n", "            {\"role\": \"system\", \"content\": system_message},\n", "            {\"role\": \"user\", \"content\": user_template.format(queries_str)},\n", "        ]\n", "        response = client.chat.completions.create(\n", "            model=\"gpt-4\", messages=messages, temperature=0.7\n", "        )\n", "\n", "        topic_title = response.choices[0].message.content\n", "        df.loc[df.cluster == c, \"cluster_name\"] = topic_title\n"]}, {"cell_type": "code", "execution_count": null, "id": "350be200-7e48-4c55-91a4-94efafb1acb3", "metadata": {}, "outputs": [], "source": ["generate_topic_titles()"]}, {"cell_type": "code", "execution_count": null, "id": "d1359317-e568-4cd8-b949-b698f1688e7a", "metadata": {}, "outputs": [], "source": ["df.head(100)"]}, {"cell_type": "code", "execution_count": null, "id": "798f0b01-1afc-437f-b3be-c326791a9024", "metadata": {}, "outputs": [], "source": ["c = 4 \n", "with pd.option_context(\"display.max_colwidth\", None):\n", "    print(df.query(f\"cluster == '{c}'\").cluster_name.values[0])\n", "    display(df.query(f\"cluster == '{c}'\").head())"]}, {"cell_type": "code", "execution_count": null, "id": "f4f3094d-16be-4b5b-9b84-41188daee659", "metadata": {}, "outputs": [], "source": ["df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "0caa19c4-9db1-43e1-921a-6a229a273bdc", "metadata": {}, "outputs": [], "source": ["df[\"cluster_name\"].unique()"]}, {"cell_type": "code", "execution_count": null, "id": "e9df678f-e350-4df6-b0ca-50544da0f2ac", "metadata": {}, "outputs": [], "source": ["df.groupby([\"cluster_name\"]).count().sort_values(by=\"cluster\", ascending=False).head(20)"]}, {"cell_type": "code", "execution_count": null, "id": "265d7e23-8b48-40c5-8ff9-99bb70d77a52", "metadata": {}, "outputs": [], "source": ["cluster_counts = df.groupby(['cluster', 'cluster_name']).size().to_dict()\n", "\n", "sorted_cluster_counts = dict(sorted(cluster_counts.items(), key=lambda item: item[1], reverse=True))\n", "\n", "sorted_cluster_counts\n"]}, {"cell_type": "code", "execution_count": null, "id": "922ddbc7", "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt"]}, {"cell_type": "code", "execution_count": null, "id": "b2a40905-8a20-40d5-9a15-d728bed96c43", "metadata": {}, "outputs": [], "source": ["labels = [item[1] for item in sorted_cluster_counts.keys()]  # cluster_name as label\n", "counts = list(sorted_cluster_counts.values())\n", "\n", "# Plotting the pie chart\n", "plt.figure(figsize=(8, 6))\n", "plt.pie(\n", "    counts, \n", "    labels=labels, \n", "    autopct='%1.1f%%', \n", "    startangle=140\n", ")\n", "plt.title(\"Distribution of Messages by Cluster Name\")\n", "plt.show()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}}, "nbformat": 4, "nbformat_minor": 5}