# Cell 1: Install dependencies and import libraries
!pip install pinecone loguru transformers==4.18.0 torch==1.11.0 git+https://github.com/naver/splade.git voyageai

import torch
from transformers import AutoTokenizer
from splade.models.transformer_rep import Splade
from loguru import logger
import voyageai
from datetime import datetime
import asyncio
import pinecone
from pinecone import Vector, SparseValues
from tqdm import tqdm


import torch
from transformers import AutoTokenizer
from splade.models.transformer_rep import Splade
from loguru import logger

class SpladeEmbedder:
    def __init__(self, model_name="naver/splade-cocondenser-ensembledistil"):
        self.tokenizer = AutoTokenizer.from_pretrained(model_name)
        self.device = torch.device("cuda")
        self.model = Splade(model_name, agg='max')
        self.model.to(self.device)

        self.model.eval()

    async def get_embedding(self, texts: str | list[str]) -> list[tuple[list[int], list[float]]]:
        if isinstance(texts, str):
            texts = [texts]
        results = []

        for text in texts:
            try:
                with torch.no_grad():
                    inputs = self.tokenizer(text, return_tensors="pt", truncation=True).to(self.device)
                    sparse_emb = self.model(d_kwargs=inputs)['d_rep'].squeeze()

                indices = sparse_emb.nonzero().squeeze().cpu().tolist()
                values = sparse_emb[indices].cpu().tolist()

                results.append((indices, values))
            except Exception as e:
                logger.error("Error processing text '%s': %s", text, str(e))
                results.append(([], []))  # Append empty results on error
        return results


splade_embedder = SpladeEmbedder()


import voyageai

class VoyageEmbedder:
    def __init__(self):
        self.client = voyageai.AsyncClient( api_key = "voyageai api key for embeddings")

    async def get_embedding(self, texts: str | list[str], input_type: str, max_workers=4) -> list[list[float]]:
        if isinstance(texts, str):
            texts = [texts]

        response = await self.client.embed(texts, model="voyage-3", input_type=input_type)
        return response.embeddings

voyage_embedder = VoyageEmbedder()

from loguru import logger
from pinecone import Pinecone, ServerlessSpec, Vector

class PineconeServerlessHook:
    def __init__(
        self,
        index_name: str,
        api_key: str,
        dimension: int = 1536,
        metric: str = "cosine",
        cloud: str = "aws",
        region: str = "us-west-2"
    ) -> None:
        # Initialize Pinecone with the dynamic API key
        self.pc = Pinecone(api_key=api_key)

        # Check for existing indexes
        existing_indexes = [index.get("name") for index in self.pc.list_indexes().get("indexes", [])]
        if index_name not in existing_indexes:
            # Create a new index with the provided specifications
            self.pc.create_index(
                name=index_name,
                dimension=dimension,
                metric=metric,
                spec=ServerlessSpec(cloud=cloud, region=region),
            )
            logger.info(f"Created index {index_name}")
        else:
            logger.info(f"Index {index_name} already exists")

        # Assign the index to the instance variable
        self.index = self.pc.Index(name=index_name)

    def query_document(
        self,
        vector: list[float],
        top_k: int = 10,
        filter: dict = None,
        namespace: str | None = None
    ) -> list[dict]:
        return self.index.query(
            vector=vector,
            top_k=top_k,
            namespace=namespace,
            include_values=True,
            include_metadata=True,
            filter=filter,
        )
    def upsert_document(self, vectors: list[Vector], namespace: str | None = None) -> None:
        upsert_response = self.index.upsert(vectors=vectors, namespace=namespace, batch_size=32)
        return upsert_response

    def update_metadata(self, id: str, categories: list[str], namespace: str | None = None) -> bool:
        for ids in self.index.list(prefix=f"{id}#", namespace=namespace):
            logger.info(f"Updating document with ID: {ids}")
            for doc_id in ids:
                self.index.update(id=doc_id, namespace=namespace, set_metadata={"categories": categories})
        return True

    def delete_document(self, id: str, namespace: str | None = None) -> bool:
        # Delete document by ID prefix
        # https://docs.pinecone.io/guides/data/manage-rag-documents#delete-all-records-for-a-parent-document
        namespace = str(namespace)
        logger.info(f"Deleting document with ID: {id}")
        for ids in self.index.list(prefix=f"{id}#", namespace=namespace):
            self.index.delete(ids=ids, namespace=namespace)
        return True

    def list_namespaces(self) -> list[str]:
        """Returns all namespaces available in the index."""
        try:
            response = self.index.describe_index_stats()
            namespaces = response.get("namespaces", {}).keys()
            logger.info(f"Found namespaces: {list(namespaces)}")
            return list(namespaces)
        except Exception as e:
            logger.error(f"Error retrieving namespaces: {e}")
            return []


SOURCE_PINECONE_SERVERLESS_API_KEY="source db api key"
SOURCE_PINECONE_ENV="us-east-1-aws"
SOURCE_PINECONE_INDEX="source index"

DESTINATION_PINECONE_SERVERLESS_API_KEY="destination db api key"
DESTINATION_PINECONE_ENV="us-east-1-aws"
DESTINATION_PINECONE_INDEX="destination index"

# Example usage
source_hook = PineconeServerlessHook(
    index_name=SOURCE_PINECONE_INDEX,
    api_key=SOURCE_PINECONE_SERVERLESS_API_KEY,
)

source_namespaces = source_hook.list_namespaces()

destination_hook = PineconeServerlessHook(
    index_name=DESTINATION_PINECONE_INDEX,
    api_key=DESTINATION_PINECONE_SERVERLESS_API_KEY,
    metric="dotproduct",
    dimension=1024,
    cloud="aws",
    region="us-east-1"
)

destination_namespaces = destination_hook.list_namespaces()

import nest_asyncio
import asyncio
from tqdm import tqdm
from datetime import datetime

nest_asyncio.apply()

async def calculate_modifications(previous_hook, new_hook, source_namespaces, destination_namespaces):
    """Calculate total number of documents to be upserted and deleted without performing any operations."""

    total_to_upsert = []
    total_to_delete = []

    destination_ids = []
    to_upsert_namespaces = []
    to_delete_namespaces = []
    for destination_namespace in destination_namespaces:
        for ids in new_hook.index.list(namespace=destination_namespace):
            destination_ids.extend(ids)

    destination_ids_set = set(destination_ids)

    for source_namespace in source_namespaces:
        source_ids = []
        for ids in previous_hook.index.list(namespace=source_namespace):
            source_ids.extend(ids)

        batch_size = 200
        for i in range(0, len(source_ids), batch_size):
            batch_slice = source_ids[i:i + batch_size]
            data = previous_hook.index.fetch(ids=batch_slice, namespace=source_namespace)

            keys_list = list(data.vectors.keys())

            for key in keys_list:
                document_id = data.vectors[key].get('id', key)

                if document_id not in destination_ids_set:
                    total_to_upsert.append(document_id)
                    to_upsert_namespaces.append(source_namespace)

    source_ids = []
    for source_namespace in source_namespaces:
        for ids in previous_hook.index.list(namespace=source_namespace):
            source_ids.extend(ids)

    source_ids_set = set(source_ids)

    for destination_namespace in destination_namespaces:
        destination_ids = []
        for ids in new_hook.index.list(namespace=destination_namespace):
            destination_ids.extend(ids)

        batch_size = 100
        for i in range(0, len(destination_ids), batch_size):
            batch_slice = destination_ids[i:i + batch_size]
            data = new_hook.index.fetch(ids=batch_slice, namespace=destination_namespace)

            keys_list = list(data.vectors.keys())

            for key in keys_list:
                document_id = data.vectors[key].get('id', key)

                if document_id not in source_ids_set:
                    total_to_delete.append(document_id)
                    to_delete_namespaces.append(destination_namespace)

    upload_document = list(set([item.split('#')[0] for item in total_to_upsert]))
    delete_document = list(set([item.split('#')[0] for item in total_to_delete]))
    print(f"Total files to be upserted: {len(upload_document)} with total document {len(total_to_upsert)}")
    print(f"Total files to be deleted: {len(delete_document)} with total document {len(total_to_delete)}")
    print(f"Namespaces's need upsert to new db: {to_upsert_namespaces}")
    print(f"Namespaces's need deletion to new db: {to_delete_namespaces}")

async def main():
    await calculate_modifications(source_hook, destination_hook, source_namespaces, destination_namespaces)

await main()


import nest_asyncio
import asyncio
from pinecone import Pinecone, ServerlessSpec, Vector, SparseValues
from tqdm import tqdm
import random
import time
from datetime import datetime

nest_asyncio.apply()

BATCH_SIZE = 40
total_documents = 0

destination_ids = []

source_ids = []
async def safe_upsert_document(vectors, namespace):
    """Safely upsert documents with retries."""
    for attempt in range(3):
        try:
            destination_hook.upsert_document(vectors, namespace=namespace)
            return
        except Exception as e:
            print(f"Attempt {attempt + 1} failed: {e}")
            await asyncio.sleep(2 ** attempt + random.uniform(0, 1))

async def compare_and_migrate(previous_hook, new_hook, source_namespaces, destination_namespaces):
    global total_documents
    pbar = tqdm(total=len(source_namespaces), desc="Processing Namespaces")

    global destination_ids
    for destination_namespace in destination_namespaces:
        for ids in destination_hook.index.list(namespace=destination_namespace):
            destination_ids.extend(ids)

    destination_ids_set = set(destination_ids)

    for source_namespace in source_namespaces:
        global source_ids
        for ids in previous_hook.index.list(namespace=source_namespace):
            source_ids.extend(ids)
        batch_size = 100

        for i in range(0, len(source_ids), batch_size):
            batch_slice = source_ids[i:i + batch_size]
            data = previous_hook.index.fetch(ids=batch_slice, namespace=source_namespace)

            keys_list = list(data.vectors.keys())
            num_documents = len(keys_list)
            print(f"Source Namespace: {source_namespace}, Number of documents: {num_documents}")

            vectors = []
            for key in keys_list:
                document_id = data.vectors[key].get('id', key)

                # Skip if the document is already in the destination
                if document_id in destination_ids_set:
                    continue

                metadata = data.vectors[key].get('metadata', {})
                text = metadata.get("text")

                if metadata.get("element_type") == 'table':
                    text = metadata.get('summary')

                # Add timestamps to the metadata
                current_time = datetime.utcnow().isoformat() + "Z"
                if 'created_at' not in metadata:
                    metadata['created_at'] = current_time
                metadata['modified_at'] = current_time

                sparse_embeddings = await splade_embedder.get_embedding(text)
                indices, values = sparse_embeddings[0]

                sparse = SparseValues(indices=indices, values=values)

                embs = await voyage_embedder.get_embedding(texts=text, input_type="document")
                vector_values = embs[0]
                vec = Vector(
                    id=document_id,
                    values=vector_values,
                    sparse_values=sparse,
                    metadata=metadata,
                )
                vectors.append(vec)

                if len(vectors) >= BATCH_SIZE:
                    await safe_upsert_document(vectors, source_namespace)
                    print(f"Number of documents upserted: {len(vectors)}")
                    await asyncio.sleep(5)
                    vectors = []

            if vectors:
                await safe_upsert_document(vectors, source_namespace)
                print(f"Number of documents upserted: {len(vectors)}")
                await asyncio.sleep(5)

        pbar.update(1)

    pbar.close()

async def main():
    await compare_and_migrate(source_hook, destination_hook, source_namespaces, destination_namespaces)

await main()


async def compare_and_delete(previous_hook, new_hook, source_namespaces, destination_namespaces):
    global total_documents
    pbar = tqdm(total=len(source_namespaces), desc="Processing Namespaces")

    source_ids = []
    for source_namespace in source_namespaces:
        for ids in previous_hook.index.list(namespace=source_namespace):
            source_ids.extend(ids)

    source_ids_set = set(source_ids)

    for destination_namespace in destination_namespaces:
        destination_ids = []
        for ids in new_hook.index.list(namespace=destination_namespace):
            destination_ids.extend(ids)
        batch_size = 100

        destination_ids_set = set(destination_ids)
        for i in range(0, len(destination_ids), batch_size):
            batch_slice = destination_ids[i:i + batch_size]
            data = new_hook.index.fetch(ids=batch_slice, namespace=destination_namespace)

            keys_list = list(data.vectors.keys())
            num_documents = len(keys_list)

            vectors = []
            for key in keys_list:
                document_id = data.vectors[key].get('id', key)

                if document_id in source_ids_set:
                    continue

                vectors.append(document_id)
            deletion_ids = list(set([item.split('#')[0] for item in vectors]))

            for deletion_id in deletion_ids:
                print(f"Deleting document with ID: {deletion_id}")
                new_hook.delete_document(deletion_id, namespace=destination_namespace)

        pbar.update(1)

    pbar.close()

async def main():
    await compare_and_delete(source_hook, destination_hook, source_namespaces, destination_namespaces)

await main()