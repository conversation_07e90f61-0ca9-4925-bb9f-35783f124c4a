help:
	@echo "available commands"
	@echo " - install    	: installs all requirements"
	@echo " - clean      	: cleans up all folders"
	@echo " - lint       	: Lint code with flake8, and check if black formatter should be applied."
	@echo " - formatter  	: Apply black formatting to code."
	@echo " - check      	: Runs base pre-commit on all files"
	@echo " - run_local  	: Runs in local mode with docker"
	@echo " - virtualenv 	: Creates virtualenv and installs dev-requirements"

install:
	pip install -r requirements-dev.txt

clean:
	find . -name '*.pyc' -exec rm -f {} +
	find . -name '*.pyo' -exec rm -f {} +
	find . -name '*~' -exec rm -f  {} +
	rm -rf .pytype/
	rm -rf dist/
	rm -rf eggs dbs logs
	find . -name '__pycache__' -type d -exec rm -rf {} +

lint:
	flake8 .
	black --check .

formatter:s
	isort .
	black .

check:
	pre-commit run -a

run_local:
	@echo "Running in local mode with docker."
	docker compose -f local.yml up --build

run_local_division:
	@echo "Running in local mode with docker."
	ENV_FILE_PATH=.env.division docker compose -f local.yml up --build

run-prod:
	@echo "Running in prod mode with docker."
	docker compose up -d --build

virtualenv:
	@echo "Creating virtualenv"
	python -m venv virtualenv
	@echo "Installing dev-requirements"
	source virtualenv/bin/activate && pip install -r requirements-dev.txt
	@echo "Done"
