import os
import re
import subprocess

import requests

OPENAI_API_KEY = os.environ.get("OPENAI_API_KEY", "")


def get_current_branch():
    result = subprocess.run(["git", "symbolic-ref", "--short", "HEAD"], capture_output=True, text=True)
    return result.stdout.strip()


def get_commit_hash():
    result = subprocess.run(["git", "rev-parse", "--short", "HEAD"], capture_output=True, text=True)
    return result.stdout.strip()


def get_last_release_tag():
    result = subprocess.run(["git", "describe", "--tags", "--abbrev=0"], capture_output=True, text=True)
    if result.returncode != 0:
        return None
    return result.stdout.strip()


def get_commits_between_releases(last_release, branch):
    if last_release:
        range_spec = f"{last_release}..{branch}"
    else:
        range_spec = branch
    result = subprocess.run(
        ["git", "log", range_spec, "--pretty=format:%s", "--no-merges"],
        capture_output=True,
        text=True,
    )
    commits = result.stdout.strip()
    filtered_commits = [
        commit
        for commit in commits.split("\n")
        if not re.match(r"^Merge pull request #\d+", commit) and not re.match(r"^Merge branch", commit)
    ]
    return "\n".join(filtered_commits)


def generate_release_notes(commits):
    prompt = (
        "Generate release notes for the given commits. The release notes should be concise but must highlight the key"
        " changes. Make sections based on their changes with proper formatting. Do not include any commit hashes. Do"
        " not output anything other than release notes. Limit response to 500 tokens. You don't need to add release"
        " heading such as Release Notes. "
    )
    try:
        response = requests.post(
            "https://api.openai.com/v1/chat/completions",
            headers={"Authorization": f"Bearer {OPENAI_API_KEY}", "Content-Type": "application/json"},
            json={
                "model": "gpt-4.1",
                "messages": [{"role": "system", "content": prompt}, {"role": "user", "content": commits}],
                "max_tokens": 500,
                "temperature": 0.7,
            },
        )
        response.raise_for_status()
        llm_notes = response.json().get("choices", [])[0].get("message", {}).get("content", "").strip()
        if llm_notes:
            return llm_notes
    except Exception as e:
        print(f"Error generating release notes with LLM: {e}:: Falling back to GitHub-style release notes")

    try:
        commit_list = commits.split("\n")
        formatted_notes = []

        for commit in commit_list:
            if commit.strip():
                formatted_notes.append(f"* {commit.strip()}")

        return "\n".join(formatted_notes)
    except Exception as e:
        print(f"Error generating GitHub-style release notes: {e}")
        return ""


def write_to_github_output(name, value):
    with open(os.environ["GITHUB_OUTPUT"], "a") as fh:
        fh.write(f"{name}<<EOF\n{value}\nEOF\n")


def main():
    branch_name = get_current_branch()
    print(f"Current Branch Name: {branch_name}")
    commit_ref = get_commit_hash()

    if branch_name != "main":
        version = f"{commit_ref}-{branch_name}"
    else:
        version = commit_ref

    last_release_tag = get_last_release_tag()
    print(f"Latest Release Tag: {last_release_tag}")
    commits = get_commits_between_releases(last_release_tag, branch_name)

    if commits:
        print(f"Commits in this Release: {commits}")
        release_notes = generate_release_notes(commits)
        write_to_github_output("release_notes", release_notes)
    else:
        write_to_github_output("release_notes", "Initial release")

    write_to_github_output("version", version)


if __name__ == "__main__":
    main()
