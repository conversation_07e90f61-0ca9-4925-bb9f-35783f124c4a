name: Deploy RATE Dev

on:
  workflow_dispatch:
    inputs:
      image_tag:
        description: 'Image tag to deploy (e.g. abc1234-staging)'
        required: true
        type: string

jobs:
  deploy-amgpt:
    runs-on: ubuntu-latest
    permissions:
      id-token: write
      contents: read
    env:
      INSTANCE_ID: i-0d293987cc790ccd1
      COMPOSE_PATH: /data/backend/docker-compose.yaml
    steps:
      - name: Configure AWS Credentials for RATE Dev
        env:
          ROLE: ${{ secrets.RATE_ACCT_ROLE }}
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ env.ROLE }}
          aws-region: us-east-1

      - name: Update Docker Compose on RATE Dev
        run: |
          aws ssm send-command \
            --document-name "AWS-RunShellScript" \
            --targets '[{"Key":"instanceIds","Values":["${{ env.INSTANCE_ID }}"]}]' \
            --parameters 'commands=["docker pull ghcr.io/${{ github.repository }}:${{ github.event.inputs.image_tag }}", "sed -i -E \"s|image: ghcr.io/${{ github.repository }}:[^ ]+|image: ghcr.io/${{ github.repository }}:${{ github.event.inputs.image_tag }}|g\" ${{ env.COMPOSE_PATH }}", "docker compose -f ${{ env.COMPOSE_PATH }} up -d"]' \
            --output text
