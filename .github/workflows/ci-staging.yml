name: Build and Push-Staging Instances

env:
  DOCKER_BUILDKIT: 1
  COMPOSE_DOCKER_CLI_BUILD: 1

on:
  push:
    branches: staging

concurrency:
  group: ${{ github.head_ref || github.run_id }}
  cancel-in-progress: true

jobs:
  build-and-push:
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./backend
    permissions:
      id-token: write
      contents: read

    outputs:
      IMAGE_TAG: ${{ steps.set-tag.outputs.IMAGE_TAG }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.CR_PAT }}

      - name: Set Docker image tag
        id: set-tag
        run: |
          if [[ "${{ github.ref }}" == "refs/heads/staging" ]]; then
            echo "IMAGE_TAG=${GITHUB_SHA::7}-staging" >> $GITHUB_OUTPUT
          else
            echo "IMAGE_TAG=${{ github.sha }}" >> $GITHUB_OUTPUT
          fi

      - name: Build Docker image
        run: |
          docker build -t ghcr.io/${{ github.repository }}:${{ steps.set-tag.outputs.IMAGE_TAG }} .

      - name: Push Docker image
        run: |
          docker push ghcr.io/${{ github.repository }}:${{ steps.set-tag.outputs.IMAGE_TAG }}


  deploy:
    needs: build-and-push
    runs-on: ubuntu-latest
    permissions:
      id-token: write
      contents: read
    strategy:
      matrix:
        account:
          - name: 'Insyde DEMO'
            instance: 'i-07ea5dce6c12e7f21'
            compose_path: '/data/insydeai-base-backend/docker-compose.yml'
            role_secret: DEMO_ACCT_ROLE

          - name: 'NFM GPT Dev'
            instance: 'i-0bb2642da3991db12'
            compose_path: '/data/backend/docker-compose.yaml'
            role_secret: NFMGPT_DEV

    steps:
      - name: Configure AWS Credentials for Insyde Demo
        env:
          ROLE: ${{ secrets.DEMO_ACCT_ROLE }}
        if: matrix.account.role_secret == 'DEMO_ACCT_ROLE'
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ env.ROLE }}
          aws-region: us-east-1

      - name: Configure AWS Credentials for NFM GPT Dev
        env:
          ROLE: ${{ secrets.NFMGPT_DEV }}
        if: matrix.account.role_secret == 'NFMGPT_DEV'
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ env.ROLE }}
          aws-region: us-east-1

      - name: Update Docker Compose
        run: |
          aws ssm send-command \
            --document-name "AWS-RunShellScript" \
            --targets '[{"Key":"instanceIds","Values":["${{ matrix.account.instance }}"]}]' \
            --parameters 'commands=["docker pull ghcr.io/${{ github.repository }}:${{ needs.build-and-push.outputs.IMAGE_TAG }}", "sed -i -E \"s|image: ghcr.io/${{ github.repository }}:[^ ]+|image: ghcr.io/${{ github.repository }}:${{ needs.build-and-push.outputs.IMAGE_TAG }}|g\" ${{ matrix.account.compose_path }}", "docker compose -f ${{ matrix.account.compose_path }} up -d"]' \
            --output text
