name: ci/cd - Division

env:
  DOCKER_BUILDKIT: 1
  COMPOSE_DOCKER_CLI_BUILD: 1

on:
  push:
    branches: division

concurrency:
  group: ${{ github.head_ref || github.run_id }}
  cancel-in-progress: true

jobs:
  build-and-push:
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./backend
    permissions:
      id-token: write
      contents: read

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.CR_PAT }}

      - name: Set Docker image tag
        id: set-tag
        run: |
          if [[ "${{ github.ref }}" == "refs/heads/division" ]]; then
            echo "IMAGE_TAG=${GITHUB_SHA::7}-division" >> $GITHUB_OUTPUT
          else
            echo "IMAGE_TAG=${{ github.sha }}" >> $GITHUB_OUTPUT
          fi

      - name: Build Docker image
        run: docker build -t ghcr.io/${{ github.repository }}:${{ steps.set-tag.outputs.IMAGE_TAG }} .

      - name: Push Docker image
        run: docker push ghcr.io/${{ github.repository }}:${{ steps.set-tag.outputs.IMAGE_TAG }}

      - name: Assume IAM Role
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ secrets.DEMO_ACCT_ROLE }}
          aws-region: us-east-1 # Specify your AWS region

      - name: Update Docker Compose on EC2
        run: |
          aws ssm send-command \
            --document-name "AWS-RunShellScript" \
            --targets '[{"Key":"instanceIds","Values":["i-0913a737ceabc6e46"]}]' \
            --parameters 'commands=["docker pull ghcr.io/${{ github.repository }}:${{ steps.set-tag.outputs.IMAGE_TAG }}", "sed -i -E \"s|image: ghcr.io/${{ github.repository }}:[^ ]+|image: ghcr.io/${{ github.repository }}:${{ steps.set-tag.outputs.IMAGE_TAG }}|g\" /data/backend/docker-compose.yml", "docker compose -f /data/backend/docker-compose.yml up -d"]' \
            --output text
