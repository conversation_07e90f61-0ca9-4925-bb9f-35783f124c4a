pydantic[email]==2.7.4
pydantic-settings==2.4.0
httpx==0.28.1
loguru==0.7.2
openai==1.79.0
# pinecone-client==4.1.0
pinecone[asyncio]==6.0.2
async-lru==2.0.4
pandas==2.2.2
lxml==5.2.2
boto3==1.34.108
python-dateutil==2.9.0.post0

# Auth
fastapi-sso==0.15.0
PyJWT[crypto]==2.8.0

# Framework and asgi
fastapi==0.111.0
uvicorn[standard]==0.34.2
watchdog==4.0.0
sse-starlette==2.1.0

# Database requirements
alembic==1.13.1
SQLAlchemy[asyncio,mypy]==2.0.30
asyncpg==0.29.0
psycopg[c]==3.1.19

# Celery
celery==5.4.0
redis==5.2.1
hiredis==2.3.2
flower==2.0.1
celery-redbeat==2.3.2

# Document Processing
langchain==0.3.7
langchain-community==0.3.7
unstructured==0.16.2
unstructured-client==0.22.0
tiktoken==0.7.0
html2text==2024.2.26

# Excel Report Generation
openpyxl==3.1.2

# PPT Generation
python-pptx==1.0.2

#cognito
cognitojwt[async]==1.4.1

# Logging
sentry-sdk[fastapi]==2.11.0

# Transformers for NLP models
# transformers==4.48.0

voyageai == 0.2.4

# Crontab validation
croniter==4.0.0

# pinecone-text[splade] == 0.9.0

#for message-analytics
scikit-learn==1.6.1

# tabulate
tabulate==0.9.0

google-genai==1.9.0

Faker==36.1.0

# PDF Generation
reportlab==4.3.1

bleach==6.2.0
limits==5.1.0
pdfid==1.1.3
python-magic==0.4.27
python-docx==1.1.2
sqlalchemy_utils==0.41.2

# for twilio integration
websockets==15.0.1
twilio==9.6.2
# for image search
faiss-cpu==1.11.0
