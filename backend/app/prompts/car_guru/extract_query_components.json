{"name": "extract_query_components", "description": "Extract and process query components to search for vehicles in the Car Guru database. Use this function to filter, aggregate, or get specific values from vehicle listings.", "strict": false, "parameters": {"type": "object", "properties": {"query_type": {"type": "string", "enum": ["FILTER", "SINGLE_VALUE", "AGGREGATE"], "description": "Type of query to perform: FILTER returns matching vehicles, SINGLE_VALUE returns a specific field value, AGGREGATE performs calculations on matching vehicles"}, "target_field": {"type": "string", "enum": ["Year", "Make", "Model", "Body type", "Doors", "Drivetrain", "Engine", "Exterior color", "Mileage", "Fuel type", "Transmission", "Price"], "description": "Field to target for SINGLE_VALUE or AGGREGATE query types"}, "aggregate_function": {"type": "string", "enum": ["AVERAGE", "COUNT", "MIN", "MAX", "SUM", "NONE"], "description": "Aggregation function to apply when query_type is AGGREGATE"}, "conditions": {"type": "array", "description": "List of conditions to filter vehicles by", "items": {"type": "object", "properties": {"field": {"type": "string", "enum": ["Year", "Make", "Model", "Body type", "Doors", "Drivetrain", "Engine", "Exterior color", "Mileage", "Fuel type", "Transmission", "Price"], "description": "Field name to apply the condition to"}, "operator": {"type": "string", "enum": ["equals", "not_equals", "greater_than", "less_than", "greater_than_equals", "less_than_equals", "contains", "not_contains", "year_equals", "year_greater_than", "year_less_than", "is_null", "is_not_null", "in_list", "not_in_list", "between"], "description": "Operator to apply in the condition"}, "values": {"type": "array", "description": "Values to use in the condition. For 'between' operator, provide exactly 2 values. For most other operators, provide 1 value. For 'in_list'/'not_in_list', provide multiple values.", "items": {"type": ["string", "number"]}}, "logical_operator": {"type": "string", "enum": ["AND", "OR"], "description": "Logical operator to combine with previous condition", "default": "AND"}}, "required": ["field", "operator", "values"]}}}, "required": ["conditions"]}}