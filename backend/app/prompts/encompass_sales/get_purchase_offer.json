{"name": "get_purchase_offer", "description": "Generate purchase offer details based on user input and parameters. All values are optional. Always call this function, even if none of the values are given.", "strict": false, "parameters": {"type": "object", "properties": {"loan_number": {"type": "string"}, "first_name": {"type": "string", "description": "The first name of the purchaser."}, "last_name": {"type": "string", "description": "The last name of the purchaser."}, "street_address": {"type": "string", "description": "Street address of the property."}, "city": {"type": "string", "description": "City where the property is located."}, "state": {"type": "string", "description": "State where the property is located."}, "zip_code": {"type": "string", "description": "Postal code for the property location."}, "purchase_price": {"type": "number", "description": "Total purchase price of the property."}, "closing_fees": {"type": "number", "description": "Closing fees for the purchase transaction."}, "property_tax_rate": {"type": "number", "description": "Property tax rate percentage."}, "annual_hoi": {"type": "number", "description": "Annual homeowners insurance amount."}, "lender_concession": {"type": "number", "description": "Lender concession amount."}, "dpa_grant": {"type": "number", "description": "DPA Grant amount."}, "option_1": {"type": "object", "properties": {"dp_percent": {"type": "number", "description": "Down payment percentage for option 1."}, "term": {"type": "number", "description": "Loan term for option 1."}, "rate": {"type": "number", "description": "Interest rate percentage for option 1."}, "points": {"type": "number", "description": "Points percentage for option 1."}, "mortgage_insurance_rate": {"type": "number", "description": "Mortgage insurance rate percentage."}, "loan_type": {"type": "string", "description": "Loan Type for option 1."}, "va_funding_fee": {"type": "number", "description": "VA Funding Fee in case Loan Type is VA."}, "desc": {"type": "string", "description": "Description for option 1."}}}, "option_2": {"type": "object", "properties": {"dp_percent": {"type": "number", "description": "Down payment percentage for option 2."}, "term": {"type": "number", "description": "Loan term for option 2."}, "rate": {"type": "number", "description": "Interest rate percentage for option 2."}, "points": {"type": "number", "description": "Points percentage for option 2."}, "mortgage_insurance_rate": {"type": "number", "description": "Mortgage insurance rate percentage."}, "loan_type": {"type": "string", "description": "Loan Type for option 2."}, "va_funding_fee": {"type": "number", "description": "VA Funding Fee in case Loan Type is VA."}, "desc": {"type": "string", "description": "Description for option 2."}}}, "option_3": {"type": "object", "properties": {"dp_percent": {"type": "number", "description": "Down payment percentage for option 3."}, "term": {"type": "number", "description": "Loan term for option 3."}, "rate": {"type": "number", "description": "Interest rate percentage for option 3."}, "points": {"type": "number", "description": "Points percentage for option 3."}, "mortgage_insurance_rate": {"type": "number", "description": "Mortgage insurance rate percentage."}, "loan_type": {"type": "string", "description": "Loan Type for option 3."}, "va_funding_fee": {"type": "number", "description": "VA Funding Fee in case Loan Type is VA."}, "desc": {"type": "string", "description": "Description for option 3."}}}, "option_4": {"type": "object", "properties": {"dp_percent": {"type": "number", "description": "Down payment percentage for option 4."}, "term": {"type": "number", "description": "Loan term for option 4."}, "rate": {"type": "number", "description": "Interest rate percentage for option 4."}, "points": {"type": "number", "description": "Points percentage for option 4."}, "mortgage_insurance_rate": {"type": "number", "description": "Mortgage insurance rate percentage."}, "loan_type": {"type": "string", "description": "Loan Type for option 4."}, "va_funding_fee": {"type": "number", "description": "VA Funding Fee in case Loan Type is VA."}, "desc": {"type": "string", "description": "Description for option 4."}}}, "time_frame": {"type": "number", "description": "Number of years the purchaser plans to stay in the property."}, "investment_roi": {"type": "number", "description": "Expected rate of return on investment."}, "property_appreciation_rate": {"type": "number", "description": "Expected annual property appreciation rate."}}, "required": []}}