{"name": "make_refinance_call", "description": "Call a provided number by the user to discuss the provided refinancing details and loan details", "strict": false, "parameters": {"type": "object", "properties": {"phone_number": {"type": "string", "deescription": "phone number provided by the user"}, "loan_number": {"type": "string"}, "first_name": {"type": "string", "description": "First name of the user. If full name is given split it."}, "last_name": {"type": "string", "description": "Last name of the user. If full name is given split it."}, "interest_rate": {"type": "number", "description": "Interest rate of the loan."}, "loan_amount": {"type": "number", "description": "Loan amount requested by the user."}, "monthly_payment_pi": {"type": "number", "description": "Monthly payment installment of the loan."}, "closed_date": {"type": "string", "description": "Date when the loan was closed strictly in the format: MM/DD/YYYY"}, "loan_term": {"type": "integer", "description": "loan term in months"}, "closing_fees": {"type": "number", "description": "Total closing fees for the refinance loan."}, "options": {"type": "array", "items": {"type": "object", "properties": {"term": {"type": "number", "description": "Loan term in years."}, "rate": {"type": "number", "description": "Interest rate percentage."}, "points": {"type": "number", "description": "Points percentage."}, "mortgage_insurance_rate": {"type": "number", "description": "Mortgage insurance rate percentage."}, "loan_type": {"type": "string", "description": "Loan Type."}, "va_funding_fee": {"type": "number", "description": "VA Funding Fee in case Loan Type is VA."}}}}}, "required": []}}