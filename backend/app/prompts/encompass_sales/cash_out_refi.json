{"name": "cash_out_refi", "description": "generate cash_out_refi email. All values are optional. Always call this function, even if none of the values are given. Do not assume any values. Use exactly as given by the user without any modifications.", "strict": false, "parameters": {"type": "object", "properties": {"loan_number": {"type": "string"}, "first_name": {"type": "string", "description": "First name of the user. If full name is given split it."}, "last_name": {"type": "string", "description": "Last name of the user. If full name is given split it."}, "interest_rate": {"type": "number", "description": "Interest rate of the loan."}, "loan_amount": {"type": "number", "description": "Loan amount requested by the user."}, "monthly_payment_pi": {"type": "number", "description": "Monthly payment installment of the loan."}, "closed_date": {"type": "string", "description": "Date when the loan was closed. Format: MM/DD/YYYY"}, "loan_term": {"type": "integer", "description": "loan term in months"}, "street_address": {"type": "string", "description": "Street address of the property"}, "city": {"type": "string", "description": "City of the property"}, "state": {"type": "string", "description": "State of the property"}, "zip_code": {"type": "string", "description": "Zip Code of the property"}, "appraised_value": {"type": "number", "description": "Appraised value of the property"}, "max_cashout": {"type": "number", "description": "Maximum Cashout for a user."}, "debt_amount": {"type": "number", "description": "Debt reconsolidated by the user."}, "debt_payment": {"type": "number", "description": "Monthly debt payment by the user."}, "desired_cashout": {"type": "number", "description": "Desired Cashout by the user."}, "closing_fees": {"type": "number", "description": "Total closing fees for the cash-out refinance loan."}, "options": {"type": "array", "items": {"type": "object", "properties": {"term": {"type": "number", "description": "Loan term in years."}, "rate": {"type": "number", "description": "Interest rate percentage."}, "points": {"type": "number", "description": "Points percentage."}, "mortgage_insurance_rate": {"type": "number", "description": "Mortgage insurance rate percentage."}, "loan_type": {"type": "string", "description": "Loan Type."}, "va_funding_fee": {"type": "number", "description": "VA Funding Fee in case Loan Type is VA."}}}}}, "required": []}}