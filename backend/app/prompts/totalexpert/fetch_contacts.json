{"name": "fetch_contacts", "description": "Fetch contacts based on given data. Users can also fetch all the contacts. To do that do not pass any arguments.", "strict": false, "parameters": {"type": "object", "properties": {"input_fields": {"type": "array", "items": {"type": "object", "properties": {"field_name": {"type": "string", "description": "Name of the field user wants to search on. Only the fields provided in the enums are available for searching. If full name is given, convert them to first and last name and use that. ", "enum": ["address", "birthday", "city", "close_date", "creation_date", "credit_score", "email", "employer_name", "employer_state", "employer_zip", "external_id", "first_name", "id", "last_contacted_date", "last_name", "phone_cell", "phone_home", "phone_office", "referred_by", "referred_to", "source", "state", "zip_code", "email_contains", "group_name", "internal_updated_at_start", "internal_updated_at_end", "pre_approval_issued_date"]}, "value": {"type": "string", "description": "values of field converted to appropriate datatype, for eg. if the value is date, it should be formatted in YYYY-MM-DDTHH:MM:SSZ format."}, "matchType": {"type": "string", "enum": ["GreaterThan", "<PERSON><PERSON><PERSON>", "Equals", "NotEqual", "GreaterThanEquals", "LessThanEquals", "Exact", "Contains", "StartsWith", "EndsWith"], "description": "Type of how to match the field with it's value."}}}, "minItems": 1, "description": "Input Fields name that user have mentioned in query. For these Fields, user will provide values. Include all values with their values."}}, "required": []}}