{"name": "fetch_contact_notes", "description": "fetch notes of a contact. One of email, contact_id, phone_number, first or last name is required. Do not ask for other information if one is already provided.", "strict": false, "parameters": {"type": "object", "properties": {"contact_id": {"type": "string", "description": "ID of contact on which the note should be added"}, "email": {"type": "string", "description": "Email of contact"}, "phone_number": {"type": "string"}, "first_name": {"type": "string", "description": "First name of contact. If full name is provided, convert it into first name and use that"}, "last_name": {"type": "string", "description": "Last name. if full name is provided, only use last name."}, "note_type": {"type": "string", "enum": ["general note", "task"], "description": "Type of note."}}, "required": []}}