{"name": "add_remove_contact_to_group", "description": "Add or remove a contact to/from a contact group.", "strict": false, "parameters": {"type": "object", "properties": {"operation": {"type": "string", "enum": ["add", "remove"], "description": "Specify whether to add or remove the contact from the group."}, "contact_group": {"type": "string", "description": "Name of the contact group."}, "create_new_group": {"type": "boolean", "description": "Whether to create a new contact group. Make sure this is true only if the user explicitly asks to create a new contact group.", "default": false}, "input_fields": {"type": "array", "items": {"type": "object", "properties": {"field_name": {"type": "string", "enum": ["id", "first_name", "last_name", "email", "email_contains", "phone_cell", "phone_home", "phone_office", "source", "archived", "internal_updated_at_start", "internal_updated_at_end"], "description": "Name of the contact field to match."}, "value": {"type": "string", "description": "Value of the field to match. For dates, use YYYY-MM-DDTHH:MM:SSZ format."}}, "required": ["field_name", "value"]}, "minItems": 1, "description": "Fields to identify the contact (e.g., first and last name)."}}, "required": ["operation", "contact_group", "input_fields"]}}