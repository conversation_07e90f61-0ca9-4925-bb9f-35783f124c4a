{"name": "add_contact", "description": "Add contact based on given data. Use the given information to add contact. Do not ask for more information If not provided. Even if no information is given, still do not ask for more information but just call the function.", "strict": false, "parameters": {"type": "object", "properties": {"input_fields": {"type": "array", "items": {"type": "object", "properties": {"field_name": {"type": "string", "description": "Name of the field. If user have provided one. Otherwise, it should be empty.", "enum": ["address", "address_2", "birthday", "city", "classification", "close_date", "creation_date", "credit_score", "credit_score_date", "credit_score_expiration_date", "email", "email_work", "employer_address", "employer_address_2", "employer_city", "employer_license_number", "employer_name", "employer_state", "employer_zip", "external_id", "fax", "first_name", "id", "internal_created_at", "internal_updated_at", "last_contacted_date", "last_modified_date", "last_name", "license_number", "linkedin_url", "list_date", "m_initial", "m_name", "nickname", "ok_to_call", "ok_to_email", "ok_to_mail", "other_url", "phone_cell", "phone_home", "phone_office", "pre_approval_issued_date", "referred_by", "referred_to", "source", "state", "suffix", "title", "zip_code"]}, "value": {"type": "string", "description": "values of field converted to appropriate datatype, for eg. if the value is date, it should be formatted in YYYY-MM-DDTHH:MM:SSZ format."}}}, "minItems": 0, "description": "Input fields user have mentioned. This should be empty if user have not provided any fields."}}, "required": []}}