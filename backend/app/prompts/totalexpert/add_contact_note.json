{"name": "add_contact_note", "description": "Add notes in contacts. All values are optional. Please always call this function to add this regardless of the values given.", "strict": false, "parameters": {"type": "object", "properties": {"note_title": {"type": "string", "description": "Title of the note. If user doesn't provide the title, generate one based on note content."}, "note": {"type": "string", "description": "Content of the note. Ask user to provide the note content. DO NOT Assume or generate the content yourself."}, "contact_information": {"type": "object", "properties": {"contact_id": {"type": "string", "description": "ID of contact on which the note should be added"}, "email": {"type": "string", "description": "Email of contact"}, "phone_number": {"type": "string"}, "first_name": {"type": "string"}, "last_name": {"type": "string"}}, "description": "Contact information on which the note should be added."}}, "required": ["contact_information"]}}