{"name": "calculate_rate_term_refi", "description": "Performs Rate term refi calculations for the given journey and loan numbers", "strict": false, "parameters": {"type": "object", "properties": {"journey_id": {"type": "string", "description": "ID of the journey to be triggered"}, "loan_numbers": {"type": "array", "description": "List of loan numbers to be used in the calculation", "items": {"type": "string"}}, "closing_fees": {"type": "number", "description": "Closing fees associated with the loan."}, "rates": {"type": "object", "properties": {"thirty_year_fixed": {"type": "number", "description": "30 year fixed interest rate."}, "twenty_five_year_fixed": {"type": "number", "description": "25 year fixed interest rate."}, "twenty_year_fixed": {"type": "number", "description": "20 year Fixed interest rate."}, "fifteen_year_fixed": {"type": "number", "description": "15 year fixed interest rate."}}}}, "required": ["journey_id", "loan_numbers"]}}