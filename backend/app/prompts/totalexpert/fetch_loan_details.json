{"name": "fetch_loan_details", "description": "Fetches loan details based on given data. None of the fields are required. Users can also fetch all the loans by not passing any arguments.", "strict": false, "parameters": {"type": "object", "properties": {"loan_type": {"type": "string", "enum": ["active", "closed", "other"], "description": "Type of loan. For active or closed loan, it will be mentioned in the query itself. If not loan will be other loan."}, "input_fields": {"type": "array", "items": {"type": "object", "properties": {"field_name": {"type": "string", "description": "Name of the field user wants to search on. If full name is given, convert them to first and last name and use that. Also remember, this field is optional. If nothing is given, leave this field empty.", "enum": ["owner_email", "amort_type", "application_date", "application_received_date", "application_sent_date", "appraisal_expected_date", "appraisal_ordered_date", "appraisal_received_date", "appraised_value", "approval_date", "approval_date_estimated", "available_line_amount", "available_principal_limit", "borrower_entity_type", "channel", "city", "closing_date", "closing_date_estimated", "completion_date", "county", "debt_to_income", "epo_date", "estimated_value", "expected_interest_rate", "first_payment_date", "last_modified_date", "legal_entity_name", "lien_position", "line_amount", "loan_amount", "loan_application_number", "loan_arm_expiration_date", "loan_date", "loan_name", "loan_number", "loan_rate", "loan_term", "loan_to_value", "loan_to_value_combined", "lock_date", "lock_expiration_date", "lock_status", "margin", "monthly_mi", "monthly_pi_payment", "occupancy_type", "pre_approval_issued", "property_type", "purchase_price", "purchased_date", "source", "state", "zip_code", "borrower", "loan_program", "loan_purpose", "loan_status", "loan_type", "borrower_id", "loan_name_contains", "first_name", "last_name", "email", "phone", "funded_date_start", "funded_date_end", "internal_updated_at_start", "internal_updated_at_end", "internal_created_at_start", "internal_created_at_end", "closing_disclosure_out_date", "max_ltv", "referral_source"]}, "value": {"type": "string", "description": "values of field converted to appropriate datatype, for eg. if the value is date, it should be formatted in YYYY-MM-DDTHH:MM:SSZ format."}, "matchType": {"type": "string", "enum": ["GreaterThan", "<PERSON><PERSON><PERSON>", "Equals", "NotEqual", "GreaterThanEquals", "LessThanEquals", "Exact", "Contains", "StartsWith", "EndsWith"], "description": "Type of how to match the field with it's value."}}}, "minItems": 1, "description": "Input Fields name that user have mentioned in query. For these Fields, user will provide values. Include all values with their values."}, "output_fields": {"type": "array", "items": {"type": "string", "enum": ["address", "adverse_action_date", "amort_type", "amort_type_arm_desc", "annual_review_date", "application_date", "application_received_date", "application_sent_date", "appraisal_expected_date", "appraisal_ordered_date", "appraisal_received_date", "appraised_value", "approval_date", "approval_date_estimated", "audits_date", "available_line_amount", "available_principal_limit", "borrower_entity_type", "business_partner", "buydown_points", "channel", "city", "closing_date", "closing_date_estimated", "closing_disclosure_out_date", "closing_disclosure_signed_date", "completion_date", "county", "created_date", "credit_report_date", "credit_report_ordered_date", "ctc_date", "debt_to_income", "denial_reason", "docs_out_date", "documents_signed_date", "epo_date", "escrow_waived", "estimated_value", "expected_interest_rate", "external_id", "first_payment_date", "funded_date", "funding_date", "funds_requested_date", "guarantee_type", "id", "index", "internal_created_at", "internal_updated_at", "investor", "is_first_time_buyer", "last_modified_date", "legal_entity_name", "lender_paid_compensation", "lien_position", "line_amount", "loan_amount", "loan_application_number", "loan_arm_expiration_date", "loan_date", "loan_name", "loan_number", "loan_rate", "loan_term", "loan_to_value", "loan_to_value_combined", "lock_date", "lock_expiration_date", "lock_status", "margin", "master_loc_number", "max_acquisition_ltc", "max_ltv", "max_rehab_ltc", "monthly_mi", "monthly_pi_payment", "monthly_pi_with_mi_payment", "monthly_service_fee", "occupancy_type", "origination_fee", "partner_account", "post_closed_date", "pre_approval_expiration_date", "pre_approval_issued_date", "pre_approval_issues", "pricing_option", "principal_limit", "processing_start_date", "processing_start_date_estimated", "property_type", "purchase_price", "purchased_date", "referral_source", "resubmittal_date", "second_appraisal_ordered_date", "second_appraisal_received_date", "second_appraisal_scheduled_date", "second_appraisal_value", "source", "state", "status_updated_date", "tenure_payment", "tenure_payment_amount", "term_payment_amount", "third_party_costs", "underwriting_approval_date", "underwriting_approval_date_estimated", "underwriting_submission_date", "underwriting_submission_date_estimated", "upfront_mip", "zip_code"]}, "description": "Fields user wants to output. This will be empty if user is not asking to output specific fields. Use first_name and last_name if borrower name is asked."}}, "required": []}}