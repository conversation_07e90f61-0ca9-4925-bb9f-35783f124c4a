{"name": "get_property_valuation_details", "description": "Fetch the valuation details of a property based on the given address.", "parameters": {"type": "object", "properties": {"street_address": {"type": "string", "description": "The street address of the property (e.g., '1600 Amphitheatre Parkway')."}, "city_state": {"type": "string", "description": "The city and state of the property (e.g., 'Mountain View, CA')."}}, "required": ["street_address", "city_state"]}}