{"name": "convert_input_to_payload", "description": "Extract field name and their values from user query. Also determine the operators that should be used for multiple value and fields.", "strict": false, "parameters": {"type": "object", "properties": {"loan_type": {"type": "string", "description": "Type of loan. For active, or closed loans, the type can be found in user query. For EPO loans, look for queries related to epo date. If none of these are given, then the loan will be other.", "default": "other", "enum": ["active", "pipeline", "purchase", "closed", "funded", "epo", "locked", "other"]}, "input_fields": {"type": "array", "items": {"type": "object", "properties": {"field_name": {"type": "string", "description": "Field name converted as API input field"}, "values": {"type": "array", "items": {"type": "string"}, "description": "values of field converted to appropriate datatype, for eg. if the value is date, it should be formatted in YYYY-MM-DD format. Single field can have multiple values"}, "matchType": {"type": "array", "items": {"type": "string", "enum": ["Equals", "NotEquals", "GreaterThan", "GreaterThanOrEquals", "<PERSON><PERSON><PERSON>", "LessThanOrEquals", "Exact", "Contains", "IsEmpty", "IsNotEmpty", "StartsWith"]}, "description": "Match type for value filtering. list matchType for each of the values. For text field values, matchType normally Should be 'Contains'"}, "operator": {"type": "string", "enum": ["AND", "OR"], "description": "logical operator to filter different values of this field."}}}, "description": "Input Fields name that user have mentioned in query. For these Fields, user will provide values. Include all values with their values."}, "operator": {"type": "string", "enum": ["AND", "OR"], "description": "Filter operators for fields"}, "sorting": {"type": "array", "items": {"type": "object", "properties": {"field_name": {"type": "string", "description": "Field name converted as API input field"}, "ordering": {"type": "string", "enum": ["asc", "desc"], "description": "Sorting order"}}}, "description": "Sorting fields and their order"}, "output_fields": {"type": "array", "items": {"type": "string"}, "description": "Fields user wants to output. This will be empty if user is not asking to output only specific fields."}, "post_processing": {"type": "array", "items": {"type": "object", "properties": {"field_name": {"type": "string", "description": "Field name converted as API input field"}, "result_type": {"type": "string", "enum": ["sum", "average", "min", "max", "group_by"], "description": "Type of post processing to be done on the field"}}}, "description": "Post Processing on the generated data"}}, "required": ["loan_type"]}}