{"name": "extract_query_components", "description": "Extract field names, their corresponding values, and aggregation operations from a loan query. Note: 1. If Property state, extract the abbreviation form of state. For example: if New York, extract NY. 2. If loan type: extract FHA, VA, or Conventional.", "strict": false, "parameters": {"type": "object", "properties": {"query_type": {"type": "string", "enum": ["FILTER", "AGGREGATE", "SINGLE_VALUE"], "description": "Type of query - FILTER for listing loans, AGGREGATE for calculations, SINGLE_VALUE for specific field value"}, "aggregate_function": {"type": "string", "enum": ["COUNT", "SUM", "AVERAGE", "MIN", "MAX", "NONE"], "description": "Aggregation function to apply. Use NONE if no aggregation is needed."}, "target_field": {"type": "string", "enum": ["Loan Number", "Property Address", "Property City", "Property State", "Property Zip", "Appraised Value", "<PERSON><PERSON>", "Occupancy", "Loan Type", "Base Loan Amount", "Total Loan Amount", "Interest Rate", "Interviewer Name", "Closed Date", "Borrower First Name", "Borrower Last Name", "Borrower Home Phone", "<PERSON><PERSON><PERSON>", "Borrower Full Name"], "description": "Field to retrieve or aggregate"}, "conditions": {"type": "array", "items": {"type": "object", "properties": {"field": {"type": "string", "enum": ["Loan Number", "Property Address", "Property City", "Property State", "Property Zip", "Appraised Value", "<PERSON><PERSON>", "Occupancy", "Loan Type", "Base Loan Amount", "Total Loan Amount", "Interest Rate", "Interviewer Name", "Closed Date", "Borrower First Name", "Borrower Last Name", "Borrower Home Phone", "<PERSON><PERSON><PERSON>", "Borrower Full Name"], "description": "Field to apply the condition on"}, "values": {"type": "array", "items": {"type": "string"}, "description": "Values to compare against"}, "operator": {"type": "string", "enum": ["equals", "not_equals", "greater_than", "less_than", "greater_than_equals", "less_than_equals", "contains", "not_contains", "starts_with", "ends_with", "year_equals", "year_greater_than", "year_less_than", "is_null", "is_not_null", "in_list", "not_in_list", "between"], "description": "Comparison operator to use"}, "logical_operator": {"type": "string", "enum": ["AND", "OR"], "description": "Logical operator to combine with the next condition"}}, "required": ["field", "values", "operator"]}, "description": "List of conditions to apply to the query"}}, "required": ["query_type", "target_field", "conditions"]}}