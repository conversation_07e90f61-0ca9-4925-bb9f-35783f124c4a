import fnmatch
import time
import uuid

import cognitojwt
from config import settings
from fastapi import Request
from fastapi.responses import JSONResponse
from loguru import logger
from rate_limit_constants import RATE_LIMITED_RULES, RateLimitRule, RateLimitScope
from tools.utils.redis import get_redis_client


def match_path(request_path: str, patterns: list[str]) -> tuple[str, dict] | None:
    """
    Match a request path against a list of patterns (supports wildcards and dynamic segments).
    Returns the matched pattern and extracted params if any.
    """
    for pattern in patterns:
        # Wildcard match (e.g., /api/v1/*/stream/)
        if fnmatch.fnmatch(request_path, pattern):
            return pattern, {}
        # Dynamic segment match (e.g., /api/v1/{id}/stream/)
        if "{" in pattern and "}" in pattern:
            parts = pattern.strip("/").split("/")
            req_parts = request_path.strip("/").split("/")
            if len(parts) != len(req_parts):
                continue
            params = {}
            matched = True
            for p, r in zip(parts, req_parts):
                if p.startswith("{") and p.endswith("}"):
                    params[p[1:-1]] = r
                elif p != r:
                    matched = False
                    break
            if matched:
                return pattern, params
    return None


def find_matching_rule(path: str) -> RateLimitRule | None:
    """
    Returns the first matching rate limit rule for the given path, or None.
    """
    for rule in RATE_LIMITED_RULES:
        if match_path(path, [rule.path]):
            return rule
    return None


def extract_bearer_token(auth_header: str | None) -> str | None:
    if not auth_header:
        return None
    parts = auth_header.strip().split()
    if len(parts) == 2 and parts[0].lower() == "bearer":
        return parts[1]
    return None


def ip_to_uuid(client_ip: str) -> str:
    return str(uuid.uuid5(uuid.NAMESPACE_DNS, client_ip.strip()))


def get_client_ip(request: Request) -> str:
    xff = request.headers.get("x-forwarded-for")
    if xff:
        # X-Forwarded-For may contain multiple IPs, take the first one
        return ip_to_uuid(xff.split(",")[0].strip())
    return ip_to_uuid(request.client.host) if request.client else "unknown"


def get_user_id(request: Request) -> str | None:
    """
    Attempts to extract user id from request.state.user, or by decoding the Bearer token.
    NOTE: Decoding the token is a fallback if user is not set in request.state.
    """
    user = getattr(request.state, "user", None)
    if user and getattr(user, "id", None):
        return str(user.id)
    # Try to decode the Bearer token if present
    auth_header = request.headers.get("authorization")
    if auth_header and auth_header.lower().startswith("bearer "):
        token = extract_bearer_token(auth_header)
        if not token:
            return None
        try:
            claims = cognitojwt.decode(
                token=token,
                region=settings.AWS_DEFAULT_REGION,
                userpool_id=settings.AWS_COGNITO_USERPOOL_ID,
                app_client_id=settings.WEB_CLIENT_ID,
                testmode=False,
            )
            user_id = claims.get("sub")
            if user_id:
                return user_id
        except Exception as e:
            logger.warning(f"Failed to decode JWT for user extraction in rate limit: {e}")
    return None


def get_key_id(request: Request, rule: RateLimitRule) -> str:
    """
    Returns the correct key for rate limiting based on rule scope.
    If scope is 'user', use user_id (fallback to IP if not found). If 'ip', use IP.
    """
    if rule.scope == RateLimitScope.USER:
        user_id = get_user_id(request)
        if user_id:
            return user_id
        # fallback to IP if user_id not found
        return get_client_ip(request)
    else:  # default to IP
        return get_client_ip(request)


async def handle_rate_limit(request: Request):
    """
    Handles rate limiting logic. Returns a Response if limited, else None.
    Supports per-IP or per-user, depending on rule's 'scope'.
    """
    path = request.url.path
    rule = find_matching_rule(path)
    if not rule:
        return None
    key_id = get_key_id(request, rule)
    redis = await get_redis_client()
    key = f"rl:{rule.path}:{key_id}"
    now = int(time.time())
    window_start = now - rule.period
    try:
        async with redis.pipeline(transaction=True) as pipe:
            await pipe.zremrangebyscore(key, 0, window_start)
            await pipe.zadd(key, {str(now): now})
            await pipe.zcard(key)
            await pipe.expire(key, rule.period)
            _, _, req_count, _ = await pipe.execute()
    except Exception as e:
        logger.error(f"Redis rate limiting failed: {e}")
        return None
    if req_count > rule.limit:
        retry_after = rule.period
        logger.warning(f"Rate limit exceeded: path={path}, key_id={key_id}, time={now}")
        return JSONResponse(
            status_code=429,
            content={
                "detail": "Too Many Requests. Rate limit exceeded.",
                "rate_limit": rule.limit,
                "rate_period": rule.period,
            },
            headers={"Retry-After": str(retry_after)},
        )
    return None
