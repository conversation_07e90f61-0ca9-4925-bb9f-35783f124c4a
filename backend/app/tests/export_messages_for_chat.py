import json
import random
import sys

import requests
from dotenv import load_dotenv

load_dotenv()  # noqa

sys.path.append("../")  # noqa
from config import settings  # noqa

BASE_URL = "https://dev-backend.insyde.ai"
TOKEN = f"Bearer {settings.API_KEY}"

ASSISTANT_WHITELIST = [
    "asst_5BNMELm5ToB2fWssL6bvoFDM",  # sidekick
    "asst_fU8PnGoba6LPyz420LfLFgFU",  # loan
    "asst_CfckZIUoQREyVCaNQZMOji1o",  # test
]

HEADERS = {
    "Accept-Language": "en-US,en;q=0.9",
    "Authorization": TOKEN,
    "Connection": "keep-alive",
    "Content-Type": "application/json",
    "Origin": "https://dev.insyde.ai",
    "Referer": "https://dev.insyde.ai/",
    "Sec-Fetch-Dest": "empty",
    "Sec-Fetch-Mode": "cors",
    "Sec-Fetch-Site": "same-site",
    "User-Agent": "Mozilla/5.0 (X11; Linux x86_64) Chrome/132.0.0.0",
    "accept": "application/json",
    "sec-ch-ua": '"Not A(Brand";v="8", "Chromium";v="132", "Google Chrome";v="132"',
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": '"Linux"',
}


def get_recent_messages(p=1, size=20):
    messages = []
    for page in range(1, 20):
        random_assistant = random.choice(ASSISTANT_WHITELIST)
        url = f"{BASE_URL}/api/analytics/recent-messages/?&page={p}&size=20&assistant_id={random_assistant}&search=adi"
        response = requests.get(url, headers=HEADERS)
        assert response.status_code == 200, "Failed to call recent messages API"
        resp = response.json()["data"]
        messages.append(resp)
    return messages


def flatten_messages(messages):
    messages_flat = []
    for item in messages:
        for sub_item in item:
            messages_flat.append(sub_item)
    return messages_flat


def filter_user_messages(messages_flat):
    return [item for item in messages_flat if item["role"] == "user"]


def save_messages_to_file(messages, filename="messages.json"):
    with open(filename, "w") as f:
        json.dump(messages, f)


if __name__ == "__main__":
    messages = get_recent_messages()
    messages_flat = flatten_messages(messages)
    user_messages = filter_user_messages(messages_flat)
    save_messages_to_file(user_messages)
