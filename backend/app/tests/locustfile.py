import json
import random
import sys
import threading
import time

from dotenv import load_dotenv
from locust import HttpUser, TaskSet, between, task

load_dotenv()  # noqa

sys.path.append("../")  # noqa

from config import settings  # noqa

BASE_URL = "https://dev-backend.insyde.ai"

# Shared lock for each user token (prevents multiple simultaneous chats)
user_locks = {
    token: threading.Lock() for token in [settings.API_KEY] + [f"{settings.API_KEY}user{i}" for i in range(1, 1001)]
}

# Global headers
GLOBAL_HEADERS = {
    "Accept-Language": "en-US,en;q=0.9",
    "Connection": "keep-alive",
    "Content-Type": "application/json",
    "Origin": "https://dev.insyde.ai",
    "Referer": "https://dev.insyde.ai/",
    "Sec-Fetch-Dest": "empty",
    "Sec-Fetch-Mode": "cors",
    "Sec-Fetch-Site": "same-site",
    "User-Agent": "Mozilla/5.0 (X11; Linux x86_64) Chrome/*********",
    "accept": "application/json",
    "sec-ch-ua": '"Not A(Brand";v="8", "Chromium";v="132", "Google Chrome";v="132"',
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": '"Linux"',
}


class UserBehavior(TaskSet):
    def on_start(self):
        """Runs when a Locust user starts"""
        with open("messages.json") as f:
            self.message = json.load(f)

        # Assign a unique user token per Locust user
        self.token = random.choice(list(user_locks.keys()))
        self.lock = user_locks[self.token]  # Shared lock for this token
        self.thread = ""

    def get_conversations(self, limit=15):
        headers = GLOBAL_HEADERS.copy()
        headers["Authorization"] = f"Bearer {self.token}"
        response = self.client.get(
            f"{BASE_URL}/api/conversation/?limit={limit}",
            headers=headers,
        )
        assert response.status_code == 200, "Failed to call conversation API"
        return response.json()

    @task(50)
    def get_conversations_task(self):
        self.get_conversations()

    @task(50)  # Other tasks should run frequently
    def get_assistants(self):
        headers = {"Authorization": f"Bearer {self.token}", "Content-Type": "application/json"}
        response = self.client.get(
            f"{BASE_URL}/api/assistant/v2/",
            headers=headers,
        )
        if response.status_code != 200:
            print(f"Failed to call assistant API, status code: {response.status_code}")

    @task(1)
    def create_conversation_task(self):
        self.create_conversation({"assistant_id": self.message[0]["assistant_name"], "meta": {}})

    @task(1)  # Ensures chat runs less frequently
    def chat_stream(self):
        """Ensures only one chat per user at a time using a shared lock"""
        if self.lock.acquire(blocking=False):  # Non-blocking lock attempt
            try:
                self.create_message_if_not_exist()
                message = random.choice(self.message)
                self.stream_chat_thread(
                    self.thread, message["message"][0]["text"], {"assistant_id": message["assistant_name"]}
                )
            finally:
                self.lock.release()  # Ensure lock is always released
        else:
            print(f"Skipping chat for user {self.token} because a chat is already in progress.")

    @task(25)  # Mock stream chat thread task
    def mock_chat_stream(self):
        """Mock stream chat thread to simulate assistant response"""
        if self.lock.acquire(blocking=False):  # Non-blocking lock attempt
            try:
                self.create_message_if_not_exist()
                message = random.choice(self.message)
                self.stream_chat_thread_mock(
                    self.thread, message["message"][0]["text"], {"assistant_id": message["assistant_name"]}
                )
            finally:
                self.lock.release()  # Ensure lock is always released
        else:
            print(f"Skipping mock chat for user {self.token} because a chat is already in progress.")

    def create_message_if_not_exist(self):
        """Ensures a conversation thread exists"""
        url = f"{BASE_URL}/api/conversation/?limit=50"
        headers = {"Authorization": f"Bearer {self.token}", "Content-Type": "application/json"}

        response = self.client.get(url, headers=headers)
        if response.status_code != 200:
            print(f"Failed to fetch recent messages, status code: {response.status_code}")
            return

        messages = response.json()
        if not messages:
            conversation = self.create_conversation({"assistant_id": self.message[0]["assistant_name"], "meta": {}})
            self.thread = conversation["id"]
        else:
            self.thread = random.choice(messages)["id"]

    def create_conversation(self, payload):
        """Creates a new conversation if none exists"""
        url = f"{BASE_URL}/api/conversation/"
        headers = {"Authorization": f"Bearer {self.token}", "Content-Type": "application/json"}

        response = self.client.post(url, json=payload, headers=headers)
        if response.status_code == 201:
            return response.json()
        else:
            print(f"Failed to create conversation, status code: {response.status_code}")
            return {}

    def stream_chat_thread(self, conversation_id, message, assistant_meta):
        """Sends a message and waits for response"""

        # Terminating after the first token for now
        url = f"{BASE_URL}/api/chat/{conversation_id}/stream/"
        headers = {"Authorization": f"Bearer {self.token}", "Accept": "application/json"}
        files = {
            "chat_input": (
                None,
                json.dumps(
                    {
                        "message": message,
                        "file_urls": [],
                        "assistant_id": assistant_meta["assistant_id"],
                        "role": "user",
                    }
                ),
                "application/json",
            )
        }

        start_time = time.time()
        response = self.client.post(url, headers=headers, files=files, stream=True)
        if response.status_code != 200:
            print(f"Failed to stream chat, status code: {response.status_code}")
            return

        for line in response.iter_lines():
            if line:
                first_token_time = time.time() - start_time
                log_data = {
                    "conversation_id": conversation_id,
                    "time_to_first_token": first_token_time,
                    "message": message,
                    "user_token": self.token,
                    "assistant_id": assistant_meta["assistant_id"],
                }
                with open("time_to_first_token_log.json", "a") as log_file:
                    log_file.write(json.dumps(log_data) + "\n")
                return  # Stop after first response token

    def stream_chat_thread_mock(self, conversation_id, message, assistant_meta):
        """Mocks sending a message and waits for response"""
        url = f"{BASE_URL}/api/load-testing/mock/{conversation_id}/stream/"
        headers = {"Authorization": f"Bearer {self.token}", "Accept": "application/json"}
        files = {
            "chat_input": (
                None,
                json.dumps(
                    {
                        "message": message,
                        "file_urls": [],
                        "assistant_id": assistant_meta["assistant_id"],
                        "role": "user",
                    }
                ),
                "application/json",
            )
        }

        start_time = time.time()
        response = self.client.post(url, headers=headers, files=files, stream=True)
        if response.status_code != 200:
            print(f"Failed to stream mock chat, status code: {response.status_code}")
            return

        for line in response.iter_lines():
            if line:
                first_token_time = time.time() - start_time
                log_data = {
                    "conversation_id": conversation_id,
                    "time_to_first_token": first_token_time,
                    "message": message,
                    "user_token": self.token,
                    "assistant_id": assistant_meta["assistant_id"],
                }
                with open("time_to_first_token_mock_log.json", "a") as log_file:
                    log_file.write(json.dumps(log_data) + "\n")
                return  # Stop after first response token


class WebsiteUser(HttpUser):
    host = BASE_URL
    tasks = [UserBehavior]
    wait_time = between(1, 5)
