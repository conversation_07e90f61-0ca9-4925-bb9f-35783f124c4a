# flake8:noqa

from config import settings

if settings.CLIENT_NAME.upper() == "NFM":
    from .ms_sso import (
        check_user_permissions,
        get_or_create_auth_user,
        get_or_create_user,
        get_session,
        is_admin_user,
        is_not_general_user,
        role_checker,
    )

else:
    from .cognito_sso import (
        check_user_permissions,
        get_or_create_auth_user,
        get_or_create_user,
        get_session,
        is_admin_user,
        is_not_general_user,
        role_checker,
    )
