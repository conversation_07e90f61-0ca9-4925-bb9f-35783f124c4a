from db.models import User
from sqlalchemy.ext.asyncio import AsyncSession


async def update_user(user_info: dict, user: User, session: AsyncSession):
    # Only update if details have changed
    updated = False
    user_fields = [
        "encompass_email",
        "encompass_loid",
    ]
    # only update if user has different encompass details for now. We can extend to other fields as required
    for field in user_fields:
        new_value = user_info.get(field, "")
        # Lowercase for email, encompass_email, encompass_loid
        if field in ["email", "encompass_email", "encompass_loid"]:
            if new_value and isinstance(new_value, str):
                new_value = new_value.lower()
        if getattr(user, field, None) != new_value and new_value != "":
            setattr(user, field, new_value)
            updated = True
    # Update meta field
    # Do not update meta as we have activity information in meta for last login information
    # if user.meta.get("user_info") != user_info:
    #     user.meta["user_info"] = user_info
    #     updated = True
    if updated:
        await session.commit()
