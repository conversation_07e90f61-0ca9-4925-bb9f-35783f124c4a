import asyncio
from datetime import datetime, timedelta, timezone

import jwt
from config import settings
from db.models import User
from db.session import get_session
from fastapi import Depends, HTTPException, status
from fastapi.security import HTTPAuthorizationCredentials, HTT<PERSON><PERSON>earer
from loguru import logger
from schema.enums import RoleTypes
from sentry_sdk import set_user
from sqlalchemy import select
from sqlalchemy.exc import IntegrityError
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload
from utils.division import get_division_by_domain
from utils.sanitize import get_domain_from_email

from .load_test_auth_utils import mock_users
from .utils import update_user

# from


bearer_schema = HTTPBearer(auto_error=False)
MAX_RETRIES = 3  # Maximum number of retry attempts


def create_jwt_token(user_info):
    expiration = datetime.now(timezone.utc) + timedelta(days=1)
    payload = {
        "sub": user_info.get("id"),
        "exp": expiration,
        "user_info": user_info,
    }
    token = jwt.encode(payload, settings.AZURE_CLIENT_SECRET, algorithm="HS256")
    logger.info(f"Created JWT token for user {user_info.get('id')}")
    return token


async def get_or_create_user(user_info: dict, session: AsyncSession) -> User:
    retries = 0
    while retries < MAX_RETRIES:
        try:
            query = (
                select(User)
                .where(User.id == user_info.get("id"))
                .options(selectinload(User.assistants))
                .options(selectinload(User.division))
            )
            result = await session.execute(query)
            user = result.scalar_one_or_none()

            if user is None:
                user = User(
                    id=user_info.get("id"),
                    email=user_info.get("email"),
                    name=user_info.get("name"),
                    division_id=user_info.get("division_id"),
                    username=user_info.get("name"),
                    display_picture=user_info.get("picture"),
                    role=user_info.get("role", "general"),
                    encompass_email=user_info.get("encompass_email"),
                    encompass_loid=user_info.get("encompass_loid"),
                    meta={"user_info": user_info},
                )
                session.add(user)
                await session.commit()
            else:
                await update_user(user_info, user, session)
            return user

        except IntegrityError as e:
            # Rollback the session to clear any pending state
            await session.rollback()
            retries += 1
            logger.info(f"Retrying... ({retries}/{MAX_RETRIES})")
            if retries >= MAX_RETRIES:
                raise e
            await asyncio.sleep(0.1)
    raise Exception("Failed to create or get user after several retries.")


def decode_jwt_token(token: str):
    payload = jwt.decode(token, algorithms=["RS256"], options={"verify_signature": False})
    return payload


async def get_or_create_auth_user(
    authorization: HTTPAuthorizationCredentials = Depends(bearer_schema), session: AsyncSession = Depends(get_session)
):
    RESTRICTED_ENVS = ["production"]
    if authorization is None:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED)
    token = authorization.credentials
    if settings.APP_ENV not in RESTRICTED_ENVS and (
        token == settings.API_KEY  # or token in settings.LOAD_TESTING_API_KEYS
    ):
        if token == settings.API_KEY:
            user_info = {
                "id": "66973c19-ed45-4abf-8565-ee521f6032b1",
                "email": "<EMAIL>",
                "name": "NFM Admin",
                "username": "admin",
                "role": "admin",
                "meta": {"user_info": {}},
            }

        else:
            test_users = mock_users

            user_info = test_users[int(token[-1]) - 1]
    else:
        try:
            payload = decode_jwt_token(token)
            user_info = {
                "id": payload.get("oid"),
                "email": payload.get("upn", "").lower(),
                "name": payload.get("name"),
                "display_picture": payload.get("picture"),
                "encompass_email": payload.get("custom:encompass_email", "").lower(),
                "encompass_loid": payload.get("custom:encompass_loid", "").lower(),
                **payload,
            }
        except jwt.PyJWTError as e:
            logger.error(f"Error decoding JWT token: {e}", exec_info=True)
            raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED)

    division = get_email_based_division(user_info.get("email"))

    user_info["division_id"] = division
    user = await get_or_create_user(user_info, session)
    await session.refresh(user)
    # Set Sentry user context
    set_user(
        {
            "id": str(user.id),
            "email": user.email,
        }
    )
    return user


async def get_email_based_division(email):
    user_domain = get_domain_from_email(email)
    division = await get_division_by_domain(user_domain)
    return division


async def is_admin_user(user: User = Depends(get_or_create_auth_user)):
    if user.role != "admin":
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="You are not an admin user.")
    return user


async def is_not_general_user(user: User = Depends(get_or_create_auth_user)):
    if user.role == "general":
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="You are a general user.")
    return user


def role_checker(allowed_roles: list[str]):
    async def checker(user: User = Depends(get_or_create_auth_user)):
        if user.role not in allowed_roles:
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="You don't have permission.")
        return user

    return checker


async def check_user_permissions(id: str, user: User = Depends(get_or_create_auth_user)):
    if user.role == RoleTypes.ASSISTANT_ADMIN:
        assistant_ids = [assistant.id for assistant in user.assistants]
        if id not in assistant_ids:
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Insufficient permissions")
    elif user.role == RoleTypes.GENERAL:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Insufficient permissions")

    return user
