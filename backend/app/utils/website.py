import re
from urllib.parse import urlparse, urlunparse

import redbeat
from celery_app import celery_app
from config import settings
from hooks.pinecone_hook import get_generic_hook
from hooks.splade_hook import splade_embedder
from hooks.voyage_hook import voyage_embedder
from html2text import html2text
from langchain.text_splitter import RecursiveCharacterTextSplitter
from loguru import logger
from pinecone import SparseValues, Vector
from redbeat import RedBeatSchedulerEntry
from redbeat.schedulers import RedBeatConfig
from utils.indexing import clean_data


def normalize_url(url):
    parsed_url = urlparse(url)
    normalized_path = parsed_url.path.rstrip("/")
    normalized_url = urlunparse(
        (parsed_url.scheme, parsed_url.netloc, normalized_path, parsed_url.params, parsed_url.query, "")
    )
    return normalized_url


def clean_markdown(content):
    """
    Remove all image markdown and extra new lines from the provided content.
    """
    # Remove all image markdown from the content
    cleaned_content = re.sub(r"!\[.*?\]\([^)]*\)", "", content)

    # Remove extra new lines
    cleaned_content = re.sub(r"\n\s*\n", "\n\n", cleaned_content.strip())
    return cleaned_content


def get_schedules() -> dict:
    config = RedBeatConfig(celery_app)
    schedule_key = config.schedule_key
    redis = redbeat.schedulers.get_redis(celery_app)
    elements = redis.zrange(schedule_key, 0, -1, withscores=False)
    entries = {el: RedBeatSchedulerEntry.from_key(key=el, app=celery_app) for el in elements}
    return entries


def get_redbeat_key_prefix():
    key_prefix = celery_app.redbeat_conf.key_prefix
    return key_prefix.split(":")[0]


async def index_website(website_content, session, is_parent):
    # Clean text
    website_text = clean_data(f"{website_content.title}\n\n{website_content.content}")
    website_text = html2text(website_text)

    # Split text into chunks
    splitter = RecursiveCharacterTextSplitter.from_tiktoken_encoder(
        model_name="gpt-4",
        chunk_size=256,
        chunk_overlap=20,
        separators=["\n\n", "\n", ".", " ", ""],
        keep_separator=True,
    )

    chunks = splitter.split_text(website_text)

    # Prepare data for indexing
    metadata = {
        "website_content_id": website_content.id,
        "title": website_content.title,
        "url": website_content.url,
        "domain": website_content.domain,
        "content_type": website_content.content_type,
        "updated_at": website_content.updated_at.isoformat(),
        "categories": website_content.categories or [],
        "assistant_id": website_content.assistant_id,
    }

    N = settings.CHUNK_SIZE
    chunks = [chunks[i : i + N] for i in range(0, len(chunks), N)]  # noqa: E203
    vectors = []
    for j, chunk in enumerate(chunks):
        embs = await voyage_embedder.get_embedding(texts=chunk, input_type="document")

        sparse_embs = await splade_embedder.encode_documents(chunk)
        for i, (d, e, se) in enumerate(zip(chunk, embs, sparse_embs)):
            chunk_metadata = metadata.copy()
            chunk_metadata["text"] = f"## {website_content.title}\n\n{d}"
            indices = se["indices"]
            values = se["values"]
            if not indices or not values:
                indices = [0]
                values = [0.0]
            sparse = SparseValues(indices=indices, values=values)
            vec = Vector(
                id=f"{website_content.id}#{j}_{i}",
                values=e,
                sparse_values=sparse,
                metadata=chunk_metadata,
            )
            vectors.append(vec)

    generic_hook = await get_generic_hook(website_content.assistant_id)
    await generic_hook.upsert_document(vectors, namespace=website_content.assistant_id)

    website_content.is_indexed = True if not is_parent else False
    await session.commit()
    logger.info(f"Len of Vectors Indexed: {len(vectors)} ")
    logger.info(f"Website Content {website_content.id} indexed")
