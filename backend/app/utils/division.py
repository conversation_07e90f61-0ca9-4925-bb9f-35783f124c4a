from db.models import Assistant, Division, DivisionCredentials, EncompassCredentials
from db.session import session_manager
from loguru import logger
from sqlalchemy import select
from sqlalchemy.orm import selectinload


async def get_assistant_division_credentials(assistant_id: str) -> dict | None:
    """
    Retrieve credentials for a specific assistant's division.

    Args:
        assistant_id (str): The ID of the assistant.

    Returns:
        Dictionary with division credentials or None if not found.
    """
    async with session_manager() as session:
        query = (
            select(Division)
            .join(Assistant, Division.id == Assistant.division_id)
            .where(Assistant.id == assistant_id)
            .options(selectinload(Division.credentials).selectinload(DivisionCredentials.encompass_credentials))
        )

        result = await session.execute(query)
        division = result.scalars().first()
        if not division:
            # logger.warning(f"Division not found for assistant ID: {assistant_id}")
            return None

        if division and division.credentials:
            return {
                "pinecone_index": division.credentials.pinecone_index,
                "pinecone_host": division.credentials.pinecone_host,
                "aws_storage_bucket_name": division.credentials.aws_storage_bucket_name,
                "total_expert_client_id": division.credentials.total_expert_client_id,
                "total_expert_client_secret": division.credentials.total_expert_client_secret,
                "encompass_credentials": [
                    {
                        "encompass_client_id": cred.encompass_client_id,
                        "encompass_client_secret": cred.encompass_client_secret,
                        "encompass_password": cred.encompass_password,
                        "encompass_username": cred.encompass_username,
                        "encompass_instance_id": cred.encompass_instance_id,
                    }
                    for cred in division.credentials.encompass_credentials
                ],
            }


async def get_division_by_domain(domain: str) -> str | None:
    """
    Asynchronously retrieves the Division ID that matches the given domain.

    Args:
        domain (str): The domain for which the division is to be fetched.

    Returns:
        str | None: The division ID if found, otherwise None.
    """
    async with session_manager() as session:
        result = await session.execute(select(Division.id).where(Division.domain == domain))
        return result.scalar_one_or_none()


async def get_encompass_division_credentials(assistant_id: str) -> dict | None:
    async with session_manager() as session:
        query = (
            select(Division, Assistant.encompass_credential_id)
            .join(Assistant, Division.id == Assistant.division_id)
            .where(Assistant.id == assistant_id)
            .options(selectinload(Division.credentials).selectinload(DivisionCredentials.encompass_credentials))
        )
        result = await session.execute(query)
        row = result.first()
        if not row:
            # logger.warning(f"Division not found for assistant ID: {assistant_id}")
            return None

        division, encompass_credential_id = row
        credentials = getattr(division.credentials, "encompass_credentials", None)
        if not credentials:
            logger.warning(f"No encompass credentials found for assistant ID: {assistant_id}")
            return None

        cred = next((c for c in credentials if c.id == encompass_credential_id), None)
        if not cred:
            logger.warning(f"No encompass credentials matched for assistant ID: {assistant_id}")
            return None

        return {
            "encompass_client_id": cred.encompass_client_id,
            "encompass_client_secret": cred.encompass_client_secret,
            "encompass_password": cred.encompass_password,
            "encompass_username": cred.encompass_username,
            "encompass_instance_id": cred.encompass_instance_id,
        }


async def create_division_with_credentials(
    division_data: dict, credentials_data: dict, encompass_data_list: list[dict]
):
    async with session_manager() as session:
        try:
            # Check if division already exists
            existing_division = await session.execute(select(Division).where(Division.id == division_data.get("id")))
            if division := existing_division.scalar_one_or_none():
                logger.warning(f"Division with domain {division_data['domain']} already exists.")
                # Update existing division
                division_data.pop("id", None)  # Remove id to avoid conflicts
                for key, value in division_data.items():
                    setattr(division, key, value)
            else:
                # Create new Division
                division = Division(**division_data)
                session.add(division)
                await session.flush()  # Ensure division.id is generated

            # Upsert DivisionCredentials
            result = await session.execute(
                select(DivisionCredentials).where(DivisionCredentials.id == credentials_data.get("id"))
            )
            division_credentials = result.scalar_one_or_none()
            if division_credentials:
                logger.info("Updating existing DivisionCredentials")
                credentials_data.pop("id", None)
                for key, value in credentials_data.items():
                    setattr(division_credentials, key, value)
            else:
                division_credentials = DivisionCredentials(**credentials_data, division_id=division.id)
                session.add(division_credentials)
                await session.flush()

            # Create EncompassCredentials for each set of credentials
            for encompass_data in encompass_data_list:
                result = await session.execute(
                    select(EncompassCredentials).where(EncompassCredentials.id == encompass_data.get("id"))
                )
                if encompass_credentials := result.scalar_one_or_none():
                    logger.info("Updating existing EncompassCredentials")
                    encompass_data.pop("id", None)
                    for key, value in encompass_data.items():
                        setattr(encompass_credentials, key, value)
                else:
                    logger.info("Creating new EncompassCredentials")
                    encompass_credentials = EncompassCredentials(
                        **encompass_data,
                        division_credentials_id=division_credentials.id,
                    )
                session.add(encompass_credentials)
            # Commit the transaction
            await session.commit()
        except Exception as e:
            logger.error(f"Error creating division with credentials: {e}")
            await session.rollback()
            raise
