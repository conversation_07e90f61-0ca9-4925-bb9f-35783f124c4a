from datetime import datetime, timezone
from urllib.parse import urlsplit

from db.models import Document, SharepointFolder
from fastapi import status
from hooks.connectors.sharepoint_hook import sharepoint_hook
from hooks.pinecone_hook import get_generic_hook
from hooks.s3 import get_s3_hook
from loguru import logger
from schema.connectors.sharepoint import FileItem, FolderInput
from schema.enums import DocumentIndexStatus, DocumentTypes
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

valid_file_mime_types = [
    "application/pdf",
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
]

valid_image_mime_types = [
    "image/jpeg",
    "image/png",
    "image/gif",
    "image/webp",
]


async def upload_new_file(file, assistant_id, site_id, drive_id) -> str:
    s3_hook = await get_s3_hook(assistant_id)
    filename = f"{assistant_id}/{site_id}/{drive_id}/{file['fullpath']}"
    file_stream = await sharepoint_hook.download_file(site_id, drive_id, file["id"])
    logger.info(f"Uploading file to S3 bucket for assistant: {assistant_id}")
    response = s3_hook.put_object(object_name=filename, file=file_stream, content_type=file["mimeType"])

    if response["ResponseMetadata"]["HTTPStatusCode"] != status.HTTP_200_OK:
        raise Exception("Failed to store file in S3 bucket")

    logger.info(f"File uploaded to S3 bucket: {filename}")
    file_url = s3_hook.get_presigned_url_for_download(filename)
    return file_url


async def upload_and_index_new_file(file: dict, assistant_id, session, categories, meta) -> str:
    file_url = await upload_new_file(file, assistant_id, file["site_id"], file["drive_id"])
    parsed_url = urlsplit(file_url)
    base_url = parsed_url.scheme + "://" + parsed_url.netloc + parsed_url.path

    if file["mimeType"] in valid_image_mime_types:
        document = Document(
            assistant_id=assistant_id,
            title=file["name"],
            type=DocumentTypes.SHAREPOINT_IMAGE,
            link=base_url,
            is_indexed=True,
            index_status=DocumentIndexStatus.INDEXED,
            categories=categories,
            meta=meta,
        )
        session.add(document)
        await session.commit()
        return None

    document = Document(
        assistant_id=assistant_id,
        title=file["name"],
        type=DocumentTypes.SHAREPOINT,
        link=base_url,
        is_indexed=False,
        index_status=DocumentIndexStatus.PENDING,
        categories=categories,
        meta=meta,
    )
    session.add(document)
    await session.commit()
    return document.id


async def handle_missing_file(document, assistant_id, session):
    logger.info(f"File not found: {document.title}")
    await session.delete(document)
    await session.commit()

    fullpath = document.meta.get("fullpath")
    s3_hook = await get_s3_hook(assistant_id)
    if fullpath:
        filename = f"{assistant_id}/{fullpath}"
        response = s3_hook.delete_object(object_name=filename)
        if response["ResponseMetadata"]["HTTPStatusCode"] != status.HTTP_204_NO_CONTENT:
            raise Exception("Failed to delete file from S3 bucket")
        logger.info(f"File deleted from S3 bucket: {filename}")

    if document.type == DocumentTypes.SHAREPOINT_IMAGE:
        return
    generic_hook = await get_generic_hook(assistant_id)
    await generic_hook.delete_document(document.id, namespace=document.assistant_id)


async def handle_modified_file(document, file, assistant_id, session, user_info) -> str:
    logger.info(f"File is modified: {file['name']}")
    file_url = await upload_new_file(file, assistant_id, document.meta["site_id"], document.meta["drive_id"])
    parsed_url = urlsplit(file_url)
    base_url = parsed_url.scheme + "://" + parsed_url.netloc + parsed_url.path
    document.link = base_url
    document.is_indexed = False
    document.index_status = DocumentIndexStatus.PENDING
    document.title = file["name"]
    # Update metadata with user information
    if user_info is not None:
        if document.meta:
            document.meta = {
                **document.meta,
                "user_info": {**document.meta["user_info"], "updated_by": user_info},
            }
        else:
            document.meta = {"user_info": {"updated_by": user_info}}

    document.meta["lastModifiedDateTime"] = file["lastModifiedDateTime"]

    if file["mimeType"] in valid_image_mime_types:
        document.is_indexed = True
        document.index_status = DocumentIndexStatus.INDEXED
        session.add(document)
        await session.commit()
        await session.refresh(document)
        return None

    session.add(document)
    await session.commit()
    await session.refresh(document)
    generic_hook = await get_generic_hook(assistant_id)
    await generic_hook.delete_document(document.id, namespace=document.assistant_id)
    return document.id


async def fetch_all_files_from_folder(
    folder: FolderInput,
    user_id: str,
    assistant_id: str,
    site_name: str,
    session: AsyncSession,
    categories: list[str],
    user_info: dict,
    type: DocumentTypes,
    valid_mime_types: list[str],
    parent_id: str | None = None,
) -> list[FileItem]:
    from tasks.sharepoint import mark_folder_indexed

    all_files = []
    folder_dict = folder.model_dump()
    meta = {**folder_dict, "user_info": {"created_by": user_info, "updated_by": user_info}}
    sharepoint_folder = SharepointFolder(
        site_id=folder.site_id,
        drive_id=folder.drive_id,
        folder_id=folder.id,
        site_name=folder.site_name,
        type=type,
        categories=categories,
        assistant_id=assistant_id,
        user_id=user_id,
        meta=meta,
        parent_id=parent_id,
        is_indexed=False,
        index_status=DocumentIndexStatus.PENDING,
    )
    session.add(sharepoint_folder)
    await session.commit()
    await session.refresh(sharepoint_folder)

    folder_contents = await sharepoint_hook.list_folder_contents(
        user_id,
        assistant_id,
        None,
        None,
        folder.site_id,
        folder.drive_id,
        folder.id,
        type,
        False,
        None,
    )
    for content in folder_contents:
        if content["type"] == "file" and content["mimeType"] in valid_mime_types:
            all_files.append(
                FileItem(
                    id=content["id"],
                    name=content["name"],
                    fullpath=content["fullpath"],
                    mimeType=content["mimeType"],
                    createdDateTime=content["createdDateTime"],
                    lastModifiedDateTime=content["lastModifiedDateTime"],
                    site_id=content["site_id"],
                    site_name=site_name,
                    drive_id=content["drive_id"],
                    folder_id=folder.id,
                )
            )
        elif content["type"] == "folder":
            subfolder = FolderInput(
                id=content["id"],
                site_id=content["site_id"],
                site_name=site_name,
                drive_id=content["drive_id"],
                createdDateTime=content["createdDateTime"],
                lastModifiedDateTime=content["lastModifiedDateTime"],
            )
            subfolder_files = await fetch_all_files_from_folder(
                subfolder,
                user_id,
                assistant_id,
                site_name,
                session,
                categories,
                user_info,
                type,
                valid_mime_types,
                sharepoint_folder.id,
            )
            if subfolder_files:
                all_files.extend(subfolder_files)
            else:
                mark_folder_indexed.delay(None, subfolder.id, assistant_id, type)
    return all_files


async def fetch_all_indexed_doc_ids(
    folder: FolderInput, user_id: str, assistant_id: str, site_name: str, type: DocumentTypes, session: AsyncSession
) -> list[str]:
    doc_ids = []

    query = select(SharepointFolder).where(
        SharepointFolder.site_id == folder.site_id,
        SharepointFolder.drive_id == folder.drive_id,
        SharepointFolder.folder_id == folder.id,
        SharepointFolder.assistant_id == assistant_id,
        SharepointFolder.type == type,
    )
    sharepoint_folder = await session.execute(query)
    sharepoint_folder = sharepoint_folder.scalar_one_or_none()
    if sharepoint_folder:
        await session.delete(sharepoint_folder)
        await session.commit()

    folder_contents = await sharepoint_hook.list_folder_contents(
        user_id,
        assistant_id,
        None,
        None,
        folder.site_id,
        folder.drive_id,
        folder.id,
        type,
        False,
        None,
    )
    for content in folder_contents:
        if (
            content["type"] == "file"
            and content["is_indexed"]
            and content["document"] is not None
            and content["document"].type == type
        ):
            doc_ids.append(content["document"].id)
        elif content["type"] == "folder":
            subfolder = FolderInput(
                site_id=content["site_id"],
                drive_id=content["drive_id"],
                id=content["id"],
                site_name=site_name,
            )
            doc_ids.extend(await fetch_all_indexed_doc_ids(subfolder, user_id, assistant_id, site_name, type, session))
    return doc_ids


async def list_new_files(
    folder: SharepointFolder, stored_date: datetime, valid_mime_types: list[str]
) -> list[FileItem]:
    # List all new files in the folder and its subfolders
    new_files = []
    folder_contents = await sharepoint_hook.list_folder_contents(
        folder.user_id,
        folder.assistant_id,
        None,
        None,
        folder.site_id,
        folder.drive_id,
        folder.folder_id,
        folder.type,
        True,
        None,
    )
    if stored_date.tzinfo is None:
        stored_date = stored_date.replace(tzinfo=timezone.utc)
    for content in folder_contents:
        last_modified = content.get("lastModifiedDateTime")
        last_modified = datetime.fromisoformat(last_modified.replace("Z", "+00:00"))
        if (
            content["type"] == "file"
            and content["mimeType"] in valid_mime_types
            and not content["is_indexed"]
            and last_modified > stored_date
        ):
            new_files.append(
                FileItem(
                    id=content["id"],
                    name=content["name"],
                    fullpath=content["fullpath"],
                    mimeType=content["mimeType"],
                    createdDateTime=content["createdDateTime"],
                    lastModifiedDateTime=content["lastModifiedDateTime"],
                    site_id=content["site_id"],
                    site_name=folder.site_name,
                    drive_id=content["drive_id"],
                    folder_id=folder.folder_id,
                )
            )
    return new_files
