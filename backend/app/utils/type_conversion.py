from datetime import datetime

from dateutil.parser import isoparse, parse
from loguru import logger


def is_valid_iso8601(date_str):
    try:
        isoparse(date_str)
        return True
    except ValueError:
        return False


class TypeConverter:
    @staticmethod
    def round_to_decimal(value: float, decimals: int = 3) -> float:
        """Rounds the value to `decimals` point. `round` function rounds values like 99.999 to 100.000 but
        we don't want that. We only want to round the decimal part but not integer part.
        Examples:

        ```
        >>> round_to_decimal(99.999, 3)
            99.999
        >>> round_to_decimal(3.1416, 3)
            3.141
        ```
        """
        rounded = round(value, decimals)  # round the value first
        if (
            rounded == int(rounded) and value != rounded
        ):  # if rounded value is equal to integer then we need to subtract
            return round(value - 0.001, decimals)
        return rounded

    @staticmethod
    def convert_to_int(value: str) -> int | str:
        try:
            return int(float(value))  # cannot convert string to int directly.
        except Exception as e:
            logger.warning(f"Could not convert value to int: {value}! Error: {e}")
            return value

    def convert_to_float(self, value: str) -> float | str:
        try:
            return self.round_to_decimal(float(value), 3)
        except Exception as e:
            logger.warning(f"Could not convert value to float. {value}! Error: {e}")
            return value

    @staticmethod
    def parse_date(value: str, time: bool = False) -> str:
        """parses string date and format it into desired format.
        Input value must be in the format: MM/DD/YYYY HH:MM:SS AM/PM
        Output format will be: MM/DD/YYYY
        Examples:
        ```
        >>> parse_date("02/23/2024 05:00:00 AM")
            "02/23/2024"
        ```
        """
        date_formats = [
            "%m/%d/%Y %I:%M:%S %p",  # 02/23/2024 05:00:00 AM
            "%Y-%m-%dT%H:%M:%S.%fZ",  # 2024-09-27T17:05:07.000000Z,
            "%Y-%m-%d %H:%M:%S",
            "%Y-%m-%dT%H:%M:%SZ",
            "%m/%d/%Y",
        ]

        for date_format in date_formats:
            try:
                date_object = datetime.strptime(value, date_format)
                if time:
                    # Return both date and time formatted as MM/DD/YYYY HH:MM:SS
                    formatted_date = date_object.strftime("%m/%d/%Y %H:%M:%S")
                else:
                    # Return only the date formatted as MM/DD/YYYY
                    formatted_date = date_object.strftime("%m/%d/%Y")
                return formatted_date
            except ValueError:
                continue
        logger.warning(f"Could not convert date: {value}! No matching format.")
        return value

    def convert_string_data(self, field_type_mapping: dict, field: str, value: str) -> str | int | float | None:
        """Formats data for easy readability.
        Like: Converting floating values into 3 decimal point. Convert date to specific format."""
        if not value:
            # if v is none or empty string return as it is
            return value

        function_mapping = {
            int: type_converter.convert_to_int,
            float: type_converter.convert_to_float,
            datetime: type_converter.parse_date,
        }
        func = function_mapping.get(field_type_mapping.get(field, str), None)
        if func:
            return func(value)
        return value

    def convert_to_type(self, field_type_mapping: dict, field: str, value: str) -> str | int | float | datetime | None:
        # Converts Given String data to int , float or datetime according to field_type_mapping
        if not value:
            # if v is none or empty string return as it is
            return value
        function_mapping = {
            int: type_converter.convert_to_int,
            float: type_converter.convert_to_float,
            datetime: parse,
        }
        func = function_mapping.get(field_type_mapping.get(field, str), None)
        if func:
            return func(value)
        return value


type_converter = TypeConverter()
