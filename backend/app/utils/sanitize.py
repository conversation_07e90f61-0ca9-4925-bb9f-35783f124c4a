import re

import bleach


def sanitize_html(value: str | None) -> str | None:
    if value is None:
        return None

    allowed_tags = ["p", "br", "strong", "em", "ul", "ol", "li", "a", "img", "span"]
    allowed_attributes = {
        "a": ["href", "title"],
        "img": ["src", "alt", "title"],
    }
    return bleach.clean(value, tags=allowed_tags, attributes=allowed_attributes, strip=True)


def get_domain_from_email(email: str) -> str:
    """
    Extracts the domain part of an email address.

    Args:
        email (str): The email address from which the domain will be extracted.

    Returns:
        str: The domain part of the email (e.g., 'example.com').

    Raises:
        ValueError: If the email address is invalid.
    """
    # Regular expression to match valid email format
    email_regex = r"(^[a-zA-Z0-9_.+-]+)@([a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$)"

    # Match the email address with the regular expression
    match = re.match(email_regex, email)

    if match:
        # Extract the domain part from the email
        domain = match.group(2)
        return domain
    else:
        raise ValueError("Invalid email address format")
