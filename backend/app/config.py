import base64
from pathlib import Path
from typing import Literal

from pydantic import AnyHttpUrl, BaseModel, HttpUrl, field_validator, model_validator
from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=True,
        extra="allow",
    )

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.load_extra_encompass_credentials()

    # CLIENT
    CLIENT_NAME: str = "Insyde AI"

    # Project settings
    @property
    def PROJECT_NAME(self) -> str:
        return f"{self.CLIENT_NAME} Backend!"

    API_STR: str = "/api"
    BASE_DIR: Path = Path(__file__).resolve().parent
    API_DIR: Path = BASE_DIR / "api"
    APP_ENV: Literal["local", "dev", "production", "division", "staging"] = "local"

    # Auth Config
    AZURE_CLIENT_ID: str
    AZURE_CLIENT_SECRET: str
    AZURE_TENANT_ID: str
    AUTHORITY: str = "https://login.microsoftonline.com"
    REDIRECT_URI: str = "https://nfmgpt-backend.nfmlending.com"
    scope: list[str] = ["openid", "profile", "email"]

    API_KEY: str = "fOVxhORu0x"
    # LOAD_TESTING_API_KEYS: list[str] = [f"fOVxhORu0xuser{i}" for i in range(1, 1001)]

    # Logger settings
    LOG_LEVEL: str = "INFO"

    # OpenAI settings
    OPENAI_API_KEY: str
    OPENAI_ORG_ID: str
    OPENAI_PROJECT_ID: str | None = None
    OPENAI_REALTIME_MODEL: str = "gpt-4o-realtime-preview-2025-06-03"

    @property
    def OPENAI_REALTIME_URL(self) -> str:
        return f"wss://api.openai.com/v1/realtime?model={self.OPENAI_REALTIME_MODEL}"

    @property
    def ASSISTANTS(self) -> list[BaseModel]:
        from assistants import assistants

        return assistants

    # Pinecone settings
    PINECONE_SERVERLESS_API_KEY: str
    PINECONE_ENV: str
    PINECONE_INDEX: str = "insyde-documents"
    PINECONE_HOST: str

    # Postgres settings
    POSTGRES_USER: str = ""
    POSTGRES_PASSWORD: str = ""
    POSTGRES_HOST: str = ""
    POSTGRES_PORT: int = 5432
    POSTGRES_DB: str = ""

    @property
    def DATABASE_URL(self) -> str:
        return f"postgresql+asyncpg://{self.POSTGRES_USER}:{self.POSTGRES_PASSWORD}@{self.POSTGRES_HOST}:{self.POSTGRES_PORT}/{self.POSTGRES_DB}"  # noqa

    # CORS settings
    BACKEND_CORS_ORIGINS: list[AnyHttpUrl] = []

    # Celery settings
    CELERY_BROKER_URL: str
    CELERY_RESULT_BACKEND: str

    # AWS S3 settings
    AWS_ACCESS_KEY_ID: str
    AWS_SECRET_ACCESS_KEY: str
    AWS_STORAGE_BUCKET_NAME: str
    AWS_S3_REGION_NAME: str
    AWS_CDN_BASE_URL: str = "https://d3anbrt0otggmr.cloudfront.net"  # local, dev, staging
    AWS_CDN_BUCKET_NAME: str = "insyde-static-content"  # local, dev, staging

    # Unstructured
    UNSTRUCTURED_API_URL: str = "https://insydeai-ep8reu4d.api.unstructuredapp.io"
    UNSTRUCTURED_API_KEY: str

    # Cognito
    AWS_DEFAULT_REGION: str = "us-east-1"
    AWS_COGNITO_USERPOOL_ID: str
    WEB_CLIENT_ID: str

    # Enable Division
    ENABLE_DIVISION_LOCAL: bool = False

    # ENCOMPASS Credentials
    ENCOMPASS_CLIENT_ID: str
    ENCOMPASS_CLIENT_SECRET: str
    ENCOMPASS_PASSWORD: str
    ENCOMPASS_USERNAME: str
    ENCOMPASS_INSTANCE_ID: str

    # ATTOM Data settings
    ATTOM_API_URL: str = "https://api.gateway.attomdata.com/propertyapi/v1.0.0/attomavm/detail/"
    ATTOM_API_KEY: str = ""

    # TOTAL EXPERT
    ENABLE_TOTAL_EXPERT_OAUTH: bool = False
    TOTAL_EXPERT_CLIENT_ID: str = ""
    TOTAL_EXPERT_CLIENT_SECRET: str = ""
    TOTAL_EXPERT_BASE_URL: str = "https://public.totalexpert.net/v1"
    TOTAL_EXPERT_TOKEN_URL: str = TOTAL_EXPERT_BASE_URL + "/token"
    TOTAL_EXPERT_REDIRECT_URI: str = ""
    TOTAL_EXPERT_SCOPE: str = "leadInteraction insightInteraction loanInteraction userInteraction"
    TOTAL_EXPERT_SSO_AUTH_URL: str = "https://totalexpert.net/authorize"
    TOTAL_EXPERT_SERVICE_PROVIDER: str = ""

    # Logger settings
    LOG_LEVEL: str = "INFO"
    SENTRY_DSN: HttpUrl | None = None
    SENTRY_ENVIRONMENT: Literal["local", "dev", "dev-division", "staging", "production"] = "staging"
    SENTRY_TRACES_SAMPLE_RATE: float = 0.01

    ASSISTANT_LIMIT: int | None = None

    # Sharepoint settings
    AZURE_SHAREPOINT_TENANT_ID: str | None = None
    AZURE_SHAREPOINT_CLIENT_ID: str | None = None
    AZURE_SHAREPOINT_CLIENT_SECRET: str | None = None
    SYNC_SHAREPOINT_CRON_HOUR: str = "*/12"
    SYNC_SHAREPOINT_CRON_MINUTE: str = "20"
    MAX_SHAREPOINT_FOLDER_SIZE_MB: int = 1024  # 1 GB

    JINA_READER_API_KEY: str | None = None

    # Embedding models
    OPENAI_EMBED_LARGE: str = "text-embedding-3-large"
    OPENAI_EMBED_SMALL: str = "text-embedding-3-small"
    OPENAI_IMAGE_GEN_MODEL: str = "dall-e-3"
    OPENAI_DEFAULT_MODEL: str = "gpt-4.1"

    VOYAGE_EMBED_MODEL: str = "voyage-3"
    VOYAGE_API_KEY: str | None = None

    SEMANTIC_EMBEDDING_MODEL: str = "vogayeai"  # change it to 'openai' if needed

    REDIS_URL: str = "redis://redis:6379/1"
    REDIS_CACHE_TTL: int = 300

    RERANKER_IMPLEMENTATION: bool = True
    RERANK_TOP_K: int = 5

    GEMINI_API_KEY: str = "AIzaSyC7IE22CXaPKL-OdKg9O1uOlrr3VWlOwFY"  # test trial a/c
    GEMINI_MODEL_NAME: str = "gemini-2.0-flash"

    IMAGE_GENERATION_MODEL: str = "imagen-4.0-generate-preview-06-06"

    EMBEDDING_API_ENDPOINT: str = "http://insyde-compute-alb-386616777.us-east-1.elb.amazonaws.com"
    EMBEDDING_API_KEY: str = "fOVxhORu0x"

    # Custom Assistants Local
    SIDEKICK_ASSISTANT_ID: str = "asst_f5U8zmP6FCz96f5ocLLDdlUu"
    LOAN_ASSISTANT_ID: str = "asst_4c8TY7lhMInTlxMwXU0J4cds"
    SALES_ASSISTANT_ID: str = "asst_7OA9rxgK9T9VMiECjvrMKks0"
    TOTAL_EXPERT_ASSISTANT_ID: str = "asst_xeRiJ0uoPeA9B8orYVOOGY3J"
    CALL_CAMPAIGN_ASSISTANT_ID: str = "asst_pDlUa4YR2oluby0BhNGnFJU9"
    DEV_LOAN_ASSISTANT_ID: str | None = None
    DEV_SALES_ASSISTANT_ID: str | None = None

    # Personal Assistants Local
    RETRIEVAL_ASSISTANT_ID: str | None = "asst_JDUE2F9ceJuL5UgelASU62qb"
    DOCUMENT_UNDERSTANDING_ID: str | None = "asst_gemini_001"
    ANALYSIS_ASSISTANT_ID: str | None = "asst_0OCWr4NyNWIrB36uHO0KAxXN"
    OUTLOOK_ASSISTANT_ID: str | None = "asst_FdRBQKOQQnb9UA4XLx1FMsvq"
    IMAGE_ASSISTANT_ID: str | None = "asst_rXDEpVtAhd6tbDeqIXsxp53f"

    # Mann Assistant for S1L
    MANN_ASSISTANT_FILE_LOC: str | None = None

    # Car Guru Assistant Amgpt (To be removed later)
    CAR_GURU_ASSISTANT_FILE_LOC: str | None = None

    PERSONAL_MESSAGES_ACCESS_PASSWORD: str = "U#ysNjx6"

    # RAG Document Processing
    CHUNK_SIZE: int = 16  # Batch size for embedding generation
    CHUNK_LENGTH: int = 250  # total tokens per chunk
    CHUNK_OVERLAP: int = 20  # overlap between chunks
    TOKENIZER_MODEL: str = "gpt-4"  # model to use for tokenization

    # Assistant Settings
    ENCOMPASS_ASSISTANT_ENABLED: bool = False
    ENCOMPASS_SALES_ASSISTANT_ENABLED: bool = False
    TOTALEXPERT_ASSISTANT_ENABLED: bool = False
    PERSONAL_ASSISTANTS_ENABLED: bool = False
    CALL_CAMPAIGN_ASSISTAN_ENABLED: bool = False

    EXTRA_ENCOMPASS_ASSISTANTS_ENVIRONMENTS: list[str] = []
    EXTRA_ENCOMPASS_SALES_ASSISTANTS_ENVIRONMENTS: list[str] = []
    ALLOWED_EXTENSIONS: set = {
        "jpg",
        "jpeg",
        "png",
        "gif",
        "pdf",
        "webp",
        "txt",
        "csv",
        "xls",
        "xlsx",
        "doc",
        "docx",
    }

    ALLOWED_MIME_TYPES: set = {
        "image/jpeg",
        "image/jpg",
        "image/png",
        "image/gif",
        "image/webp",
        "application/pdf",
        "application/vnd.ms-excel",
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        "application/msword",
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        "text/csv",
        "text/plain",
    }
    ENCRYPTION_KEY: str = "default-secret-key"

    # twilio
    TWILIO_ACCOUNT_SID: str | None = None
    TWILIO_AUTH_TOKEN: str | None = None
    TWILIO_PHONE_NUMBER: str | None = None
    DOMAIN_URL: str | None = None

    # Polly

    POLLY_BASE_URL: str = "https://lx.pollyex.com/api/v2/"
    POLLY_USERNAME: str | None = None
    POLLY_PASSWORD: str | None = None
    POLLY_CLIENT_ID: str | None = None
    POLLY_CLIENT_SECRET: str | None = None
    POLLY_ORG_TICKER: str | None = None
    POLLY_ORG_NAME: str | None = None
    POLLY_AUDIENCE_ID: str | None = None

    @classmethod
    def decode_base64(cls, value: str) -> str:
        """Decode a base64 encoded string."""
        try:
            return base64.b64decode(value, validate=True).decode("utf-8")
        except Exception:
            # If decoding fails, return the original value
            return value

    @field_validator("*", mode="before")
    @classmethod
    def decode_base64_fields(cls, value, info):
        # Skip non-string values and specific fields
        if not isinstance(value, str) or info.field_name in (
            "BASE_DIR",
            "API_DIR",
            "SYNC_SHAREPOINT_CRON_HOUR",
            "SYNC_SHAREPOINT_CRON_MINUTE",
        ):
            return value

        # Special handling for Literal fields
        if info.field_name == "APP_ENV":
            decoded = cls.decode_base64(value)
            if decoded in ("local", "dev", "production"):
                return decoded
            return value

        # For other string fields, try to decode
        return cls.decode_base64(value)

    @model_validator(mode="after")
    def validate_db_name(self):
        if self.ENABLE_DIVISION_LOCAL and self.APP_ENV != "local":
            raise ValueError("If `ENABLE_DIVISION_LOCAL` is True, `APP_ENV` must be 'local'")

        if self.ENABLE_DIVISION_LOCAL and self.POSTGRES_DB != "insyde_division":
            raise ValueError("If `ENABLE_DIVISION_LOCAL` is True, `POSTGRES_DB` must be 'insyde_division'")
        return self

    def load_extra_encompass_credentials(self):
        """Load environment variables for each environment."""
        env_vars = [
            "ENCOMPASS_CLIENT_ID",
            "ENCOMPASS_CLIENT_SECRET",
            "ENCOMPASS_USERNAME",
            "ENCOMPASS_PASSWORD",
            "ENCOMPASS_INSTANCE_ID",
        ]
        for prefix in self.EXTRA_ENCOMPASS_ASSISTANTS_ENVIRONMENTS:
            for var in env_vars:
                env_name = f"{prefix.upper()}_{var}"
                value = getattr(self, env_name, None)
                if value is None:
                    # Try to load from environment if not already set
                    import os

                    value = os.getenv(env_name, "")
                setattr(self, env_name, self.decode_base64(value))


settings = Settings()
