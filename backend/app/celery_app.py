import logging
import sys

import sentry_sdk
from celery import Celery
from celery.schedules import crontab
from config import settings
from loguru import logger
from sentry_sdk.integrations.celery import CeleryIntegration
from sentry_sdk.integrations.logging import LoggingIntegration
from sentry_sdk.integrations.redis import RedisIntegration

celery_app = Celery(__name__)
celery_app.conf.broker_url = settings.CELERY_BROKER_URL
celery_app.conf.result_backend = settings.CELERY_RESULT_BACKEND
celery_app.autodiscover_tasks(["tasks"])

logger.remove(0)
logger.add(
    sink=sys.stdout,
    level=settings.LOG_LEVEL,
)

if settings.APP_ENV != "local":

    sentry_sdk.init(
        dsn=settings.SENTRY_DSN,
        integrations=[
            CeleryIntegration(monitor_beat_tasks=True),
            RedisIntegration(),
            LoggingIntegration(level=settings.LOG_LEVEL, event_level=logging.ERROR),
        ],
        environment=settings.SENTRY_ENVIRONMENT,
        traces_sample_rate=settings.SENTRY_TRACES_SAMPLE_RATE,
    )

    beat_schedule = {
        "sync_loan_officer_data": {
            "task": "tasks.loid.sync_loan_officer_data",
            "schedule": crontab(hour="*/6", minute=0),  # Refresh every 6 hours
        },
        "delete_expired_documentations": {
            "task": "tasks.documentation.delete_expired_documentations",
            "schedule": crontab(hour="*/3", minute=0),  # Delete at the start of every third hour
        },
        "refresh_beat_schedule": {
            "task": "tasks.website.refresh_beat_schedule",
            "schedule": crontab(minute="*/30"),  # Refresh every 30 minutes
        },
        "process_assistant_messages_every_week": {
            "task": "tasks.message_analytics.analyze_messages",
            "schedule": crontab(minute=0, hour=4, day_of_week=0),  # Every Sunday at midnight
        },
        "refresh-expiring-te-tokens-every-hour": {
            "task": "tasks.total_expert_token.refresh_expiring_tokens",
            "schedule": crontab(hour="*/1", minute=0),  # Every 1 hour
        },
        # "sync_insights": {
        #     "task": "tasks.total_expert.sync_insights", # TODO: need to fix for division
        #     "schedule": crontab(minute=0, hour=0, day_of_week=6),  # Every Saturday at UTC 0:00
        # },
    }

    if settings.AZURE_SHAREPOINT_TENANT_ID is not None:
        beat_schedule["sync_sharepoint_documents"] = {
            "task": "tasks.sharepoint.sync_sharepoint",
            "schedule": crontab(hour=settings.SYNC_SHAREPOINT_CRON_HOUR, minute=settings.SYNC_SHAREPOINT_CRON_MINUTE),
        }

    celery_app.conf.beat_schedule = beat_schedule

else:
    beat_schedule = {
        "process_assistant_messages_every_week": {
            "task": "tasks.message_analytics.analyze_messages",
            "schedule": crontab(minute=0, hour=4, day_of_week=0),
        }
    }
    celery_app.conf.beat_schedule = beat_schedule
