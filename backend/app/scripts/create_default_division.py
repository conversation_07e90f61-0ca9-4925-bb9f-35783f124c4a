import argparse
import async<PERSON>

from config import settings
from db.models import Division, DivisionCredentials, EncompassCredentials
from db.session import session_manager
from schema.enums import EncompassTypes
from sqlalchemy import select


async def create_division_with_credentials(domain: str):
    division_data = {
        "name": settings.CLIENT_NAME,
        "domain": domain,
        "description": None,
        "logo": None,
        "avatar": None,
        "primary_color": None,
        "secondary_color": None,
    }

    async with session_manager() as session:
        try:
            # Check if division already exists
            existing_division = await session.execute(
                select(Division).where(Division.domain == division_data["domain"])
            )
            if existing_division.scalar_one_or_none():
                print(f"Division with domain {division_data['domain']} already exists.")
                return

            # Create new Division
            division = Division(
                name=division_data["name"],
                domain=division_data["domain"],
                description=division_data["description"],
                logo=division_data["logo"],
                avatar=division_data["avatar"],
                primary_color=division_data["primary_color"],
                secondary_color=division_data["secondary_color"],
            )
            session.add(division)
            await session.flush()  # Ensure division.id is generated

            # Create DivisionCredentials
            division_credentials = DivisionCredentials(
                pinecone_index=settings.PINECONE_INDEX,
                pinecone_host=settings.PINECONE_HOST,
                aws_storage_bucket_name=settings.AWS_STORAGE_BUCKET_NAME,
                total_expert_client_id=settings.TOTAL_EXPERT_CLIENT_ID,
                total_expert_client_secret=settings.TOTAL_EXPERT_CLIENT_SECRET,
                division_id=division.id,
                enabled_total_expert=False,
                enabled_personal_assistant=False,
                enabled_sales_assistant=False,
            )
            session.add(division_credentials)
            await session.flush()  # Ensure division_credentials.id is generated

            # Create EncompassCredentials
            encompass_credentials = EncompassCredentials(
                name=settings.CLIENT_NAME,
                encompass_client_id=settings.ENCOMPASS_CLIENT_ID,
                encompass_client_secret=settings.ENCOMPASS_CLIENT_SECRET,
                encompass_password=settings.ENCOMPASS_PASSWORD,
                encompass_username=settings.ENCOMPASS_USERNAME,
                encompass_instance_id=settings.ENCOMPASS_INSTANCE_ID,
                division_credentials_id=division_credentials.id,
                encompass_type=EncompassTypes.PROD,
                enabled=False,
            )
            session.add(encompass_credentials)

            # Commit the transaction
            await session.commit()
            print(f"Successfully created division: {division_data['name']} with credentials.")

        except Exception as e:
            await session.rollback()
            print(f"Error creating division and credentials: {str(e)}")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Create a division with credentials for a specified domain.")
    parser.add_argument("--domain", type=str, required=True, help="The domain for the division (e.g., insyde.ai)")

    # Parse arguments
    args = parser.parse_args()
    asyncio.run(create_division_with_credentials(args.domain))
