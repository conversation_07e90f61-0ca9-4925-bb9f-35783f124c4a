import asyncio
import json
from pathlib import Path

import typer
from db.models import Division, DivisionCredentials, EncompassCredentials
from db.session import session_manager
from schema.enums import EncompassTypes
from sqlalchemy import select

app = typer.Typer()


def validate_encompass_credentials(encompass_data: dict) -> bool:
    required_fields = [
        "name",
        "encompass_client_id",
        "encompass_client_secret",
        "encompass_password",
        "encompass_username",
        "encompass_instance_id",
    ]
    values = [encompass_data.get(field, "").strip() for field in required_fields]
    has_any = any(values)
    has_all = all(values)
    if has_any and not has_all:
        raise typer.BadParameter("All Encompass credentials fields are required if any is provided.")
    return has_any


async def create_division_with_credentials(
    division_data: dict, credentials_data: dict, encompass_data_list: list[dict]
):
    async with session_manager() as session:
        try:
            existing_division = await session.execute(
                select(Division).where(Division.domain == division_data["domain"])
            )
            if existing_division.scalar_one_or_none():
                typer.echo(f"Division with domain {division_data['domain']} already exists.")
                return

            division = Division(
                name=division_data["name"],
                domain=division_data["domain"],
                description=division_data.get("description") or None,
                logo=division_data.get("logo") or None,
                avatar=division_data.get("avatar") or None,
                primary_color=division_data.get("primary_color") or None,
                secondary_color=division_data.get("secondary_color") or None,
            )
            session.add(division)
            await session.flush()

            division_credentials = DivisionCredentials(
                pinecone_index=credentials_data["pinecone_index"],
                pinecone_host=credentials_data["pinecone_host"],
                aws_storage_bucket_name=credentials_data["aws_storage_bucket_name"],
                total_expert_client_id=credentials_data.get("total_expert_client_id") or None,
                total_expert_client_secret=credentials_data.get("total_expert_client_secret") or None,
                division_id=division.id,
                enabled_total_expert=credentials_data.get("enabled_total_expert", False),
                enabled_personal_assistant=credentials_data.get("enabled_personal_assistant", False),
                enabled_sales_assistant=credentials_data.get("enabled_sales_assistant", False),
            )
            session.add(division_credentials)
            await session.flush()

            for encompass_data in encompass_data_list:
                if not validate_encompass_credentials(encompass_data):
                    continue
                encompass_credentials = EncompassCredentials(
                    name=encompass_data.get("name") or None,
                    encompass_client_id=encompass_data.get("encompass_client_id") or None,
                    encompass_client_secret=encompass_data.get("encompass_client_secret") or None,
                    encompass_password=encompass_data.get("encompass_password") or None,
                    encompass_username=encompass_data.get("encompass_username") or None,
                    encompass_instance_id=encompass_data.get("encompass_instance_id") or None,
                    division_credentials_id=division_credentials.id,
                    encompass_type=encompass_data.get("encompass_type", EncompassTypes.PROD.value),
                    enabled=encompass_data.get("enabled", False),
                )
                session.add(encompass_credentials)

            await session.commit()
            typer.echo(
                f"Successfully created division: {division_data['name']} "
                f"with credentials for domain: {division_data['domain']}"
            )
        except Exception as e:
            await session.rollback()
            typer.echo(f"Error creating division and credentials: {str(e)}")


@app.command()
def from_config(config_path: str = typer.Argument(..., help="Path to config.json file")):
    """
    Create divisions and credentials from a JSON config file (must be named config.json).
    """
    config_file = Path(config_path)
    if config_file.name != "config.json":
        typer.echo("Error: Only config.json is accepted as input.")
        raise typer.Exit(code=1)
    if not config_file.exists():
        typer.echo(f"Config file not found: {config_path}")
        raise typer.Exit(code=1)

    with open(config_file) as f:
        try:
            divisions = json.load(f)
        except Exception as e:
            typer.echo(f"Failed to parse JSON: {e}")
            raise typer.Exit(code=1)

    if not isinstance(divisions, list):
        typer.echo("Config JSON must be a list of division objects.")
        raise typer.Exit(code=1)

    async def process_divisions():
        for division in divisions:
            division_data = {
                "name": division.get("name"),
                "domain": division.get("domain"),
                "description": division.get("description", ""),
                "logo": division.get("logo", ""),
                "avatar": division.get("avatar", ""),
                "primary_color": division.get("primary_color", ""),
                "secondary_color": division.get("secondary_color", ""),
            }
            credentials = division.get("credentials", {})
            credentials_data = {
                "pinecone_index": credentials.get("pinecone_index"),
                "pinecone_host": credentials.get("pinecone_host"),
                "aws_storage_bucket_name": credentials.get("aws_storage_bucket_name"),
                "total_expert_client_id": credentials.get("total_expert_client_id", ""),
                "total_expert_client_secret": credentials.get("total_expert_client_secret", ""),
                "enabled_total_expert": credentials.get("enabled_total_expert", False),
                "enabled_personal_assistant": credentials.get("enabled_personal_assistant", False),
                "enabled_sales_assistant": credentials.get("enabled_sales_assistant", False),
            }
            encompass_data_list = credentials.get("encompass_credentials", [])
            await create_division_with_credentials(division_data, credentials_data, encompass_data_list)

    asyncio.run(process_divisions())


if __name__ == "__main__":
    app()
