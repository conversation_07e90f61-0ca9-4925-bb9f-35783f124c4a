import asyncio

import typer
from db.models import Division, DivisionCredentials, EncompassCredentials
from db.session import session_manager
from schema.enums import EncompassTypes
from sqlalchemy import select

app = typer.Typer()


def validate_encompass_credentials(encompass_data: dict) -> bool:
    required_fields = [
        "name",
        "encompass_client_id",
        "encompass_client_secret",
        "encompass_password",
        "encompass_username",
        "encompass_instance_id",
    ]
    values = [encompass_data.get(field, "").strip() for field in required_fields]
    has_any = any(values)
    has_all = all(values)
    if has_any and not has_all:
        raise typer.BadParameter("All Encompass credentials fields are required if any is provided.")
    return has_any


async def create_division_with_credentials(
    division_data: dict, credentials_data: dict, encompass_data_list: list[dict]
):
    async with session_manager() as session:
        try:
            # Check if division already exists
            existing_division = await session.execute(
                select(Division).where(Division.domain == division_data["domain"])
            )
            if existing_division.scalar_one_or_none():
                typer.echo(f"Division with domain {division_data['domain']} already exists.")
                return

            # Create new Division
            division = Division(
                name=division_data["name"],
                domain=division_data["domain"],
                description=division_data["description"] or None,
                logo=division_data["logo"] or None,
                avatar=division_data.get("avatar") or None,
                primary_color=division_data["primary_color"] or None,
                secondary_color=division_data["secondary_color"] or None,
            )
            session.add(division)
            await session.flush()  # Ensure division.id is generated

            # Create DivisionCredentials
            division_credentials = DivisionCredentials(
                pinecone_index=credentials_data["pinecone_index"],
                pinecone_host=credentials_data["pinecone_host"],
                aws_storage_bucket_name=credentials_data["aws_storage_bucket_name"],
                total_expert_client_id=credentials_data["total_expert_client_id"] or None,
                total_expert_client_secret=credentials_data["total_expert_client_secret"] or None,
                division_id=division.id,
                enabled_total_expert=credentials_data["enabled_total_expert"],
                enabled_personal_assistant=credentials_data["enabled_personal_assistant"],
                enabled_sales_assistant=credentials_data["enabled_sales_assistant"],
            )
            session.add(division_credentials)
            await session.flush()  # Ensure division_credentials.id is generated

            # Create EncompassCredentials for each set of credentials
            for encompass_data in encompass_data_list:
                encompass_credentials = EncompassCredentials(
                    name=encompass_data["name"] or None,
                    encompass_client_id=encompass_data["encompass_client_id"] or None,
                    encompass_client_secret=encompass_data["encompass_client_secret"] or None,
                    encompass_password=encompass_data["encompass_password"] or None,
                    encompass_username=encompass_data["encompass_username"] or None,
                    encompass_instance_id=encompass_data["encompass_instance_id"] or None,
                    division_credentials_id=division_credentials.id,
                    encompass_type=encompass_data["encompass_type"],
                    enabled=encompass_data["enabled"],
                )
                session.add(encompass_credentials)

            # Commit the transaction
            await session.commit()
            typer.echo(
                f"Successfully created division: {division_data['name']} "
                f"with credentials for domain: {division_data['domain']}"
            )

        except Exception as e:
            await session.rollback()
            typer.echo(f"Error creating division and credentials: {str(e)}")


@app.command()
def create_division(
    name: str = typer.Option(..., prompt="Division Name", help="Name of the division (required)"),
    domain: str = typer.Option(..., prompt="Domain (e.g., insyde.ai)", help="Domain for the division (required)"),
    description: str = typer.Option(
        "", prompt="Description (optional, press Enter to skip)", help="Description of the division (optional)"
    ),
    logo: str = typer.Option(
        "", prompt="Logo URL (optional, press Enter to skip)", help="Logo URL for the division (optional)"
    ),
    avatar: str = typer.Option(
        "", prompt="Avatar URL (optional, press Enter to skip)", help="Avatar URL for the division (optional)"
    ),
    primary_color: str = typer.Option(
        "",
        prompt="Primary Color (e.g., #E87127, optional, press Enter to skip)",
        help="Primary color for the division (optional)",
    ),
    secondary_color: str = typer.Option(
        "",
        prompt="Secondary Color (e.g., #FB8D18, optional, press Enter to skip)",
        help="Secondary color for the division (optional)",
    ),
    pinecone_index: str = typer.Option(..., prompt="Pinecone Index", help="Pinecone index name (required)"),
    pinecone_host: str = typer.Option(..., prompt="Pinecone Host", help="Pinecone host (required)"),
    aws_storage_bucket_name: str = typer.Option(
        ..., prompt="AWS Storage Bucket Name", help="AWS storage bucket name (required)"
    ),
    total_expert_client_id: str = typer.Option(
        "", prompt="Total Expert Client ID (optional, press Enter to skip)", help="Total Expert client ID (optional)"
    ),
    total_expert_client_secret: str = typer.Option(
        "",
        prompt="Total Expert Client Secret (optional, press Enter to skip)",
        help="Total Expert client secret (optional)",
    ),
    enabled_total_expert: bool = typer.Option(
        False, prompt="Enable Total Expert?", help="Enable Total Expert integration"
    ),
    enabled_personal_assistant: bool = typer.Option(
        False, prompt="Enable Personal Assistant?", help="Enable Personal Assistant"
    ),
    enabled_sales_assistant: bool = typer.Option(
        False, prompt="Enable Sales Assistant?", help="Enable Sales Assistant"
    ),
):
    """
    Create a division with associated credentials and multiple Encompass credentials interactively.
    """
    division_data = {
        "name": name,
        "domain": domain,
        "description": description,
        "logo": logo,
        "avatar": avatar,
        "primary_color": primary_color,
        "secondary_color": secondary_color,
    }

    credentials_data = {
        "pinecone_index": pinecone_index,
        "pinecone_host": pinecone_host,
        "aws_storage_bucket_name": aws_storage_bucket_name,
        "total_expert_client_id": total_expert_client_id,
        "total_expert_client_secret": total_expert_client_secret,
        "enabled_total_expert": enabled_total_expert,
        "enabled_personal_assistant": enabled_personal_assistant,
        "enabled_sales_assistant": enabled_sales_assistant,
    }

    # Collect multiple EncompassCredentials interactively
    encompass_data_list = []
    while True:
        typer.echo("\n=== Encompass Credentials (all fields required if any is provided) ===")
        encompass_data = {
            "name": typer.prompt(
                "Encompass Name (optional, press Enter to skip)", default="Encompass Assistant", show_default=False
            ),
            "encompass_client_id": typer.prompt(
                "Encompass Client ID (optional, press Enter to skip)", default="", show_default=False
            ),
            "encompass_client_secret": typer.prompt(
                "Encompass Client Secret (optional, press Enter to skip)", default="", show_default=False
            ),
            "encompass_password": typer.prompt(
                "Encompass Password (optional, press Enter to skip)", default="", show_default=False
            ),
            "encompass_username": typer.prompt(
                "Encompass Username (optional, press Enter to skip)", default="", show_default=False
            ),
            "encompass_instance_id": typer.prompt(
                "Encompass Instance ID (optional, press Enter to skip)", default="", show_default=False
            ),
        }
        if validate_encompass_credentials(encompass_data):  # Add only if at least one field is provided
            # Prompt for encompass_type and enabled
            encompass_type_choices = [e.value for e in EncompassTypes]
            encompass_type = typer.prompt(
                f"Encompass Type {encompass_type_choices} (default: {EncompassTypes.PROD})",
                default=EncompassTypes.PROD,
                show_default=True,
            )
            if encompass_type not in encompass_type_choices:
                typer.echo(f"Invalid encompass_type. Using default: {EncompassTypes.PROD}")
                encompass_type = EncompassTypes.PROD
            encompass_data["encompass_type"] = encompass_type
            encompass_data["enabled"] = typer.confirm("Enable this Encompass credentials set?", default=False)
            encompass_data_list.append(encompass_data)
            typer.echo("Encompass credentials added.")
        else:
            typer.echo("No Encompass credentials provided for this set.")

        # Ask if the user wants to add another set
        if not typer.confirm("Would you like to add another set of Encompass credentials?", default=False):
            break

    if not encompass_data_list:
        typer.echo("No Encompass credentials were provided.")

    asyncio.run(create_division_with_credentials(division_data, credentials_data, encompass_data_list))


if __name__ == "__main__":
    app()
