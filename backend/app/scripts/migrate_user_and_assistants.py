import asyncio

from db.models import Assistant, Division, DivisionCredentials, User
from db.session import session_manager
from schema.enums import AssistantSubTypes, AssistantTypes, RoleTypes
from sqlalchemy import select
from sqlalchemy.orm import selectinload


async def migrate():
    async with session_manager() as session:
        result = await session.execute(
            select(Division).options(
                selectinload(Division.credentials).selectinload(DivisionCredentials.encompass_credentials)
            )
        )
        divisions = result.scalars().all()
        if not divisions:
            print("No divisions found. Exiting.")
            return
        domain_to_division = {d.domain.lower(): d for d in divisions}

        # Migrate users
        result = await session.execute(select(User).where(User.division_id.is_(None)))
        users = result.scalars().all()
        for user in users:
            email_domain = user.email.split("@")[-1].lower()
            division = domain_to_division.get(email_domain)
            if division:
                user.role = RoleTypes.DIVISION_ADMIN if user.role == RoleTypes.ADMIN else user.role
                user.division_id = division.id
                print(f"Assigned user {user.email} to division {division.name}")
                session.add(user)

        # Migrate assistants
        first_division = divisions[0]
        first_division_encompass_credentials = (
            first_division.credentials.encompass_credentials[0]
            if first_division.credentials.encompass_credentials
            else None
        )
        result = await session.execute(select(Assistant).where(Assistant.division_id.is_(None)))
        assistants = result.scalars().all()
        for assistant in assistants:
            assistant.division_id = first_division.id
            if assistant.type == AssistantTypes.CUSTOM and assistant.sub_type in [
                AssistantSubTypes.ENCOMPASS,
                AssistantSubTypes.ENCOMPASS_SALES,
            ]:
                assistant.encompass_credential_id = (
                    first_division_encompass_credentials.id if first_division_encompass_credentials else None
                )
            print(f"Assigned assistant {assistant.name} to division {first_division.name}")
            session.add(assistant)

        await session.commit()
        print("Migration completed.")


if __name__ == "__main__":
    asyncio.run(migrate())
