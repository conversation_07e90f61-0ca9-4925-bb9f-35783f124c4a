import asyncio
import hashlib
import json
from io import BytesIO
from urllib.parse import urlparse

import redis.asyncio as aioredis
from config import settings
from db.models import Document, SharepointFolder
from db.session import session_manager
from fastapi import status
from httpx import AsyncClient, HTTPStatusError
from loguru import logger
from schema.enums import DocumentTypes
from sqlalchemy import select


class SharePointHook:
    def __init__(self):
        self.tenant_id = settings.AZURE_SHAREPOINT_TENANT_ID
        self.client_id = settings.AZURE_SHAREPOINT_CLIENT_ID
        self.client_secret = settings.AZURE_SHAREPOINT_CLIENT_SECRET
        self.resource_url = "https://graph.microsoft.com/"
        self.base_url = f"https://login.microsoftonline.com/{self.tenant_id}/oauth2/v2.0/token"
        self.headers = {"Content-Type": "application/x-www-form-urlencoded"}
        self.access_token = None
        self.client = AsyncClient(timeout=None)
        logger.info(f"SharePointHook initialized with tenant ID: {self.tenant_id}")
        self.redis = None

    async def initialize_redis(self):
        self.redis = await aioredis.from_url(settings.REDIS_URL)

    async def get_access_token(self):
        body = {
            "grant_type": "client_credentials",
            "client_id": self.client_id,
            "client_secret": self.client_secret,
            "scope": self.resource_url + ".default",
        }
        response = await self.client.post(self.base_url, headers=self.headers, data=body)
        response.raise_for_status()
        access_token = response.json().get("access_token")
        self.access_token = access_token
        await self.initialize_redis()
        logger.info("SharePoint access token retrieved successfully")
        return access_token

    async def set_access_token(self, access_token):
        self.access_token = access_token

    def convert_sharepoint_url(self, url):
        parsed_url = urlparse(url)

        # Remove the protocol part and construct the new site URL
        netloc = parsed_url.netloc
        path_parts = parsed_url.path.split("/")
        try:
            sites_index = path_parts.index("sites")
        except ValueError:
            return None  # 'sites' not found in the URL path

        # Construct the path up to and including the site name
        base_path = "/".join(path_parts[: sites_index + 2])
        return f"{netloc}:{base_path}"

    def generate_cache_key(self, user_id, site_id, drive_id, folder_id):
        # Use a hash of the user ID to generate a unique cache key
        user_hash = hashlib.sha256(str(user_id).encode()).hexdigest()
        return f"folder_contents:{user_hash}:{site_id}:{drive_id}:{folder_id}"

    async def fetch_folder_contents_from_api(
        self, user_id, site_id, drive_id, folder_id, access_token, hard_refresh=False
    ):
        # Create a unique cache key based on site_id, drive_id, folder_id
        cache_key = self.generate_cache_key(user_id, site_id, drive_id, folder_id)
        # Check if the API call result is already cached in Redis
        if not hard_refresh:
            cached_data = await self.redis.get(cache_key)
            if cached_data:
                return json.loads(cached_data)

        # If not cached, make the API call
        folder_contents_url = (
            f"https://graph.microsoft.com/v1.0/sites/{site_id}/drives/{drive_id}/items/{folder_id}/children"
        )
        try:
            contents_response = await self.client.get(
                folder_contents_url, headers={"Authorization": f"Bearer {access_token}"}
            )
            contents_response.raise_for_status()
            folder_contents = contents_response.json()
        except HTTPStatusError as e:
            if e.response.status_code in [status.HTTP_401_UNAUTHORIZED, status.HTTP_403_FORBIDDEN]:
                return None
            else:
                raise

        # Cache the result in Redis with a TTL
        if not hard_refresh:
            await self.redis.setex(cache_key, settings.REDIS_CACHE_TTL, json.dumps(folder_contents))
        return folder_contents

    async def get_site_id(self, site_url):
        """Get site id from site URL"""
        site_url = self.convert_sharepoint_url(site_url)
        full_url = f"https://graph.microsoft.com/v1.0/sites/{site_url}"
        response = await self.client.get(full_url, headers={"Authorization": f"Bearer {self.access_token}"})
        response.raise_for_status()
        site_id = response.json().get("id")
        logger.info(f"Site ID retrieved successfully: {site_id} for {site_url}")
        return site_id

    async def get_drive_id(self, site_id):
        """Retrieve drive IDs and names associated with a site"""
        drives_url = f"https://graph.microsoft.com/v1.0/sites/{site_id}/drives"
        response = await self.client.get(drives_url, headers={"Authorization": f"Bearer {self.access_token}"})
        response.raise_for_status()
        drives = response.json().get("value", [])
        return [{"id": drive["id"], "name": drive["name"]} for drive in drives]

    async def check_is_indexed(self, assistant_id, file_id, last_modified, type):
        """Check if a document is indexed and if it has been modified"""
        is_modified = False
        is_indexed = False
        document = None
        async with session_manager() as session:
            query = (
                select(Document)
                .where(Document.assistant_id == assistant_id)
                .where(Document.type == type)
                .where(Document.meta["id"].astext == file_id)
            )
            result = await session.execute(query)
            documents = result.scalars().all()
            if documents:
                document = documents[0]
                is_indexed = document.is_indexed
                stored_date = document.meta.get("lastModifiedDateTime", "")
                is_modified = stored_date != last_modified
        return is_indexed, is_modified, document

    async def check_folder_indexed(self, assistant_id, site_id, drive_id, folder_id, last_modified, type):
        """Check if a folder is indexed"""
        is_indexed = False
        is_modified = False
        async with session_manager() as session:
            query = (
                select(SharepointFolder)
                .where(SharepointFolder.assistant_id == assistant_id)
                .where(SharepointFolder.site_id == site_id)
                .where(SharepointFolder.drive_id == drive_id)
                .where(SharepointFolder.folder_id == folder_id)
                .where(SharepointFolder.type == type)
            )
            result = await session.execute(query)
            folder = result.scalars().first()
            if folder:
                is_indexed = folder.is_indexed
                stored_date = folder.meta.get("lastModifiedDateTime", "") if folder.meta else ""
                if stored_date:
                    is_modified = stored_date != last_modified
        return is_indexed, is_modified, folder

    async def list_folder_contents(
        self,
        user_id,
        assistant_id,
        filename_filter,
        categories,
        site_id,
        drive_id,
        folder_id,
        type,
        hard_refresh,
        is_indexed_filter,
    ):
        """List folder contents recursively"""
        try:
            folder_contents = await self.fetch_folder_contents_from_api(
                user_id, site_id, drive_id, folder_id, self.access_token, hard_refresh
            )
            if not folder_contents:
                return []
            folder_hierarchy = []

            for item in folder_contents.get("value", []):
                if "folder" in item:
                    is_folder_indexed, is_folder_modified, folder = await self.check_folder_indexed(
                        assistant_id, site_id, drive_id, item["id"], item.get("lastModifiedDateTime", ""), type
                    )
                    if is_indexed_filter is None or is_folder_indexed == is_indexed_filter:
                        folder_hierarchy.append(
                            {
                                "id": item["id"],
                                "name": item["name"],
                                "type": "folder",
                                "createdBy": item.get("createdBy", {}),
                                "createdDateTime": item.get("createdDateTime", ""),
                                "lastModifiedBy": item.get("lastModifiedBy", {}),
                                "lastModifiedDateTime": item.get("lastModifiedDateTime", ""),
                                "site_id": site_id,
                                "drive_id": drive_id,
                                "is_indexed": is_folder_indexed,
                                "is_modified": is_folder_modified,
                                "document": folder,
                                "size": item.get("size", 0),
                            }
                        )
                elif "file" in item:
                    path_parts = item["parentReference"]["path"].split("root:")
                    path = path_parts[1].lstrip("/") if len(path_parts) > 1 else ""
                    full_path = f"{path}/{item['name']}" if path else item["name"]
                    is_indexed, is_modified, document = await self.check_is_indexed(
                        assistant_id, item["id"], item.get("lastModifiedDateTime", ""), type
                    )

                    # Check if the document matches the categories filter
                    if document and categories:
                        document_categories = document.categories or []
                        if not any(category in document_categories for category in categories):
                            continue

                    # Check if the filename matches the filename filter
                    if filename_filter and filename_filter.lower() not in item["name"].lower():
                        continue

                    if is_indexed_filter is None or is_indexed == is_indexed_filter:
                        folder_hierarchy.append(
                            {
                                "id": item["id"],
                                "name": item["name"],
                                "type": "file",
                                "createdBy": item.get("createdBy", {}),
                                "createdDateTime": item.get("createdDateTime", ""),
                                "lastModifiedBy": item.get("lastModifiedBy", {}),
                                "lastModifiedDateTime": item.get("lastModifiedDateTime", ""),
                                "mimeType": item["file"]["mimeType"],
                                "uri": item.get("@microsoft.graph.downloadUrl", ""),
                                "fullpath": full_path,
                                "is_indexed": is_indexed,
                                "is_modified": is_modified,
                                "site_id": site_id,
                                "drive_id": drive_id,
                                "document": document,
                                "size": item.get("size", 0),
                            }
                        )

            return folder_hierarchy
        except Exception as e:
            # Handle specific exceptions (e.g., 404)
            if hasattr(e, "response") and e.response.status_code == status.HTTP_404_NOT_FOUND:
                logger.warning(f"Resource not found (404): {e}")
                return []

            # Log unexpected exceptions and re-raise
            logger.error(f"An unexpected error occurred: {e}", exc_info=True)
            raise

    async def download_file(self, site_id, drive_id, file_id) -> BytesIO:
        """Download file"""
        logger.info(f"Downloading file: {site_id}, {drive_id}, {file_id}")
        download_url = f"https://graph.microsoft.com/v1.0/sites/{site_id}/drives/{drive_id}/items/{file_id}/content"
        response = await self.client.get(download_url, headers={"Authorization": f"Bearer {self.access_token}"})
        if response.status_code == status.HTTP_302_FOUND:
            redirect_url = response.headers.get("Location")
            response = await self.client.get(redirect_url, headers={"Authorization": f"Bearer {self.access_token}"})
        response.raise_for_status()
        return BytesIO(response.content)

    async def get_file(self, site_id, drive_id, file_id) -> dict | None:
        """Get file"""
        logger.info(f"Get file: {site_id}, {drive_id}, {file_id}")
        get_file_url = f"https://graph.microsoft.com/v1.0/sites/{site_id}/drives/{drive_id}/items/{file_id}"
        try:
            response = await self.client.get(get_file_url, headers={"Authorization": f"Bearer {self.access_token}"})
            response.raise_for_status()
            return response.json()
        except HTTPStatusError as e:
            if e.response.status_code == status.HTTP_404_NOT_FOUND:
                logger.warning(f"File not found: {get_file_url}")
                return None
            else:
                logger.error(f"HTTP error occurred: {e}")
                raise
        except Exception as e:
            logger.error(f"An unexpected error occurred: {e}")
            raise

    async def list_sites(self):
        """List all SharePoint sites"""
        full_url = "https://graph.microsoft.com/v1.0/sites?search=*"
        response = await self.client.get(full_url, headers={"Authorization": f"Bearer {self.access_token}"})
        response.raise_for_status()
        sites = response.json().get("value", [])
        logger.info(f"Successfully retrieved {len(sites)} sites.")
        return [
            {"id": site["id"], "displayName": site["displayName"], "name": site["name"], "url": site["webUrl"]}
            for site in sites
        ]

    def generate_pages_cache_key(self, user_id: str, site_id: str, filter: str = None):
        user_hash = hashlib.sha256(str(user_id).encode()).hexdigest()
        filter_part = f":{filter}" if filter else ""
        return f"site_pages:{user_hash}:{site_id}{filter_part}"

    async def fetch_page_from_api(
        self, user_id: str, site_id: str, access_token: str, hard_refresh: bool, filter: str = None
    ):
        """Fetch pages for a SharePoint site with caching"""
        cache_key = self.generate_pages_cache_key(user_id, site_id, filter)
        if not hard_refresh:
            cached_data = await self.redis.get(cache_key)
            if cached_data:
                return json.loads(cached_data)

        # If not cached, make the API call
        select_fields = "id,name,title,webUrl,createdDateTime,lastModifiedDateTime,createdBy,lastModifiedBy"
        url = f"https://graph.microsoft.com/v1.0/sites/{site_id}/pages"
        try:
            response = await self.client.get(
                url,
                params={"$select": select_fields, "$filter": filter or ""},
                headers={"Authorization": f"Bearer {access_token}"},
            )
            response.raise_for_status()
            pages_data = response.json()
            if not hard_refresh:
                await self.redis.setex(cache_key, settings.REDIS_CACHE_TTL, json.dumps(pages_data))
            return pages_data
        except HTTPStatusError as e:
            if e.response.status_code in [status.HTTP_401_UNAUTHORIZED, status.HTTP_403_FORBIDDEN]:
                logger.warning(f"Access denied or unauthorized: {e}")
                return None
            else:
                logger.error(f"HTTP error occurred while fetching pages: {e}")
                raise

    async def list_pages(
        self,
        user_id: str,
        assistant_id: str,
        categories: str,
        site_id: str,
        is_indexed_filter: bool,
        hard_refresh: bool = False,
        filter: str | None = None,
    ) -> list[dict]:
        """List all pages for a SharePoint site"""
        logger.info(f"Listing SharePoint pages for site: {site_id}")
        pages_data = await self.fetch_page_from_api(user_id, site_id, self.access_token, hard_refresh, filter)
        if not pages_data:
            return []

        pages = []
        for item in pages_data.get("value", []):
            is_indexed, is_modified, document = await self.check_is_indexed(
                assistant_id, item["id"], item.get("lastModifiedDateTime", ""), DocumentTypes.SHAREPOINT_PAGE
            )

            if document and categories:
                document_categories = document.categories or []
                if not any(category in document_categories for category in categories):
                    continue

            if is_indexed_filter is not None and is_indexed != is_indexed_filter:
                continue

            pages.append(
                {
                    "id": item.get("id"),
                    "name": item.get("name"),
                    "title": item.get("title"),
                    "webUrl": item.get("webUrl"),
                    "site_id": site_id,
                    "createdDateTime": item.get("createdDateTime"),
                    "lastModifiedDateTime": item.get("lastModifiedDateTime"),
                    "createdBy": item.get("createdBy", {}),
                    "lastModifiedBy": item.get("lastModifiedBy", {}),
                    "is_indexed": is_indexed,
                    "is_modified": is_modified,
                    "document": document,
                }
            )

        return pages

    async def get_page_content(
        self,
        user_id: str,
        assistant_id: str,
        categories: str,
        site_id: str,
        page_id: str,
        is_indexed_filter: bool,
        hard_refresh: bool = False,
    ) -> dict:
        """Get a SharePoint page content"""
        logger.info(f"Getting SharePoint page content for page: {page_id} in site: {site_id}")
        url = f"https://graph.microsoft.com/v1.0/sites/{site_id}/pages/{page_id}/microsoft.graph.sitePage/webparts"
        try:
            response = await self.client.get(url, headers={"Authorization": f"Bearer {self.access_token}"})
            response.raise_for_status()
            page_data = response.json()
        except HTTPStatusError as e:
            logger.error(f"HTTP error occurred: {e}")
            raise
        except Exception as e:
            logger.error(f"An unexpected error occurred: {e}")
            raise

        # Initialize content categories
        page_content = {"text": "", "links": [], "external_links": [], "documents": []}

        # Collect all .aspx URLs for batch processing
        aspx_urls = []

        for webpart in page_data.get("value", []):
            # Handle text web parts
            if webpart.get("@odata.type") == "#microsoft.graph.textWebPart":
                html_content = webpart.get("innerHtml", "")
                if html_content:
                    page_content["text"] += html_content + "<br><br>"

            # Handle document/file web parts
            elif webpart.get("@odata.type") == "#microsoft.graph.standardWebPart":
                data = webpart.get("data", {})
                properties = data.get("properties", {})

                if properties.get("file"):
                    url = properties.get("file")
                    if url.endswith((".pdf", ".docx", "doc")):
                        document_info = {
                            "title": url.split("/")[-1],
                            "url": url,
                        }
                        page_content["documents"].append(document_info)

                # Process any web part with serverProcessedContent links
                server_processed = (
                    webpart.get("data", {}).get("serverProcessedContent", {}) if webpart.get("data") else {}
                )
                links = server_processed.get("links", []) if server_processed else []
                for link in links:
                    if link.get("key") == "url" and link.get("value"):
                        url_value = link.get("value")

                        # Categorize link as external or SharePoint
                        if url_value.startswith(("http://", "https://")) and not url_value.endswith(".aspx"):
                            page_content["external_links"].append(url_value)
                        elif url_value.endswith(".aspx"):
                            aspx_urls.append(url_value)

        # Batch process all .aspx URLs
        if aspx_urls:
            page_tasks = [
                self.list_pages(
                    user_id,
                    assistant_id,
                    categories,
                    site_id,
                    is_indexed_filter,
                    hard_refresh,
                    filter=f"contains(webUrl, '{url_value}')",
                )
                for url_value in aspx_urls
            ]
            page_results = await asyncio.gather(*page_tasks, return_exceptions=True)
            for i, result in enumerate(page_results):
                if isinstance(result, Exception):
                    logger.warning(f"Failed to fetch page for URL {aspx_urls[i]}: {result}")
                elif result:
                    page_content["links"].append(result[0])

        # Combine the page data with its content
        result = {"id": page_id, "site_id": site_id, "page_content": page_content}
        return result


sharepoint_hook = SharePointHook()
