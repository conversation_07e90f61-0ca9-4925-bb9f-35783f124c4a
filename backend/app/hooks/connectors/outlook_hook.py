import base64
import json
import os
import tempfile
from datetime import <PERSON><PERSON><PERSON>

import httpx
import redis.asyncio as aioredis
from config import settings
from html2text import html2text
from httpx import AsyncClient, HTTPStatusError
from loguru import logger
from openai import AsyncOpenAI
from schema.connectors.outlook import TranscriptStatus


class OutlookHook:
    def __init__(self):
        self.tenant_id = settings.AZURE_SHAREPOINT_TENANT_ID
        self.client_id = settings.AZURE_SHAREPOINT_CLIENT_ID
        self.client_secret = settings.AZURE_SHAREPOINT_CLIENT_SECRET
        self.client = AsyncClient(timeout=None)
        self.redis = None
        self.resource_url = "https://graph.microsoft.com/v1.0/"
        self.open_ai_client = AsyncOpenAI(api_key=settings.OPENAI_API_KEY, organization=settings.OPENAI_ORG_ID)

    async def get_confidential_token(self):
        data = {
            "grant_type": "client_credentials",
            "client_id": self.client_id,
            "client_secret": self.client_secret,
            "scope": "https://graph.microsoft.com/.default",
        }

        url = f"https://login.microsoftonline.com/{self.tenant_id}/oauth2/v2.0/token"

        async with httpx.AsyncClient() as client:
            try:
                response = await client.post(url, data=data)
                response.raise_for_status()
                access_token = response.json().get("access_token")
                return access_token
            except httpx.HTTPStatusError as e:
                logger.error(f"HTTP error getting access token: {e.response.status_code} - {e.response.text}")
            except Exception as e:
                logger.exception(f"Unexpected error getting access token: {e}")

        return None

    async def create_completion(self, messages: list[dict], model="gpt-4o", temperature: float = 0.0, **kwargs) -> str:
        response = await self.open_ai_client.chat.completions.create(
            model=model,
            messages=messages,
            temperature=temperature,
            **kwargs,
        )
        return response.choices[0].message.content

    async def initialize_redis(self):
        if not self.redis:
            self.redis = await aioredis.from_url(settings.REDIS_URL)

    async def get_user_details(self, search_query, token: str):
        if search_query:
            url = (
                f"{self.resource_url}users?"
                f"$filter=startswith(displayName,'{search_query}') "
                f"or startswith(givenName,'{search_query}') "
                f"or startswith(surname,'{search_query}') "
                f"or startswith(mail,'{search_query}') "
                f"or startswith(userPrincipalName,'{search_query}')"
            )  # noqa
        else:
            url = f"{self.resource_url}me/people/"
        try:
            response = await self.client.get(url, headers={"Authorization": f"Bearer {token}"})
            response.raise_for_status()
            return response.json().get("value")
        except HTTPStatusError as e:
            logger.error(f"Failed to list messages: {e.response.text}")
            return []

    async def get_email(self, token: str):
        url = f"{self.resource_url}me"
        if not token:
            raise Exception("Access token cannot be empty.")
        try:
            response = await self.client.get(url, headers={"Authorization": f"Bearer {token}"})
            response.raise_for_status()
            json_response = response.json()
            self.user_details = json_response
            return json_response
        except HTTPStatusError as e:
            logger.warning(f"Failed to list messages: {e.response.text}")
            raise

    async def list_filtered_messages(self, search_query=None, search_folder="Inbox", top=5, token: str = None):
        if not token:
            raise Exception("Access token cannot be empty.")
        if search_query:
            url = f'{self.resource_url}me/mailFolders/{search_folder}/messages?$search="{search_query}"&$top={top}'
        else:
            url = f"{self.resource_url}me/mailFolders/{search_folder}/messages?top={top}"
        try:
            response = await self.client.get(url, headers={"Authorization": f"Bearer {token}"})
            response.raise_for_status()
            messages = response.json().get("value", [])
            return self.extract_important_fields(messages)
        except HTTPStatusError as e:
            logger.error(f"Failed to list messages: {e.response.text}")
            raise

    def extract_important_fields(self, mails):
        filtered_mails = []
        for mail in mails:
            receiver_mails = [
                recipient.get("emailAddress", {}).get("address")
                for recipient in mail.get("toRecipients", [])
                if recipient.get("emailAddress", {}).get("address")
            ]
            filtered_mail = {
                "id": mail.get("id"),
                "receivedDateTime": mail.get("receivedDateTime"),
                "sentDateTime": mail.get("sentDateTime"),
                "hasAttachments": mail.get("hasAttachments"),
                "subject": mail.get("subject"),
                "body": mail["body"]["content"],
                "importance": mail.get("importance"),
                "isRead": mail.get("isRead"),
                "sender": mail.get("sender", {}).get("emailAddress", {}),
                "recipients": receiver_mails,
                "webLink": mail.get("webLink"),
            }
            filtered_mails.append(filtered_mail)
        return filtered_mails

    async def get_message(self, message_id, token: str):
        """Get details of a specific message."""
        url = f"{self.resource_url}me/messages/{message_id}"
        try:
            response = await self.client.get(url, headers={"Authorization": f"Bearer {token}"})
            response.raise_for_status()
            message = response.json()
            logger.info(f"Retrieved message ID: {message_id}")
            return message
        except HTTPStatusError as e:
            logger.error(f"Failed to get message: {e.response.text}")
            raise

    async def get_attachment_download_path(self, message_id, attachment_id, token: str):
        """Single attachment details"""
        url = f"{self.resource_url}me/messages/{message_id}/attachments/{attachment_id}"
        try:
            response = await self.client.get(url, headers={"Authorization": f"Bearer {token}"})
            response.raise_for_status()
            response = response.json()
            content_bytes = response.pop("contentBytes", None)
            if content_bytes:
                try:
                    decoded_content = base64.b64decode(content_bytes)
                    file_name = response["name"]
                    temp_file_path = os.path.join(tempfile.gettempdir(), file_name)
                    with open(temp_file_path, "wb") as temp_file:
                        temp_file.write(decoded_content)
                    return temp_file_path
                except Exception as e:
                    logger.error(f"Error processing attachment {response.get('name')}: {e}")
            return response
        except HTTPStatusError as e:
            logger.error(f"Failed to fetch attachment: {e.response.text}")
            raise

    async def get_message_attachments(self, message_id, token: str):
        """Download an attachment from a message."""
        url = f"{self.resource_url}me/messages/{message_id}/attachments"
        try:
            response = await self.client.get(url, headers={"Authorization": f"Bearer {token}"})
            response.raise_for_status()
            return response.json()
        except HTTPStatusError as e:
            logger.error(f"Failed to fetch attachment: {e.response.text}")
            raise

    async def send_email(self, recipients: list[str], subject: str, body: str, token: str):
        if not token:
            raise Exception("Access token cannot be empty.")
        email_data = {
            "message": {
                "subject": subject,
                "body": {"contentType": "HTML", "content": body},
                "toRecipients": [{"emailAddress": {"address": email}} for email in recipients],
            }
        }
        logger.info(f"Sending email to {', '.join(recipients)}")
        url = f"{self.resource_url}me/sendMail"
        headers = {"Authorization": f"Bearer {token}", "Content-Type": "application/json"}
        response = await self.client.post(url, headers=headers, json=email_data)
        response.raise_for_status()
        logger.info(f"Email sent to {', '.join(recipients)} successfully")

    async def reply_to_email(self, message_id: str, reply_body: str, recipients: list, token: str):
        if not token:
            raise Exception("Access token cannot be empty")
        headers = {"Authorization": f"Bearer {token}", "Content-Type": "application/json"}
        create_reply_url = f"{self.resource_url}me/messages/{message_id}/createReply"
        create_response = await self.client.post(create_reply_url, headers=headers)
        create_response.raise_for_status()
        draft = create_response.json()
        draft_id = draft["id"]
        original_content = draft.get("body", {}).get("content", "")
        combined_content = f"{reply_body}<br/><br/>{original_content}"
        update_url = f"{self.resource_url}me/messages/{draft_id}"
        update_data = {
            "body": {"contentType": "HTML", "content": combined_content},
            "toRecipients": [{"emailAddress": {"address": email}} for email in recipients],
        }
        update_response = await self.client.patch(update_url, headers=headers, json=update_data)
        update_response.raise_for_status()
        send_url = f"{self.resource_url}me/messages/{draft_id}/send"
        send_response = await self.client.post(send_url, headers=headers)
        send_response.raise_for_status()
        logger.info(f"Replied to email {message_id} successfully with additional recipients")

    async def forward_email(self, message_id: str, recipients: list[str], forward_body: str, token: str):
        if not token:
            raise Exception("Access token is not set. Call get_access_token() first.")
        email_data = {
            "comment": forward_body,
            "toRecipients": [{"emailAddress": {"address": email}} for email in recipients],
        }
        logger.info(f"Forwarding email with ID {message_id} to {', '.join(recipients)}")
        url = f"{self.resource_url}me/messages/{message_id}/forward"
        headers = {"Authorization": f"Bearer {token}", "Content-Type": "application/json"}
        response = await self.client.post(url, headers=headers, json=email_data)
        response.raise_for_status()
        logger.info(f"Forwarded email {message_id} successfully to {', '.join(recipients)}")

    async def get_event_details(self, event_id: str, token: str) -> dict:
        if not token:
            raise Exception("Access token is not set.")
        url = f"{self.resource_url}me/events/{event_id}"
        try:
            response = await self.client.get(url, headers={"Authorization": f"Bearer {token}"})
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"Failed to fetch event: {e.response.text}")
            raise

    async def create_event(self, event: dict, token: str):
        if not token:
            raise Exception("Access token is not set.")
        url = f"{self.resource_url}me/events"
        try:
            response = await self.client.post(url, headers={"Authorization": f"Bearer {token}"}, json=event)
            response.raise_for_status()
        except Exception as e:
            logger.error(f"Failed to create event: {e.response.text}")
            raise

    async def get_calendar_details(self, calendar_id: str, token: str) -> dict:
        if not token:
            raise Exception("Access token is not set.")
        url = f"{self.resource_url}me/calendars/{calendar_id}"
        try:
            response = await self.client.get(url, headers={"Authorization": f"Bearer {token}"})
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"Failed to fetch calendar details: {e.response.text}")
            raise

    async def check_transcript_availability(self, events, user_email, calendar, token, startDate, endDate):
        # Check if the event is personal calendar event or a shared event
        is_personal_calendar = user_email.get("mail") == calendar.get("owner", {}).get("address")
        for event in events:
            # Case 1: Personal Calendar Event
            if is_personal_calendar:
                if event.get("isOnlineMeeting") and event.get("onlineMeeting", {}).get("joinUrl"):
                    online_meeting_id = await self.get_online_meeting_id(
                        join_url=event.get("onlineMeeting")["joinUrl"], token=token
                    )
                    if online_meeting_id is not None:
                        # First, call the filtered URL (past events)
                        url_date_filtered = f"{self.resource_url}me/onlineMeetings/{online_meeting_id}/transcripts?$filter=createdDateTime ge {startDate} and createdDateTime le {endDate}"  # noqa
                        url_non_filtered = f"{self.resource_url}me/onlineMeetings/{online_meeting_id}/transcripts"  # For future events or general retrieval # noqa
                        try:
                            # First API call with filtering
                            response = await self.client.get(
                                url_date_filtered, headers={"Authorization": f"Bearer {token}"}
                            )
                            response.raise_for_status()
                            transcript_data = response.json()
                            transcripts = transcript_data.get("value", [])
                            if len(transcripts) > 0:
                                event["transcript_available"] = TranscriptStatus.AVAILABLE
                            elif event.get("type") in ["seriesMaster", "occurrence", "exception"]:
                                # If no transcripts found, call the second API without filtering
                                response = await self.client.get(
                                    url_non_filtered, headers={"Authorization": f"Bearer {token}"}
                                )
                                response.raise_for_status()
                                transcript_data = response.json()
                                transcripts = transcript_data.get("value", [])
                                if len(transcripts) > 0:
                                    event["transcript_available"] = TranscriptStatus.PREV_AVAILABLE
                                else:
                                    event["transcript_available"] = TranscriptStatus.UNAVAILABLE
                            else:
                                event["transcript_available"] = TranscriptStatus.UNAVAILABLE
                        except HTTPStatusError as e:
                            if e.response.status_code == 400:
                                logger.warning(
                                    f"Bad Request: Check if the meeting ID is correct and the API supports transcripts: {e}"  # noqa
                                )
                            else:
                                logger.warning(
                                    f"Failed to fetch Teams meeting transcript for {online_meeting_id}: {e}"
                                )
                            event["transcript_available"] = TranscriptStatus.UNAVAILABLE
                        except Exception as e:
                            logger.warning(f"Unexpected error while fetching transcript: {e}")
                            event["transcript_available"] = TranscriptStatus.UNAVAILABLE
                    else:
                        event["transcript_available"] = TranscriptStatus.UNAVAILABLE
                else:
                    event["transcript_available"] = TranscriptStatus.UNAVAILABLE
            # Case 2: Shared Calendar Event (Set transcript avaibility explicitly to 'Unavailable')
            else:
                event["transcript_available"] = TranscriptStatus.UNAVAILABLE
        return events

    async def get_calendar_events(self, start_date: str, end_date: str, token: str):
        if not token:
            raise Exception("Access token is not set.")
        url = f"{self.resource_url}me/calendars/"
        user_email = await self.get_email(token)
        try:
            all_events = []
            response = await self.client.get(url, headers={"Authorization": f"Bearer {token}"})
            if response.status_code == 401:
                logger.warning("Unauthorized: Invalid or expired token.")
                return {"error": "Access Denied. Your token is invalid or has expired."}
            response.raise_for_status()
            for calendar in response.json().get("value", []):
                if calendar.get("name") not in ["United States holidays", "Birthdays"]:
                    events_url = (
                        f"{self.resource_url}me/calendars/{calendar.get('id')}/calendarView?startDateTime={start_date}"
                        f"&endDateTime={end_date}&$orderby=start/dateTime"
                    )  # noqa
                    headers = {"Authorization": f"Bearer {token}", "Content-Type": "application/json"}
                    logger.info(f"Retrieving calendar events from {start_date} to {end_date}")
                    response = await self.client.get(events_url, headers=headers)
                    response.raise_for_status()
                    events = response.json().get("value", [])
                    # checking whether the events have available transcritps or not
                    events = await self.check_transcript_availability(
                        events, user_email, calendar, token, start_date, end_date
                    )
                    logger.info(f"Retrieved {len(events)} events")
                    event_details = {
                        "Calendar_owner": calendar.get("owner", None),
                        "Calendar_id": calendar.get("id"),
                        "events": events,
                    }
                    if calendar.get("owner", None):
                        all_events.append(event_details)
            return all_events
        except HTTPStatusError as e:
            logger.error(f"Failed to fetch events: {e.response.text}")
            raise

    async def get_related_emails(
        self, subject: str, organizer: str, attendees: list, user_mail, top: int = 10, token: str = None
    ):
        if not token:
            raise Exception("Access token is not set. Call set_access_token() first.")
        url = f"{self.resource_url}users/{user_mail}/messages"
        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json",
            "Prefer": "outlook.body-content-type=text",
        }
        # Construct the search query to include only relevant emails
        search_query = f"(from:{organizer} OR to:{organizer})"
        params = {
            "$search": f'"{search_query}"',
            "$top": 20,
            "$select": "subject,uniqueBody,sentDateTime,receivedDateTime,sender",
        }
        try:
            response = await self.client.get(url, headers=headers, params=params)
            response.raise_for_status()
            emails = response.json().get("value", [])
        except HTTPStatusError as e:
            if e.response.status_code == 403:
                logger.warning("Access Denied: Check API permissions or token expiry.")
                return [], []
            else:
                logger.error(f"Failed to fetch emails: {e.response.text}")
                return [], []

        # use openai to filter emails
        prompt = (
            f"Given the title: ```{subject}``` of the calender event, "
            "determine if the following email subjects are related to the calender event. "
            f"Email subjects:\n\n{[email.get('subject') for email in emails]}"
            "\n\nPlease follow these instructions carefully:\n"
            "1. If the email is related to the calender event, type 'yes'. "
            "2. If the email is not related to the calender event, type 'no'."
            "4. Provide the output in a structured JSON format as follows:\n"
            "```{'relevant_emails': ['yes', 'no', 'yes']}```"
        )
        response_format = {
            "type": "json_schema",
            "json_schema": {
                "name": "filtered_response",
                "schema": {
                    "type": "object",
                    "properties": {
                        "relevant_emails": {"type": "array", "items": {"type": "string"}},
                    },
                    "required": ["relevant_emails"],
                    "additionalProperties": False,
                },
                "strict": True,
            },
        }
        messages = [{"role": "user", "content": prompt}]
        response = await self.create_completion(messages=messages, response_format=response_format)
        response = json.loads(response)
        relevant_emails = response.get("relevant_emails")
        filtered_emails = [email for email, relevant in zip(emails, relevant_emails) if relevant == "yes"]
        filtered_emails = filtered_emails[: int(top)]
        return filtered_emails, emails[: int(top)]

    async def get_hierarchy(self, user_id, level=2, token: str = None):
        """Fetches hierarchy 2 levels up and 2 levels down for the given user."""
        hierarchy = {"managers": [], "direct_reports": []}
        current_id = user_id
        for _ in range(level):
            url = f"{self.resource_url}users/{current_id}/manager"
            headers = {"Authorization": f"Bearer {token}", "Content-Type": "application/json"}
            response = await self.client.get(url, headers=headers)
            if response.status_code == 200:
                manager = response.json()
                hierarchy["managers"].append(manager)
                current_id = manager["id"]
            else:
                break
        queue = [(user_id, 0)]
        while queue:
            current_id, current_level = queue.pop(0)
            if current_level < level:
                url = f"{self.resource_url}users/{current_id}/directReports"
                response = await self.client.get(url, headers=headers)
                if response.status_code == 200:
                    direct_reports = response.json().get("value", [])
                    hierarchy["direct_reports"].extend(direct_reports)
                    queue.extend([(dr["id"], current_level + 1) for dr in direct_reports])
        return hierarchy

    async def fetch_transcript_content(self, transcript_data, confidential_token):
        transcript_content = []
        for transcript in transcript_data[:2]:
            createdDate = transcript.get("createdDateTime", None)
            try:
                transcript_content_url = f"{transcript.get('transcriptContentUrl')}?$format=text/vtt"
                content_response = await self.client.get(
                    transcript_content_url, headers={"Authorization": f"Bearer {confidential_token}"}
                )
                if createdDate:
                    content_response = f"Transcript of this meeting is based on event happened on {createdDate}:\n{content_response.text}"  # noqa
                else:
                    content_response = content_response.text
                transcript_content.append(content_response)
            except HTTPStatusError as e:
                if e.response.status_code == 401:
                    logger.warning(f"Unauthorized access: {e}")
                elif e.response.status_code == 403:
                    logger.warning(f"Forbidden access: {e}")
                else:
                    logger.error(f"Failed to fetch transcript content: {e}")
                transcript_content.append("No Transcript available of this meeting")
        return transcript_content

    async def get_meeting_transcript(self, meeting_id, event, token):
        confidential_token = await self.get_confidential_token()
        # event start and end date
        startDate = f"{event.get('start')['dateTime'].split('.')[0]}Z"
        endDate = f"{event.get('end')['dateTime'].split('.')[0]}Z"
        if not token or not confidential_token:
            missing = "access token" if not token else "confidential access token"
            logger.error(f"Failed to fetch Teams meeting transcript for {meeting_id}: No {missing}")
            return [], False
        url_non_filtered = (
            f"{self.resource_url}me/onlineMeetings/{meeting_id}/transcripts?$filter=createdDateTime le {startDate}"
        )
        url_date_filtered = f"{self.resource_url}me/onlineMeetings/{meeting_id}/transcripts?$filter=createdDateTime ge {startDate} and createdDateTime le {endDate}"  # noqa
        try:
            response = await self.client.get(url_date_filtered, headers={"Authorization": f"Bearer {token}"})
            response.raise_for_status()
            transcript_data = response.json()
            transcripts = transcript_data.get("value", [])
            if len(transcripts) > 0:
                transcripts_content = await self.fetch_transcript_content(
                    transcript_data=transcripts, confidential_token=confidential_token
                )
                return transcripts_content, False
            elif event.get("type") in ["seriesMaster", "occurrence", "exception"]:
                # If no transcripts found, call the second API without filtering
                response = await self.client.get(url_non_filtered, headers={"Authorization": f"Bearer {token}"})
                response.raise_for_status()
                transcript_data = response.json()
                transcripts = transcript_data.get("value", [])
                transcripts_content = await self.fetch_transcript_content(
                    transcript_data=transcripts, confidential_token=confidential_token
                )
                return transcripts_content, True
            else:
                logger.warning(f"No transcripts found for meeting {meeting_id}.")
                return [], False
        except HTTPStatusError as e:
            if e.response.status_code == 401:
                logger.warning(f"Unauthorized access: {e}")
            elif e.response.status_code == 403:
                logger.warning(f"Forbidden access: {e}")
            else:
                logger.error(f"Failed to fetch transcript content: {e}")
            return [], False

    async def get_online_meeting_id(self, join_url, token):
        url = f"{self.resource_url}me/onlineMeetings?$filter=JoinWebUrl eq '{join_url}'"
        try:
            response = await self.client.get(url, headers={"Authorization": f"Bearer {token}"})
            if response.status_code == 403:
                logger.warning("Forbidden: Ensure the token has the correct permissions.")
                return None
            data = response.json()
            if not data or "value" not in data or not data["value"]:
                logger.warning("No meeting found for the given Join URL.")
                return None
            return data["value"][0].get("id")
        except HTTPStatusError as e:
            logger.warning(f"Failed to fetch online meeting: {e}")
            return None

    async def summarize_event(self, event: dict, user_mail, token: str) -> str:
        user_details = await self.get_email(token)
        event_id = event.get("id")
        cache_key = f"event_summary:{event_id}-{user_mail}"
        # Check if the summary is already cached
        cached_summary = await self.redis.get(cache_key)
        if cached_summary:
            return cached_summary.decode("utf-8")
        previous_transcripts = None
        future_event = None
        if (
            event.get("isOnlineMeeting")
            and event.get("onlineMeeting")["joinUrl"]
            and user_details.get("mail") == user_mail
        ):
            online_meeting_id = await self.get_online_meeting_id(
                join_url=event.get("onlineMeeting")["joinUrl"], token=token
            )

            if online_meeting_id:
                previous_transcripts, future_event = await self.get_meeting_transcript(
                    meeting_id=online_meeting_id, event=event, token=token
                )
        # Fetch related emails
        subject = event.get("subject", "")
        organizer = event.get("organizer", {}).get("emailAddress", {}).get("address", "")
        attendees = [attendee.get("emailAddress", {}).get("address", "") for attendee in event.get("attendees", [])]
        related_emails, _ = await self.get_related_emails(
            subject, organizer, attendees, user_mail=user_mail, token=token
        )
        event_details = {
            "subject": event.get("subject"),
            "body": html2text(event.get("body", {}).get("content") or ""),
        }
        related_email_details = [
            {
                "subject": email.get("subject"),
                "body": email.get("uniqueBody", {}).get("content"),
                "receivedDateTime": email.get("receivedDateTime"),
                "sender": email.get("sender", {}).get("emailAddress", {}).get("address"),
                "sentDateTime": email.get("sentDateTime"),
            }
            for email in related_emails
        ]
        prompt = (
            f"Please analyze the following Outlook event, related emails, and meeting transcripts to provide a structured summary. "  # noqa
            f"Organize the response into three sections:\n\n"
            f"Event Summary:\n"
            f"- Provide a concise summary of the event in no more than two sentences in a bullet point.\n"
            f"- Focus only on the essential information and exclude unnecessary details such as the organizer, attendees, start time, end time, meeting passcode, link, or ID.\n\n"  # noqa
            f"Related Email Insights:\n"
            f"- Summarize key points discussed in emails related to the meeting subject.\n"
            f"- Use bullet points to highlight important insights, discussions, decisions, or concerns from the emails.\n"  # noqa
            f"- Ignore any emails related to cancellations, rescheduling, or unrelated topics.\n"
            f"- If no relevant emails are found, respond with: 'No emails related to this event.'\n\n"
            f"Meeting Transcript Analysis:\n"
            f"{'No transcript available for this event.' if not previous_transcripts else ''}"
            f"{'Add a note by mentioning the date (in human readable form) of meetings from the provided transcript details. Clearly state that the information refers to a previous session and not the current event.' if future_event else ''}\n"  # noqa
            f"{'' if not previous_transcripts else '- Extract key topics and subtopics discussed using a structured format (e.g., numbered or bullet points with sub-bullets).'}\n"  # noqa
            f"{'' if not previous_transcripts else '- Provide a detailed summary of key insights and major takeaways from the discussion. Ensure that every important statement is attributed to the correct speaker by name (e.g., John mentioned..., John and David discussed...). Do not omit speaker attributions.'}\n"  # noqa
            f"{'' if not previous_transcripts else '- List any action items, decisions made, or follow-ups required, along with who is responsible for each action (if mentioned).'}\n"  # noqa
            f"**Important Notes:**\n"
            f"- The response should be strictly based on the provided event details, emails, and transcripts.\n"
            f"- Do not include any additional information beyond what is explicitly requested.\n\n"
            f"**Event Details:**\n{event_details}\n\n"
            f"**Related Emails (new emails first):**\n{related_email_details}\n\n"
            f"**Meeting Transcripts (if available, in VTT format):**\n{previous_transcripts}\n"
        )
        message = [
            {
                "role": "user",
                "content": prompt,
            }
        ]
        summary = await self.create_completion(messages=message)
        await self.redis.setex(cache_key, timedelta(days=1), summary)
        return summary


outlook_hook = OutlookHook()
