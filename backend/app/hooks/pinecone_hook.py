import asyncio  # noqa

from config import settings
from loguru import logger
from pinecone import PineconeAsyncio  # Added for async management
from pinecone import Pinecone, ServerlessSpec


class AsyncPineconeServerlessHook:
    _instances = {}

    @classmethod
    async def get_instance(cls, index_name: str, host: str) -> "AsyncPineconeServerlessHook":
        """Get or create a singleton instance for an index."""
        if index_name not in cls._instances:
            instance = AsyncPineconeServerlessHook(index_name, host)
            await instance._initialize()
            cls._instances[index_name] = instance
        return cls._instances[index_name]

    def __init__(self, index_name: str, host: str) -> None:
        """Initialize the hook with index name."""
        self.index_name = index_name
        self.host = host
        self.pc = None
        self.async_pc = None

    async def _initialize(self) -> None:
        """Initialize Pinecone connection and ensure index exists."""
        self.pc = Pinecone(api_key=settings.PINECONE_SERVERLESS_API_KEY)

        # Initialize async client for index management
        self.async_pc = PineconeAsyncio(api_key=settings.PINECONE_SERVERLESS_API_KEY)

        # Check if index exists
        has_index = await self.async_pc.has_index(self.index_name)

        if not has_index:
            await self.async_pc.create_index(
                name=self.index_name,
                dimension=1536,
                metric="cosine",
                spec=ServerlessSpec(cloud="aws", region="us-east-1"),
                deletion_protection="disabled",
            )
            logger.info(f"Created index {self.index_name}")
        else:
            logger.info(f"Index {self.index_name} already exists")

    async def get_async_index(self):
        """Get the async index object."""
        if not self.pc:
            await self._initialize()
        return self.pc.IndexAsyncio(host=self.host)

    async def upsert_document(self, vectors: list[dict], namespace: str | None = None) -> dict:
        """Upsert vectors to the index."""
        async with await self.get_async_index() as index:
            upsert_response = await index.upsert(vectors=vectors, namespace=namespace, batch_size=32)
            return upsert_response

    async def query_document(
        self,
        vector: list[float],
        sparse_vector: dict = None,
        top_k: int = 2,
        filter: dict | None = None,
        namespace: str | None = None,
        alpha: float = 0.5,
    ) -> dict:
        """Query the index with dense and sparse vectors."""
        async with await self.get_async_index() as index:
            return await index.query(
                vector=vector,
                sparse_vector=sparse_vector,
                top_k=top_k,
                namespace=namespace,
                include_values=True,
                include_metadata=True,
                filter=filter,
                alpha=alpha,
            )

    async def delete_document(self, id: str, namespace: str | None = None) -> bool:
        """Delete document by ID prefix."""
        namespace = str(namespace) if namespace else None
        logger.info(f"Deleting document with ID: {id}")

        async with await self.get_async_index() as index:
            # Collect IDs from the async generator
            ids_to_delete = []
            async for batch in index.list(prefix=f"{id}#", namespace=namespace):
                if batch:
                    ids_to_delete.extend(batch)

            # Delete the collected IDs if any exist
            if ids_to_delete:
                batch_size = 1000
                for i in range(0, len(ids_to_delete), batch_size):
                    batch_ids = ids_to_delete[i : i + batch_size]  # noqa
                    logger.info(f"Deleting batch {i // batch_size + 1} with {len(batch_ids)} IDs")
                    await index.delete(ids=batch_ids, namespace=namespace)
            return True

    async def delete_namespace(self, namespace: str | None = None) -> bool:
        """Delete all vectors in a namespace."""
        namespace = str(namespace) if namespace else None
        async with await self.get_async_index() as index:
            await index.delete(delete_all=True, namespace=namespace)
            return True

    async def update_metadata(self, id: str, categories: list[str], namespace: str | None = None) -> bool:
        """Update metadata for documents with ID prefix."""
        namespace = str(namespace) if namespace else None
        logger.info(f"Updating document with ID: {id}")

        async with await self.get_async_index() as index:
            # Collect IDs from the async generator
            ids_to_update = []
            async for batch in index.list(prefix=f"{id}#", namespace=namespace):
                if batch:
                    ids_to_update.extend(batch)

            # Update metadata for each ID
            for doc_id in ids_to_update:
                await index.update(id=doc_id, namespace=namespace, set_metadata={"categories": categories})

            return True


# Create a factory function to get the global instance
async def get_generic_hook(assistant_id: str) -> AsyncPineconeServerlessHook:
    from utils.division import get_assistant_division_credentials

    division_credentials = await get_assistant_division_credentials(assistant_id)
    if division_credentials:
        index_name = division_credentials["pinecone_index"]
        host = division_credentials["pinecone_host"]
    else:
        index_name = settings.PINECONE_INDEX
        host = settings.PINECONE_HOST

    return await AsyncPineconeServerlessHook.get_instance(index_name=index_name, host=host)
