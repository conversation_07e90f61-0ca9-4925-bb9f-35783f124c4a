import asyncio
import json
import traceback
from collections.abc import AsyncGenerator
from datetime import datetime
from io import Bytes<PERSON>
from tempfile import NamedTemporaryFile
from typing import Literal

from config import settings
from fastapi import File, UploadFile
from hooks.connectors.outlook_hook import outlook_hook
from hooks.s3 import get_s3_hook
from loguru import logger
from openai import AsyncOpenAI
from pydantic import BaseModel
from schema.chat import Transcription
from schema.user import UserBase
from tools.fields.encompass import fetch_all_fields, fetch_encompass_assistants
from tools.fields.outlook import fetch_outlook_assistants
from tools.fields.sidekick import fetch_sidekick_assistants
from tools.functions.call_campaign import call_campaign_function
from tools.functions.encompass import encompass_function
from tools.functions.generic import generic_function
from tools.functions.marketing import marketing_function
from tools.functions.outlook import outlook_function
from tools.functions.total_expert import total_expert_function
from tools.schema import ToolResponse
from tools.utils.encompass.loan import loan_utils as encompass_utils

MANN_ASSISTANT_ID = None
CAR_GURU_ASSISTANT_ID = None

if settings.MANN_ASSISTANT_FILE_LOC:
    from tools.functions.mann_assistant import mann_loan_processor

    MANN_ASSISTANT_ID = settings.MANN_ASSISTANT_FILE_LOC.split("/")[0]

if settings.CAR_GURU_ASSISTANT_FILE_LOC:
    from tools.functions.car_guru_assistant import car_guru_processor

    CAR_GURU_ASSISTANT_ID = settings.CAR_GURU_ASSISTANT_FILE_LOC.split("/")[0]

GROUNDING_INSTRUCTIONS = (
    " In responding to user queries, you should always use the context obtained "
    "from the get_relevant_documents function. Before answering, ensure that the "
    "information is sufficiently accurate. If the query is related to retreiving images/logos "
    "use get_relevant_images function."
)

ADDITIONAL_INSTRUCTIONS = (
    " When context is provided below, use **only** that context to answer the user's query."
    " Please provide your response in a readable and informative manner, summarizing key points."
    " Avoid stating 'I could not find relevant information from the Knowledge Base.' if context is provided."
    " If the context provided below states that it is from the web, then strictly respond with: 'I could not find relevant information from the Knowledge Base. However the following information from my general knowledge and internet might help you:'"  # noqa: E501
    " If the context provided has the information from knowledgebase and the web(internet), then strictly maintain different sections for the information from knowledgebase and the web(internet). Donot mix them together otherwise it will be considered as incorrect and you will be punished."  # noqa: E501
    " If no context is provided, then respond with: 'I could not find relevant information from the Knowledge Base. However the following information might help you:'"  # noqa: E501
    " Then, in a new paragraph, provide a short, precise and succinct  answer based on your own knowledge."
)

ADDITIONAL_HARD_FILTER_INSTRUCTIONS = (
    " When context is provided below, use **only** that context to answer the user's query."
    " Please provide your response in a readable and informative manner, summarizing key points."
    " Avoid stating 'I could not find relevant information from the Knowledge Base.' if context is provided."
    " If no context is provided, then simply respond with: 'Sorry, I could not find relevant information from the Knowledge Base.'"  # noqa: E501
    " Avoid speculating or providing answers based on your own knowledge if the context is missing or insufficient."
)

ADDITIONAL_HARD_FILTER_INSTRUCTIONS_OUTLOOK = (
    "When email details are provided below, use **only** those email details to answer the user's query. "
    "Do not make your own assumptions while summarizing the emails. "
    "If no email details are provided, then strictly respond with: **'Sorry, I could not find the details about the mails regarding your query. Could you specify more details?'** "  # noqa: E501
    "Avoid speculating or providing answers based on external knowledge if the email details are missing or insufficient. "  # noqa: E501
    "When sending, replying to, or forwarding an email, always use a tool call function. Do not state that the email has been sent, replied to, or forwarded. "  # noqa: E501
    "Instead, respond with: **'A mail has been drafted for [sending/replying/forwarding].'** "
    "In the case of forwarding an email, strictly include the details of the original mail like it's body content while you are forwarding along with any additional comments. "  # noqa: E501
    "Additionally, strictly display the drafted email details—such as subject, body, recipients, and comments—in **plain text only**. "  # noqa: E501
    "Response in a code block or Markdown is **strictly prohibited**, otherwise, you will be punished. "
    "**Response Format (Strictly Plain Text, No Markdown or Code Blocks):**\n"
    "Respond **only** in the plain text, using raw text without Markdown, special characters, or formatting. "
    "While showing availble mails allways show in well structured format (i.e with topics like subject, date, summary and other available fields in bullet points and descriptions)"  # noqa: E501
    "Any deviation from this format is incorrect and will not be accepted."
)


get_relavant_image_tool_func = {
    "type": "function",
    "function": {
        "name": "get_relevant_images",
        "description": "Retrieve relevant images that match the user query from database",
        "strict": True,
        "parameters": {
            "type": "object",
            "properties": {
                "img_file_name": {
                    "type": "string",
                    "description": "The name of the image file mentioned by the user to be retrieved",
                }
            },
            "required": ["img_file_name"],
            "additionalProperties": False,
        },
    },
}

web_search_tool_func = {
    "type": "function",
    "function": {
        "name": "web_search",
        "description": "Only call this function if and only if the user explicity asks for web search results.",
        "strict": False,
        "parameters": {
            "type": "object",
            "properties": {
                "query": {
                    "type": "string",
                    "description": "The query to search the web for",
                }
            },
            "required": ["query"],
        },
    },
}


class EventModel(BaseModel):
    event: Literal["text", "component", "payload", "hint", "complete", "status"]
    data: str | dict | list[dict] | None = None


class OpenAIHook:
    def __init__(self):
        self.client = AsyncOpenAI(api_key=settings.OPENAI_API_KEY, organization=settings.OPENAI_ORG_ID)

    async def create_thread(self, **kwargs) -> str:
        thread = await self.client.beta.threads.create(**kwargs)
        return thread.id

    async def delete_thread(self, thread_id: str) -> bool:
        response = await self.client.beta.threads.delete(thread_id)
        return response.deleted

    async def create_message(self, thread_id: str, content: list[dict], attachments: list[dict] = None) -> str:
        # Validate that content is a list
        if not isinstance(content, list):
            raise ValueError("Content must be a list of message parts.")

        # Validate that attachments, if provided, is a list
        if attachments is not None and not isinstance(attachments, list):
            raise ValueError("Attachments must be a list if provided.")

        if not await self.cancel_runs(thread_id):
            raise Exception("Error while Cancelling runs!")

        message = await self.client.beta.threads.messages.create(
            thread_id=thread_id, content=content, attachments=attachments, role="user"
        )
        return message.id

    async def list_messages(self, thread_id: str) -> list:
        messages = await self.client.beta.threads.messages.list(thread_id=thread_id)
        return messages.data

    async def retrieve_assistant(self, assistant_id: str) -> BaseModel:
        assistant = await self.client.beta.assistants.retrieve(assistant_id)
        return assistant

    async def create_run(
        self,
        thread_id: str,
        assistant_id: str,
        additional_instructions: str,
        enable_hard_filter: bool,
    ) -> BaseModel:
        run_args = {
            "thread_id": thread_id,
            "assistant_id": assistant_id,
            "additional_instructions": additional_instructions,
            "tool_choice": "required" if enable_hard_filter else "auto",
        }
        assistant_details = await self.retrieve_assistant(assistant_id)
        reasoning_model = assistant_details.model.startswith("o")
        if not reasoning_model:
            run_args["temperature"] = 0

        run = await self.client.beta.threads.runs.create(**run_args)
        return run

    async def cancel_run(self, thread_id: str, run_id: str) -> BaseModel:
        run = await self.client.beta.threads.runs.cancel(thread_id=thread_id, run_id=run_id)
        return run

    async def is_run_cancelled(self, thread_id: str, run_id: str) -> bool:
        run_status = await self.client.beta.threads.runs.retrieve(thread_id=thread_id, run_id=run_id)
        return run_status.status in ["cancelled", "failed"]

    async def cancel_runs(self, thread_id: str) -> bool:
        try:
            existing_runs = await self.list_runs(thread_id)

            if existing_runs:
                for run in existing_runs:
                    if run.status in ["queued", "in_progress", "requires_action"]:
                        logger.info(f"Cancelling Run with id: {run.id}!")
                        await self.cancel_run(thread_id, run.id)
            else:
                logger.info("There are no incomplete runs!")

            return True
        except Exception as e:
            logger.error(f"Error while cancelling runs: {e}")
            return False

    async def list_runs(self, thread_id: str) -> list:
        runs = await self.client.beta.threads.runs.list(thread_id)
        return runs.data

    async def retrieve_run(self, thread_id: str, run_id: str) -> BaseModel | None:
        try:
            run = await self.client.beta.threads.runs.retrieve(thread_id=thread_id, run_id=run_id)
            return run
        except Exception as e:
            logger.error(e)
            return None

    async def list_assistants(self) -> list:
        assistants = await self.client.beta.assistants.list()
        return assistants.data

    async def create_assistant(
        self, name, instructions, categories, is_category_filtered, grounding_instructions, **kwargs
    ):
        instructions += grounding_instructions
        tools = [
            {
                "type": "function",
                "function": {
                    "name": "get_relevant_documents",
                    "description": "Retrieve relevant documents to answer the question from vector database.",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "query": {
                                "type": "string",
                                "description": (  # noqa: E501
                                    "A standalone query with resolved references, suitable for vector search."
                                ),
                            },
                        },
                        "required": ["query"],
                    },
                },
            },
            get_relavant_image_tool_func,
            web_search_tool_func,
        ]
        if is_category_filtered:
            tools = [
                {
                    "type": "function",
                    "function": {
                        "name": "get_relevant_documents",
                        "description": "Retrieve relevant documents to answer the question from vector database.",
                        "parameters": {
                            "type": "object",
                            "properties": {
                                "query": {
                                    "type": "array",
                                    "items": {"type": "string"},
                                    "description": (
                                        "A list of search queries including the original question, synonyms, and any clarified references. "  # noqa
                                        "Multiple queries help capture different ways to express the same information need. "  # noqa
                                        "Limit to 2-3 queries."
                                    ),
                                },
                                "categories": {
                                    "type": "array",
                                    "items": {
                                        "type": "string",
                                        "enum": categories,
                                    },
                                    "description": (  # noqa: E501
                                        "Analyze the query and select applicable categories from the provided options that best match the content. "  # noqa: E501
                                        "Choose only from the categories available in the enum array - do not create new ones. "  # noqa: E501
                                        "Select all categories that apply to the query's subject matter. "  # noqa: E501
                                        "Multiple categories can be selected when the query spans several relevant areas. "  # noqa: E501
                                        "If no categories clearly apply to the query content, return an empty array. "  # noqa: E501
                                        "The goal is to focus the search on the most relevant document collections without "  # noqa: E501
                                        "making assumptions about categories not explicitly mentioned in the query. "  # noqa: E501
                                    ),
                                },
                            },
                            "required": ["query"],
                        },
                    },
                },
                get_relavant_image_tool_func,
                web_search_tool_func,
            ]

        assistant = await self.client.beta.assistants.create(
            name=name,
            instructions=instructions,
            model="gpt-4o",
            tools=tools,
            **kwargs,
        )
        return assistant.id

    async def update_assistant(
        self,
        assistant_id: str,
        instructions: str,
        categories: list[str] | None,
        is_category_filtered: bool,
        grounding_instructions: str,
    ):
        instructions += grounding_instructions
        if is_category_filtered:
            tools = [
                {
                    "type": "function",
                    "function": {
                        "name": "get_relevant_documents",
                        "description": "Retrieve relevant documents to answer the question from vector database.",
                        "parameters": {
                            "type": "object",
                            "properties": {
                                "query": {
                                    "type": "array",
                                    "items": {"type": "string"},
                                    "description": (
                                        "A list of search queries including the original question, synonyms, and any clarified references. "  # noqa
                                        "Multiple queries help capture different ways to express the same information need. "  # noqa
                                        "Limit to 2-3 queries. "
                                    ),
                                },
                                "categories": {
                                    "type": "array",
                                    "items": {
                                        "type": "string",
                                        "enum": categories,
                                    },
                                    "description": (  # noqa: E501
                                        "Analyze the query and select applicable categories from the provided options that best match the content. "  # noqa: E501
                                        "Choose only from the categories available in the enum array - do not create new ones. "  # noqa: E501
                                        "Select all categories that apply to the query's subject matter. "  # noqa: E501
                                        "Multiple categories can be selected when the query spans several relevant areas. "  # noqa: E501
                                        "If no categories clearly apply to the query content, return an empty array. "  # noqa: E501
                                        "The goal is to focus the search on the most relevant document collections without "  # noqa: E501
                                        "making assumptions about categories not explicitly mentioned in the query. "  # noqa: E501
                                    ),
                                },
                            },
                            "required": ["query"],
                        },
                    },
                },
                get_relavant_image_tool_func,
                web_search_tool_func,
            ]
            assistant = await self.client.beta.assistants.update(assistant_id, instructions=instructions, tools=tools)
        else:
            tools = [
                {
                    "type": "function",
                    "function": {
                        "name": "get_relevant_documents",
                        "description": "Retrieve relevant documents to answer the question from vector database.",
                        "parameters": {
                            "type": "object",
                            "properties": {
                                "query": {
                                    "type": "string",
                                    "description": (  # noqa: E501
                                        "A standalone query with resolved references, suitable for vector search."
                                    ),
                                },
                            },
                            "required": ["query"],
                        },
                    },
                },
                get_relavant_image_tool_func,
                web_search_tool_func,
            ]
            assistant = await self.client.beta.assistants.update(assistant_id, instructions=instructions, tools=tools)
        return assistant.id

    async def generate_error_message(self, error_message: str) -> str:
        """
        Generates a user-friendly error message based on the provided error message.

        Args:
            error_message (str): The error message to be processed.

        Returns:
            str: A user-friendly error message generated by the assistant.
        """
        try:
            messages = [
                {
                    "role": "system",
                    "content": (
                        "Generate clear and concise error messages for system users based on the provided error"
                        " context. The messages should be easy to understand and free of technical jargon or error"
                        " codes. Your goal is to explain the problem in simple terms without giving excessive"
                        " detail.\n Provide a one to two-sentence error message in plain language"
                        " understandable by non-technical users."
                    ),
                },
                {"role": "user", "content": f"An error occurred: {error_message}"},
            ]
            response = await self.create_completion(messages=messages, temperature=0.7)
            return response
        except Exception as e:
            logger.error(f"Failed to generate error message: {str(e)}")
            return "An error occured while processing your request"

    async def create_custom_assistant(self, name, instructions, grounding_instructions, tools, **kwargs):
        instructions += grounding_instructions
        assistant = await self.client.beta.assistants.create(
            name=name,
            instructions=instructions,
            model=settings.OPENAI_DEFAULT_MODEL,
            tools=tools,
            **kwargs,
        )
        return assistant.id

    async def update_custom_assistant(
        self,
        assistant_id: str,
        instructions: str,
        grounding_instructions: str,
    ):
        if instructions:
            instructions += grounding_instructions
        else:
            instructions = grounding_instructions
        assistant = await self.client.beta.assistants.update(assistant_id, instructions=instructions)
        return assistant.id

    async def delete_assistant(self, assistant_id: str) -> bool:
        try:
            response = await self.client.beta.assistants.delete(assistant_id)
            return response.deleted
        except Exception as e:
            logger.error(f"Unexpected error: {e}")
            return None

    @staticmethod
    def get_function(function_name: str) -> callable:
        function_map = {
            **encompass_function.list_functions(),
            **total_expert_function.list_functions(),
            **marketing_function.list_functions(),
            **generic_function.list_functions(),
            **outlook_function.list_functions(),
            **call_campaign_function.list_functions(),
        }
        if settings.MANN_ASSISTANT_FILE_LOC:
            function_map.update(**mann_loan_processor.list_functions())
        if settings.CAR_GURU_ASSISTANT_FILE_LOC:
            function_map.update(**car_guru_processor.list_functions())
        return function_map.get(function_name)

    @staticmethod
    def get_function_status(function_name: str) -> str | None:
        function_modules = {"encompass": (encompass_function, "Processing the Request.")}

        for module, default_message in function_modules.values():
            if function_name in module.list_functions():
                try:
                    if hasattr(module, "function_status_mapping"):
                        return module.function_status_mapping().get(function_name, default_message)
                    return default_message

                except Exception:
                    return default_message
        return None

    @staticmethod
    async def _get_additional_instruction_for_assistant(
        assistant_id: str, enable_hard_filter: bool, persona, user, **kwargs
    ) -> str:
        additional_instructions = ""
        encompass_assistants = await fetch_encompass_assistants()
        outlook_assistant = await fetch_outlook_assistants()
        encompass_loan_id = ""

        if assistant_id in [assistant.id for assistant in encompass_assistants]:
            data_fields = await fetch_all_fields(assistant_id)
            FIELD_MAPPING = data_fields.get("FIELD_MAPPING")
            # EXTRA_FIELDS = data_fields.get("EXTRA_FIELDS")
            # available_fields = list(FIELD_MAPPING.values())
            # if EXTRA_FIELDS:
            #     available_fields += list(EXTRA_FIELDS.values())

            additional_instructions += (
                "## AVAILABLE FIELDS FOR FILTERING AND OUTUPUTS:\nFollowing Loan Fields are available. Use them"
                f" accordingly.\n{str(list(FIELD_MAPPING.values()))}"
            )
            # TODO: Find optimal solution, since we already call this function in loan fetch process too.
            encompass_loan_id = await encompass_utils.map_email_loid(user=user, assistant_id=assistant_id)
            encompass_loan_id = f"Encompass Login ID: {encompass_loan_id}"

        elif outlook_assistant and any(assistant_id == asst.id for asst in outlook_assistant):
            token = kwargs.get("outlook_token")
            additional_instructions = (
                "My mail details are:"
                f"{await outlook_hook.get_email(token=token)}, Use these details while drafting mails"
                + ADDITIONAL_HARD_FILTER_INSTRUCTIONS_OUTLOOK  # noqa: E501
            )
        elif enable_hard_filter:
            additional_instructions = ADDITIONAL_HARD_FILTER_INSTRUCTIONS
        else:
            sidekick_assistant = await fetch_sidekick_assistants()
            if sidekick_assistant and any(assistant_id == asst.id for asst in sidekick_assistant):
                additional_instructions = ""
            else:
                additional_instructions = ADDITIONAL_INSTRUCTIONS

        if persona is not None:
            if persona.profession:
                additional_instructions = (
                    f"{additional_instructions}\nAlign response with user profession: `{persona.profession}`"
                )
            if persona.description:
                additional_instructions = f"{additional_instructions}\nRespond in Persona: `{persona.description}`"

        # Add today's date on top of the instructions # Monday, 23 June 2024
        current_context = (
            f"Today's Date: {datetime.now().strftime('%A, %d %B %Y')}\nCurrent User Informaiton:\nFull Name:"
            f" `{user.name}`\n{encompass_loan_id}"
        )

        additional_instructions = f"{additional_instructions}\n\n{current_context}"
        return additional_instructions

    async def wait_on_run(self, run, thread_id: str):
        while run.status == "queued" or run.status == "in_progress":
            run = await self.retrieve_run(thread_id=thread_id, run_id=run.id)
            await asyncio.sleep(0.5)
        return run

    async def execute_action(self, run, thread_id: str, user, user_query, assistant_id: str, **kwargs):
        tool_calls = run.required_action.submit_tool_outputs.tool_calls
        tool_outputs = []
        components = []
        payloads = []
        for tool_call in tool_calls:
            function_name = tool_call.function.name
            function_args = json.loads(tool_call.function.arguments)

            logger.info(f"Executing function {function_name} with args {function_args}")
            func = self.get_function(function_name)
            if not func:
                tool_outputs.append({"tool_call_id": tool_call.id, "output": "Function doesn't exist with that name"})
            else:
                user = UserBase.model_validate(user)

                # If outlook assistant, pass outlook token
                if assistant_id in [await fetch_outlook_assistants()]:
                    outlook_token = kwargs.get("outlook_token")
                    response = await func(
                        **function_args,
                        user=user,
                        user_query=user_query,
                        assistant_id=assistant_id,
                        outlook_token=outlook_token,
                    )
                else:
                    response = await func(**function_args, user=user, user_query=user_query, assistant_id=assistant_id)

                if isinstance(response, ToolResponse):
                    tool_outputs.append({"tool_call_id": tool_call.id, "output": json.dumps(response.message)})
                    components.append(response.component.model_dump() if response.component else None)
                    payloads.append(response.payload if response.payload else None)
                else:
                    tool_outputs.append({"tool_call_id": tool_call.id, "output": json.dumps(response)})
                logger.debug(f"Function {function_name} returned {response}")

        run = await self.client.beta.threads.runs.submit_tool_outputs(
            thread_id=thread_id,
            run_id=run.id,
            tool_outputs=tool_outputs,
        )
        run = await self.wait_on_run(run=run, thread_id=thread_id)
        return run, components, payloads

    async def ask_assistant(
        self,
        thread_id: str,
        assistant_id: str,
        message_id: str,
        user,
        persona,
        user_query,
        enable_hard_filter: bool,
        **kwargs,
    ) -> dict:
        # Execute our run
        run = await self.create_run(
            thread_id=thread_id,
            assistant_id=assistant_id,
            additional_instructions=await self._get_additional_instruction_for_assistant(
                assistant_id, enable_hard_filter, persona, user, **kwargs
            ),
            enable_hard_filter=enable_hard_filter,
        )
        # Wait for completion
        run = await self.wait_on_run(run=run, thread_id=thread_id)

        components = []
        payloads = []
        while run.status == "requires_action":
            run, components, payloads = await self.execute_action(
                run=run, thread_id=thread_id, user=user, user_query=user_query, assistant_id=assistant_id, **kwargs
            )

        # Retrieve all the messages added after our last user message
        messages = await self.client.beta.threads.messages.list(thread_id=thread_id, order="asc", after=message_id)
        contents = messages.data[-1].content
        result = []
        s3_hook = await get_s3_hook(assistant_id)
        for content in contents:
            if hasattr(content, "type") and content.type == "image_file":
                file_id = content.image_file.file_id
                file_name, file_content = await self.retrieve_file_thread(file_id=file_id)
                file_name = f"{datetime.now().strftime('%Y%m%d%H%M%S')}_{file_name.split('/')[-1]}"
                object_name = f"{assistant_id}/{user.id}/{file_name}.png"
                image_url = s3_hook.upload_file(object_name, file_content, content_type="image/png")
                result.append({"type": content.type, "file_id": file_id, "image_url": image_url})
            elif hasattr(content, "type") and content.type == "text":
                result.append(
                    {
                        "type": content.type,
                        "text": content.text.value,
                    }
                )
            elif hasattr(content, "type") and content.type == "image_url":
                result.append(
                    {
                        "type": content.type,
                        "image_url": content.image_url.url,
                    }
                )
        attachments = messages.data[-1].attachments
        files = []
        for attachment in attachments:
            if hasattr(attachment, "file_id"):
                file_id = attachment.file_id
                file_name, file_content = await self.retrieve_file_thread(file_id=file_id)
                file_name = f"{datetime.now().strftime('%Y%m%d%H%M%S')}_{file_name.split('/')[-1]}"
                object_name = f"{assistant_id}/{user.id}/{file_name}"
                file_url = s3_hook.upload_file(object_name, file_content)
                files.append({"file_id": file_id, "file_url": file_url})

        return {
            "content": result,
            "message_id": messages.data[0].id,
            "components": components,
            "payloads": payloads,
            "attachments": files,
        }

    @staticmethod
    async def process_message_deltas(content_deltas) -> AsyncGenerator[EventModel, None]:
        for content_delta in content_deltas or []:
            if content_delta.type == "text" and content_delta.text and content_delta.text.value:
                logger.debug(f"Processing message delta: {content_delta.text.value}")
                yield EventModel(event="text", data=content_delta.text.value)

    async def handle_event(self, event, thread_id, user, user_query, assistant_id, **kwargs):
        s3_hook = await get_s3_hook(assistant_id)
        if event.event == "thread.message.created":
            yield EventModel(event="hint", data={"message_id": event.data.id})

        elif event.event == "thread.message.delta":
            async for event in self.process_message_deltas(event.data.delta.content):
                yield event

        elif event.event == "thread.run.requires_action":
            async for event in self.execute_action_streaming(
                event.data, thread_id, user, user_query, assistant_id, **kwargs
            ):
                yield event

        elif event.event == "thread.message.completed":
            contents = event.data.content
            result = []
            for content in contents:
                if hasattr(content, "type") and content.type == "image_file":
                    file_id = content.image_file.file_id
                    file_name, file_content = await self.retrieve_file_thread(file_id=file_id)
                    file_name = f"{datetime.now().strftime('%Y%m%d%H%M%S')}_{file_name.split('/')[-1]}"
                    object_name = f"{assistant_id}/{user.id}/{file_name}.png"
                    image_url = s3_hook.upload_file(object_name, file_content, content_type="image/png")
                    result.append({"type": content.type, "file_id": file_id, "image_url": image_url})
                elif hasattr(content, "type") and content.type == "text":
                    result.append(
                        {
                            "type": content.type,
                            "text": content.text.value,
                        }
                    )
                elif hasattr(content, "type") and content.type == "image_url":
                    result.append(
                        {
                            "type": content.type,
                            "image_url": content.image_url.url,
                        }
                    )
            attachments = event.data.attachments
            files = []
            for attachment in attachments:
                if hasattr(attachment, "file_id"):
                    file_id = attachment.file_id
                    file_name, file_content = await self.retrieve_file_thread(file_id=file_id)
                    file_name = f"{datetime.now().strftime('%Y%m%d%H%M%S')}_{file_name.split('/')[-1]}"
                    object_name = f"{assistant_id}/{user.id}/{file_name}"
                    file_url = s3_hook.upload_file(object_name, file_content)
                    files.append({"file_id": file_id, "file_url": file_url})
            yield EventModel(
                event="complete",
                data={
                    "message_id": event.data.id,
                    "content": result,
                    "attachments": files,
                },
            )

    async def execute_action_streaming(
        self, run_data, thread_id, user, user_message, assistant_id, **kwargs
    ) -> AsyncGenerator[EventModel, None]:
        tool_outputs = []
        components = []
        payloads = []

        for tool_call in run_data.required_action.submit_tool_outputs.tool_calls:
            function_name = tool_call.function.name
            function_args = json.loads(tool_call.function.arguments)

            logger.info(f"Executing function streaming {function_name} with args {function_args}")

            if function_name == "web_search":
                yield EventModel(event="status", data="Searching in web")

            if function_name == "get_relevant_documents":
                categories_list = function_args.get("categories", [])

                yield EventModel(
                    event="status",
                    data=(
                        f"Searching in Categories: {categories_list[:5]}"
                        if categories_list
                        else "Searching in Knowledge Base"
                    ),
                )

            func = self.get_function(function_name)
            # show status on assistants
            task_status = self.get_function_status(function_name)
            if task_status:
                yield EventModel(event="status", data=task_status)
            # If outlook assistant, pass outlook token to function
            try:
                if any(assistant_id == asst.id for asst in await fetch_outlook_assistants()):
                    outlook_token = kwargs.get("outlook_token")
                    response = await func(
                        **function_args,
                        user=user,
                        user_query=user_message,
                        assistant_id=assistant_id,
                        outlook_token=outlook_token,
                    )
                else:
                    response = await func(
                        **function_args,
                        user=user,
                        user_query=user_message,
                        assistant_id=assistant_id,
                    )
            except Exception as e:
                traceback.print_exc()
                logger.error(f"Error occurred during execution of the function {func} {e}")
                response = (
                    "Some unexpected error occurred while processing your request. Could you please try again by"
                    " creating a new chat? That might help resolve the issue. If the issue persists, you may want to"
                    " contact the support team."
                )

            if isinstance(response, ToolResponse):
                tool_outputs.append({"tool_call_id": tool_call.id, "output": json.dumps(response.message)})
                components.append(response.component.model_dump() if response.component else None)
                payloads.append(response.payload if response.payload else None)
            else:
                tool_outputs.append({"tool_call_id": tool_call.id, "output": json.dumps(response)})

        if components:
            yield EventModel(event="component", data=[c for c in components if c])

        if payloads:
            yield EventModel(event="payload", data=[payload for payload in payloads if payload])

        logger.info("Submitting function response to assistant.")
        tool_stream = await self.client.beta.threads.runs.submit_tool_outputs(
            thread_id=thread_id, run_id=run_data.id, tool_outputs=tool_outputs, stream=True
        )

        async for tool_event in tool_stream:
            async for event in self.handle_event(tool_event, thread_id, user, user_message, assistant_id):
                yield event

    async def ask_assistant_streaming(
        self,
        thread_id: str,
        assistant_id: str,
        message_id: str,
        user,
        persona,
        user_query,
        enable_hard_filter: bool,
        **kwargs,
    ) -> AsyncGenerator[EventModel, None]:
        # encompass_assistants = await fetch_encompass_assistants()
        # total_expert_assistants = await fetch_totalexpert_assistants()

        try:
            run_args = {
                "thread_id": thread_id,
                "assistant_id": assistant_id,
                "additional_instructions": await self._get_additional_instruction_for_assistant(
                    assistant_id, enable_hard_filter, persona, user, **kwargs
                ),
                "stream": True,
                "tool_choice": "required" if (enable_hard_filter) else "auto",
            }

            if kwargs.get("web_search"):
                run_args["tool_choice"] = "required"
                run_args["tools"] = [web_search_tool_func]

            # check if model is OPENAI Omni model: o1, o3 etc
            assistant_details = await self.retrieve_assistant(assistant_id)
            reasoning_model = assistant_details.model.startswith("o")
            if not reasoning_model:
                run_args["temperature"] = 0

            encompass_assistants = await fetch_encompass_assistants()
            if assistant_id in [assistant.id for assistant in encompass_assistants]:
                run_args["tool_choice"] = "required"

            stream = await self.client.beta.threads.runs.create(**run_args)
            async for run_event in stream:
                async for event in self.handle_event(
                    run_event,
                    thread_id,
                    user,
                    user_query=user_query,
                    assistant_id=assistant_id,
                    **kwargs,
                ):
                    yield event
        except Exception as e:
            logger.error(f"Error in ask_assistant_streaming: {e}")
            error_message = (
                f"Some unexpected error occured while processing your request.Error msg: {e} Could you please try"
                " again by creating a New Chat or restarting the page? That might help resolve the issue. If the issue persists, you may"  # noqa
                " want to contact the support team."
            )
            error_message_response = await self.generate_error_message(error_message)
            yield EventModel(
                event="complete",
                data={"content": [{"type": "text", "text": error_message_response}], "attachments": []},
            )

    async def generate_title(self, content: str) -> str:
        response = await self.client.chat.completions.create(
            model="gpt-4o",
            messages=[
                {
                    "role": "user",
                    "content": (
                        "Generate a short 3-5 word conversation title(without quotes) "
                        f"for the following message: {content}"
                    ),
                },
            ],
            max_tokens=8,
            temperature=0.1,
        )
        response_text = response.choices[0].message.content.strip()
        return response_text

    async def get_embedding(self, text: str) -> list[float]:
        response = await self.client.embeddings.create(
            model="text-embedding-ada-002",
            input=text,
        )
        return response.data[0].embedding

    async def get_embedding_v3(self, text: str) -> list[float]:
        response = await self.client.embeddings.create(
            model="text-embedding-3-small",
            input=text,
        )
        return response.data[0].embedding

    # Files
    async def list_files(self, assistant_id: str) -> list:
        files = await self.client.beta.assistants.files.list(assistant_id=assistant_id)
        return files

    async def upload_file(self, assistant_id: str, file: UploadFile = File(...), purpose: str = "") -> dict:
        file = await self.client.files.create(file=file.read(), purpose=purpose)
        assistant_file = await self.client.beta.assistants.files.create(assistant_id=assistant_id, file_id=file.id)
        return assistant_file

    async def upload_file_thread(self, file: UploadFile = File(...), purpose: str = "") -> dict:
        contents = await file.read()
        file = await self.client.files.create(file=(file.filename, contents, file.content_type), purpose=purpose)
        return file

    async def upload_file_thread_from_s3(self, s3_hook, link: str, purpose: str = "") -> dict:
        key = s3_hook.get_key_from_url(link)
        contents = s3_hook.download_and_read_object_tempfile(key)
        filename = key.split("/")[-1]
        file = await self.client.files.create(file=(filename, contents), purpose=purpose)
        return file

    async def retrieve_file_thread(self, file_id: str) -> dict:
        file = await self.client.files.retrieve(file_id=file_id)
        content = await self.retrieve_file_contents(file_id=file_id)
        content_bytes = await content.aread()
        return file.filename, BytesIO(content_bytes)

    async def delete_file(self, assistant_id: str, file_id: str) -> dict:
        response = await self.client.beta.assistants.files.delete(assistant_id=assistant_id, file_id=file_id)
        return response

    async def retrieve_file(self, assistant_id: str, file_id: str) -> dict:
        file = await self.client.beta.assistants.files.retrieve(assistant_id=assistant_id, file_id=file_id)
        return file

    async def retrieve_file_contents(self, file_id: str) -> bytes:
        content = await self.client.files.content(file_id=file_id)
        return content

    async def transcribe(self, file: UploadFile = File(...)) -> Transcription:
        with NamedTemporaryFile(suffix=".mp3") as temp_file:
            contents = await file.read()
            temp_file.write(contents)
            temp_file.flush()

            with open(temp_file.name, "rb") as audio_file:
                transcript = await self.client.audio.transcriptions.create(
                    model="whisper-1",
                    file=audio_file,
                )
        return transcript

    async def create_completion(self, messages: list[dict], model="gpt-4o", temperature: float = 0.0, **kwargs) -> str:
        args = {"messages": messages, "model": model, **kwargs}
        if model.startswith("o"):
            # set default reasoning errort to high
            if "reasoning_effort" not in args:
                kwargs["reasoning_effort"] = "high"
        else:
            # use temperature
            args["temperature"] = temperature

        response = await self.client.chat.completions.create(**args)
        return response.choices[0].message.content

    async def embed_texts(self, texts: str | list[str]) -> list[list[float]]:
        if isinstance(texts, str):
            texts = [texts]
        response = await self.client.embeddings.create(input=texts, model=settings.OPENAI_EMBED_LARGE)
        return [d.embedding for d in response.data]

    async def generate_image(self, prompt: str, model: str = "dall-e-3", size: str = "1024x1024") -> str:
        completion = await self.client.images.generate(
            model=model, prompt=prompt, size=size, response_format="b64_json", quality="hd", n=1
        )
        return completion.data[0].b64_json

    async def stream_audio(
        self, text: str, response_format: str = "aac", voice="alloy"
    ) -> AsyncGenerator[bytes, None]:
        async with self.client.audio.speech.with_streaming_response.create(
            model="tts-1", input=text, voice=voice, response_format=response_format
        ) as response:
            async for chunk in response.iter_bytes(1024):
                yield chunk

    async def create_response(self, prompt: str, model="gpt-4.1", **kwargs):
        args = {"model": model, "input": prompt, **kwargs}
        response = await self.client.responses.create(**args)
        return response


oai_hook = OpenAIHook()
