import asyncio
import base64
import json
from pathlib import Path

import websockets
from config import settings
from loguru import logger
from starlette.websockets import WebSocket, WebSocketDisconnect
from tools.functions.encompass.loan import loan_function
from tools.functions.twilio import TwilioUtils
from twilio.rest import Client


class TwilioCallHook:
    def __init__(
        self,
        websocket: WebSocket,
        tools: list,
        system_prompt_path: str,
        start_conversation_message: str,
        session_init_args: dict = None,
    ):
        self.websocket = websocket
        self.tools = tools
        self.system_prompt = Path(system_prompt_path).read_text()
        self.start_conversation_message = start_conversation_message
        self.session_init_args = session_init_args or {}
        self.state = {}
        self.stream_sid = None
        self.end_call_flag = False
        self.openai_ws = None
        self.twilio_utils = TwilioUtils()
        self.call_sid = None

    async def handle_twilio_call(self):
        """
        Main entry point to handle the Twilio <-> OpenAI audio bridge.
        """
        async with websockets.connect(
            settings.OPENAI_REALTIME_URL,
            additional_headers={
                "Authorization": f"Bearer {settings.OPENAI_API_KEY}",
                "OpenAI-Beta": "realtime=v1",
            },
        ) as openai_ws:
            self.openai_ws = openai_ws
            await asyncio.gather(self._receive_from_twilio(), self._send_to_twilio())

    async def _receive_from_twilio(self):
        try:
            while True:
                try:
                    message = await asyncio.wait_for(self.websocket.receive_text(), timeout=30)
                except TimeoutError:
                    logger.info("No event received from Twilio for 30 seconds, ending call.")
                    self.end_call_flag = True
                    await self.end_call()
                    break

                data = json.loads(message)
                if data["event"] == "media":
                    audio_append = {"type": "input_audio_buffer.append", "audio": data["media"]["payload"]}
                    await self.openai_ws.send(json.dumps(audio_append))
                elif data["event"] == "start":
                    self.call_sid = data["start"]["callSid"]
                    self.stream_sid = data["start"]["streamSid"]
                    params = data["start"].get("customParameters", {})
                    prompt_args = {**self.session_init_args, **params}
                    self.session_init_args = prompt_args
                    logger.info(f"Prompt args: {prompt_args}")
                    loan_details = None
                    if prompt_args.get("phone_number"):
                        try:
                            loan_details = await loan_function.get_loan_details_by_phone_number(
                                phone_number=prompt_args.get("phone_number"), division_id=prompt_args.get("division")
                            )
                        except Exception as e:
                            logger.error(f"Error fetching loan details for {prompt_args.get('phone_number')}: {e}")
                            loan_details = {"message": "Could not fetch loan details"}
                    if loan_details is not None:
                        prompt_args["loan_details"] = loan_details
                    formatted_prompt = self.system_prompt.format(**prompt_args)
                    await self.initialize_session(formatted_prompt)
                elif data["event"] == "stop":
                    self.end_call_flag = True
                    break

        except WebSocketDisconnect:
            logger.info("Client disconnected.")
            if self.openai_ws and self.openai_ws.open:
                await self.openai_ws.close()

    async def _send_to_twilio(self):
        try:
            if not (
                hasattr(self.websocket, "application_state")
                and getattr(self.websocket.application_state, "name", None) == "CONNECTED"
            ):
                if self.openai_ws and getattr(self.openai_ws, "open", False):
                    await self.openai_ws.close()
                logger.error("WebSocket is not active. Closing openai_ws and exiting _send_to_twilio.")
                return

            async for openai_message in self.openai_ws:
                response = json.loads(openai_message)
                if response.get("type") == "input_audio_buffer.speech_started":
                    await self.websocket.send_json({"streamSid": self.stream_sid, "event": "clear"})
                if response.get("type") == "response.output_item.done":
                    await self._handle_function_call(response)
                if response.get("type") == "response.create":
                    logger.info(f"Response created: {response}")
                if response.get("type"):
                    logger.info(f"Received event: {response.get('type')}")
                if response.get("type") == "response.done":
                    if self.end_call_flag:
                        await self.end_call()
                if response.get("type") == "error":
                    logger.error(f"Error: {response}")
                if response.get("type") == "response.audio.delta" and response.get("delta"):
                    try:
                        audio_payload = base64.b64encode(base64.b64decode(response["delta"])).decode("utf-8")
                        audio_delta = {
                            "event": "media",
                            "streamSid": self.stream_sid,
                            "media": {"payload": audio_payload},
                        }
                        await self.websocket.send_json(audio_delta)
                    except Exception as e:
                        logger.error(f"Error processing audio data: {e}")
        except Exception as e:
            logger.error(f"Error in send_to_twilio: {e}")

    async def _handle_function_call(self, response):
        item = response.get("item", {})
        if item.get("type") == "function_call":
            function_name = item.get("name")
            function_args = item.get("arguments", {})
            logger.info(f"Function call: {function_name} with args: {function_args}")
            twilio_functions = self.twilio_utils.list_functions()
            func = twilio_functions.get(function_name)
            if func:
                try:
                    if not isinstance(function_args, dict):
                        if isinstance(function_args, str):
                            try:
                                function_args = json.loads(function_args)
                            except Exception:
                                function_args = {}
                        else:
                            function_args = {}
                    if asyncio.iscoroutinefunction(func):
                        result = await func(**{**self.session_init_args, **function_args})
                    else:
                        result = func({**self.session_init_args, **function_args})
                    await self.openai_ws.send(
                        json.dumps(
                            {
                                "type": "conversation.item.create",
                                "item": {
                                    "type": "function_call_output",
                                    "call_id": item.get("call_id"),
                                    "output": f"The available refinance options are: {json.dumps(result)}",
                                },
                            }
                        )
                    )
                    await self.openai_ws.send(json.dumps({"type": "response.create"}))
                except Exception as e:
                    logger.error(f"Error executing {function_name}: {e}")
            if function_name == "end_call":
                self.end_call_flag = True

            if function_name == "transfer_call":
                await self.forward_call_to_LO()

    async def end_call(self):
        client = Client(settings.TWILIO_ACCOUNT_SID, settings.TWILIO_AUTH_TOKEN)
        try:
            client.calls(self.call_sid).update(status="completed")
        except Exception as e:
            logger.error(f"Failed to hang up call: {e}")
        await self.websocket.close()
        await self.openai_ws.close()

    async def forward_call_to_LO(self, **kwargs):
        outbound_twiml = (
            '<?xml version="1.0" encoding="UTF-8"?>'
            "<Response>"
            f"  <Say>Connecting you to the Loan Officer.</Say>"
            f'  <Dial>{kwargs.get("forward_to_number")}</Dial>'
            f"  <Say>Goodbye</Say>"
            "</Response>"
        )
        client = Client(settings.TWILIO_ACCOUNT_SID, settings.TWILIO_AUTH_TOKEN)
        call = client.calls(self.call_sid).update(twiml=outbound_twiml)
        return f"Call forwarded to Loan Officer with call sid: {call.sid}"

    async def send_initial_conversation_item(self):
        """Send initial conversation so AI talks first."""
        initial_conversation_item = {
            "type": "conversation.item.create",
            "item": {
                "type": "message",
                "role": "user",
                "content": [
                    {
                        "type": "input_text",
                        "text": self.start_conversation_message,
                    }
                ],
            },
        }
        await self.openai_ws.send(json.dumps(initial_conversation_item))
        await self.openai_ws.send(json.dumps({"type": "response.create"}))

    async def initialize_session(self, system_prompt):
        """Control initial session with OpenAI."""
        VOICE = "sage"
        session_update = {
            "type": "session.update",
            "session": {
                "turn_detection": {
                    "type": "server_vad",
                    "threshold": 0.9,
                    "prefix_padding_ms": 50,
                    "silence_duration_ms": 500,
                    "create_response": True,
                },
                "tools": self.tools,
                "tool_choice": "auto",
                "input_audio_format": "g711_ulaw",
                "input_audio_transcription": {
                    "model": "gpt-4o-transcribe",
                    "language": "en",
                },
                "output_audio_format": "g711_ulaw",
                "voice": VOICE,
                "instructions": system_prompt,
                "modalities": ["text", "audio"],
                "temperature": 0.8,
            },
        }
        response = await self.openai_ws.send(json.dumps(session_update))
        logger.info(f"Session update response: {response}")
        await self.send_initial_conversation_item()
