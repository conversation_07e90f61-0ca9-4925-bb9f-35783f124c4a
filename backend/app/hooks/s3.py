import os
from collections.abc import Generator
from tempfile import NamedTemporaryFile
from urllib.parse import unquote, urlparse

import boto3
import pandas as pd
from config import settings
from loguru import logger


class S3Hook:
    _instances = {}

    @classmethod
    async def get_instance(cls, bucket_name: str, region_name: str) -> "S3Hook":
        key = f"{bucket_name}:{region_name}"
        if key not in cls._instances:
            cls._instances[key] = S3Hook(bucket_name, region_name)
        return cls._instances[key]

    def __init__(self, bucket_name: str, region_name: str) -> None:
        self.bucket = bucket_name
        self.s3_client = boto3.client(
            "s3",
            region_name=region_name,
            config=boto3.session.Config(signature_version="s3v4"),
        )

    def _generate_presigned_url(self, operation_name: str, key: str, content_type=None) -> str | None:
        try:
            params = {"Bucket": self.bucket, "Key": key}
            if content_type:
                params["ContentType"] = content_type
            response = self.s3_client.generate_presigned_url(operation_name, Params=params, ExpiresIn=3600)
        except Exception as e:
            logger.error(f"Failed to generate presigned url: {e}")
            return None
        return response

    def get_presigned_url_for_upload(self, object_name: str, content_type) -> str | None:
        return self._generate_presigned_url("put_object", object_name, content_type)

    def get_presigned_url_for_delete(self, object_name: str) -> str | None:
        return self._generate_presigned_url("delete_object", object_name)

    def get_presigned_url_for_download(self, object_name: str) -> str | None:
        return self._generate_presigned_url("get_object", object_name)

    def get_key_from_url(self, url: str) -> str:
        parsed_url = urlparse(url)

        if parsed_url.scheme == "s3":
            # Eg: s3://nfmgpt/documents/asst_CuAxXlMSk7V4uERgSKbClRSy/401k.pdf
            s3_bucket_name = parsed_url.netloc
            key = unquote(parsed_url.path.lstrip("/"))
        elif parsed_url.scheme in ["http", "https"]:
            # Eg: https://nfmgpt.s3.amazonaws.com/documents/asst_CuAxXlMSk7V4uERgSKbClRSy/401k.pdf
            s3_bucket_name = parsed_url.netloc.split(".s3.amazonaws.com")[0]
            key = unquote(parsed_url.path.lstrip("/"))
        else:
            raise ValueError("Invalid URL scheme. URL must start with 's3://' or 'http://' or 'https://'")

        if s3_bucket_name != self.bucket:
            raise ValueError(
                f"Bucket name in URL ({s3_bucket_name}) does not match expected bucket name ({self.bucket})"
            )

        return key

    def get_presigned_url_from_link(self, url: str) -> str | None:
        key = self.get_key_from_url(url)
        return self._generate_presigned_url("get_object", key)

    def get_object(self, key: str) -> bytes:
        obj = self.s3_client.get_object(Bucket=self.bucket, Key=key)
        return obj["Body"].read()

    def get_objects(self, keys: list[str]) -> Generator[bytes, None, None]:
        for key in keys:
            try:
                obj = self.s3_client.get_object(Bucket=self.bucket, Key=key)
                body = obj["Body"].read()
                yield body
            except Exception as e:
                logger.error(f"Failed to get object: {e}")

    def get_object_from_url(self, url: str) -> bytes:
        key = self.get_key_from_url(url)
        return self.get_object(key)

    def delete_object(self, object_name: str) -> dict | None:
        try:
            return self.s3_client.delete_object(Bucket=self.bucket, Key=object_name)
        except Exception as e:
            logger.error(f"Failed to delete object: {e}")
            return None

    def put_object(self, object_name, file, content_type=None):
        try:
            if content_type:
                return self.s3_client.put_object(
                    Bucket=self.bucket, Key=object_name, Body=file, ContentType=content_type
                )
            else:
                return self.s3_client.put_object(Bucket=self.bucket, Key=object_name, Body=file)
        except Exception as e:
            logger.error(f"Failed to put object: {e}")
            return None

    def download_and_read_object_tempfile(self, key: str) -> bytes:
        try:
            with NamedTemporaryFile(delete=False) as tmp_file:
                self.s3_client.download_fileobj(self.bucket, key, tmp_file)
                tmp_file.seek(0)
                file_content = tmp_file.read()
                temp_file_name = tmp_file.name
            logger.info(f"Downloaded object {key} to temporary file {temp_file_name}")

            os.remove(temp_file_name)
            logger.info(f"Deleted temporary file {temp_file_name}")
            return file_content
        except Exception as e:
            logger.error(f"Failed to download and read object {key}: {e}")
            raise

    def download_and_read_csv_with_pandas(self, key: str) -> pd.DataFrame:
        try:
            with NamedTemporaryFile(delete=False) as tmp_file:
                self.s3_client.download_fileobj(self.bucket, key, tmp_file)
                temp_file_name = tmp_file.name

            logger.info(f"Downloaded object {key} to temporary file {temp_file_name}")

            csv_data = pd.read_csv(temp_file_name)

            os.remove(temp_file_name)
            logger.info(f"Deleted temporary file {temp_file_name}")
            return csv_data
        except Exception as e:
            logger.error(f"Failed to download and read CSV object {key}: {e}")
            raise

    def upload_file(self, object_name: str, content: str, content_type=None):
        try:
            response = self.put_object(object_name=object_name, file=content, content_type=content_type)
            if response["ResponseMetadata"]["HTTPStatusCode"] == 200:
                logger.info(f"File uploaded to S3 bucket: {object_name}")
                file_url = self.get_presigned_url_for_download(object_name)
                return file_url
            else:
                raise Exception("Failed to store file in S3 bucket")
        except Exception as e:
            logger.error(f"An error occurred: {e}")
            return None


# Factory function to get dynamic S3Hook instance
async def get_s3_hook(assistant_id: str) -> S3Hook:
    from utils.division import get_assistant_division_credentials

    division_credentials = await get_assistant_division_credentials(assistant_id)
    if division_credentials:
        bucket_name = division_credentials["aws_storage_bucket_name"]
    else:
        bucket_name = settings.AWS_STORAGE_BUCKET_NAME

    return await S3Hook.get_instance(bucket_name, settings.AWS_S3_REGION_NAME)


default_s3_hook = S3Hook(settings.AWS_STORAGE_BUCKET_NAME, settings.AWS_S3_REGION_NAME)
