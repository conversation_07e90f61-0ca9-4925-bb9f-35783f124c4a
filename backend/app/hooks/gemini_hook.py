import datetime
import secrets
import string
import time

from config import settings
from db.models import Conversation, Message
from google import genai
from google.genai import types
from loguru import logger
from sqlalchemy import delete, select

MAX_RETRIES = 3


class GeminiHook:
    def __init__(self, api_key, model_name, cache_ttl_minutes=5):
        self.client = genai.Client(api_key=api_key)
        self.cache_ttl = datetime.timedelta(minutes=cache_ttl_minutes)
        self.cache_map = {}
        self.model_name = model_name

    def _generate_id(self, prefix: str, length: int = 22) -> str:
        random_string = "".join(secrets.choice(string.ascii_letters + string.digits) for _ in range(length))
        return f"{prefix}_gemini_{random_string}"

    async def create_thread(self, **kwargs) -> str:
        thread_id = self._generate_id("thread")
        return thread_id

    async def delete_thread(self, session, thread_id: str) -> bool:
        await session.execute(delete(Conversation).where(Conversation.id == thread_id))
        await session.commit()
        return True

    async def create_message(self) -> str:
        message_id = self._generate_id("msg")

        return message_id

    async def list_messages(self, session, thread_id: str) -> list:
        query = select(Message).where(Message.conversation_id == thread_id)
        result = await session.execute(query)
        messages = result.scalars().all()
        return messages

    async def create_custom_assistant(self):
        assistant_id = self._generate_id("asst")

        return assistant_id

    async def upload_and_cache_file(self, file_path, cache_name, system_instruction):
        """Upload a file and create a cache for it."""
        print(f"Uploading file: {file_path}")
        file = self.client.files.upload(file=file_path)

        while file.state.name == "PROCESSING":
            print("Waiting for file to be processed...")
            time.sleep(2)
            file = self.client.files.get(file.name)

        print(f"File processing complete: {file.uri}")

        if cache_name in self.cache_map:
            print(f"Using existing cache: {cache_name}")
            return self.cache_map[cache_name]

        print(f"Creating cache with TTL of {self.cache_ttl}.")
        cache = self.client.caches.create(
            model=self.model_name,
            config=types.CreateCachedContentConfig(
                display_name=cache_name,
                system_instruction=system_instruction,
                contents=[file],
                ttl=self.cache_ttl,
            ),
        )

        self.cache_map[cache_name] = cache
        return cache

    async def get_conversation_history(self, session, thread_id):
        query = select(Message).where(Message.conversation_id == thread_id)
        result = await session.execute(query)
        conversation = result.scalars().all()

        if not conversation:
            return []

        conversation_data = [f"role: {entry.role}: {entry.content[0]['text']}" for entry in conversation]

        return conversation_data[-8:]

    async def query_model(self, user_input, file_urls):
        """Query the model using cached content."""
        try:
            context = "\n".join(user_input)

            file_contents = []

            for file_url in file_urls:
                file_contents.append(self.client.files.upload(file=file_url))

            for chunk in self.client.models.generate_content_stream(
                model=self.model_name,
                contents=[context] + file_contents,
            ):
                yield chunk.text

        except Exception as e:
            yield f"Error: {str(e)}"

    async def query_image_model(self, user_input, model_name, aspect_ratio="16:9"):
        retries = 0
        while retries < MAX_RETRIES:
            try:
                response = self.client.models.generate_images(
                    model=model_name,
                    prompt=user_input,
                    config=types.GenerateImagesConfig(
                        number_of_images=1,
                        aspectRatio=aspect_ratio,
                    ),
                )
                image = response.generated_images[0].image.image_bytes
                return image
            except Exception as e:
                logger.warning(f"Gemini error: Failed to generate image. Attempt {retries + 1}: {str(e)}")
                retries += 1
        logger.warning("Gemini error: Failed to generate image after 3 retries")
        return None


gemini_hook = GeminiHook(settings.GEMINI_API_KEY, settings.GEMINI_MODEL_NAME, 5)
