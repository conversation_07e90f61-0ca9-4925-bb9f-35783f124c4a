import asyncio

import httpx
from config import settings
from loguru import logger


class Sp<PERSON>Hook:
    def __init__(self, endpoint: str = settings.EMBEDDING_API_ENDPOINT, max_retries=3, retry_delay=2):
        self.endpoint = endpoint
        self.max_retries = max_retries
        self.retry_delay = retry_delay

    async def _encode_document_remote(self, document: str | list[str]) -> list[dict]:
        """Calls the embedding API to encode a document.

        Args:
            document (str | list[str]): text chunks to encode

        Returns:
            list[dict]: encoded document [{"values": [], "indices": []}]
        """
        async with httpx.AsyncClient(timeout=None) as client:
            for attempt in range(self.max_retries):
                try:
                    response = await client.post(
                        f"{self.endpoint}/embed",
                        json={
                            "inputs": [document] if isinstance(document, str) else document,
                            "client": settings.CLIENT_NAME,
                            "environment": settings.APP_ENV,
                        },
                        headers={
                            "Content-Type": "application/json",
                            "Authorization": f"Bearer {settings.EMBEDDING_API_KEY}",
                        },
                    )
                    response.raise_for_status()
                    json_response = response.json()
                    return json_response if isinstance(document, list) else json_response[0]
                except httpx.HTTPStatusError as e:
                    if attempt < self.max_retries - 1:
                        await asyncio.sleep(self.retry_delay)
                    else:
                        logger.error(f"HTTPStatusError: {e}")
                        raise e
                except (RuntimeError, Exception) as e:
                    if "CUDA out of memory" in str(e) and attempt < self.max_retries - 1:
                        await asyncio.sleep(self.retry_delay)
                    else:
                        logger.error(f"RuntimeError: {e}")
                        raise e

    async def encode_documents(self, documents: list[str]) -> list[dict]:
        """Encodes a list of documents. If the environment is restricted, it will use the local model."""

        return await self._encode_document_remote(documents)

    async def encode_queries(self, text: str | list[str]) -> list[dict]:
        """Encodes a list of queries. If the environment is restricted, it will use the local model."""

        return await self._encode_document_remote(text)


splade_embedder = SpladeHook(endpoint=settings.EMBEDDING_API_ENDPOINT)
