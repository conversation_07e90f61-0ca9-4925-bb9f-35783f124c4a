from textwrap import dedent

from db.models import User

# flake8: noqa

# For Rate Term Refi and Cashout
REQUIRE_ALL_OPTION_FIELDS = False  # Require only 'term' and 'rate' if False; all fields if True


# Rate Term Refi
APPLY_SAVINGS_LOGIC = (
    True  # If monthly savings > $0 and total savings < $0, keep total savings blank in email, if True
)

RATE_TERM_EMAIL_SUBJECT = dedent(
    """
    {first_name} {last_name}/ Your Mortgage Rate & Payment Reduction is Available
    """
)

RATE_TERM_EMAIL_BODY_HEAD = dedent(
    """
    **Hello {first_name},**

    As we discussed, I ran the numbers on a refinance for you and wanted to share a couple of options that could help lower your monthly payment or shorten your loan term.
    """
).strip()

RATE_TERM_EMAIL_BODY_TAIL = dedent(
    """
    If you would like to move forward, please respond with the option(s) you are interested in and I will be in touch with next steps! Please let me know if you have any questions.
    """
).strip()


# Cashout Details - Table
LOAN_AMOUNT_TITLE = "Current Loan Amount"
DISPLAY_ESTIMATED_PAYOFF = True  # If True, display estimated payoff in cashout details table
ROUNDOFF_UPB_TO_NEARNEST_10 = True


# Cashout - Debt Consolidation
CASHOUT_DEBTCON_EMAIL_SUBJECT = dedent(
    """
    {first_name} {last_name}/ Your Refinance Cashout Options are Available
    """
)

CASHOUT_DEBTCON_EMAIL_BODY_HEAD = dedent(
    """
    **Hello {first_name},**

    Based on our conversation about consolidating debt, here are two cash-out refinance options that could help simplify your finances and reduce your overall monthly payments.
    """
).strip()

CASHOUT_DEBTCON_EMAIL_BODY_TAIL = dedent(
    """
    If you would like to move forward, please respond with the option(s) you are interested in and I will be in touch with next steps! Please let me know if you have any questions.
    """
).strip()

INCLUDE_DEBT_IN_TOTAL = True  # Add debt to original loan amount in cashout debt consolidation if True

CASHOUT_DEBTCON_DETAILS = [
    "options",
    "terms",
    "loan_amounts",
    "rates",
    "debt_consolidation",
    "additional_cashout",
    "total_loan_amounts",
    "payments",
    "monthly_other_debt",
    "total_payments",
    "monthly_savings",
    "projected_savings",
]


# Cashout - Desired
CASHOUT_DESIRED_EMAIL_SUBJECT = dedent(
    """
    {first_name} {last_name}/ Your Refinance Cashout Options are Available
    """
)

CASHOUT_DESIRED_EMAIL_BODY_HEAD = dedent(
    """
    **Hello {first_name},**

    Here are the cash-out refinance options we talked about, based on the amount you’re looking to pull from your home’s equity.
    """
).strip()

CASHOUT_DESIRED_EMAIL_BODY_TAIL = dedent(
    """
    If you would like to move forward, please respond with the option(s) you are interested in and I will be in touch with next steps! Please let me know if you have any questions.
    """
).strip()

CASHOUT_DESIRED_DETAILS = [
    "options",
    "terms",
    "loan_amounts",
    "rates",
    "desired_cashout_amount",
    "total_loan_amounts",
    "payments",
]

CASHOUT_DESIRED_MAPPING = {
    "terms": "Term",
    "loan_amounts": "Loan Amount",
    "rates": "Rate(APR)",
    "desired_cashout_amount": "Desired Cashout Amount",
    "total_loan_amounts": "Total Loan Amount",
    "payments": "Payment (Principal & Interest)",
}


# Purchase Offer
PURCHASE_EMAIL_SUBJECT = dedent(
    """
    {first_name} {last_name}: Your Purchase Options
    """
)

PURCHASE_OFFER_EMAIL_BODY_HEAD = dedent(
    """
    **Hello {first_name},**

    Here’s your customized purchase offer:
    """
).strip()

PURCHASE_OFFER_EMAIL_BODY_MID = dedent(
    """
    **Long Term Analysis based on {time_frame} years:**
    """
).strip()

PURCHASE_OFFER_EMAIL_BODY_TAIL = dedent(
    """
    **Please reply to go over these options, otherwise, we'll let you know when the next opportunity is available for you.**
    As always, contact me with any questions or to discuss any other home loan needs.
    """
).strip()


# Signature
SHOW_SIGNATURE = "False"

# Force Display or No Display Send Email Button
FORCE_SEND_EMAIL_BUTTON = False  # If True, always show the send email button; if False, hide it
DISABLE_SEND_EMAIL_BUTTON = False  # If True, disable the send email button


# Email Disclaimer - Rate Term Refi & Cashout Refi Offer
DISCLAIMER_TEMPLATE_REFI = dedent(
    """
    <small>
        <img src="https://d23yowha4dxiyx.cloudfront.net/Logo+-+Equal+Housing.png" alt="equal_housing_logo" style="max-width: 50px; margin-top: 10px;">
        This information for "Your Current Mortgage" is based on the mortgage you closed with us and does not account for any refinances, or additional home financing, obtained by you since your loan closed with us.
        Any additional loans obtained since your loan closed with us would impact the numbers and scenarios represented.
        Rates quoted in the Options presented are effective as of {date} and are estimates based on a 780 or above credit score, debt to income under 45%, and loan-to-value ratio of 70% or less.
        Rates quoted are subject to change at any time without prior notice before a rate lock is executed.
        The APR represents the total cost of borrowing, expressed as a monthly percentage, utilizing average estimated closing costs.
        The payments listed include principal and interest only and do not include taxes, homeowner's insurance, or private mortgage insurance that may apply to your specific situation and selected loan criteria, and as a result, your actual payment will be higher than the values shown.
        A total estimate of new loan costs will be itemized on your initial Loan Estimate if applying for a loan.
        Payments and rates may vary based on borrower's credit score, actual closing costs, and other variables.
        The total savings provided are estimated based on the total number of months remaining on Your Current Mortgage based on the date your loan was originated with us, multiplied by the monthly principal and interest payment for Your Current Mortgage, minus each Option's Payment multiplied by the number of months for each Option's stated Term.
        <em>Savings, if any, vary based on the consumer's credit profile, interest rate availability, and other factors.</em>
        Loan is subject to credit and underwriting approval.
        Not all applicants will be approved.
        The information provided is not a commitment to lend.
    </small><br><br>
    """.strip()
)

# Email Disclaimer - Purchase Offer
DISCLAIMER_TEMPLATE = dedent(
    """
    <small>
        <img src="https://d23yowha4dxiyx.cloudfront.net/Logo+-+Equal+Housing.png" alt="equal_housing_logo" style="max-width: 50px; margin-top: 10px;">
        Equal Housing Lender. MLO licensing information: {nmls_number}. Rate Companies LLC d/b/a Rate Companies. For Rate Companies LLC's full agency and state licensing information, please visit <a href="www.ratecompanies.com/licensing" target="_blank">www.ratecompanies.com/licensing</a>. Rate Companies LLC's NMLS #2611 (<a href="https://www.nmlsconsumeraccess.org" target="_blank">www.nmlsconsumeraccess.org</a>). Rate Companies LLC is not affiliated with, or an agent or division of, a governmental agency or a depository institution. Copyright ©2025 Rate Companies LLC dba Rate Companies. Licensed by the Department of Financial Protection and Innovation under the California Residential Mortgage Lending Act.<br>
        This information is based on the mortgage you closed with us and does not account for any refinances you may have done since. Refinancing an existing loan may result in the total finance charges being higher over the life of the loan.<br>
        <em>For example, you may have two months of no payments if your loan closes between the 1st and 10th of August. In this scenario your next payment would not be until October. You would skip your August and September payments and they would be carried over into the new loan. Of course, you should always continue making your current mortgage payments until your loan closes. Not all borrowers will be able to delay or carry-over two mortgage payments – this is contingent upon the closing date.</em><br>
        Rates effective {date}. Rates quoted are estimates and based on a 780 or above credit score, debt to income under 45%, loan-to-value at 70% or lower. This payment does not include taxes, homeowner's insurance, private mortgage insurance, or other homeowner's insurance (HOA) dues, and is an estimate; your actual payment will be higher. The payment examples may increase your loan amount to cover potential costs such as title, pre-paids (taxes and insurance) and other costs. Any such costs will be itemized on your initial loan estimate. An appraisal fee may be required prior to closing. An annual and monthly mortgage insurance premium may be required and will vary depending on the loan characterization.<br>
        Payments and rates may vary based on borrower's credit score, actual closing costs and other variables. Depending on your situation, flood, property hazard, and mortgage insurance may be needed, which could increase the monthly payment and Annual Percentage Rate (APR).<br>
        The APR describes the interest rate for a whole year (annualized), rather than just a monthly fee/rate. The APR allows a borrower to compare costs of credit because it factors in term, interest rate and fees associated with the loan.<br>
        The total savings were estimated based on the total number of months left on your current term times monthly principal and interest payment, minus your current principal and interest payment times numbers of months on the projected term.<br>
        *Values are estimated using standard amortization schedule.
    </small><br><br>
    """.strip()
)


# Open House Flyer
OPEN_HOUSE_FLYER_ENABLED = False


# Pre-Qualification Letter Config
COMPANY_LOGO = dedent("https://d3rp5alps573tj.cloudfront.net/Rate%20Companies_2%20Color_Black.png")
EQUAL_HOUSING_LENDER_LOGO = dedent("https://d23yowha4dxiyx.cloudfront.net/Logo+-+Equal+Housing.png")
PRE_QUALIFICATION_LETTER_ENABLED = False


# Division Details
def get_division_theme(user: User) -> dict:
    """Get division branding theme including logo and colors based on email domain."""
    # check if user has division
    division = user.division
    if division:
        division_theme = {
            "logo": division.logo,
            "primary_color": division.primary_color,
            "secondary_color": division.secondary_color,
        }
    else:
        division_theme = {"logo": COMPANY_LOGO, "primary_color": "#cc333b", "secondary_color": "#242729"}

    return division_theme


# Find Lending Opportunity
FIND_LENDING_OPPORTUNITY_ENABLED = False
