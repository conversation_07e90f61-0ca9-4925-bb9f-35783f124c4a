from textwrap import dedent

from db.models import User

# flake8: noqa

# For Rate Term Refi and Cashout
REQUIRE_ALL_OPTION_FIELDS = False  # Require only 'term' and 'rate' if False; all fields if True


# Rate Term Refi
APPLY_SAVINGS_LOGIC = (
    False  # If monthly savings > $0 and total savings < $0, keep total savings blank in email, if True
)

RATE_TERM_EMAIL_SUBJECT = dedent(
    """
    {first_name} {last_name}/ Your Mortgage Rate & Payment Reduction is Available
    """
)

RATE_TERM_EMAIL_BODY_HEAD = dedent(
    """
    **Hello {first_name},**

    Your loan was closed on {closed_date}. At the time, your interest rate was {interest_rate}%. Right now, with potentially no out-of-pocket cost or money due at closing, you could refinance with one of the following options. Let us know which option you want, and our team will be in touch with next steps.
    """
).strip()

RATE_TERM_EMAIL_BODY_TAIL = dedent(
    """
    These are estimates. The payments above do not include taxes or homeowner’s insurance. Since taxes and insurance can change from time to time, the payments only include principal and interest to show your true savings.

    **Due to market volatility, please respond as soon as possible with which option you want to move forward with. Or let me know if you are not interested at this time or have already refinanced.**

    By the way, when you refinance, you may also get up to two months without a mortgage payment\\* and have a refund of your escrow balance at the time of closing, which puts additional money in your pocket.
    """
).strip()


# Cashout Details - Table
LOAN_AMOUNT_TITLE = "Current Loan Amount"
DISPLAY_ESTIMATED_PAYOFF = False  # If True, display estimated payoff in cashout details table
ROUNDOFF_UPB_TO_NEARNEST_10 = False


# Cashout - Debt Consolidation
CASHOUT_DEBTCON_EMAIL_SUBJECT = dedent(
    """
    {first_name} {last_name}/ Your Refinance Cashout Options are Available
    """
)

CASHOUT_DEBTCON_EMAIL_BODY_HEAD = dedent(
    """
    **Hello {first_name},**

    With potentially no out-of-pocket cost or money due at closing, you could refinance with one of the following options. Let us know which option you want, and our team will be in touch with next steps.
    """
).strip()

CASHOUT_DEBTCON_EMAIL_BODY_TAIL = dedent(
    """
    These are estimates. The payments above do not include taxes or homeowner’s insurance. Since taxes and insurance can change from time to time, the payments only include principal and interest to show your true savings.

    **Due to market volatility, please respond as soon as possible with which option you want to move forward with. Or let me know if you are not interested at this time or have already refinanced.**

    By the way, when you refinance, you may also get up to two months without a mortgage payment\\* and have a refund of your escrow balance at the time of closing, which puts additional money in your pocket.
    """
).strip()

INCLUDE_DEBT_IN_TOTAL = True  # Add debt to original loan amount in cashout debt consolidation if True

CASHOUT_DEBTCON_DETAILS = [
    "options",
    "terms",
    "loan_amounts",
    "rates",
    "debt_consolidation",
    "additional_cashout",
    "total_loan_amounts",
    "payments",
    "monthly_other_debt",
    "total_payments",
    "monthly_savings",
    "projected_savings",
]


# Cashout - Desired
CASHOUT_DESIRED_EMAIL_SUBJECT = dedent(
    """
    {first_name} {last_name}/ Your Refinance Cashout Options are Available
    """
)

CASHOUT_DESIRED_EMAIL_BODY_HEAD = dedent(
    """
    **Hello {first_name},**

    With potentially no out-of-pocket cost or money due at closing, you could refinance with one of the following options. Let us know which option you want, and our team will be in touch with next steps.
    """
).strip()

CASHOUT_DESIRED_EMAIL_BODY_TAIL = dedent(
    """
    These are estimates. The payments above do not include taxes or homeowner’s insurance. Since taxes and insurance can change from time to time, the payments only include principal and interest to show your true savings.

    **Due to market volatility, please respond as soon as possible with which option you want to move forward with. Or let me know if you are not interested at this time or have already refinanced.**

    By the way, when you refinance, you may also get up to two months without a mortgage payment\\* and have a refund of your escrow balance at the time of closing, which puts additional money in your pocket.
    """
).strip()

CASHOUT_DESIRED_DETAILS = [
    "options",
    "terms",
    "loan_amounts",
    "rates",
    "desired_cashout_amount",
    "total_loan_amounts",
    "payments",
]

CASHOUT_DESIRED_MAPPING = {
    "terms": "Term",
    "loan_amounts": "Loan Amount",
    "rates": "Rate(APR)",
    "desired_cashout_amount": "Desired Cashout Amount",
    "total_loan_amounts": "Total Loan Amount",
    "payments": "Payment (Principal & Interest)",
}


# Purchase Offer
PURCHASE_EMAIL_SUBJECT = dedent(
    """
    {first_name} {last_name}: Your Purchase Options
    """
)

PURCHASE_OFFER_EMAIL_BODY_HEAD = dedent(
    """
    **Hello {first_name},**

    Here’s your customized purchase offer:
    """
).strip()

PURCHASE_OFFER_EMAIL_BODY_MID = dedent(
    """
    **Long Term Analysis based on {time_frame} years:**
    """
).strip()

PURCHASE_OFFER_EMAIL_BODY_TAIL = dedent(
    """
    **Please reply to go over these options, otherwise, we'll let you know when the next opportunity is available for you.**
    As always, contact me with any questions or to discuss any other home loan needs.
    """
).strip()


# Signature
SHOW_SIGNATURE = "True"

# Force Display or No Display Send Email Button
FORCE_SEND_EMAIL_BUTTON = False  # If True, always show the send email button; if False, hide it
DISABLE_SEND_EMAIL_BUTTON = False  # If True, disable the send email button


#  Email Disclaimer - Rate Term Refi & Cashout Refi Offer
DISCLAIMER_TEMPLATE_REFI = ""

# Email Disclaimer - Purchase Offer
DISCLAIMER_TEMPLATE = DISCLAIMER_TEMPLATE_REFI


# Open House Flyer
OPEN_HOUSE_FLYER_ENABLED = False


# Pre-Qualification Letter Config
COMPANY_LOGO = None
EQUAL_HOUSING_LENDER_LOGO = None
PRE_QUALIFICATION_LETTER_ENABLED = False


# Division Details
def get_division_theme(user: User) -> dict:
    """Get division branding theme including logo and colors based on email domain."""
    email = user.email
    domain = email.split("@")[-1].lower() if "@" in email else ""

    domain_mapping = {}

    return domain_mapping.get(domain, {})


# Find Lending Opportunity
FIND_LENDING_OPPORTUNITY_ENABLED = False
