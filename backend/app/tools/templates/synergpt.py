from textwrap import dedent

from db.models import User

# flake8: noqa

# For Rate Term Refi and Cashout
REQUIRE_ALL_OPTION_FIELDS = True  # Require only 'term' and 'rate' if False; all fields if True


# Rate Term Refi
APPLY_SAVINGS_LOGIC = (
    True  # If monthly savings > $0 and total savings < $0, keep total savings blank in email, if True
)

RATE_TERM_EMAIL_SUBJECT = dedent(
    """
    Is Your Rate Still the Best? See New Options.
    """
)

RATE_TERM_EMAIL_BODY_HEAD = dedent(
    """
    **Hi {first_name},**

    Let's get straight to it. Your loan from {closed_date} at {interest_rate}% was great then, but the market's changed – are you *still* getting the best deal? We're talking serious potential savings here, with options that could mean zero out-of-pocket and zero due at closing. Who doesn't want that?

    We asked: "Is your current loan truly working for your goals?" And we found solutions. As your financial partner, I'm laying it out clearly: which of these options makes you say, "That's the move!"? Let me know, and my team will jump on it. Smooth process, all your questions answered.

    **Here's the breakdown:**
    """
).strip()

RATE_TERM_EMAIL_BODY_TAIL = dedent(
    """
    **Important Notes:**

    * These payments are estimates of principal and interest only and do not include taxes and insurance. The actual payment will be higher. Taxes and homeowner's insurance are subject to change and may increase over time.

    * All figures are estimates and not a guarantee of approval or specific terms.

    * Think of this as a "financial upgrade." Are you ready to optimize your cash flow?

    * The market's moving fast! Let's lock in your choice ASAP.

    Let's chat and get you the best deal. Let's get after it!

    *By refinancing an existing loan, total finance charges may be higher over the life of the loan. Please consult a licensed financial advisor for advice on financial planning.*

    *Your rate may be different depending on factors such as credit scoring and other details. This email does not guarantee these rates and is for informational purposes only. Contact me for more information and to get preapproved with your personalized rate.*
    """
).strip()


# Cashout Details - Table
LOAN_AMOUNT_TITLE = "Original Loan Amount"
DISPLAY_ESTIMATED_PAYOFF = False  # If True, display estimated payoff in cashout details table
ROUNDOFF_UPB_TO_NEARNEST_10 = False


# Cashout - Debt Consolidation
CASHOUT_DEBTCON_EMAIL_SUBJECT = dedent(
    """
    Simplify Your Finances? Let's Explore Debt Consolidation!
    """
)

CASHOUT_DEBTCON_EMAIL_BODY_HEAD = dedent(
    """
    **Hey {first_name},**

    Ever feel like managing multiple debts is a juggling act? What if you could simplify things with debt consolidation? It's about combining your existing debts into one easier payment, and it could even mean zero out-of-pocket costs at closing – pretty neat!

    We're wondering if simplifying your payments could help you reach your financial goals faster. If the idea of one manageable payment sounds good, let's chat! My team is ready to make the process smooth and answer all your questions.

    **Here's a quick look at potential savings:**
    """
).strip()

CASHOUT_DEBTCON_EMAIL_BODY_TAIL = dedent(
    """
    *These are just examples and your actual savings will depend on your specific debt and consolidation options.*

    Debt consolidation can mean one less payment to track and potentially a lower overall interest rate. It's like streamlining your finances for more breathing room.

    Keep in mind that while it simplifies things, understanding the terms of any new loan is key. Also, consolidating may extend your repayment period, potentially increasing total interest paid over time. For personalized advice, chatting with a financial advisor is always a good idea. Your specific options will depend on your financial situation, so let's connect to explore what's possible for you!

    Ready for a simpler way to manage your debts? Which one of the scenarios above interests you the most?
    """
).strip()

INCLUDE_DEBT_IN_TOTAL = False  # Add debt to original loan amount in cashout debt consolidation if True

CASHOUT_DEBTCON_DETAILS = [
    "options",
    "terms",
    "loan_amounts",
    "est_current_loan_amounts",
    "rates",
    "debt_consolidation",
    "additional_cashout",
    "total_loan_amounts",
    "payments",
    "monthly_other_debt",
    "total_payments",
    "monthly_savings",
    "projected_savings",
]


# Cashout - Desired
CASHOUT_DESIRED_EMAIL_SUBJECT = dedent(
    """
    {first_name} {last_name}/ Your Refinance Cashout Options are Available
    """
)

CASHOUT_DESIRED_EMAIL_BODY_HEAD = dedent(
    """
    **Hello {first_name},**

    With potentially no out-of-pocket cost or money due at closing, you could refinance with one of the following options. Let us know which option you want, and our team will be in touch with next steps.
    """
).strip()

CASHOUT_DESIRED_EMAIL_BODY_TAIL = dedent(
    """
    These are estimates. The payments above do not include taxes or homeowner’s insurance. Since taxes and insurance can change from time to time, the payments only include principal and interest to show your true savings.

    **Due to market volatility, please respond as soon as possible with which option you want to move forward with. Or let me know if you are not interested at this time or have already refinanced.**

    By the way, when you refinance, you may also get up to two months without a mortgage payment\\* and have a refund of your escrow balance at the time of closing, which puts additional money in your pocket.
    """
).strip()

CASHOUT_DESIRED_DETAILS = [
    "options",
    "terms",
    "loan_amounts",
    "est_current_loan_amounts",
    "rates",
    "desired_cashout_amount",
    "total_loan_amounts",
    "payments",
]

CASHOUT_DESIRED_MAPPING = {
    "terms": "Term",
    "loan_amounts": "Loan Amount",
    "est_current_loan_amounts": "Est. Current Loan Amount",
    "rates": "Rate(APR)",
    "desired_cashout_amount": "Desired Cashout Amount",
    "total_loan_amounts": "Total Loan Amount",
    "payments": "Payment (Principal & Interest)",
}


# Purchase Offer
PURCHASE_EMAIL_SUBJECT = dedent(
    """
    {first_name} {last_name}: Your Purchase Options
    """
)

PURCHASE_OFFER_EMAIL_BODY_HEAD = dedent(
    """
    **Hey {first_name},**

    Exciting news! I’ve put together some personalized loan options to help you make your move to {property_address} a reality. We know that understanding the numbers is key, so we've laid out a comparison to help you see what works best for your goals.

    Take a look at these potential scenarios –I've considered different loan types and payment structures to give you a clear picture:
    """
).strip()

PURCHASE_OFFER_EMAIL_BODY_MID = dedent(
    """
    **Long Term Analysis based on {time_frame} years:**
    """
).strip()

PURCHASE_OFFER_EMAIL_BODY_TAIL = dedent(
    """
    *These figures are estimates based on current market conditions and preliminary information. Your actual rates and payments may vary based on your creditworthiness, down payment, and other factors. A more detailed, personalized quote will be provided upon full application.*

    Choosing the right loan is a big decision, and we want to make sure you feel confident in your choice.

    I’m here to walk you through each option in detail, answer any questions you have, and help you determine which loan best aligns with your financial situation and long-term goals for {property_address}.

    Ready to dive deeper and get a personalized quote? Let's schedule a quick call to discuss these options and take the next step towards your new home!
    """
).strip()


# Signature
SHOW_SIGNATURE = "False"

# Force Display or No Display Send Email Button
FORCE_SEND_EMAIL_BUTTON = False  # If True, always show the send email button; if False, hide it
DISABLE_SEND_EMAIL_BUTTON = False  # If True, disable the send email button


# Email Disclaimer - Rate Term Refi & Cashout Refi Offer
DISCLAIMER_TEMPLATE_REFI = dedent(
    """
    <small>
        <img src="https://d12g9gnoufq1hk.cloudfront.net/production/79c2fe11-f9dd-4e81-8302-21f46a314166.png" alt="equal_housing_logo" style="max-width: 50px; margin-top: 10px;">
        Equal Housing Lender. MLO licensing information: {nmls_number}. S1L LLC d/b/a S1L. For S1L LLC's full agency and state licensing information, please visit <a href="www.s1l.com/licensing" target="_blank">www.s1l.com/licensing</a>. S1L LLC's NMLS #1907235 (<a href="https://www.nmlsconsumeraccess.org" target="_blank">www.nmlsconsumeraccess.org</a>). S1L LLC is not affiliated with, or an agent or division of, a governmental agency or a depository institution. Copyright ©2025 S1L LLC dba S1L. Licensed by the Department of Financial Protection and Innovation under the California Residential Mortgage Lending Act.<br>
        This information is based on the mortgage you closed with us and does not account for any refinances you may have done since. Refinancing an existing loan may result in the total finance charges being higher over the life of the loan.<br>
        <em>For example, you may have two months of no payments if your loan closes between the 1st and 10th of August. In this scenario your next payment would not be until October. You would skip your August and September payments and they would be carried over into the new loan. Of course, you should always continue making your current mortgage payments until your loan closes. Not all borrowers will be able to delay or carry-over two mortgage payments – this is contingent upon the closing date.</em><br>
        Rates effective {date}. Rates quoted are estimates and based on a 780 or above credit score, debt to income under 45%, loan-to-value at 70% or lower. This payment does not include taxes, homeowner's insurance, private mortgage insurance, or other homeowner's insurance (HOA) dues, and is an estimate; your actual payment will be higher. The payment examples may increase your loan amount to cover potential costs such as title, pre-paids (taxes and insurance) and other costs. Any such costs will be itemized on your initial loan estimate. An appraisal fee may be required prior to closing. An annual and monthly mortgage insurance premium may be required and will vary depending on the loan characterization.<br>
        Payments and rates may vary based on borrower's credit score, actual closing costs and other variables. Depending on your situation, flood, property hazard, and mortgage insurance may be needed, which could increase the monthly payment and Annual Percentage Rate (APR).<br>
        The APR describes the interest rate for a whole year (annualized), rather than just a monthly fee/rate. The APR allows a borrower to compare costs of credit because it factors in term, interest rate and fees associated with the loan.<br>
        The total savings were estimated based on the total number of months left on your current term times monthly principal and interest payment, minus your current principal and interest payment times numbers of months on the projected term.
    </small><br><br>
    """
)

# Email Disclaimer - Purchase Offer
DISCLAIMER_TEMPLATE = DISCLAIMER_TEMPLATE_REFI


# Open House Flyer
OPEN_HOUSE_FLYER_ENABLED = False


# Pre-Qualification Letter Config
COMPANY_LOGO = dedent("https://d12g9gnoufq1hk.cloudfront.net/production/S1L%20Logo.png")
EQUAL_HOUSING_LENDER_LOGO = dedent(
    "https://d12g9gnoufq1hk.cloudfront.net/production/79c2fe11-f9dd-4e81-8302-21f46a314166.png"
)
PRE_QUALIFICATION_LETTER_ENABLED = False


# Division Details
def get_division_theme(user: User) -> dict:
    """Get division branding theme including logo and colors based on email domain."""
    email = user.email
    domain = email.split("@")[-1].lower() if "@" in email else ""

    domain_mapping = {}

    return domain_mapping.get(domain, {"logo": COMPANY_LOGO, "primary_color": "#FFC105", "secondary_color": "#EEB311"})


# Find Lending Opportunity
FIND_LENDING_OPPORTUNITY_ENABLED = True
