from textwrap import dedent

from db.models import User

# flake8: noqa

# For Rate Term Refi and Cashout
REQUIRE_ALL_OPTION_FIELDS = False  # Require only 'term' and 'rate' if False; all fields if True


# Rate Term Refi
APPLY_SAVINGS_LOGIC = (
    False  # If monthly savings > $0 and total savings < $0, keep total savings blank in email, if True
)

RATE_TERM_EMAIL_SUBJECT = dedent(
    """
    {first_name} {last_name}/ Your Mortgage Rate & Payment Reduction is Available
    """
)

RATE_TERM_EMAIL_BODY_HEAD = dedent(
    """
    **Hello {first_name},**

    Here are some options for you to refinance your home.
    """
).strip()

RATE_TERM_EMAIL_BODY_TAIL = dedent(
    """
    Please contact me if you would like to refinance your home using one of the options listed above.
    """
).strip()


# Cashout Details - Table
LOAN_AMOUNT_TITLE = "Current Loan Amount"
DISPLAY_ESTIMATED_PAYOFF = False  # If True, display estimated payoff in cashout details table
ROUNDOFF_UPB_TO_NEARNEST_10 = True


# Cashout - Debt Consolidation
CASHOUT_DEBTCON_EMAIL_SUBJECT = dedent(
    """
    {first_name} {last_name}/ Your Refinance Cashout Options are Available
    """
)

CASHOUT_DEBTCON_EMAIL_BODY_HEAD = dedent(
    """
    **Hello {first_name},**

    Here are some options for you to refinance your home.
    """
).strip()

CASHOUT_DEBTCON_EMAIL_BODY_TAIL = dedent(
    """
    Please contact me if you would like to refinance your home using one of the options listed above.
    """
).strip()

INCLUDE_DEBT_IN_TOTAL = True  # Add debt to original loan amount in cashout debt consolidation if True

CASHOUT_DEBTCON_DETAILS = [
    "options",
    "terms",
    "loan_amounts",
    "rates",
    "debt_consolidation",
    "additional_cashout",
    "total_loan_amounts",
    "payments",
    "monthly_other_debt",
    "total_payments",
    "monthly_savings",
    "projected_savings",
]


# Cashout - Desired
CASHOUT_DESIRED_EMAIL_SUBJECT = dedent(
    """
    {first_name} {last_name}/ Your Refinance Cashout Options are Available
    """
)

CASHOUT_DESIRED_EMAIL_BODY_HEAD = dedent(
    """
    **Hello {first_name},**

    Here are some options for you to refinance your home.
    """
).strip()

CASHOUT_DESIRED_EMAIL_BODY_TAIL = dedent(
    """
    Please contact me if you would like to refinance your home using one of the options listed above.
    """
).strip()

CASHOUT_DESIRED_DETAILS = [
    "options",
    "terms",
    "loan_amounts",
    "rates",
    "desired_cashout_amount",
    "total_loan_amounts",
    "payments",
]

CASHOUT_DESIRED_MAPPING = {
    "terms": "Term",
    "loan_amounts": "Loan Amount",
    "rates": "Rate(APR)",
    "desired_cashout_amount": "Desired Cashout Amount",
    "total_loan_amounts": "Total Loan Amount",
    "payments": "Payment (Principal & Interest)",
}


# Purchase Offer
PURCHASE_EMAIL_SUBJECT = dedent(
    """
    {first_name} {last_name}: Your Purchase Options
    """
)

PURCHASE_OFFER_EMAIL_BODY_HEAD = dedent(
    """
    **Hello {first_name},**

    Here are some options for you to purchase your home.
    """
).strip()

PURCHASE_OFFER_EMAIL_BODY_MID = dedent(
    """
    **Long Term Analysis based on {time_frame} years:**
    """
).strip()

PURCHASE_OFFER_EMAIL_BODY_TAIL = dedent(
    """
    Please contact me if you would like to purchase your home using one of the options listed above.
    """
).strip()


# Signature
SHOW_SIGNATURE = "True"

# Force Display or No Display Send Email Button
FORCE_SEND_EMAIL_BUTTON = False  # If True, always show the send email button; if False, hide it
DISABLE_SEND_EMAIL_BUTTON = False  # If True, disable the send email button


# Email Disclaimer - Rate Term Refi & Cashout Refi Offer
DISCLAIMER_TEMPLATE_REFI = dedent(
    """
    <small>
        <img src="https://d3anbrt0otggmr.cloudfront.net/production/artificial_mortgage/41a98565-7e61-4ede-b30f-a12f192bf74e.png" alt="equal_housing_logo" style="max-width: 50px; margin-top: 10px;">
        Equal Housing Lender. MLO licensing information: {nmls_number}. AM LLC d/b/a Artificial Mortgage. For AM LLC’s full agency and state licensing information, please visit
        <a href="https://www.artiicial_mortgage.com/licensing" target="_blank">www.artiicial_mortgage.com/licensing</a>.
        AM LLC’s NMLS #1234 (<a href="https://www.nmlsconsumeraccess.org" target="_blank">www.nmlsconsumeraccess.org</a>).
        AM LLC is not affiliated with, or an agent or division of, a governmental agency or a depository institution. Copyright &copy;{year} AM LLC dba Artificial Mortgage.
        Licensed by the Department of Financial Protection and Innovation
        under the California Residential Mortgage Lending Act.<br>
        This information is based on the mortgage you closed with us and does not account for any refinances you may have done since. Refinancing an existing loan may result
        in the total finance charges being higher over the life of the loan.<br>
        <em>For example, you may have two months of no payments if your loan closes between the 1<sup>st</sup> and 10<sup>th</sup> of August. In this scenario your next payment would not be until October.
        You would skip your August and September payments and they would be carried over into the new loan. Of course, you should always continue making your current mortgage payments
        until your loan closes. Not all borrowers will be able to delay or carry-over two mortgage payments – this is contingent upon the closing date.</em><br>
        Rates effective {date}. Rates quoted are estimates and based on a 780 or above credit score, debt to income under 45%, loan-to-value at 70% or lower. This payment does not
        include taxes, homeowner’s insurance, private mortgage insurance, or other homeowner’s insurance (HOA) dues, and is an estimate; your actual payment will be higher. The payment
        examples may increase your loan amount to cover potential costs such as title, pre-paids (taxes and insurance) and other costs. Any such costs will be itemized on your initial
        loan estimate. An appraisal fee may be required prior to closing. An annual and monthly mortgage insurance premium may be required and will vary depending on the loan characterization.<br>
        Payments and rates may vary based on borrower’s credit score, actual closing costs and other variables. Depending on your situation, flood, property hazard, and mortgage
        insurance may be needed, which could increase the monthly payment and Annual Percentage Rate (APR).<br>
        The APR describes the interest rate for a whole year (annualized), rather than just a monthly fee/rate. The APR allows a borrower to compare costs of credit because it factors
        in term, interest rate and fees associated with the loan.<br>
        The total savings were estimated based on the total number of months left on your current term times monthly principal and interest payment, minus your current principal and
        interest payment times numbers of months on the projected term.
    </small><br><br>
    """
)

# Email Disclaimer - Purchase Offer
DISCLAIMER_TEMPLATE = DISCLAIMER_TEMPLATE_REFI


# Open House Flyer
OPEN_HOUSE_FLYER_ENABLED = True


# Pre-Qualification Letter Config
COMPANY_LOGO = dedent("https://d3anbrt0otggmr.cloudfront.net/production/artificial_mortgage/AMGPT+Logo.png")
EQUAL_HOUSING_LENDER_LOGO = dedent(
    "https://d3anbrt0otggmr.cloudfront.net/production/artificial_mortgage/41a98565-7e61-4ede-b30f-a12f192bf74e.png"
)
PRE_QUALIFICATION_LETTER_ENABLED = True


# Division Details
def get_division_theme(user: User) -> dict:
    """Get division branding theme including logo and colors based on email domain."""
    email = user.email
    domain = email.split("@")[-1].lower() if "@" in email else ""

    domain_mapping = {}

    return domain_mapping.get(domain, {"logo": COMPANY_LOGO, "primary_color": "#1a2953", "secondary_color": "#1599d6"})


# Find Lending Opportunity
FIND_LENDING_OPPORTUNITY_ENABLED = False
