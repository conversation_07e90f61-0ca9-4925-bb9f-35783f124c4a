from textwrap import dedent

from db.models import User

# flake8: noqa

# For Rate Term Refi and Cashout
REQUIRE_ALL_OPTION_FIELDS = False  # Require only 'term' and 'rate' if False; all fields if True


# Rate Term Refi
APPLY_SAVINGS_LOGIC = (
    False  # If monthly savings > $0 and total savings < $0, keep total savings blank in email, if True
)

RATE_TERM_EMAIL_SUBJECT = dedent(
    """
    Exciting Refinance Options Available for You
    """
)

RATE_TERM_EMAIL_BODY_HEAD = dedent(
    """
    **Dear {first_name},**

    I hope this email finds you well. I wanted to reach out to you with some exciting news regarding your mortgage. We have several refinance options available that could potentially save you money and provide more favorable terms.
    """
).strip()

RATE_TERM_EMAIL_BODY_TAIL = dedent(
    """
    Refinancing your mortgage can offer benefits such as lower interest rates, reduced monthly payments, and the ability to tap into your home's equity. Our team is here to guide you through the process and help you find the best option that suits your needs.

    Please feel free to reach out to me directly if you have any questions or would like to discuss these options further. I look forward to assisting you.
    """
).strip()


# Cashout Details - Table
LOAN_AMOUNT_TITLE = "Current Loan Amount"
DISPLAY_ESTIMATED_PAYOFF = False  # If True, display estimated payoff in cashout details table
ROUNDOFF_UPB_TO_NEARNEST_10 = False


# Cashout - Debt Consolidation
CASHOUT_DEBTCON_EMAIL_SUBJECT = dedent(
    """
    {first_name} {last_name}/ Your Refinance Cashout Options are Available
    """
)

CASHOUT_DEBTCON_EMAIL_BODY_HEAD = dedent(
    """
    **Hello {first_name},**

    With potentially no out-of-pocket cost or money due at closing, you could refinance with one of the following options. Let us know which option you want, and our team will be in touch with next steps.
    """
).strip()

CASHOUT_DEBTCON_EMAIL_BODY_TAIL = dedent(
    """
    These are estimates. The payments above do not include taxes or homeowner’s insurance. Since taxes and insurance can change from time to time, the payments only include principal and interest to show your true savings.

    **Due to market volatility, please respond as soon as possible with which option you want to move forward with. Or let me know if you are not interested at this time or have already refinanced.**

    By the way, when you refinance, you may also get up to two months without a mortgage payment\\* and have a refund of your escrow balance at the time of closing, which puts additional money in your pocket.
    """
).strip()

INCLUDE_DEBT_IN_TOTAL = True  # Add debt to original loan amount in cashout debt consolidation if True

CASHOUT_DEBTCON_DETAILS = [
    "options",
    "terms",
    "loan_amounts",
    "rates",
    "debt_consolidation",
    "additional_cashout",
    "total_loan_amounts",
    "payments",
    "monthly_other_debt",
    "total_payments",
    "monthly_savings",
    "projected_savings",
]


# Cashout - Desired
CASHOUT_DESIRED_EMAIL_SUBJECT = dedent(
    """
    {first_name} {last_name}/ Your Refinance Cashout Options are Available
    """
)

CASHOUT_DESIRED_EMAIL_BODY_HEAD = dedent(
    """
    **Hello {first_name},**

    With potentially no out-of-pocket cost or money due at closing, you could refinance with one of the following options. Let us know which option you want, and our team will be in touch with next steps.
    """
).strip()

CASHOUT_DESIRED_EMAIL_BODY_TAIL = dedent(
    """
    These are estimates. The payments above do not include taxes or homeowner’s insurance. Since taxes and insurance can change from time to time, the payments only include principal and interest to show your true savings.

    **Due to market volatility, please respond as soon as possible with which option you want to move forward with. Or let me know if you are not interested at this time or have already refinanced.**

    By the way, when you refinance, you may also get up to two months without a mortgage payment\\* and have a refund of your escrow balance at the time of closing, which puts additional money in your pocket.
    """
).strip()

CASHOUT_DESIRED_DETAILS = [
    "options",
    "terms",
    "loan_amounts",
    "rates",
    "desired_cashout_amount",
    "total_loan_amounts",
    "payments",
]

CASHOUT_DESIRED_MAPPING = {
    "terms": "Term",
    "loan_amounts": "Loan Amount",
    "rates": "Rate(APR)",
    "desired_cashout_amount": "Desired Cashout Amount",
    "total_loan_amounts": "Total Loan Amount",
    "payments": "Payment (Principal & Interest)",
}


# Purchase Offer
PURCHASE_EMAIL_SUBJECT = dedent(
    """
    {first_name} {last_name}: Your Purchase Options
    """
)

PURCHASE_OFFER_EMAIL_BODY_HEAD = dedent(
    """
    **Hello {first_name},**

    Here’s your customized purchase offer:
    """
).strip()

PURCHASE_OFFER_EMAIL_BODY_MID = dedent(
    """
    **Long Term Analysis based on {time_frame} years:**
    """
).strip()

PURCHASE_OFFER_EMAIL_BODY_TAIL = dedent(
    """
    **Please reply to go over these options, otherwise, we'll let you know when the next opportunity is available for you.**
    As always, contact me with any questions or to discuss any other home loan needs.
    """
).strip()


# Signature
SHOW_SIGNATURE = "True"

# Force Display or No Display Send Email Button
FORCE_SEND_EMAIL_BUTTON = True  # If True, always show the send email button; if False, hide it
DISABLE_SEND_EMAIL_BUTTON = False  # If True, disable the send email button


# Email Disclaimer - Rate Term Refi & Cashout Refi Offer
DISCLAIMER_TEMPLATE_REFI = dedent(
    """
    <small>
        <img src="https://d3anbrt0otggmr.cloudfront.net/production/alcova/6a1e5ad8-b720-4043-bba9-345b1c55127a.png" alt="equal_housing_logo" style="max-width: 50px; margin-top: 10px;">
        ALCOVA Mortgage, LLC | NMLS #140508 | (<a href="https://www.nmlsconsumeraccess.org/" target="_blank">www.nmlsconsumeraccess.org</a>) | 308 Market St. SE, Roanoke VA 24011.<br><br>
        This communication may contain privileged or other confidential information.
        If you are not the intended recipient, or believe that you have received this communication in error, please do not print, copy, retransmit, disseminate, or otherwise use the information.
        This communication represents the originator's personal views, which may not reflect those of ALCOVA Mortgage.
        Finally, the recipient should check this email and any attachments for the presence of viruses.
        ALCOVA Mortgage accepts no liability for any damage caused by any virus transmitted by this email.<br><br>
        Caution: Cyber fraud and wiring scams are prevalent in the real estate industry.
        Please visit <a href="https://alcova.com/safeguard" target="_blank">https://alcova.com/safeguard</a> on our website for details and safeguards.
    </small><br><br>
    """
)

# Email Disclaimer - Purchase Offer
DISCLAIMER_TEMPLATE = DISCLAIMER_TEMPLATE_REFI


# Open House Flyer
OPEN_HOUSE_FLYER_ENABLED = False


# Pre-Qualification Letter Config
COMPANY_LOGO = dedent("https://dndyx5c0rtlog.cloudfront.net/ALcova%20Logo.png")
EQUAL_HOUSING_LENDER_LOGO = dedent(
    "https://d3anbrt0otggmr.cloudfront.net/production/alcova/6a1e5ad8-b720-4043-bba9-345b1c55127a.png"
)
PRE_QUALIFICATION_LETTER_ENABLED = False


# Division Details
def get_division_theme(user: User) -> dict:
    """Get division branding theme including logo and colors based on email domain."""
    email = user.email
    domain = email.split("@")[-1].lower() if "@" in email else ""

    domain_mapping = {}

    return domain_mapping.get(domain, {"logo": COMPANY_LOGO, "primary_color": "#129AD9", "secondary_color": "#052552"})


# Find Lending Opportunity
FIND_LENDING_OPPORTUNITY_ENABLED = False
