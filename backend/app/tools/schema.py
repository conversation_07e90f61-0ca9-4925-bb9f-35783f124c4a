from enum import Enum

from pydantic import BaseModel


class ComponentEnum(str, Enum):
    SourceCitation = "SourceCitation"
    SlidePresentation = "SlidePresentation"
    PropertyAVMComponent = "PropertyAVMComponent"
    Image = "Image"
    LoanDetails = "LoanDetails"
    SalesEmail = "SalesEmail"
    TotalExpert = "TotalExpert"
    TotalExpertNotes = "TotalExpertNotes"
    TotalExpertActivities = "TotalExpertActivities"
    OutlookMail = "OutlookMail"
    TotalExpertJourneys = "TotalExpertJourneys"
    OpenHouseFlyer = "OpenHouseFlyer"
    PreQual = "PreQual"
    FindLendingOpportunity = "FindLendingOpportunity"
    CallCampaign = "CallCampaign"


class Component(BaseModel):
    component_name: ComponentEnum
    component_props: list[dict] | dict | None = None


class ToolResponse(BaseModel):
    message: str | dict
    component: Component | None = None
    payload: dict | None = None
