closed_loan_context = (
    "To fetch closed loans, Check if user has provided some date range or not. They might sometimes say last week,"
    " last year instead of direct date range. If that is the case, you should convert it into date range based on"
    " today's date. And then use `Fund Released Date` field."
)

CONTEXT_DATA = {
    "active": "To fetch active loans, you don't need to use any fields. Just call the function with loan_type active.",
    "pipeline": (
        "To fetch pipeline loans, you don't need to use any fields. Just call the function with loan_type pipeline."
    ),
    "purchase": (
        "To fetch purchase loans, you don't need to use any fields. Just call the function with loan_type purchase."
    ),
    "locked": (
        "To fetch locked loans, you should use Lock Status field based on the given values: Locked, Not Locked etc."
    ),
    "closed": closed_loan_context,
    "funded": closed_loan_context,
    "epo": (
        "To fetch loans related to EPO date, use EPO Date field if duration is given otherwise call the function with"
        " loan_type epo"
    ),
    "open_condition": "To fetch open condition call get_open_condition using the loan number.",
    "other": (
        "Analyze the user query thoroughly and choose the most relevant fields. "
        "If the query matches any of the following loan-related scenarios, Answer the question. "
        "Otherwise, call the function out_of_scope_query. "
        "1. Monthly Payment Estimation"
        "2. Affordability Based on Income and DTI"
        "3. Loan Adjustment (Interest Rate or Loan Amount Change)"
        "4. Refinance Impact Assessment"
        "5. Down Payment and Purchase Option Comparisons"
        "6. Full Payment Estimate with Taxes, Insurance, and Mortgage Insurance"
        "7. Buy vs Rent Financial Comparison"
    ),
}


def fetch_context_data(category: str):
    return CONTEXT_DATA.get(category, CONTEXT_DATA["other"])
