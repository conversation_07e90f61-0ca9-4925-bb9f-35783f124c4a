import importlib

from config import settings
from loguru import logger

# Find client_name and import templates
client_module = settings.CLIENT_NAME.lower().replace(" ", "_")
try:
    client_datapulse_settings = importlib.import_module(f"tools.datapulse.{client_module}")
except ImportError:
    logger.warning(f"DataPulse settngs for {client_module} not found! Using default templates")
    client_datapulse_settings = importlib.import_module("tools.datapulse._default")
