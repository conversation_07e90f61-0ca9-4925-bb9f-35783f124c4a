DATAPULSE_ENABLE = True

SALES_ALERTS = {
    "id": "96cabb48-6d5b-4bde-a1b3-be2525c8ea57",
    "name": "Sales Alerts",
    "description": "New loans started last week",
    "payload": {
        "filter": {
            "operator": "AND",
            "terms": [
                {
                    "canonicalName": "Fields.Log.MS.DateTime.Started",
                    "value": "_7daysago",
                    "matchType": "GreaterThanOrEquals",
                    "precision": "day",
                }
            ],
        },
        "fields": [
            "Fields.364",
            "Fields.Log.MS.DateTime.Started",
            "Fields.2",
            "Fields.1172",
            "Fields.1401",
            "Fields.4000",
            "Fields.4002",
            "Fields.Log.MS.CurrentMilestone",
            "Fields.763",
            "Fields.VEND.X139",
        ],
        "includeArchivedLoans": "True",
        "sortOrder": [{"canonicalName": "Fields.Log.MS.DateTime.Started", "order": "asc"}],
    },
    "primary_display_fields": [
        "Fields.364",
        "Fields.Log.MS.DateTime.Started",
        "Fields.2",
        "Fields.1172",
        "Fields.1401",
        "Fields.4000",
        "Fields.4002",
        "Fields.Log.MS.CurrentMilestone",
        "Fields.763",
        "Fields.VEND.X139",
    ],
    "secondary_display_fields": ["Fields.364", "Fields.Log.MS.DateTime.Started", "Fields.Log.MS.CurrentMilestone"],
    "is_visible": True,
    "order": 1,
    "requires_condition_pending": False,
    "ttl": 86400,
    "requires_custom_date": False,
}

ACTIVE_PIPELINE = {
    "id": "8397072e-b5a2-4f7e-8efa-60500c99fbc9",
    "name": "Active Pipeline",
    "description": "Loans closing in the next 30 days that are not locked",
    "payload": {
        "filter": {
            "operator": "AND",
            "terms": [
                {
                    "canonicalName": "Fields.763",
                    "value": "_currentdate",
                    "matchType": "GreaterThanOrEquals",
                    "precision": "day",
                },
                {
                    "canonicalName": "Fields.763",
                    "value": "_30daysfromnow",
                    "matchType": "LessThanOrEquals",
                    "precision": "day",
                },
                {"canonicalName": "Loan.LockStatus", "value": "NotLocked", "matchType": "exact"},
            ],
        },
        "fields": [
            "Fields.364",
            "Fields.4000",
            "Fields.4002",
            "Fields.Log.MS.CurrentMilestone",
            "Loan.LockStatus",
            "Fields.UWC.ALLPTACount",
            "Fields.763",
        ],
        "sortOrder": [{"canonicalName": "Fields.763", "order": "asc"}],
    },
    "primary_display_fields": [
        "Fields.364",
        "Fields.4000",
        "Fields.4002",
        "Fields.Log.MS.CurrentMilestone",
        "Loan.LockStatus",
        "Fields.UWC.ALLPTACount",
        "Fields.763",
    ],
    "secondary_display_fields": ["Fields.364", "Fields.UWC.ALLPTACount", "Fields.763"],
    "is_visible": True,
    "order": 2,
    "requires_condition_pending": True,
    "ttl": 86400,
    "requires_custom_date": False,
}

URGENT_LOANS = {
    "id": "3f26404e-ab27-432d-b73b-cf714524fa9c",
    "name": "Urgent Loans",
    "description": "Loans closing in the next 7 days",
    "payload": {
        "filter": {
            "operator": "AND",
            "terms": [
                {
                    "canonicalName": "Fields.763",
                    "value": "_currentdate",
                    "matchType": "GreaterThanOrEquals",
                    "precision": "day",
                },
                {
                    "canonicalName": "Fields.763",
                    "value": "_7daysfromnow",
                    "matchType": "LessThanOrEquals",
                    "precision": "day",
                },
            ],
        },
        "fields": [
            "Fields.364",
            "Fields.4000",
            "Fields.4002",
            "Fields.763",
            "Fields.Log.MS.CurrentMilestone",
            "Fields.3977",
            "Fields.UWC.ALLPTACount",
        ],
        "sortOrder": [{"canonicalName": "Fields.763", "order": "asc"}],
    },
    "primary_display_fields": [
        "Fields.364",
        "Fields.4000",
        "Fields.4002",
        "Fields.763",
        "Fields.Log.MS.CurrentMilestone",
        "Fields.3977",
        "Fields.UWC.ALLPTACount",
    ],
    "secondary_display_fields": ["Fields.364", "Fields.763", "Fields.UWC.ALLPTACount"],
    "is_visible": True,
    "order": 3,
    "requires_condition_pending": True,
    "ttl": 86400,
    "requires_custom_date": False,
}

REFI_HEADS_UP = {
    "id": "0b11b808-f042-42bd-995b-8e9d73dab1b0",
    "name": "Refi Heads Up",
    "description": "Loans coming off EPO in the next 30 days ",
    "payload": {
        "filter": {
            "operator": "AND",
            "terms": [
                {
                    "canonicalName": "Fields.CX.EPO",
                    "value": "_currentdate",
                    "matchType": "GreaterThanOrEquals",
                    "precision": "day",
                },
                {
                    "canonicalName": "Fields.CX.EPO",
                    "value": "_30daysfromnow",
                    "matchType": "LessThanOrEquals",
                    "precision": "day",
                },
            ],
        },
        "fields": [
            "Fields.364",
            "Fields.2",
            "Fields.3",
            "Fields.4000",
            "Fields.4002",
            "Fields.1490",
            "Fields.353",
            "Fields.VASUMM.X23",
            "Fields.CX.EPO",
        ],
        "sortOrder": [{"canonicalName": "Fields.CX.EPO", "order": "asc"}],
    },
    "primary_display_fields": [
        "Fields.364",
        "Fields.2",
        "Fields.3",
        "Fields.4000",
        "Fields.4002",
        "Fields.1490",
        "Fields.353",
        "Fields.VASUMM.X23",
        "Fields.CX.EPO",
    ],
    "secondary_display_fields": ["Fields.364", "Fields.CX.EPO"],
    "is_visible": True,
    "order": 4,
    "requires_condition_pending": False,
    "ttl": 86400,
    "requires_custom_date": False,
}

LOANS_CLOSED = {
    "id": "0c2ad442-aefc-4728-9e8b-420a7d57ef74",
    "name": "Loans Closed",
    "description": "Loans closed in X time",
    "payload": {
        "filter": {
            "operator": "AND",
            "terms": [
                {
                    "canonicalName": "Fields.1999",
                    "value": "_custom_startdate",
                    "matchType": "GreaterThanOrEquals",
                    "precision": "day",
                },
                {
                    "canonicalName": "Fields.1999",
                    "value": "_custom_enddate",
                    "matchType": "LessThanOrEquals",
                    "precision": "day",
                },
            ],
        },
        "fields": [
            "Fields.364",
            "Fields.4000",
            "Fields.4002",
            "Fields.2",
            "Fields.3",
            "Fields.4",
            "Fields.19",
            "Fields.1172",
            "Fields.1999",
        ],
        "includeArchivedLoans": "True",
        "sortOrder": [{"canonicalName": "Fields.1999", "order": "asc"}],
    },
    "primary_display_fields": [
        "Fields.364",
        "Fields.4000",
        "Fields.4002",
        "Fields.2",
        "Fields.3",
        "Fields.4",
        "Fields.19",
        "Fields.1172",
        "Fields.1999",
    ],
    "secondary_display_fields": ["Fields.364", "Fields.1999"],
    "is_visible": True,
    "order": 5,
    "requires_condition_pending": False,
    "ttl": 86400,
    "requires_custom_date": True,
}

DATAPULSE_REPORTS = [SALES_ALERTS, ACTIVE_PIPELINE, URGENT_LOANS, REFI_HEADS_UP, LOANS_CLOSED]
