import redis.asyncio as aioredis
from config import settings
from loguru import logger

_redis_client = None


async def get_redis_client() -> aioredis.Redis:
    """
    Get a Redis client instance. Uses a singleton pattern to reuse the same connection.

    Returns:
        redis.Redis: A Redis client instance
    """
    global _redis_client

    if _redis_client is None:
        try:
            _redis_client = aioredis.from_url(settings.REDIS_URL)
            # Test the connection
            await _redis_client.ping()
        except aioredis.RedisError as e:
            logger.error(f"Failed to connect to Redis: {e}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error connecting to Redis: {e}")
            raise

    return _redis_client
