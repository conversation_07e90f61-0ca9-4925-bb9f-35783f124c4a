from datetime import datetime
from io import BytesIO

import pandas as pd
from hooks.s3 import default_s3_hook
from loguru import logger

MANN_FIELD_NAMES_MAPPING = {
    "Loan Number": "Loan Number",
    "Total Loan Amount": "Total Loan Amount",
    "Base Loan Amount": "Base Loan Amount",
    "Note Rate": "Interest Rate",
    "Borrower First Name": "Borrower First Name",
    "Borrower Last Name": "Borrower Last Name",
    "Property State": "Property State",
    "Property Address": "Property Address",
    "Property City": "Property City",
    "Property Zip": "Property Zip",
    "Interviewer Name": "Interviewer Name",
    "Interviewer Email": "Work Contact: Work Email",
    "Loan Type": "Loan Type",
    "Loan Purpose": "Loan Purpose",
    "Occupancy": "Occupancy",
    "Funds Sent Date": "Closed Date",
    "Appraised Value": "Appraised Value",
    "Borr Home Phone": "Borrower Home Phone",
    "Borr Email": "Borrower Email",
    "Borrower Full Name": "Borrower Full Name",
}


DISPLAY_ORDER = list(MANN_FIELD_NAMES_MAPPING.keys())
DISPLAY_ORDER.pop()  # Remove Borrower Full Name
SEARCH_FIELDS = DISPLAY_ORDER


class MannLoanUtils:
    def __init__(self):
        self.s3_hook = default_s3_hook

    def retreive_df(self, object_name: str) -> pd.DataFrame:
        try:
            logger.info("Downloading CSV presigned url")
            presigned_url = self.s3_hook.get_presigned_url_for_download(object_name=object_name)
            key = self.s3_hook.get_key_from_url(presigned_url)
            csv_data = self.s3_hook.download_and_read_csv_with_pandas(key=key)
            df_temp = self.clean_df(csv_data)
            return df_temp

        except Exception as e:
            logger.error(f"Failed to download and read CSV object {key}: {e}")
            raise

    def clean_df(self, df):
        """
        Cleans and converts the specified columns in the DataFrame to numeric.

        Parameters:
        - df: The pandas DataFrame to process.
        - columns: List of column names to clean and convert.

        Returns:
        - DataFrame with cleaned and converted columns.
        """
        # df = pd.read_csv(mann_data_path)
        df = df.copy()
        columns_to_clean = ["Appraised Value", "Base Loan Amount", "Total Loan Amount"]

        for col in columns_to_clean:
            # Step 1: Remove commas and strip whitespace
            df[col] = df[col].str.replace(",", "").str.strip()

            # Step 2: Replace empty strings with NaN
            df[col] = df[col].replace("", pd.NA)

            # Step 3: Convert to numeric, coercing any invalid values to NaN
            df[col] = pd.to_numeric(df[col], errors="coerce")

            # Optional: Fill NaN values with 0 or any other value
            df[col] = df[col].fillna(0)

            # Convert the column to datetime format
        df["Funds Sent Date"] = pd.to_datetime(df["Funds Sent Date"], format="%m/%d/%Y")

        # Combine full name column
        df["Borrower Full Name"] = df["Borrower First Name"].str.cat(df["Borrower Last Name"], sep=" ")
        df = df.rename(columns={"Work Contact: Work Email": "Interviewer Email"})

        return df

    def reformat_df(self, df):
        # Change Date Format
        df["Funds Sent Date"] = pd.to_datetime(df["Funds Sent Date"], unit="ms").dt.strftime("%Y-%m-%d")
        # Reorder df
        df = df[DISPLAY_ORDER]
        return df

    def generate_and_upload_excel(self, prefix: str, report_data: dict, folder: str = "loan_data") -> str | None:
        try:
            df = pd.DataFrame(report_data)

            excel_buffer = BytesIO()
            df.to_excel(excel_buffer, index=False)
            excel_buffer.seek(0)

            # Upload to S3
            filename = f"{folder}/{prefix}_{datetime.now().strftime('%Y%m%d%H%M%S')}.xlsx"
            response = self.s3_hook.put_object(object_name=filename, file=excel_buffer)

            if response["ResponseMetadata"]["HTTPStatusCode"] == 200:
                excel_url = self.s3_hook.get_presigned_url_for_download(filename)
                return excel_url
            else:
                return "Failed to upload Excel file."

        except Exception as e:
            logger.error(f"Failed to generate and upload Excel file: {e}")
            return "Failed to generate and upload Excel file."


mann_utils = MannLoanUtils()
