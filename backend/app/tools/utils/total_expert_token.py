import json
import random
import string
from datetime import datetime, timedelta
from urllib.parse import urlencode
from uuid import UUID

import redis.asyncio as aioredis
from config import settings
from db.models import CustomLoanOfficerMapping, TotalExpertToken, User
from fastapi import HTT<PERSON>Ex<PERSON>, status
from httpx import AsyncClient, HTTPStatusError
from loguru import logger
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession
from tools.utils.redis import get_redis_client
from utils.division import get_assistant_division_credentials

# Redis key prefix for Total Expert tokens
TE_TOKEN_KEY_PREFIX = "te_token:"
TE_ACCESS_TOKEN_EXPIRY = 3600  # 1 hour in seconds
TE_ACCESS_TOKEN_THRESHOLD = 300  # 5 minutes in seconds

TE_REFRESH_TOKEN_EXPIRY = 1209600  # 14 days default
TE_REFRESH_TOKEN_THRESHOLD = 691200  # 8 days default


class TotalExpertTokenManager:
    def __init__(self, session: AsyncSession, redis_client: aioredis.Redis | None = None):
        self.session = session
        self.client = AsyncClient(timeout=None)
        self.redis_client = redis_client

    async def initialize(self):
        """Initialize Redis client if not provided"""
        if not self.redis_client:
            self.redis_client = await get_redis_client()

    def _get_redis_key(self, user_id: UUID, assistant_id: str) -> str:
        """Get Redis key for storing token"""
        return f"{TE_TOKEN_KEY_PREFIX}{user_id}:{assistant_id}"

    async def clear_redis_tokens(self, user_id: UUID | None = None, assistant_id: str | None = None):
        """Clear tokens from Redis, optionally filtered by user_id and/or assistant_id"""
        if not self.redis_client:
            await self.initialize()

        if user_id and assistant_id:
            # Clear specific token
            redis_key = self._get_redis_key(user_id, assistant_id)
            await self.redis_client.delete(redis_key)
        else:
            # Clear all tokens
            pattern = f"{TE_TOKEN_KEY_PREFIX}*"
            keys = await self.redis_client.keys(pattern)
            if keys:
                await self.redis_client.delete(*keys)

    async def get_oauth_te_token(self, user_id: UUID, assistant_id: str) -> TotalExpertToken | None:
        """Get token from Redis or database"""
        # Try Redis first
        if not self.redis_client:
            await self.initialize()

        redis_key = self._get_redis_key(user_id, assistant_id)
        if redis_key:
            token_data = await self.redis_client.get(redis_key)
            if token_data:
                token = token_data.decode() if isinstance(token_data, bytes) else token_data
                try:
                    token_dict = json.loads(token)
                    return token_dict
                except json.JSONDecodeError:
                    logger.warning("Found token in old format in Redis, clearing it")
                    await self.clear_redis_tokens(user_id, assistant_id)

        # Not in Redis, try database
        query = select(TotalExpertToken).where(
            TotalExpertToken.user_id == user_id, TotalExpertToken.assistant_id == assistant_id
        )
        result = await self.session.execute(query)
        token = result.scalar_one_or_none()

        if token:
            try:
                refreshed_token = await self.refresh_access_token_if_needed(token)
                if refreshed_token:
                    # Store in Redis for future use
                    redis_key = self._get_redis_key(user_id, assistant_id)
                    token_dict = {
                        "user_id": str(refreshed_token.user_id),
                        "assistant_id": refreshed_token.assistant_id,
                        "access_token": refreshed_token.access_token,
                        "refresh_token": refreshed_token.refresh_token,
                        "token_type": refreshed_token.token_type,
                        "expires_at": refreshed_token.expires_at.isoformat(),
                        "refresh_token_expires_at": refreshed_token.refresh_token_expires_at.isoformat(),
                        "id": str(refreshed_token.id),
                        "created_at": refreshed_token.created_at.isoformat(),
                        "updated_at": refreshed_token.updated_at.isoformat(),
                    }
                    await self.redis_client.setex(redis_key, TE_ACCESS_TOKEN_EXPIRY, json.dumps(token_dict))
                    return refreshed_token
            except Exception as e:
                logger.error(f"Error refreshing token in get_oauth_te_token: {str(e)}")
                # If refresh fails, return the original token
                return token

        return token

    async def save_token(
        self,
        user_id: UUID,
        assistant_id: str,
        access_token: str,
        refresh_token: str,
        expires_in: int,
        token_type: str = "Bearer",
        refresh_token_expires_at: int = TE_REFRESH_TOKEN_EXPIRY,
    ) -> TotalExpertToken:
        """Save new token to database and Redis"""
        expires_at = datetime.utcnow() + timedelta(seconds=expires_in)
        refresh_token_expires_at = datetime.utcnow() + timedelta(seconds=refresh_token_expires_at)

        # Create or update token in database
        query = select(TotalExpertToken).where(
            TotalExpertToken.user_id == user_id, TotalExpertToken.assistant_id == assistant_id
        )
        result = await self.session.execute(query)
        token = result.scalar_one_or_none()

        if token:
            # Update existing token
            token.access_token = access_token
            token.refresh_token = refresh_token
            token.token_type = token_type
            token.expires_at = expires_at
            token.refresh_token_expires_at = refresh_token_expires_at
        else:
            # Create new token
            token = TotalExpertToken(
                user_id=user_id,
                assistant_id=assistant_id,
                access_token=access_token,
                refresh_token=refresh_token,
                token_type=token_type,
                expires_at=expires_at,
                refresh_token_expires_at=refresh_token_expires_at,
            )
            self.session.add(token)

        await self.session.commit()
        await self.session.refresh(token)

        # Store in Redis
        redis_key = self._get_redis_key(user_id, assistant_id)
        token_dict = {
            "user_id": str(token.user_id),
            "assistant_id": token.assistant_id,
            "access_token": token.access_token,
            "refresh_token": token.refresh_token,
            "token_type": token.token_type,
            "expires_at": token.expires_at.isoformat(),
            "refresh_token_expires_at": token.refresh_token_expires_at.isoformat(),
            "id": str(token.id),
            "created_at": token.created_at.isoformat(),
            "updated_at": token.updated_at.isoformat(),
        }
        await self.redis_client.setex(redis_key, TE_ACCESS_TOKEN_EXPIRY, json.dumps(token_dict))

        return token

    async def refresh_access_token_if_needed(self, token: TotalExpertToken) -> TotalExpertToken | None:
        if not token:
            raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="No token found")

        # Check if access token needs refresh
        time_to_expiry = (token.expires_at - datetime.utcnow()).total_seconds()
        if time_to_expiry > TE_ACCESS_TOKEN_THRESHOLD:
            return token

        try:
            # Call Total Expert API to refresh access token
            new_token_data = await self.refresh_token(token.assistant_id, token.refresh_token)
            if not new_token_data:
                raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Failed to refresh token")

            return await self.save_token(
                user_id=token.user_id,
                assistant_id=token.assistant_id,
                access_token=new_token_data["access_token"],
                refresh_token=new_token_data["refresh_token"],
                expires_in=new_token_data["expires_in"],
                token_type=new_token_data.get("token_type", "Bearer"),
                refresh_token_expires_at=TE_REFRESH_TOKEN_EXPIRY,
            )

        except Exception as e:
            logger.error(f"Error refreshing token: {str(e)}")
            raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Failed to refresh token")

    async def refresh_token_if_needed(self, token: TotalExpertToken) -> TotalExpertToken:
        """Check if token needs refresh and refresh if needed"""
        if not token:
            raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="No token found")

        # Check if refresh token needs refresh
        time_to_expiry = (token.refresh_token_expires_at - datetime.utcnow()).total_seconds()
        if time_to_expiry > TE_REFRESH_TOKEN_THRESHOLD:
            logger.info(f"Refresh Token for user {token.user_id} is not old enough to refresh.")
            return token

        try:
            # Call Total Expert API to refresh token
            new_token_data = await self.refresh_token(token.assistant_id, token.refresh_token)

            # Save new token
            return await self.save_token(
                user_id=token.user_id,
                assistant_id=token.assistant_id,
                access_token=new_token_data["access_token"],
                refresh_token=new_token_data["refresh_token"],
                expires_in=new_token_data["expires_in"],
                token_type=new_token_data.get("token_type", "Bearer"),
                refresh_token_expires_at=TE_REFRESH_TOKEN_EXPIRY,
            )

        except Exception as e:
            logger.error(f"Error refreshing token: {str(e)}")
            raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Failed to refresh token")

    async def delete_token(self, user_id: UUID, assistant_id: str) -> None:
        """Delete token from database and Redis"""
        # Delete from database
        query = select(TotalExpertToken).where(
            TotalExpertToken.user_id == user_id, TotalExpertToken.assistant_id == assistant_id
        )
        result = await self.session.execute(query)
        token = result.scalar_one_or_none()

        if token:
            await self.session.delete(token)
            await self.session.commit()

        # Delete from Redis
        redis_key = self._get_redis_key(user_id, assistant_id)
        await self.redis_client.delete(redis_key)

    @staticmethod
    def generate_state() -> str:
        """Generate a random state value for OAuth security"""
        return "".join(random.choices(string.ascii_letters + string.digits, k=16))

    async def _decode_data(self, response) -> dict | None:
        try:
            response_text = response.text
            response_data = json.loads(response_text)
        except json.JSONDecodeError as e:
            logger.error(f"JSON Decode error when decoding output from total expert: {e}")
            try:
                response_text = response.content.decode("utf-8", errors="ignore")
                response_data = json.loads(response_text)
            except Exception as e:
                logger.error(f"Some error occurred while converting total expert response to JSON: {e}")
                return None

        return response_data

    async def get_access_token(self, code: str, assistant_id: str) -> dict | None:
        """
        Exchange authorization code for access token

        Args:
            assistant_id (str): The assistant ID
            code (str): The authorization code received from OAuth callback

        Returns:
            dict | None: Token data containing access_token, refresh_token, etc. or None if failed
        """
        try:
            te_client_id, te_client_secret = await self.get_te_credentials(assistant_id)
            data = {
                "client_id": te_client_id,
                "client_secret": te_client_secret,
                "code": code,
                "redirect_uri": settings.TOTAL_EXPERT_REDIRECT_URI,
                "grant_type": "authorization_code",
            }

            response = await self.client.post(settings.TOTAL_EXPERT_TOKEN_URL, data=data)
            response.raise_for_status()

            token_data = await self._decode_data(response)
            if not token_data:
                logger.error("Failed to decode token response")
                return None

            logger.info("Successfully obtained access token")
            return token_data

        except HTTPStatusError as e:
            logger.error(f"Error getting access token. Status Code: {e.response.status_code}")
            logger.error(f"Response: {e.response.text}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error getting access token: {e}")
            return None

    async def refresh_token(self, assistant_id: str, refresh_token: str) -> dict | None:
        """
        Refresh access token using refresh token

        Args:
            assistant_id (str): Assistant ID
            refresh_token (str): The refresh token to use

        Returns:
            dict | None: New token data or None if failed
        """
        try:
            te_client_id, te_client_secret = await self.get_te_credentials(assistant_id)
            data = {
                "client_id": te_client_id,
                "client_secret": te_client_secret,
                "refresh_token": refresh_token,
                "grant_type": "refresh_token",
            }

            response = await self.client.post(settings.TOTAL_EXPERT_TOKEN_URL, data=data)
            response.raise_for_status()

            token_data = await self._decode_data(response)
            if not token_data:
                logger.error("Failed to decode refresh token response")
                return None

            logger.info("Successfully refreshed access token")
            return token_data

        except HTTPStatusError as e:
            logger.error(f"Error refreshing token. Status Code: {e.response.status_code}")
            logger.error(f"Response: {e.response.text}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error refreshing token: {e}")
            return None

    async def get_te_credentials(self, assistant_id: str):
        credentials = await get_assistant_division_credentials(assistant_id)
        if credentials and credentials.get("total_expert_client_id") and credentials.get("total_expert_client_secret"):
            total_expert_client_id = credentials.get("total_expert_client_id")
            total_expert_client_secret = credentials.get("total_expert_client_secret")
        else:
            total_expert_client_id = settings.TOTAL_EXPERT_CLIENT_ID
            total_expert_client_secret = settings.TOTAL_EXPERT_CLIENT_SECRET
        return total_expert_client_id, total_expert_client_secret

    async def generate_auth_url(self, assistant_id: str) -> str:
        te_client_id, te_client_secret = await self.get_te_credentials(assistant_id)

        if settings.TOTAL_EXPERT_SERVICE_PROVIDER:
            auth_url = f"{settings.TOTAL_EXPERT_SSO_AUTH_URL}?" + urlencode(
                {
                    "client_id": te_client_id,
                    "response_type": "code",
                    "scope": settings.TOTAL_EXPERT_SCOPE,
                    "state": assistant_id,
                    "serviceProvider": settings.TOTAL_EXPERT_SERVICE_PROVIDER,
                }
            )
        else:
            auth_url = f"{settings.TOTAL_EXPERT_SSO_AUTH_URL}?" + urlencode(
                {
                    "client_id": te_client_id,
                    "response_type": "code",
                    "scope": settings.TOTAL_EXPERT_SCOPE,
                    "state": assistant_id,
                }
            )
        return auth_url

    async def get_auth_url(self, user: User, assistant_id: str, session: AsyncSession) -> dict:
        """
        Get Total Expert SSO URL or check if user is already authenticated.

        Args:
            session (AsyncSession): Database session
            user (User): The authenticated user object
            assistant_id (str): The assistant's ID

        Returns:
            dict: Either the SSO URL or a success status
        """
        try:
            user_id = user.id
            if not settings.ENABLE_TOTAL_EXPERT_OAUTH:
                return {"authenticated": True, "message": "User is authenticated with Total Expert"}

            query = select(CustomLoanOfficerMapping).where(
                CustomLoanOfficerMapping.email == user.email, CustomLoanOfficerMapping.assistant_id == assistant_id
            )
            result = await session.execute(query)
            existing_mapping = result.scalars().all()
            if existing_mapping:
                return {"authenticated": True, "message": "User is Impersonate authenticated"}

            # Try to get existing token
            token = await self.get_oauth_te_token(user_id, assistant_id)

            if isinstance(token, dict):
                try:
                    # token_dict = json.loads(token)
                    token_dict = token
                    # Convert string dates back to datetime objects
                    for date_field in ["expires_at", "refresh_token_expires_at", "created_at", "updated_at"]:
                        if date_field in token_dict and isinstance(token_dict[date_field], str):
                            token_dict[date_field] = datetime.fromisoformat(
                                token_dict[date_field].replace("Z", "+00:00")
                            )
                    # Convert UUID string back to UUID object
                    if "user_id" in token_dict and isinstance(token_dict["user_id"], str):
                        token_dict["user_id"] = UUID(token_dict["user_id"])
                    token = type("Token", (), token_dict)
                    refresh_token_expires_at = token_dict.get("refresh_token_expires_at")
                except json.JSONDecodeError as e:
                    logger.error(f"Error decoding token from Redis: {e}")
                    # If we can't decode the token, treat it as invalid
                    token = None
                    refresh_token_expires_at = None
            elif token:
                refresh_token_expires_at = token.refresh_token_expires_at

            if token and refresh_token_expires_at:
                # Check if refresh token is still valid
                if isinstance(refresh_token_expires_at, datetime):
                    time_to_expiry = (refresh_token_expires_at - datetime.utcnow()).total_seconds()
                    if time_to_expiry > TE_REFRESH_TOKEN_THRESHOLD:
                        return {"authenticated": True, "message": "User is already authenticated with Total Expert"}

            # If we get here, either token is None or refresh token is expired
            # state = self.generate_state()

            auth_url = await self.generate_auth_url(assistant_id)

            return {"authenticated": False, "auth_url": auth_url, "message": "Authentication required"}

        except Exception as e:
            logger.error(f"Error generating Total Expert auth URL: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to generate Total Expert authentication URL",
            )
