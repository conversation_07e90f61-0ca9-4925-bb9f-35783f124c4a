import re

import pandas as pd
from hooks.s3 import default_s3_hook
from loguru import logger

CAR_GURU_FIELD_NAME_MAPPING = {
    "Year": "Year",
    "Make": "Make",
    "Model": "Model",
    "Body type": "Body type",
    "Doors": "Doors",
    "Drivetrain": "Drivetrain",
    "Engine": "Engine",
    "Exterior color": "Exterior color",
    "Mileage": "Mileage",
    "Fuel type": "Fuel type",
    "Transmission": "Transmission",
    "Price": "Price",
}
DISPLAY_ORDER = list(CAR_GURU_FIELD_NAME_MAPPING.keys())


class CarGuruUtils:
    def __init__(self):
        self.s3_hook = default_s3_hook

    def retreive_df(self, object_name: str) -> pd.DataFrame:
        try:
            logger.info(f"Downloading CSV from {object_name}")
            presigned_url = self.s3_hook.get_presigned_url_for_download(object_name=object_name)
            key = self.s3_hook.get_key_from_url(presigned_url)

            csv_data = self.s3_hook.download_and_read_csv_with_pandas(key=key)

            if csv_data is None or csv_data.empty:
                logger.warning(f"Downloaded CSV is empty: {key}")
                return pd.DataFrame()

            logger.info(f"Successfully downloaded CSV with {len(csv_data)} rows")
            df_temp = self.clean_df(csv_data)
            return df_temp

        except Exception as e:
            logger.error(f"Failed to download and read CSV: {e}")
            import traceback

            logger.error(f"Stack trace: {traceback.format_exc()}")
            raise

    def clean_df(self, df):
        """Clean and prepare the DataFrame for querying"""
        df = df.copy()

        logger.debug(f"Cleaning DataFrame with {len(df)} rows")

        # Ensure Year is an integer
        if "Year" in df.columns:
            df["Year"] = pd.to_numeric(df["Year"], errors="coerce")
            df["Year"] = df["Year"].fillna(0).astype(int)

        # Clean and convert Price to numeric
        if "Price" in df.columns:
            df["Price"] = df["Price"].astype(str).str.replace("$", "").str.replace(",", "").str.strip()
            df["Price"] = pd.to_numeric(df["Price"], errors="coerce")

        # Extract numeric values from MPG
        df["MPG_Value"] = df["Mileage"].apply(self.extract_mpg_value)

        # Extract horsepower from Engine
        df["HP_Value"] = df["Engine"].apply(self.extract_hp_value)

        logger.debug(
            f"Cleaned DataFrame, MPG range: {df['MPG_Value'].min()}-{df['MPG_Value'].max()}, "
            f"HP range: {df['HP_Value'].min()}-{df['HP_Value'].max()}"
        )

        return df

    def extract_mpg_value(self, mileage_str):
        """Extract numeric MPG value from strings like '30 MPG'"""
        try:
            if pd.isna(mileage_str) or not isinstance(mileage_str, str) or mileage_str.strip() == "":
                return 0
            if "MPG" in mileage_str:
                mpg_part = mileage_str.split("MPG")[0].strip()
                mpg_digits = "".join(c for c in mpg_part if c.isdigit())
                if mpg_digits:
                    return int(mpg_digits)
            return 0
        except Exception as e:
            logger.warning(f"Error extracting MPG value from '{mileage_str}': {e}")
            return 0

    def extract_hp_value(self, engine_str):
        """Extract horsepower value from strings like '190 hp 1.5L I4'"""
        try:
            if pd.isna(engine_str) or not isinstance(engine_str, str):
                return 0

            # If engine contains "hp", extract the horsepower value
            if "hp" in engine_str.lower():
                hp_part = engine_str.split("hp")[0].strip()
                hp_digits = "".join(c for c in hp_part if c.isdigit())
                if hp_digits:
                    return int(hp_digits)

            # For engines without explicit hp, try to estimate from engine size
            engine_size_match = re.search(r"(\d+\.?\d*)L", engine_str)
            if engine_size_match:
                engine_size = float(engine_size_match.group(1))
                # Rough estimate based on engine size
                return int(engine_size * 100)

            return 0
        except Exception as e:
            logger.warning(f"Error extracting HP value from '{engine_str}': {e}")
            return 0


car_guru = CarGuruUtils()
