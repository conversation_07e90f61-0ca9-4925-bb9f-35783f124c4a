import asyncio
import json

from config import settings
from hooks.pinecone_hook import get_generic_hook
from hooks.splade_hook import splade_embedder
from hooks.voyage_hook import voyage_embedder
from loguru import logger
from pinecone import SparseValues


async def get_context(text_queries: list[str], assistant_id: str, namespace: str, categories: list):
    """
    Retrieve and combine relevant documents from multiple queries in parallel,
    prioritizing feedback documents (question_answer and question types).
    Both feedback and general queries are executed in parallel for each search query.

    Args:
        text_queries: List of search queries to process
        assistant_id: ID of the assistant making the request
        namespace: Vector DB namespace to search in
        categories: Optional list of categories to filter by

    Returns:
        Dictionary containing combined matches with feedback documents prioritized
    """
    # Track unique documents across all queries
    all_matches = {}
    generic_hook = await get_generic_hook(assistant_id)

    async def process_query(text):
        query_results = {}

        # Get dense embeddings
        embs = await voyage_embedder.get_embedding(texts=text, input_type="query")
        embs = embs[0]

        # Get sparse embeddings
        sparse_embs = await splade_embedder.encode_queries(text)
        sparse = SparseValues(indices=sparse_embs["indices"], values=sparse_embs["values"])

        # Create filters
        base_filter = {"categories": {"$in": categories}} if categories else {}
        feedback_filter = {"type": {"$in": ["question_answer", "question"]}}
        if base_filter:
            feedback_filter.update(base_filter)

        # Execute both feedback and general queries in parallel
        general_top_k = 20 if settings.RERANKER_IMPLEMENTATION else 5

        # Create two query tasks and execute them concurrently
        feedback_task = generic_hook.query_document(
            vector=embs,
            sparse_vector=sparse,
            top_k=3,
            namespace=namespace,
            filter=feedback_filter,
        )

        general_task = generic_hook.query_document(
            vector=embs,
            sparse_vector=sparse,
            top_k=general_top_k,
            namespace=namespace,
            filter=base_filter,
        )

        # Wait for both queries to complete
        feedback_matches, general_matches = await asyncio.gather(feedback_task, general_task)

        # Track feedback document IDs
        feedback_ids = set()

        # Add feedback matches to results first (priority)
        for match in feedback_matches["matches"]:
            match_id = match["id"]
            feedback_ids.add(match_id)
            query_results[match_id] = match

        # Add general matches that aren't duplicates
        for match in general_matches["matches"]:
            match_id = match["id"]

            # Skip if we already have this from feedback matches
            if match_id in feedback_ids:
                continue

            # Add to results if it's not already there
            if match_id not in query_results:
                query_results[match_id] = match

        return query_results

    # Process all queries in parallel
    query_tasks = [process_query(text) for text in text_queries]
    query_results_list = await asyncio.gather(*query_tasks)

    # Merge results from all queries, keeping the highest scoring duplicates
    for query_results in query_results_list:
        for match_id, match in query_results.items():
            if match_id in all_matches:
                if match.get("score", 0) > all_matches[match_id].get("score", 0):
                    all_matches[match_id] = match
            else:
                all_matches[match_id] = match

    # Convert matches dictionary to list
    combined_results = {"matches": list(all_matches.values())}

    return combined_results


async def get_filtered_matches(matches, user_query):
    """
    Filters contexts to find the most relevant contexts.
    """
    from hooks.openai_hook import oai_hook

    try:
        prompt = (
            f"Given the query: ```{user_query}```, determine which of the following contexts can"
            " actually answer the query **fully on its own**.\n\n"
            + "\n".join([f"{i+1}: {context.get('metadata').get('text')}" for i, context in enumerate(matches)])
            + "\n\nPlease follow these instructions carefully:\n"
            + "1. For each context, evaluate if the context **can answer the query completely by itself or by combining contexts** without "  # noqa: E501
            "requiring external information, references to other sections, chapters, or documents.\n"
            + "2. **If a context cannot fully answer the query on its own**, exclude it immediately. Do not include "
            "any context that is incomplete or requires assumptions or additional references.\n"
            + "3. Rank the relevant contexts by their relevance to answering the query based **only on the "
            "information explicitly stated in the context**.\n"
            + "4. Provide the output in a structured JSON format as follows:\n"
            + '```{"relevant_contexts": [2, 1, 3, ... k]}```'
        )

        response_format = {
            "type": "json_schema",
            "json_schema": {
                "name": "filtered_response",
                "schema": {
                    "type": "object",
                    "properties": {
                        "relevant_contexts": {"type": "array", "items": {"type": "integer"}},
                    },
                    "required": ["relevant_contexts"],
                    "additionalProperties": False,
                },
                "strict": True,
            },
        }

        response = await oai_hook.create_completion(
            messages=[{"role": "user", "content": prompt}],
            model="o3-mini",
            temperature=0,
            response_format=response_format,
        )
        if not response:
            return []
        response = json.loads(response)
        relevant_indices = [int(index) - 1 for index in response["relevant_contexts"]]
        return [matches[i] for i in relevant_indices]
    except Exception as e:
        logger.error(f"An error occurred while filtering context: {e}")
        return []


async def remove_duplicate_documents(matches):
    """
    Remove duplicate documents based on qa_id.
    If there are duplicates, remove any one of them.
    """
    seen_qa_ids = set()
    filtered_matches = []
    for match in matches:
        metadata = match.get("metadata")
        qa_id = metadata.get("qa_id")
        if qa_id:
            if qa_id not in seen_qa_ids:
                seen_qa_ids.add(qa_id)
                filtered_matches.append(match)
        else:
            filtered_matches.append(match)
    return filtered_matches
