import string
from datetime import datetime
from io import BytesIO

from config import settings
from dateutil.relativedelta import relativedelta
from hooks.s3 import get_s3_hook
from httpx import AsyncClient, HTTPStatusError
from loguru import logger
from reportlab.lib.pagesizes import A4
from reportlab.lib.utils import ImageReader
from reportlab.pdfgen import canvas
from tools.templates import client_templates

from .loan import loan_utils


class SalesUtils:
    def __init__(self):
        self.client = AsyncClient(timeout=None)

    async def calculate_refinance_details(
        self,
        original_loan_amount,
        current_rate,
        current_payment,
        closed_date,
        current_term,
        option,
        closing_fees=5000,
        apr_fees=5000,
        separate_apr=False,
    ):
        # Extract refinance option fields
        new_term = option["term"] * 12
        new_rate = option["rate"]
        points = option.get("points") if option.get("points") is not None else 0.0
        mortgage_insurance_rate = (
            option.get("mortgage_insurance_rate") if option.get("mortgage_insurance_rate") is not None else 0.0
        )
        loan_type = option.get("loan_type") if option.get("loan_type") is not None else "Conventional"
        va_funding_fee = option.get("va_funding_fee") if option.get("va_funding_fee") is not None else 0.0

        # Calculate months left on the current mortgage
        date_formats = [
            "%m/%d/%Y %I:%M:%S %p",  # 02/23/2024 05:00:00 AM
            "%Y-%m-%dT%H:%M:%S.%fZ",  # 2024-09-27T17:05:07.000000Z,
            "%Y/%m/%d",  # 2024/09/27
            "%m/%d/%Y",  # 09/27/2024
            "%Y-%m-%d %H:%M:%S",  # 2024-09-27 17:05:07 FOR TE
        ]
        d1 = None
        for date_format in date_formats:
            try:
                d1 = datetime.strptime(closed_date, date_format)
            except Exception:
                continue
        if not d1:
            logger.warning(f"Could not format date: {closed_date}")
            return {
                "term": "N/A",
                "rate": "N/A",
                "payment": "N/A",
                "total_savings": "N/A",
            }
        d2 = datetime.now()
        delta = relativedelta(d2, d1)
        months_diff = delta.years * 12 + delta.months
        months_left = current_term - months_diff

        base_loan_amount = await loan_utils.remaining_balance(
            original_loan_amount, current_rate, current_payment, months_diff
        )
        points_value = (points / 100) * base_loan_amount
        new_loan_amount = (
            (
                base_loan_amount + (0.0175 * base_loan_amount)
                if loan_type == "FHA"
                else base_loan_amount + va_funding_fee if loan_type == "VA" else base_loan_amount
            )
            + points_value
            + closing_fees
        )

        mortgage_rate = 0.0 if loan_type == "VA" else mortgage_insurance_rate
        mortgage_insurance = (new_loan_amount * (mortgage_rate / 100)) / 12
        monthly_pi = await loan_utils.calculate_monthly_payment(new_loan_amount, new_rate, new_term)
        new_payment = monthly_pi + mortgage_insurance
        apr = await loan_utils.calculate_apr_rate(new_rate, apr_fees, new_loan_amount, new_term)

        total_savings = loan_utils.calculate_total_savings(current_payment, months_left, new_payment, new_term)
        monthly_savings = current_payment - new_payment

        if client_templates.APPLY_SAVINGS_LOGIC and monthly_savings > 0 and total_savings < 0:
            total_savings = ""
        else:
            total_savings = f"${total_savings:,.2f}"

        if client_templates.ROUNDOFF_UPB_TO_NEARNEST_10:
            new_loan_amount = f"${round(new_loan_amount, -1):,.2f} *"
        else:
            new_loan_amount = f"${new_loan_amount:,.2f}"

        final_data = {
            "term": f"{new_term // 12}-year fixed",
            "new_loan_amount": new_loan_amount,
            "rate": f"{new_rate:,.3f}% ({apr:,.3f}% APR)",
            "payment": f"${new_payment:,.2f}",
            "monthly_savings": f"${monthly_savings:,.2f}",
            "total_savings": total_savings,
        }
        if separate_apr:
            final_data["apr"] = f"{apr:,.3f}%"
            final_data["rate"] = f"{new_rate:,.3f}%"
        return final_data

    async def generate_refinance_options(self, loan_details, options):
        original_loan_amount = float(loan_details.get("Loan Amount"))
        current_rate = float(loan_details.get("Interest Rate"))
        current_payment = float(loan_details.get("MoPymtPI"))
        loan_term_from_LD = loan_details.get("Loan Term", 360)
        try:
            current_term = int(loan_term_from_LD) if loan_term_from_LD else 360
        except ValueError:
            current_term = 360  # fallback if non-empty but still invalid
        closed_date = loan_details.get("Fund Released Date")
        closing_fees = loan_details.get("Closing Fees", 5000)
        apr_fees = closing_fees

        current_apr = await loan_utils.calculate_apr_rate(current_rate, apr_fees, original_loan_amount, current_term)

        # Initialize refinance details as row-wise lists
        refi_options = ["", "Your Current Mortgage"]
        terms = ["Term", f"{current_term//12}-year fixed"]
        loan_amounts = ["Loan Amount", f"${original_loan_amount:,.2f}"]
        rates = ["Rate (APR)", f"{current_rate:,.3f}% ({current_apr:,.3f}% APR)"]
        payments = ["Payment (Principal & Interest)", f"${current_payment:,.2f}"]
        monthly_savings = ["Monthly Savings", "--"]
        total_savings = ["Total Savings", "--"]

        for idx, option in enumerate(options):
            # Calculate refinance details for each option
            refi_data = await self.calculate_refinance_details(
                original_loan_amount,
                current_rate,
                current_payment,
                closed_date,
                current_term,
                option,
                closing_fees,
                apr_fees,
            )
            # Append results for each refinance option
            refi_options.append(f"Option {string.ascii_uppercase[idx]}")
            terms.append(refi_data["term"])
            loan_amounts.append(refi_data["new_loan_amount"])
            rates.append(refi_data["rate"])
            payments.append(refi_data["payment"])
            monthly_savings.append(refi_data["monthly_savings"])
            total_savings.append(refi_data["total_savings"])

        refinance_details = [refi_options, terms, loan_amounts, rates, payments, monthly_savings, total_savings]
        return refinance_details

    async def get_estimated_payoff(self, loan_details):
        original_loan_amount = float(loan_details["Loan Amount"])
        current_rate = float(loan_details["Interest Rate"])
        current_payment = float(loan_details["MoPymtPI"])
        closed_date = loan_details["Fund Released Date"]

        # Calculate months elapsed on current mortage
        date_formats = [
            "%m/%d/%Y %I:%M:%S %p",  # 02/23/2024 05:00:00 AM
            "%Y-%m-%dT%H:%M:%S.%fZ",  # 2024-09-27T17:05:07.000000Z,
            "%Y/%m/%d",  # 2024/09/27
            "%m/%d/%Y",  # 09/27/2024
            "%Y-%m-%d %H:%M:%S",  # 2024-09-27 17:05:07 FOR TE
        ]
        d1 = None
        for date_format in date_formats:
            try:
                d1 = datetime.strptime(closed_date, date_format)
            except Exception:
                continue
        if not d1:
            logger.warning(f"Could not format date: {closed_date}")
        d2 = datetime.now()
        delta = relativedelta(d2, d1)
        months_elapsed = delta.years * 12 + delta.months

        # Calculate remaining balance on current mortgage
        estimated_payoff = await loan_utils.remaining_balance(
            original_loan_amount, current_rate, current_payment, months_elapsed
        )
        return estimated_payoff

    async def get_property_estimated_value(self, loan_details):
        street_address = str(loan_details["Address"])
        city = str(loan_details["City"])
        state = str(loan_details["State"])
        zip_code = str(loan_details["Zip Code"])
        city_state_zip = f"{city}, {state} {zip_code}"

        if not all([street_address, city, state, zip_code]):
            logger.warning("Missing address details.")
            return None

        params = {"address1": street_address, "address2": city_state_zip}
        headers = {"APIKey": settings.ATTOM_API_KEY, "accept": "application/json"}

        logger.info(
            f"Requesting estimated property value for address: {street_address}, {city}, {state}, {zip_code}..."
        )
        try:
            response = await self.client.get(settings.ATTOM_API_URL, headers=headers, params=params)
            response.raise_for_status()
        except HTTPStatusError as e:
            logger.error(f"Request failed with status {e.response.status_code}.")
            return None

        response_data = response.json()
        if not response_data:
            logger.error("No response data.")
            return None

        try:
            property_data = response_data.get("property", [])[0]
            avm = property_data.get("avm", {}).get("amount", {})
            estimated_value = avm.get("value")
            logger.info(f"Successfully retrieved property valuation details: {estimated_value}")
            return estimated_value
        except KeyError as e:
            logger.error(f"Error parsing response data: {e}")
            return None

    async def calculate_cashout_details(
        self,
        original_loan_amount,
        current_payment,
        option,
        estimated_payoff,
        debt_amount=0,
        debt_payment=None,
        desired_cashout=0,
        closing_fees=5000,
        apr_fees=5000,
    ):
        # Extract cashout option fields
        new_term = option["term"] * 12
        new_rate = option["rate"]
        points = option.get("points") if option.get("points") is not None else 0.0
        mortgage_insurance_rate = (
            option.get("mortgage_insurance_rate") if option.get("mortgage_insurance_rate") is not None else 0.0
        )
        loan_type = option.get("loan_type") if option.get("loan_type") is not None else "Conventional"
        va_funding_fee = option.get("va_funding_fee") if option.get("va_funding_fee") is not None else 0.0

        base_loan_amount = (
            estimated_payoff + debt_amount + desired_cashout if debt_payment else estimated_payoff + desired_cashout
        )
        points_value = (points / 100) * base_loan_amount
        new_loan_amount = (
            (
                base_loan_amount + (0.0175 * base_loan_amount)
                if loan_type == "FHA"
                else base_loan_amount + va_funding_fee if loan_type == "VA" else base_loan_amount
            )
            + points_value
            + closing_fees
        )

        mortgage_rate = 0.0 if loan_type == "VA" else mortgage_insurance_rate
        mortgage_insurance = (new_loan_amount * (mortgage_rate / 100)) / 12
        monthly_pi = await loan_utils.calculate_monthly_payment(new_loan_amount, new_rate, new_term)
        new_payment = monthly_pi + mortgage_insurance
        apr = await loan_utils.calculate_apr_rate(new_rate, apr_fees, new_loan_amount, new_term)

        amount = estimated_payoff if client_templates.DISPLAY_ESTIMATED_PAYOFF else original_loan_amount
        if client_templates.ROUNDOFF_UPB_TO_NEARNEST_10:
            loan_amount = f"${round(amount, -1):,.2f} *"
        else:
            loan_amount = f"${amount:,.2f}"

        final_data = {
            "term": f"{new_term // 12}-year fixed",
            "loan_amount": loan_amount,
            "est_current_loan_amount": f"${estimated_payoff:,.2f}",
            "rate": f"{new_rate:,.3f}% ({apr:,.3f}% APR)",
            "total_loan_amount": f"${new_loan_amount:,.2f}",
            "payment": f"${new_payment:,.2f}",
        }

        if debt_payment:
            total_savings = (current_payment + debt_payment) - new_payment
            final_data.update(
                {
                    "debt_consolidation": f"${debt_amount:,.2f}",
                    "additional_cashout": f"${desired_cashout:,.2f}",
                    "monthly_other_debt": "--",
                    "total_payments": f"${new_payment:,.2f}",
                    "monthly_savings": f"${total_savings:,.2f}",
                    "projected_savings": f"${total_savings * 12:,.2f}",
                }
            )
        else:
            final_data.update({"desired_cashout_amount": f"${desired_cashout:,.2f}"})

        return final_data

    async def generate_cashout_details(
        self,
        loan_details,
        options,
        estimated_payoff,
        debt_amount=0,
        debt_payment=None,
        desired_cashout=0,
        closing_fees=5000,
        apr_fees=5000,
    ):
        original_loan_amount = float(loan_details.get("Loan Amount"))
        current_rate = float(loan_details.get("Interest Rate"))
        current_payment = float(loan_details.get("MoPymtPI"))
        loan_term_from_LD = loan_details.get("Loan Term", 360)
        try:
            current_term = int(loan_term_from_LD) if loan_term_from_LD else 360
        except ValueError:
            current_term = 360  # fallback if non-empty but still invalid

        current_apr = await loan_utils.calculate_apr_rate(current_rate, apr_fees, original_loan_amount, current_term)

        # Initialize all common fields
        loan_amount_title = client_templates.LOAN_AMOUNT_TITLE
        cashout_options = ["", "Your Current Mortgage"]
        terms = ["Term", f"{current_term // 12}-year fixed"]
        loan_amounts = [loan_amount_title, f"${original_loan_amount:,.2f}"]
        est_current_loan_amounts = ["Est. Current Loan Amount", f"${estimated_payoff:,.2f}"]
        rates = ["Rate (APR)", f"{current_rate:,.3f}% ({current_apr:,.3f}% APR)"]
        payments = ["Payment (Principal & Interest)", f"${current_payment:,.2f}"]

        # Initialize all optional fields (Avoid error in field mapping)
        debt_consolidation = ["Debt Consolidation Amount"]
        additional_cashout = ["Additional Cashout Amount"]
        monthly_other_debt = ["Monthly Other Debt"]
        total_payments = ["Total Payment"]
        monthly_savings = ["Monthly Payment Savings"]
        projected_savings = ["Projected Annual Savings"]
        desired_cashout_amount = ["Desired Cashout Amount"]

        if debt_payment:
            debt_consolidation = (
                ["Debt Consolidation Amount", "--"]
                if not client_templates.INCLUDE_DEBT_IN_TOTAL
                else ["Debt Consolidation Amount", f"${debt_amount:,.2f}"]
            )
            additional_cashout = ["Additional Cashout Amount", "--"]
            total_loan_amounts = (
                ["Total Loan Amount", f"${original_loan_amount + debt_amount:,.2f}"]
                if client_templates.INCLUDE_DEBT_IN_TOTAL
                else ["Total Loan Amount", f"${original_loan_amount:,.2f}"]
            )
            monthly_other_debt = ["Monthly Other Debt", f"${debt_payment:,.2f}"]
            total_payments = ["Total Payment", f"${current_payment + debt_payment:,.2f}"]
            monthly_savings = ["Monthly Payment Savings", "--"]
            projected_savings = ["Projected Annual Savings", "--"]
        else:
            desired_cashout_amount = ["Desired Cashout Amount", "--"]
            total_loan_amounts = ["Total Loan Amount", f"${original_loan_amount:,.2f}"]

        for idx, option in enumerate(options):
            cashout_data = await self.calculate_cashout_details(
                original_loan_amount,
                current_payment,
                option,
                estimated_payoff,
                debt_amount=debt_amount,
                debt_payment=debt_payment,
                desired_cashout=desired_cashout,
                closing_fees=closing_fees,
                apr_fees=apr_fees,
            )

            cashout_options.append(f"Option {string.ascii_uppercase[idx]}")
            terms.append(cashout_data["term"])
            loan_amounts.append(cashout_data["loan_amount"])
            est_current_loan_amounts.append(cashout_data["est_current_loan_amount"])
            rates.append(cashout_data["rate"])
            total_loan_amounts.append(cashout_data["total_loan_amount"])
            payments.append(cashout_data["payment"])

            if debt_payment:
                debt_consolidation.append(cashout_data["debt_consolidation"])
                additional_cashout.append(cashout_data["additional_cashout"])
                monthly_other_debt.append(cashout_data["monthly_other_debt"])
                total_payments.append(cashout_data["total_payments"])
                monthly_savings.append(cashout_data["monthly_savings"])
                projected_savings.append(cashout_data["projected_savings"])
            else:
                desired_cashout_amount.append(cashout_data["desired_cashout_amount"])

        field_mapping = {
            "options": cashout_options,
            "terms": terms,
            "loan_amounts": loan_amounts,
            "est_current_loan_amounts": est_current_loan_amounts,
            "rates": rates,
            "payments": payments,
            "total_loan_amounts": total_loan_amounts,
            "debt_consolidation": debt_consolidation,
            "additional_cashout": additional_cashout,
            "monthly_other_debt": monthly_other_debt,
            "total_payments": total_payments,
            "monthly_savings": monthly_savings,
            "projected_savings": projected_savings,
            "desired_cashout_amount": desired_cashout_amount,
        }

        # Determine field list from client template
        if debt_payment:
            field_list = client_templates.CASHOUT_DEBTCON_DETAILS
        else:
            field_list = client_templates.CASHOUT_DESIRED_DETAILS

        # Generate cashout details based on field list
        cashout_details = [field_mapping[field] for field in field_list]
        return cashout_details

    async def generate_purchase_offer_details(self, purchase_details):
        purchase_price = purchase_details.get("Purchase Price")
        property_tax_rate = purchase_details.get("Property Tax Rate")
        monthly_property_tax = ((property_tax_rate / 100) * purchase_price) / 12
        annual_hoi = purchase_details.get("Annual HOI")
        monthly_insurance = annual_hoi / 12

        lender_concession = purchase_details.get("Lender Concession")
        dpa_grant = purchase_details.get("DPA Grant")
        closing_fees = purchase_details.get("Closing Fees")
        apr_fees = closing_fees

        time_frame = purchase_details.get("Time Frame")
        months_elapsed = time_frame * 12
        property_appreciation_rate = purchase_details.get("Property Appreciation Rate")
        property_appreciated_value = await loan_utils.future_value(
            purchase_price, property_appreciation_rate, time_frame
        )
        investment_roi = purchase_details.get("Investment ROI")
        options = purchase_details.get("Options")

        graph_json = {}
        total_monthly_payments, down_payment_amounts = [], []

        for option in options:
            dp_percent = option["dp_percent"]
            down_payment_amount = (dp_percent / 100) * purchase_price
            down_payment_amounts.append(down_payment_amount)
            base_loan_amount = purchase_price - down_payment_amount

            loan_type = option["loan_type"]
            va_funding_fee = option["va_funding_fee"]
            loan_amount = (
                base_loan_amount + (0.0175 * base_loan_amount)
                if loan_type == "FHA"
                else base_loan_amount + va_funding_fee if loan_type == "VA" else base_loan_amount
            )

            interest_rate = option["rate"]
            term = option["term"] * 12
            mortgage_insurance_rate = 0.0 if loan_type == "VA" else option["mortgage_insurance_rate"]
            mortgage_rate = mortgage_insurance_rate if dp_percent < 20 else 0.0
            mortgage_insurance = (loan_amount * (mortgage_rate / 100)) / 12

            monthly_pi = await loan_utils.calculate_monthly_payment(loan_amount, interest_rate, term)
            total_monthly_payment = monthly_pi + monthly_property_tax + monthly_insurance + mortgage_insurance
            total_monthly_payments.append(total_monthly_payment)

        max_total_monthly_payment = max(total_monthly_payments)
        largest_down_payment = max(down_payment_amounts)

        descriptions = [""]
        down_payment_percents = ["Down Payment"]
        purchase_prices = ["Purchase Price"]
        loan_amounts = ["Loan Amount"]
        interest_rates = ["Interest Rate"]
        terms = ["Term (mos)"]
        monthly_pis = ["P&I (1st)"]
        monthly_property_taxes = ["Property Tax"]
        monthly_insurances = ["Hazard Ins"]
        mortgage_insurances = ["Mtg Insurance"]
        monthly_payments = ["Monthly Payment"]
        cash_to_closes = ["Cash to Close"]
        monthly_savings = ["Monthly Savings"]

        options_list = [""]
        home_values = ["Est. Home Value"]
        loan_balances = ["Loan Balance"]
        total_pitis = ["Total PITI"]
        investment_balances = ["Investment Bal"]
        investment_rois = ["Rate of Return"]
        returns_on_investment = ["Est. Return on Investment"]
        net_worths = ["Net Worth"]
        net_rois = ["Net ROI on Initial Cash to Close"]

        for i, option in enumerate(options):
            dp_percent = option["dp_percent"]
            term = option["term"] * 12
            interest_rate = option["rate"]
            points = option["points"]
            loan_type = option["loan_type"]
            mortgage_insurance_rate = 0.0 if loan_type == "VA" else option["mortgage_insurance_rate"]
            va_funding_fee = option["va_funding_fee"]
            desc = option["desc"]

            down_payment_amount = (dp_percent / 100) * purchase_price
            base_loan_amount = purchase_price - down_payment_amount
            loan_amount = (
                base_loan_amount + (0.0175 * base_loan_amount)
                if loan_type == "FHA"
                else base_loan_amount + va_funding_fee if loan_type == "VA" else base_loan_amount
            )
            apr_rate = await loan_utils.calculate_apr_rate(interest_rate, apr_fees, loan_amount, term)
            points_value = (points / 100) * purchase_price
            closing_costs = down_payment_amount + closing_fees + points_value - lender_concession - dpa_grant

            mortgage_rate = mortgage_insurance_rate if dp_percent < 20 else 0.0
            mortgage_insurance = (loan_amount * (mortgage_rate / 100)) / 12

            monthly_pi = await loan_utils.calculate_monthly_payment(loan_amount, interest_rate, term)
            total_monthly_payment = monthly_pi + monthly_property_tax + monthly_insurance + mortgage_insurance
            monthly_saving = max_total_monthly_payment - total_monthly_payment
            total_piti = total_monthly_payment * time_frame * 12

            remaining_loan_balance = await loan_utils.remaining_balance(
                loan_amount, interest_rate, monthly_pi, months_elapsed
            )

            investable_amount = (
                0 if down_payment_amount == largest_down_payment else (largest_down_payment - down_payment_amount)
            )
            investment_returns = await loan_utils.future_value(investable_amount, investment_roi, time_frame)
            equity = property_appreciated_value - remaining_loan_balance
            net_worth = equity + investment_returns
            net_roi = ((net_worth - closing_costs) / closing_costs) * 100 if closing_costs > 0 else float("inf")

            descriptions.append(desc)
            down_payment_percents.append(f"{dp_percent}% Down")
            purchase_prices.append(f"${round(purchase_price):,}")
            loan_amounts.append(f"${round(loan_amount):,}")
            interest_rates.append(f"{interest_rate:,.3f}% ({apr_rate:,.3f}% APR)")
            terms.append(term)
            monthly_pis.append(f"${monthly_pi:,.2f}")
            monthly_property_taxes.append(f"${monthly_property_tax:,.2f}")
            monthly_insurances.append(f"${monthly_insurance:,.2f}")
            mortgage_insurances.append(f"${mortgage_insurance:,.2f}")
            monthly_payments.append(f"${total_monthly_payment:,.2f}")
            cash_to_closes.append(f"${round(closing_costs):,}")
            monthly_savings.append(f"${monthly_saving:,.2f}")

            options_list.append(desc)
            home_values.append(f"${round(property_appreciated_value):,}")
            loan_balances.append(f"${round(remaining_loan_balance):,}")
            total_pitis.append(f"${total_piti:,.2f}")
            investment_balances.append(f"${round(investable_amount):,}")
            investment_rois.append(f"{investment_roi:,.3f}%")
            returns_on_investment.append(f"${round(investment_returns):,}")
            net_worths.append(f"${round(net_worth):,}")
            net_rois.append(f"{net_roi:,.3f}%")

            graph_json[desc] = {
                "Equity": f"${equity:,.2f}",
                "Assets": f"${investment_returns:,.2f}",
            }

        loan_table = [
            descriptions,
            down_payment_percents,
            purchase_prices,
            loan_amounts,
            interest_rates,
            terms,
            monthly_pis,
            monthly_property_taxes,
            monthly_insurances,
            mortgage_insurances,
            monthly_payments,
            cash_to_closes,
            monthly_savings,
        ]

        investment_table = [
            options_list,
            home_values,
            loan_balances,
            total_pitis,
            investment_balances,
            investment_rois,
            returns_on_investment,
            net_worths,
            net_rois,
        ]

        return loan_table, investment_table, graph_json

    async def create_open_house_flyer(self, flyer_details, loan_table):
        address = flyer_details.get("Address", "")
        city = flyer_details.get("City", "")
        state = flyer_details.get("State", "")
        zip_code = flyer_details.get("Zip Code", "")
        full_address = f"{address}, {city}, {state} {zip_code}"

        date = datetime.now().strftime("%Y-%m-%d")
        time = datetime.now().strftime("%I:%M %p")

        bedrooms_number = flyer_details.get("Bedrooms Number")
        bathrooms_number = flyer_details.get("Bathrooms Number")
        amenities = flyer_details.get("Amenities")
        status = flyer_details.get("Status")

        interest_rate = flyer_details.get("Interest Rate")
        point = flyer_details.get("Point")
        property_tax_rate = flyer_details.get("Property Tax Rate")
        annual_hoi = flyer_details.get("Annual HOI")

        agent_name = flyer_details.get("Agent Name", "N/A")
        agent_cell_phone = flyer_details.get("Agent Cell Phone", "N/A")
        company_name = flyer_details.get("Company Name", "N/A")
        company_office_phone = flyer_details.get("Company Office Phone", "N/A")

        source = flyer_details.get("Source", "N/A")
        mls_number = flyer_details.get("MLS Number", "N/A")

        pdf_buffer = BytesIO()
        image_url = flyer_details.get("Property Image")

        c = canvas.Canvas(pdf_buffer, pagesize=A4)
        width, height = A4

        # Title
        c.setFont("Helvetica-Bold", 18)
        c.drawCentredString(width / 2, height - 50, "Open House Flyer")

        # Property Image
        image = ImageReader(image_url)
        img_width = 400
        img_height = 200
        c.drawImage(image, (width - img_width) / 2, height - 270, width=img_width, height=img_height)

        # Invitation
        c.setFont("Helvetica-Bold", 16)
        c.drawCentredString(width / 2, height - 300, "You're Invited!")

        # Open House Details
        c.setFont("Helvetica", 12)
        text_lines = [
            "Join us for an Open House at:",
            f"{full_address}",
        ]

        y_position = height - 330
        for line in text_lines:
            c.drawString(20, y_position, line)
            y_position -= 18

        text_lines = [
            f"Date: {date}",
            f"Time: {time}",
            f"Location: {full_address}",
        ]

        y_position -= 12
        for line in text_lines:
            c.drawString(20, y_position, line)
            y_position -= 18

        line = "Come see this beautiful home in person and explore all it has to offer!"
        c.drawString(20, y_position - 12, line)

        # Property Features
        c.setFont("Helvetica-Bold", 14)
        c.drawString(20, y_position - 50, "Property Features:")

        c.setFont("Helvetica", 12)
        features = [
            f"- {bedrooms_number} Beds | {bathrooms_number} Baths | " + " | ".join(amenities),
            "- " + " | ".join(status),
        ]
        y_position -= 70
        for feature in features:
            c.drawString(20, y_position, feature)
            y_position -= 18

        # Affordability Table
        c.setFont("Helvetica-Bold", 14)
        c.drawString(20, y_position - 20, "Affordability Made Simple!")

        y_position -= 30
        c.setFont("Helvetica", 10)

        col_widths = [100, 90, 70, 70, 80, 70, 80]  # Different column widths
        row_height = 20
        x_start = 20
        y_start = y_position

        # Draw table borders
        for row_idx in range(len(loan_table) + 1):
            c.line(
                x_start, y_start - (row_idx * row_height), x_start + sum(col_widths), y_start - (row_idx * row_height)
            )

        x_position = x_start
        for width in col_widths:
            c.line(x_position, y_start, x_position, y_start - (row_height * len(loan_table)))
            x_position += width
        c.line(x_position, y_start, x_position, y_start - (row_height * len(loan_table)))  # Final vertical line

        # Draw table content
        for row in loan_table:
            x_position = x_start + 5
            for idx, item in enumerate(row):
                c.drawString(x_position, y_start - 15, item)
                x_position += col_widths[idx]
            y_start -= row_height

        # Assumptions
        c.setFont("Helvetica", 10)
        assumptions = [
            f"*Assumptions: 30-year fixed loan at {interest_rate}% with {point} point, property tax rate at {property_tax_rate}%, and homeowners insurance at ${annual_hoi}.",  # noqa: E501
            " Actual payments may vary.*",
        ]
        for assumption in assumptions:
            c.drawString(20, y_start - 15, assumption)
            y_start -= 10

        # Listing Agent
        c.setFont("Helvetica-Bold", 14)
        y_start -= 50
        c.drawString(20, y_start, "Listed by:")

        c.setFont("Helvetica", 12)
        agent_info = [
            f"{agent_name}",
            f"{agent_cell_phone}",
            f"{company_name}",
            f"{company_office_phone}",
        ]
        y_start -= 20
        for info in agent_info:
            c.drawString(20, y_start, info)
            y_start -= 18

        # MLS Source
        c.setFont("Helvetica", 10)
        y_start -= 10
        c.drawString(20, y_start, f"Source: {source}, MLS#: {mls_number}")

        c.save()
        pdf_buffer.seek(0)
        return pdf_buffer

    async def generate_open_house_flyer(self, assistant_id, flyer_details):
        purchase_price = flyer_details.get("Purchase Price")
        property_tax_rate = flyer_details.get("Property Tax Rate")
        monthly_property_tax = ((property_tax_rate / 100) * purchase_price) / 12
        annual_hoi = flyer_details.get("Annual HOI")
        monthly_insurance = annual_hoi / 12
        interest_rate = flyer_details.get("Interest Rate")

        table_titles = [
            "Down Payment",
            "Loan Amount",
            "Mtg. Ins.",
            "PMI",
            "Property Taxes",
            "Home Ins.",
            "Total Payment",
        ]
        down_payment_10, down_payment_20 = [], []

        down_payment_percents = [(10, down_payment_10), (20, down_payment_20)]
        for down_payment_percent, down_payment_table in down_payment_percents:
            down_payment_amount = (down_payment_percent / 100) * purchase_price
            loan_amount = purchase_price - down_payment_amount

            if down_payment_percent < 10:
                mortgage_rate = 0.5
            elif 10 <= down_payment_percent < 20:
                mortgage_rate = 0.25
            else:
                mortgage_rate = 0.0
            mortgage_insurance = (loan_amount * (mortgage_rate / 100)) / 12

            monthly_pi = await loan_utils.calculate_monthly_payment(loan_amount, interest_rate, 360)
            total_monthly_payment = monthly_pi + monthly_property_tax + monthly_insurance + mortgage_insurance

            down_payment_table.extend(
                [
                    f"{down_payment_percent}% (${down_payment_amount:,.2f})",
                    f"${loan_amount:,.2f}",
                    f"${mortgage_insurance:,.2f}",
                    f"${monthly_pi:,.2f}",
                    f"${monthly_property_tax:,.2f}",
                    f"${monthly_insurance:,.2f}",
                    f"${total_monthly_payment:,.2f}",
                ]
            )

        loan_table = [table_titles, down_payment_10, down_payment_20]
        pdf_buffer = await self.create_open_house_flyer(flyer_details, loan_table)
        s3_hook = await get_s3_hook(assistant_id)
        pdf_url = s3_hook.upload_file("open_house_flyer/open_house_flyer.pdf", pdf_buffer)
        return pdf_url

    async def close(self):
        await self.client.aclose()


sales_utils = SalesUtils()
