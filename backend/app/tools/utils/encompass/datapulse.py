import copy
from datetime import datetime, timedelta

import pandas as pd
from clients import client_config
from db.models import DataPulseReport
from db.session import session_manager
from fastapi import HTT<PERSON>Ex<PERSON>, status
from httpx import AsyncClient, HTTPStatusError
from loguru import logger
from schema.encompass import UpdateDataPulseReport
from sqlalchemy import select, update
from tools.functions.encompass import constants, loan_function
from tools.payloads import get_restricted_terms
from tools.utils.encompass.loan import loan_utils


class DataPulseUtils:

    def __init__(self) -> None:
        self.client = AsyncClient(timeout=None)
        self.encompass_url = constants.LOAN_PIPELINE_URL

    date_filter_map = {
        "_7daysago": lambda: (datetime.now() - timedelta(days=7)).date().isoformat(),
        "_currentdate": lambda: datetime.now().date().isoformat(),
        "_7daysfromnow": lambda: (datetime.now() + timedelta(days=7)).date().isoformat(),
        "_30daysfromnow": lambda: (datetime.now() + timedelta(days=30)).date().isoformat(),
    }

    async def adjust_report_order(self, report: DataPulseReport, new_order: int, session) -> None:
        if new_order < report.order:
            await session.execute(
                update(DataPulseReport)
                .where(DataPulseReport.order >= new_order)
                .where(DataPulseReport.order < report.order)
                .values(order=DataPulseReport.order + 1)
            )
        elif new_order > report.order:
            await session.execute(
                update(DataPulseReport)
                .where(DataPulseReport.order <= new_order)
                .where(DataPulseReport.order > report.order)
                .values(order=DataPulseReport.order - 1)
            )
        return None

    async def update_report(self, report_id: str, update_data: UpdateDataPulseReport, assistant_id: str) -> dict:
        """Edit a report by report ID with the provided update data."""
        async with session_manager() as session:
            result = await session.execute(
                select(DataPulseReport).where(
                    (DataPulseReport.id == report_id) & (DataPulseReport.assistant_id == assistant_id)
                )
            )
            report = result.scalars().first()

            if not report:
                logger.info(f"Report with ID {report_id} not found")
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="No report found with the provided ID. Make sure you’re using the correct one.",
                )

            allowed_fields = {
                "name",
                "description",
                "payload",
                "primary_display_fields",
                "secondary_display_fields",
                "is_visible",
                "order",
                "ttl",
            }

            updated_fields = []
            update_dict = update_data.model_dump(exclude_unset=True)

            new_order = update_dict.get("order")
            if new_order is not None and new_order != report.order:
                await self.adjust_report_order(report, new_order, session)
                updated_fields.append("order")

            for field, value in update_dict.items():
                if field != "order" and field in allowed_fields and hasattr(report, field):
                    setattr(report, field, value)
                    updated_fields.append(field)

            if not updated_fields:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="No valid fields provided for update.",
                )

            if new_order is not None:
                report.order = new_order

            try:
                await session.commit()
                await session.refresh(report)

                return {**report.__dict__, "updated_fields": updated_fields}

            except Exception as e:
                await session.rollback()
                logger.error(f"Error updating report {report_id}: {e}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Failed to update report. Please try again later.",
                )

    async def get_reports_from_db(self, assistant_id: str) -> list:
        async with session_manager() as session:
            result = await session.execute(
                select(DataPulseReport)
                .where(DataPulseReport.assistant_id == assistant_id)
                .order_by(DataPulseReport.order)
            )
            reports = result.scalars().all()

            return [report.__dict__ for report in reports]

    def parse_payload(self, report, start_date, end_date):
        """
        Parses the payload to replace date filters with actual dates.
        """

        payload = copy.deepcopy(report.get("payload"))

        if report.get("requires_custom_date", False):
            try:
                start_date = start_date or datetime.now().replace(day=1).date().isoformat()
                end_date = end_date or datetime.now().date().isoformat()
                datetime.fromisoformat(start_date)
                datetime.fromisoformat(end_date)
            except ValueError:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Invalid date format. Dates must be in ISO format (YYYY-MM-DD).",
                )

            if start_date > end_date:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Start date cannot be greater than end date.",
                )

            if "filter" in payload and "terms" in payload["filter"]:
                for term in payload["filter"]["terms"]:
                    if term.get("value") == "_custom_startdate":
                        term["value"] = start_date

                    elif term.get("value") == "_custom_enddate":
                        term["value"] = end_date

        else:
            if "filter" in payload and "terms" in payload["filter"]:
                for term in payload["filter"]["terms"]:
                    value = term.get("value")
                    if value in self.date_filter_map:
                        term["value"] = self.date_filter_map[value]()

        return payload, start_date, end_date

    async def get_cache_key(self, assistant_id, user, report_id, start_date=None, end_date=None):
        """Get the cache key for the report."""
        loan_officer = await loan_utils.get_loan_officer_details(
            assistant_id=assistant_id,
            user=user,
        )
        reports = await self.get_reports_from_db(assistant_id=assistant_id)

        try:
            report = list(filter(lambda r: r.get("id") == report_id, reports))[0]
        except Exception as e:
            logger.info(f"Report with ID {report_id} not found: {e}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Report not found.",
            )

        report_id = report.get("id")
        payload, start_date, end_date = self.parse_payload(report, start_date, end_date)

        key_parts = [report_id, assistant_id, user.email]

        if loan_officer:
            key_parts.append(loan_officer.get("loan_id"))

        if report.get("requires_custom_date", False):
            if start_date:
                key_parts.append(f"start_{start_date}")
            if end_date:
                key_parts.append(f"end_{end_date}")

        return "_".join(key_parts)

    async def fetch_loan_data(self, assistant_id, user, payload):
        """Fetch loan data from the Encompass API."""
        token = await loan_utils.get_impersonation_auth_token(assistant_id=assistant_id, user=user)

        if not token:
            logger.error("Failed to get impersonation token.")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Could not verify user. Please make sure your email "
                "address match the email address in your loan account.",
            )

        headers = {"Content-Type": "application/json", "Authorization": token}
        params_initial = {"cursortype": "randomAccess", "limit": 1}

        lo_details = {}
        if client_config.ENCOMPASS_LOAN_IMPERSONATION_RESTRICTION and not user.encompass_impersonation_access:
            lo_details = await loan_utils.get_loan_officer_details(
                assistant_id=assistant_id,
                user=user,
            )

        payload["filter"]["terms"].extend(get_restricted_terms(**lo_details))

        try:
            initial_response = await self.client.post(
                self.encompass_url, headers=headers, json=payload, params=params_initial
            )
            initial_response.raise_for_status()
            loans_count = int(initial_response.headers.get("x-total-count", 0))
            cursor_id = initial_response.headers.get("x-cursor")
        except HTTPStatusError as e:
            logger.error(f"Request failed with status {e.response.status_code}: {e.response.text}")
            raise HTTPException(
                status_code=e.response.status_code, detail="Failed to fetch loan details. Please try again."
            )

        all_response_data = []
        start = 0
        while len(all_response_data) < loans_count:
            try:
                params = {"cursor": cursor_id, "limit": 1000, "start": start}
                response = await self.client.post(
                    self.encompass_url, headers=headers, json={"fields": payload.get("fields")}, params=params
                )
                response.raise_for_status()
                batch_data = await loan_function._decode_loan_details(response)
                all_response_data.extend(batch_data)
                start += len(batch_data)

            except HTTPStatusError as e:
                logger.error(f"Request failed with status {e.response.status_code}: {e.response.text}")
                raise HTTPException(
                    status_code=e.response.status_code, detail="Failed to fetch loan details. Please try again."
                )

        return all_response_data

    async def process_loan_data(self, all_response_data, FIELD_MAPPING, FIELD_TYPE_MAPPING):
        """Process loan data and map fields."""
        loan_details = []
        for record in all_response_data:
            fields = record.get("fields", {})
            mapped_fields = {
                FIELD_MAPPING.get(k): await loan_function.format_encompass_data(
                    k, v, field_type_mapping=FIELD_TYPE_MAPPING
                )
                for k, v in fields.items()
                if k in FIELD_MAPPING
            }
            loan_details.append(mapped_fields)

        return loan_details

    async def generate_summary(self, loan_details):
        """Generate summary statistics from loan details."""

        try:
            df = pd.DataFrame(loan_details)

            summary = (
                df.groupby("Loan Officer Login ID")
                .agg(
                    **{
                        "Loan Officer": ("Loan Officer", "first"),
                        "Loan Officer Email": ("Loan Officer Email", "first"),
                        "Number of Loans": ("Loan Amount", "count"),
                        "Loan Volume": ("Loan Amount", "sum"),
                    }
                )
                .reset_index()
            )

            summary["Loan Volume"] = summary["Loan Volume"].apply(lambda x: f"${x:,.0f}")

            total_loans = len(df)
            total_volume = df["Loan Amount"].sum()

            all_row = {
                "Loan Officer Login ID": "all",
                "Loan Officer": "all",
                "Loan Officer Email": "all",
                "Number of Loans": total_loans,
                "Loan Volume": f"${total_volume:,.0f}",
            }

            summary = pd.concat([summary, pd.DataFrame([all_row])], ignore_index=True)

            return summary.to_dict(orient="records")

        except Exception as e:
            logger.error(f"Error calculating summary statistics: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to calculate loan summary statistics. Please try again.",
            )

    async def convert_cannonical_to_display(self, data, FIELD_MAPPING):
        """Convert canonical field names to display names based on FIELD_MAPPING."""
        return [FIELD_MAPPING.get(field, field) for field in data]

    async def add_open_conditions(self, assistant_id: str, user, loan_details: list):
        """Add open conditions to each loan in the details."""
        for loan in loan_details:
            guid = loan.get("Loan GUID")
            number_of_conditions = loan.get("Number of Conditions Pending", 0)
            if number_of_conditions == 0:
                loan["View Conditions Pending"] = "N/A"
            else:
                try:
                    open_conditions = await self.get_open_conditions(assistant_id, user, guid)
                    loan["View Conditions Pending"] = open_conditions
                except Exception as e:
                    logger.error(f"Unexpected error fetching conditions for {str(e)}")
                    loan["View Conditions Pending"] = "Error fetching conditions"

        return loan_details

    async def get_open_conditions(self, assistant_id: str, user, guid: str):
        """Get open conditions for a loan by GUID."""
        token = await loan_utils.get_impersonation_auth_token(assistant_id=assistant_id, user=user)

        if not token:
            logger.error("Failed to get impersonation token.")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Could not verify user. "
                "Please make sure your email address matches the email address in your loan account.",
            )

        headers = {"Authorization": token}
        params = {"filter": "status:!=:Cleared,status:!=:Waived"}

        try:
            response = await self.client.get(
                f"{constants.LOAN_URL}{guid}/conditions/underwriting",
                params=params,
                headers=headers,
            )
            response.raise_for_status()

            response_data = await loan_function._decode_loan_details(response)

            if not response_data:
                return "N/A"

            open_conditions = [data.get("title") for data in response_data if data.get("title")]

            return "<br>".join(open_conditions) if open_conditions else "N/A"

        except HTTPException as e:
            logger.error(f"Request failed with status {e.response.status_code}: {e.response.text}")
            return "Error fetching conditions"
        except Exception as e:
            logger.error(f"Unexpected error fetching conditions {str(e)}")
            return "Error fetching conditions"


datapulse_utils = DataPulseUtils()
