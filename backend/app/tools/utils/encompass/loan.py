import asyncio
import json
from datetime import datetime
from io import BytesIO, <PERSON><PERSON>
from textwrap import dedent

import pandas as pd
import redis.asyncio as aioredis
from async_lru import alru_cache
from clients import client_config
from config import settings
from dateutil.relativedelta import relativedelta
from db.models import CustomLoanOfficerMapping, LoanOfficer
from db.session import session_manager
from hooks.connectors.sharepoint_hook import sharepoint_hook
from httpx import AsyncClient, HTTPStatusError
from loguru import logger
from pydantic import BaseModel
from schema.encompass import Credentials
from schema.enums import LoanOfficerMappingType
from sqlalchemy import func, select
from tools.fields.encompass import fetch_all_fields
from tools.functions.encompass import constants
from tools.payloads import get_closed_loan_terms, get_restricted_terms
from utils.division import get_encompass_division_credentials
from utils.type_conversion import type_converter


class LoanUtils:
    def __init__(self):
        self.client = AsyncClient(timeout=None)
        self.redis = None

    async def initialize_redis(self):
        if self.redis is None:
            try:
                self.redis = await aioredis.from_url(settings.REDIS_URL)
            except Exception as e:
                logger.error(f"Failed to connect to Redis: {e}")
                self.redis = None

    async def fetch_refi_data_from_api(
        self, user, fha_rate: float, va_rate: float, conv_rate: float, conv_non_rate: float, rate_t: float, **kwargs
    ) -> dict | None:
        fha_rate_max = fha_rate + rate_t
        va_rate_max = va_rate + rate_t
        conv_rate_max = conv_rate + rate_t
        conv_non_rate_max = conv_non_rate + rate_t

        six_months_ago = (datetime.now() - relativedelta(months=3)).strftime("%Y-%m-%d")
        two_years_ago = (datetime.now() - relativedelta(years=2)).strftime("%Y-%m-%d")
        data_fields = await fetch_all_fields(kwargs.get("assistant_id"))
        FIELD_MAPPING = data_fields.get("FIELD_MAPPING")
        REVERSE_FIELD_MAPPING = {val: key for key, val in FIELD_MAPPING.items()}
        fund_released_date_field = REVERSE_FIELD_MAPPING.get("Fund Released Date", "Fields.1999")
        required_fields = [
            fund_released_date_field,
            "Fields.3",
            "Fields.2",
            "Fields.4",
            "Fields.HMDA.X83",
            "Fields.1172",
            "Fields.11",
            "Fields.14",
            "Fields.13",
            "Fields.15",
            "Fields.1811",
            "Fields.5",
            "Fields.364",
            "Fields.317",
            "Fields.1407",
            "Fields.4000",
            "Fields.4002",
            "Fields.1240",
            "Fields.1490",
            "Fields.1811",
            "Fields.3335",
            "Fields.1401",
        ]

        payload = {
            "filter": {
                "operator": "AND",
                "terms": [
                    {
                        "operator": "OR",
                        "terms": [
                            {
                                "operator": "AND",
                                "terms": [
                                    {
                                        "operator": "OR",
                                        "terms": [
                                            {"canonicalName": "Fields.1172", "value": "FHA", "matchType": "Exact"},
                                            {"canonicalName": "Fields.1172", "value": "VA", "matchType": "Exact"},
                                        ],
                                    },
                                    {
                                        "canonicalName": "Fields.682",
                                        "value": six_months_ago,
                                        "matchType": "LessThanOrEquals",
                                    },
                                    {
                                        "operator": "AND",
                                        "terms": [
                                            {
                                                "canonicalName": fund_released_date_field,
                                                "value": six_months_ago,
                                                "matchType": "LessThanOrEquals",
                                                "precision": "day",
                                            },
                                            {
                                                "canonicalName": fund_released_date_field,
                                                "value": two_years_ago,
                                                "matchType": "GreaterThanOrEquals",
                                                "precision": "day",
                                            },
                                        ],
                                    },
                                ],
                            },
                            {
                                "operator": "AND",
                                "terms": [
                                    {
                                        "operator": "AND",
                                        "terms": [
                                            {"canonicalName": "Fields.1172", "value": "FHA", "matchType": "NotEquals"},
                                            {"canonicalName": "Fields.1172", "value": "VA", "matchType": "NotEquals"},
                                        ],
                                    },
                                    {
                                        "canonicalName": "Fields.682",
                                        "value": six_months_ago,
                                        "matchType": "LessThanOrEquals",
                                    },
                                    {
                                        "operator": "AND",
                                        "terms": [
                                            {
                                                "canonicalName": fund_released_date_field,
                                                "value": six_months_ago,
                                                "matchType": "LessThanOrEquals",
                                                "precision": "day",
                                            },
                                            {
                                                "canonicalName": fund_released_date_field,
                                                "value": two_years_ago,
                                                "matchType": "GreaterThanOrEquals",
                                                "precision": "day",
                                            },
                                        ],
                                    },
                                ],
                            },
                        ],
                    },
                    {
                        "operator": "OR",
                        "terms": [
                            {
                                "operator": "AND",
                                "terms": [
                                    {
                                        "canonicalName": "Fields.1172",
                                        "value": "Conventional",
                                        "matchType": "Exact",
                                    },
                                    {
                                        "canonicalName": "Fields.1811",
                                        "value": "PrimaryResidence",
                                        "matchType": "Exact",
                                    },
                                    {
                                        "canonicalName": "Fields.3",
                                        "value": conv_rate_max,
                                        "matchType": "GreaterThanOrEquals",
                                    },
                                ],
                            },
                            {
                                "operator": "AND",
                                "terms": [
                                    {
                                        "canonicalName": "Fields.1172",
                                        "value": "FHA",
                                        "matchType": "Exact",
                                    },
                                    {
                                        "canonicalName": "Fields.3",
                                        "value": fha_rate_max,
                                        "matchType": "GreaterThanOrEquals",
                                    },
                                ],
                            },
                            {
                                "operator": "AND",
                                "terms": [
                                    {
                                        "canonicalName": "Fields.1172",
                                        "value": "VA",
                                        "matchType": "Exact",
                                    },
                                    {
                                        "canonicalName": "Fields.3",
                                        "value": va_rate_max,
                                        "matchType": "GreaterThanOrEquals",
                                    },
                                ],
                            },
                            {
                                "operator": "AND",
                                "terms": [
                                    {
                                        "canonicalName": "Fields.1172",
                                        "value": "Conventional",
                                        "matchType": "Exact",
                                    },
                                    {
                                        "canonicalName": "Fields.3",
                                        "value": conv_non_rate_max,
                                        "matchType": "GreaterThanOrEquals",
                                    },
                                ],
                            },
                        ],
                    },
                ],
            },
            "fields": list(set(list(FIELD_MAPPING.keys()) + required_fields)),
            "sort": [{"canonicalName": fund_released_date_field, "sortOrder": "desc"}],
            "includeArchivedLoans": True,
        }
        payload["filter"]["terms"].extend(get_closed_loan_terms())

        # client config must have restriction enabled and if the user doesn't have impersonation access
        # (restriction enabled)
        lo_details = {}
        if client_config.ENCOMPASS_LOAN_IMPERSONATION_RESTRICTION and not user.encompass_impersonation_access:
            lo_details = await self.get_loan_officer_details(**kwargs)

        payload["filter"]["terms"].extend(get_restricted_terms(**lo_details))
        url = "https://api.elliemae.com/encompass/v1/loanPipeline"

        token = await self.get_impersonation_auth_token(
            user=user,
            mapping_type=LoanOfficerMappingType.REFI_REPORT,
            assistant_id=kwargs.get("assistant_id"),
        )
        if not token:
            logger.error("Couldn't fetch token for authentication into Encompass!")
            return None

        headers = {"Content-Type": "application/json", "Authorization": token, "accept": "application/json"}
        logger.info(f"Filtering all re-finance loans for user with email {user.email}...")
        try:
            response = await self.client.post(url, headers=headers, json=payload)
            response.raise_for_status()
        except HTTPStatusError as e:
            logger.error(f"Request failed with status {e.response.status_code} :: {e.response.text}.")
            logger.warning(f"PAYLOAD: {payload}")
            return None

        try:
            response_text = response.text
            loans = json.loads(response_text)
            if not loans:
                logger.warning(f"PAYLOAD: {payload}")
            return loans
        except Exception as e:
            logger.error(f"Some error occured while processing response from encompass : {e}")
            return None

    async def get_refi_report(self, user, fha_rate, va_rate, conv_rate, conv_non_rate, rate_t, **kwargs):
        data_fields = await fetch_all_fields(kwargs.get("assistant_id"))
        FIELD_MAPPING = data_fields.get("FIELD_MAPPING")
        REVERSE_FIELD_MAPPING = {val: key for key, val in FIELD_MAPPING.items()}
        fund_released_date_field = REVERSE_FIELD_MAPPING.get("Fund Released Date", "Fields.1999")
        fha_rate = float(fha_rate)
        va_rate = float(va_rate)
        conv_non_rate = float(conv_non_rate)
        conv_rate = float(conv_rate)
        rate_t = float(rate_t)

        def PMT(interest: float, num_of_payments: float, pv: float, fv: float = 0.00, type: float = 0) -> float:
            xp = (1 + interest) ** num_of_payments
            type_multiplier = 1 if type == 0 else 1 / (interest + 1)
            return (pv * interest * xp / (xp - 1) + interest / (xp - 1) * fv) * type_multiplier

        refi_response = await self.fetch_refi_data_from_api(
            user, fha_rate, va_rate, conv_rate, conv_non_rate, rate_t, **kwargs
        )
        if not refi_response:
            logger.info("No refi data fetched from encompass!")
            return None
        loan_term_field = "Fields.4" if settings.CLIENT_NAME.lower() in ["insyde ai"] else "Fields.HMDA.X83"

        final_response = []
        for loan_details in refi_response:
            fields = loan_details.get("fields", {})
            closing_date = fields.get(fund_released_date_field, "")
            product_loan_rate = float(fields.get("Fields.3", 0))
            loan_amount = float(fields.get("Fields.2", 0))
            cls_cost = 5000
            term = float(
                360
                if not fields.get(loan_term_field) or fields.get(loan_term_field).upper() in ["NA", "NULL"]
                else fields.get(loan_term_field)
            )
            loan_type = fields.get("Fields.1172", "")
            address = (
                f"{fields.get('Fields.11')} {fields.get('Fields.13')}"
                f" {fields.get('Fields.14')} {fields.get('Fields.15')}"
            )

            if loan_type.upper() == "VA":
                rate_apply = va_rate
            elif loan_type.upper() == "FHA":
                rate_apply = fha_rate
            else:
                if fields.get("Fields.1811", "").upper() == "PRIMARYRESIDENCE":
                    rate_apply = conv_rate
                else:
                    rate_apply = conv_non_rate

            if float(fields.get("Fields.3", 0)) <= rate_apply:
                continue

            if closing_date:
                date_format = "%m/%d/%Y %I:%M:%S %p"
                d1 = datetime.strptime(closing_date, date_format)
                d2 = datetime.now()
                delta = relativedelta(d2, d1)
                months_diff = delta.years * 12 + delta.months
                payment_made = months_diff - 1
            else:
                payment_made = 0

            est_pay_off = 0
            if product_loan_rate > 0:
                # total_monthly = PMT((product_loan_rate / 100) / 12, term, loan_amount + cls_cost)
                pmt = PMT((product_loan_rate / 100) / 12, term, loan_amount)
                div_mul = loan_amount * (product_loan_rate / 100) / 12

                est_pay_off = loan_amount - ((pmt - div_mul) * payment_made)
                # est_pay_off_primary = est_pay_off

            month360 = PMT((rate_apply / 100) / 12, term, est_pay_off + cls_cost)
            current_total = (term - payment_made) * float(fields.get("Fields.5", 0))  # mopymtPI
            new_total = term * month360

            total_saving = current_total - new_total

            if est_pay_off:
                return_response = {
                    "Loan Number": fields.get("Fields.364", "N/A"),
                    "Loan Type": fields.get("Fields.1172", "N/A"),
                    "Loan Officer Name": fields.get("Fields.317", "N/A"),
                    "Occupancy": fields.get("Fields.1811", "N/A"),
                    "Borrower Name": f"{fields.get('Fields.4000', 'N/A')} {fields.get('Fields.4002', 'N/A')}",
                    "Borrower Email": fields.get("Fields.1240", "N/A"),
                    "Borrower Cell": fields.get("Fields.1490", "N/A"),
                    "Address": address,
                    "Interest Rate": fields.get("Fields.3", "N/A"),
                    "Total Loan Amount": fields.get("Fields.2", "N/A"),
                    "Current Monthly P&I": f"${float(fields['Fields.5']):,.2f}" if fields.get("Fields.5") else "N/A",
                    "Est PayOff": f"${float(est_pay_off):,.2f}",
                    "Closed Date": type_converter.parse_date(closing_date),
                    "New Rate": f"{float(rate_apply):,.3f} %",
                    "New P&I": f"${float(month360):,.2f}",
                    "Monthly Savings": f"${float(fields.get('Fields.5', 0)) - month360:,.2f}",
                    "30 Yr lifetime savings": f"${float(total_saving):,.2f}",
                    # Could be used later
                    # "SubjectPropertyStreet": fields.get("Fields.11", "N/A"),
                    # "SubjectPropertyCity": fields.get("Fields.13", "N/A"),
                    # "SubjectPropertyState": fields.get("Fields.14", "N/A"),
                    # "SubjectPropertyZip": fields.get("Fields.15", "N/A"),
                    # "LoanOfficerEmail": fields.get("Fields.1407", "N/A"),
                    # "OccupancyType": fields.get("Fields.3335", "N/A"),
                    # "LoanProgram": fields.get("Fields.1401", "N/A"),
                    # "FundingFundsReleased": fields.get(fund_released_date_field, "N/A"),
                }
                final_response.append(return_response)

        # Sort final_response on MonthlySavings in descending order
        final_response.sort(key=lambda x: float(x["Monthly Savings"].replace("$", "").replace(",", "")), reverse=True)
        return final_response

    @alru_cache(ttl=60 * 60 * 24)
    async def get_interest_rates(self) -> dict:
        mnd_url = "https://www.mortgagenewsdaily.com/mortgage-rates/mnd"
        try:
            response = await self.client.get(mnd_url)
            response.raise_for_status()
            dfs = pd.read_html(StringIO(response.text))
            df = dfs[0]
            df.columns = df.columns.get_level_values(1)
            df = df[["Average Rates", "Current"]]
            df = df.set_index("Average Rates")
            loan_dict = df.to_dict()["Current"]
            return {**loan_dict, "source": mnd_url}
        except Exception as e:
            logger.error(f"Failed to get interest rates: {e}")
            return {}

    async def close(self):
        await self.client.aclose()

    async def fetch_access_token(self, payload: dict) -> str | None:
        url = "https://api.elliemae.com/oauth2/v1/token"
        headers = {"Content-Type": "application/x-www-form-urlencoded"}
        try:
            response = await self.client.post(url, headers=headers, data=payload)
            response.raise_for_status()  # Raises an exception for 4xx and 5xx responses
        except HTTPStatusError as e:
            logger.error(f"Request failed with status {e.response.status_code}.")
            return None

        response_data = response.json()
        token_type = response_data.get("token_type")
        access_token = response_data.get("access_token")

        if not token_type or not access_token:
            logger.error("No token type or access token.")
            return None

        return f"{token_type} {access_token}"

    async def get_auth_token(self, assistant_id: str | None = None) -> str | None:
        credentials = await self._get_encompass_credentials(assistant_id)

        payload = {
            "grant_type": "password",
            "username": f"{credentials.username}@encompass:{credentials.instance_id}",
            "password": credentials.password,
            "client_id": credentials.client_id,
            "client_secret": credentials.client_secret,
        }
        return await self.fetch_access_token(payload)

    async def map_email_loid(self, **kwargs) -> str | None:
        """Function that maps loan id with loan officer emails."""
        user = kwargs.get("user")
        mapping_type = kwargs.get("mapping_type", LoanOfficerMappingType.OTHER_REPORT)
        assistant_id = kwargs.get("assistant_id")
        lo_email = user.email
        logger.info(f"Fetching mapping for loan officer with email: {lo_email}, mapping_type: {mapping_type}")
        async with session_manager() as session:
            # check custom mapping
            query = (
                select(CustomLoanOfficerMapping)
                .where(
                    func.lower(CustomLoanOfficerMapping.email) == lo_email.lower(),
                    CustomLoanOfficerMapping.assistant_id == assistant_id,
                    CustomLoanOfficerMapping.mapping_type.in_((mapping_type, LoanOfficerMappingType.COMMON)),
                )
                .order_by(CustomLoanOfficerMapping.mapping_type.asc())
                .limit(1)
            )
            custom_mapping_result = await session.execute(query)
            custom_mapping = custom_mapping_result.scalar()
            if custom_mapping:
                if custom_mapping.loan_id:
                    logger.info(
                        f"Found mapped loan id: {custom_mapping.loan_id} for loan officer with email: {lo_email}"
                    )
                    return custom_mapping.loan_id
                logger.info(f"Found mapped email: {custom_mapping.lo_email} for loan officer with email: {lo_email}")
                lo_email = custom_mapping.lo_email

            # if no impersonation is found, we check if user has encompass loid or email
            if user.encompass_loid:
                logger.info(f"Found Encompass Loan id: {user.encompass_loid} in User Profile.")
                return user.encompass_loid
            elif user.encompass_email:
                logger.info(f"Found Encompass Email: {user.encompass_email} in User Profile.")
                lo_email = user.encompass_email

            query = select(LoanOfficer.loan_id).where(
                func.lower(LoanOfficer.email) == lo_email.lower(), LoanOfficer.assistant_id == assistant_id
            )
            result = await session.execute(query)
            loan_officer_record = result.scalar()

            if loan_officer_record:
                logger.info(f"Found loan_id: {loan_officer_record} for loan officer with email: {lo_email}")
                return loan_officer_record
            else:
                logger.info("User is not a loan officer. Restricting access!")
                return None

    async def get_loan_officer_details(self, **kwargs) -> dict:
        """Function that maps loan id with loan officer emails."""
        user = kwargs.get("user")
        mapping_type = kwargs.get("mapping_type", LoanOfficerMappingType.OTHER_REPORT)
        assistant_id = kwargs.get("assistant_id")
        lo_email = user.email
        loan_id = None
        async with session_manager() as session:
            # check custom mapping
            query = (
                select(CustomLoanOfficerMapping)
                .where(
                    func.lower(CustomLoanOfficerMapping.email) == lo_email.lower(),
                    CustomLoanOfficerMapping.assistant_id == assistant_id,
                    CustomLoanOfficerMapping.mapping_type.in_((mapping_type, LoanOfficerMappingType.COMMON)),
                )
                .order_by(CustomLoanOfficerMapping.mapping_type.asc())
                .limit(1)
            )
            custom_mapping_result = await session.execute(query)
            custom_mapping = custom_mapping_result.scalar()
            if custom_mapping:
                if custom_mapping.loan_id:
                    logger.info(
                        f"Found mapped loan id: {custom_mapping.loan_id} for loan officer with email: {lo_email}"
                    )
                    loan_id = custom_mapping.loan_id
                else:
                    lo_email = custom_mapping.lo_email

            else:
                # if no impersonation if found, check whether the user already has
                # encompass creds on profile
                loan_id = user.encompass_loid or loan_id
                lo_email = user.encompass_email or lo_email

            if loan_id:
                # if loan_id is found, use that to fetch details as loan_id is 100% unique value in encompass
                query = select(LoanOfficer).where(
                    func.lower(LoanOfficer.loan_id) == loan_id.lower(), LoanOfficer.assistant_id == assistant_id
                )
            else:
                # else try using email
                query = select(LoanOfficer).where(
                    func.lower(LoanOfficer.email) == lo_email.lower(), LoanOfficer.assistant_id == assistant_id
                )

            result = await session.execute(query)
            loan_officer_record = result.scalar()

            if loan_officer_record:
                logger.info(f"Found loan_id: {loan_officer_record.loan_id} for loan officer with email: {lo_email}")
                return {
                    "name": f"{loan_officer_record.first_name} {loan_officer_record.last_name}",
                    "lo_email": loan_officer_record.email,
                    "loan_id": loan_officer_record.loan_id,
                }
            else:
                logger.info("User is not a loan officer. Restricting access!")
                return {}

    async def get_impersonation_auth_token(self, **kwargs) -> str | None:
        loid = await self.map_email_loid(**kwargs)
        if not loid:
            return None
        assistant_id = kwargs.get("assistant_id")

        token = await self.get_auth_token(assistant_id)
        credentials = await self._get_encompass_credentials(assistant_id)
        if not token:
            return None
        payload = {
            "grant_type": "urn:ietf:params:oauth:grant-type:token-exchange",
            "actor_token_type": "urn:ietf:params:oauth:token-type:access_token",
            "subject_user_id": loid,
            "actor_token": token.split()[-1],
            "client_id": credentials.client_id,
            "client_secret": credentials.client_secret,
        }
        return await self.fetch_access_token(payload)

    async def fetch_loan_officer_page(self, url, headers, start, limit):
        """Fetch a single page of loan officer data."""
        params = {"start": start, "limit": limit}
        try:
            response = await self.client.get(url, headers=headers, params=params)
            response.raise_for_status()  # Raises an exception for 4xx and 5xx responses
            return response.json()
        except Exception as e:
            logger.error(f"Error fetching loan officer data at start={start}: {e}")
            return []

    async def get_latest_loan_officer_data(self, assistant_id: str):
        token = await self.get_auth_token(assistant_id)
        if not token:
            logger.error("Failed to get auth token for fetching loan officer data.")
            return pd.DataFrame()  # Return an empty DataFrame in case of an error

        headers = {"Content-Type": "application/json", "Authorization": token, "Accept-Encoding": "gzip"}
        base_url = "https://api.elliemae.com/encompass/v1/company/users"

        start = 1
        limit = 1000
        # Initial request to get the total count from headers
        try:
            initial_response = await self.client.get(base_url, headers=headers, params={"start": start, "limit": 1})
            initial_response.raise_for_status()
            total_count = int(initial_response.headers.get("X-Total-Count", 0))
        except Exception as e:
            logger.error(f"Error fetching initial loan officer data: {e}")
            return pd.DataFrame()

        # Calculate the total number of pages needed
        total_pages = (total_count // limit) + (1 if total_count % limit > 0 else 0)

        # Prepare to fetch all pages in parallel
        starts = [1 + limit * i for i in range(total_pages)]
        tasks = [self.fetch_loan_officer_page(base_url, headers, start, limit) for start in starts]
        pages = await asyncio.gather(*tasks)

        # Flatten the list of lists into a single list of users
        all_users = [user for page in pages for user in page]

        logger.info(f"Total {len(all_users)} loan officer data fetched.")
        df = pd.DataFrame(all_users)
        df_enabled = df[df["userIndicators"].apply(lambda a: "Enabled" in a)]
        logger.info(f"Total {df_enabled.shape[0]} loan officer data with enabled userIndicators.")
        data_df = df_enabled[["id", "lastName", "firstName", "email"]]
        return data_df

    def generate_and_upload_excel(
        self, s3_hook, prefix: str, report_data: dict, folder: str = "loan_data"
    ) -> str | None:
        try:
            df = pd.DataFrame(report_data)

            excel_buffer = BytesIO()
            df.to_excel(excel_buffer, index=False)
            excel_buffer.seek(0)

            # Upload to S3
            filename = f"{folder}/{prefix}_{datetime.now().strftime('%Y%m%d%H%M%S')}.xlsx"
            response = s3_hook.put_object(object_name=filename, file=excel_buffer)

            if response["ResponseMetadata"]["HTTPStatusCode"] == 200:
                excel_url = s3_hook.get_presigned_url_for_download(filename)
                return excel_url
            else:
                return "Failed to upload Excel file."

        except Exception as e:
            logger.error(f"Failed to generate and upload Excel file: {e}")
            return "Failed to generate and upload Excel file."

    async def remove_empty_fields(self, loan_details: list[dict], **kwargs) -> list[dict]:
        FIELD_MAPPING = kwargs.get("field_mapping", {})
        # Transpose the `loan_details` to get columns as lists
        columns = {key: [details.get(key) for details in loan_details] for key in FIELD_MAPPING.values()}

        # Determine columns to remove (those which have all values as None)
        columns_to_remove = [key for key, values in columns.items() if all(not value for value in values)]

        # Filter out the columns with all values as None from each loan detail
        loan_details = [{k: v for k, v in details.items() if k not in columns_to_remove} for details in loan_details]
        return loan_details

    async def payment_details_synergpt(self, loan: dict):
        def online_or_mail():
            return dedent(
                """Online at S1l.com “PayOnline”
                    By Mail: Synergy One Lending
                    PO Box 95741
                    South Jordan, UT 84095""".strip()
            )

        if "bridge" in loan.get("Fields.1401", "").lower():
            return online_or_mail()

        if not loan.get("Fields.1999"):
            return "This loan is not yet funded."

        field_3534 = loan.get("Fields.3534", "").lower()
        if "retained" in field_3534:
            if loan.get("Fields.Service.X111"):
                return dedent(
                    """Regular Mail
                            Synergy One Lending
                            Attn: Payment Processing
                            PO Box 650094
                            Dallas, TX 75265-0094\n
                            Overnight Mail
                            Synergy One Lending
                            Attn: Cashiering
                            3138 E Elwood street
                            Phoenix, AZ 85034
                            """.strip()
                )
            return online_or_mail()

        if "released" in field_3534:
            if not loan.get("Fields.2370"):
                return online_or_mail()

            if loan.get("Fields.682") == loan.get("Fields.3514"):
                return dedent(
                    f"""Servicer name: {loan.get('Fields.VEND.X378')}
                        Servicer phone number: {loan.get('Fields.VEND.X384')}
                        Servicer hours: {loan.get('Fields.VEND.X385')}""".strip()
                )
            fields_service_x14 = loan.get("Fields.Service.X14")

            if not fields_service_x14 or fields_service_x14 == loan.get("Fields.682"):
                return online_or_mail()

            if fields_service_x14 == loan.get("Fields.3514"):
                return dedent(
                    f"""Servicer name: {loan.get('Fields.VEND.X378')}
                        Servicer phone number: {loan.get('Fields.VEND.X384')}
                        Servicer hours: {loan.get('Fields.VEND.X385')}""".strip()
                )

        return "No payment details available."

    @staticmethod
    async def calculate_monthly_payment(principal: float, rate: float, term_months: int) -> float:
        """Calculate the monthly mortgage payment (Principal & Interest)"""
        monthly_rate = rate / 100 / 12  # Convert annual rate to monthly rate in decimal
        xp = (1 + monthly_rate) ** term_months
        payment = principal * (monthly_rate * xp) / (xp - 1)
        return payment

    @staticmethod
    def calculate_total_savings(
        current_payment: float, months_left: int, new_payment: float, new_term_months: int
    ) -> float:
        """Calculate the total savings over the life of the loan by refinancing"""
        current_total = current_payment * months_left
        new_total = new_payment * new_term_months
        total_savings = current_total - new_total
        return total_savings

    @staticmethod
    async def calculate_apr_rate(note_rate: float, apr_fees: float, principal: float, term_months: int) -> float:
        """Estimate the APR rate based on the note rate and added fees"""
        total_amount = principal + apr_fees
        apr_rate = note_rate + (apr_fees / total_amount) * (12 * 100 / term_months)
        return apr_rate

    @staticmethod
    async def remaining_balance(principal: float, rate: float, payment: float, months_elapsed: int) -> float:
        rate_monthly = rate / 100 / 12
        # Calculate balance remaining after some months
        balance = principal * (1 + rate_monthly) ** months_elapsed - payment * (
            ((1 + rate_monthly) ** months_elapsed - 1) / rate_monthly
        )
        return balance

    @staticmethod
    async def future_value(present_value: float, rate: float, time_years: int) -> float:
        """Calculate future value in given time period"""
        rate = rate / 100
        future_value = present_value * (1 + rate) ** time_years
        return future_value

    async def get_organization_details(self, org_id: str, assistant_id: str | None) -> dict:
        token = await self.get_auth_token(assistant_id)
        if not token:
            logger.error("Failed to get auth token for fetching loan officer data.")
            return None

        headers = {"Content-Type": "application/json", "Authorization": token}
        base_url = f"https://api.elliemae.com/encompass/v1/organizations/{org_id}"
        params = {"view": "Summary"}
        try:
            response = await self.client.get(base_url, headers=headers, params=params)
            response.raise_for_status()
        except Exception as e:
            logger.error(f"Error fetching initial loan officer data: {e}")
            return pd.DataFrame()

        response_text = response.text
        json_response = json.loads(response_text)
        return json_response

    async def fetch_loan_officer_branch(self, **kwargs) -> dict:
        token = await self.get_impersonation_auth_token(**kwargs)
        if not token:
            logger.error("Failed to get auth token for fetching loan officer data.")
            return None

        # Fetch initial user data
        json_response = await self.fetch_user_data(token)
        org_information = json_response.get("organization", {})
        return org_information.get("entityName", None)

    async def fetch_user_data(self, token: str, loan_id: str = "me") -> dict:
        headers = {"Content-Type": "application/json", "Authorization": token}
        base_url = f"https://api.elliemae.com/encompass/v3/users/{loan_id}"
        params = {"entities": "Summary,AccountInformation,EmailSignature"}
        try:
            response = await self.client.get(base_url, headers=headers, params=params)
            response.raise_for_status()
        except Exception as e:
            logger.error(f"Error fetching initial loan officer data: {e}")
            return pd.DataFrame()

        response_text = response.text
        json_response = json.loads(response_text)
        return json_response

    async def get_job_title(self, user, lo_email=None):
        try:
            token = await sharepoint_hook.get_access_token()
            if not token:
                logger.error("Failed to get auth token for fetching loan officer data.")
                return "Loan Originator"

            headers = {"Content-Type": "application/json", "Authorization": f"Bearer {token}"}
            lo_email = lo_email if lo_email else user.email
            base_url = f"https://graph.microsoft.com/v1.0/users/{lo_email}"
            response = await self.client.get(base_url, headers=headers)
            response.raise_for_status()
            job_title = response.json().get("jobTitle", "Loan Originator")
        except Exception as e:
            logger.error(f"Error fetching initial loan officer data: {e}")
            return "Loan Originator"

        return job_title

    async def fetch_loan_officer_details(
        self, loid: str = None, lo_email: str = None, prequal=False, **kwargs
    ) -> dict:
        user = kwargs.get("user")
        email = user.email
        assistant_id = kwargs.get("assistant_id")
        token = await self.get_impersonation_auth_token(**kwargs)
        if not token:
            logger.error("Failed to get auth token for fetching loan officer data.")
            return None

        # Fetch initial user data
        json_response = await self.fetch_user_data(token)
        job_title = json_response.get("jobTitle", None)
        if not job_title:
            job_title = await self.get_job_title(user)

        if not json_response.get("nmlsOriginatorId") and loid:
            token = await self.get_auth_token(assistant_id)
            json_response = await self.fetch_user_data(token, loid)
            job_title = json_response.get("jobTitle", None)
            email = lo_email
            if not job_title:
                job_title = await self.get_job_title(user, lo_email)

        organization = json_response.get("organization", {})
        org_id = organization.get("entityId")
        if org_id:
            org_information = await self.get_organization_details(org_id, assistant_id)
            office_phone = org_information.get("orgInformation", {}).get("phone", "")
            branch_address = org_information.get("orgInformation", {}).get("address", {})
            broker_lender_address = f"{branch_address.get('street1', '')}, {branch_address.get('unitType', '')} {branch_address.get('street2', '')}"  # noqa: E501
            broker_lender_city = branch_address.get("city", "")
            broker_lender_state = branch_address.get("state", "")
            broker_lender_zip = branch_address.get("zip", "")
            city_state_zip = (
                f" {branch_address.get('city', '')}, {branch_address.get('state', '')}, {branch_address.get('zip')}"
            )
        else:
            office_phone = ""
            broker_lender_address = ""
            broker_lender_city = ""
            broker_lender_state = ""
            broker_lender_zip = ""
            city_state_zip = ""

        lo_details = {
            "name": json_response.get("fullName"),
            "email": email,
            "phone": json_response.get("phone") or json_response.get("cellPhone"),
            "nmls_number": json_response.get("nmlsOriginatorId"),
        }
        if prequal:
            lo_details.update(
                {
                    "office_phone": office_phone,
                    "broker_lender_address": broker_lender_address,
                    "broker_lender_city": broker_lender_city,
                    "broker_lender_state": broker_lender_state,
                    "broker_lender_zip": broker_lender_zip,
                }
            )
        else:
            lo_details.update(
                {
                    "street_address": broker_lender_address,
                    "city_state_zip": city_state_zip,
                    "title": job_title or "Loan Originator",
                }
            )

        return lo_details

    async def _get_encompass_credentials(self, assistant_id: str | None = None) -> BaseModel:
        credentials = await get_encompass_division_credentials(assistant_id)
        if credentials:
            return Credentials(
                client_id=credentials.get("encompass_client_id"),
                client_secret=credentials.get("encompass_client_secret"),
                username=credentials.get("encompass_username"),
                password=credentials.get("encompass_password"),
                instance_id=credentials.get("encompass_instance_id"),
            )
        return Credentials(
            client_id=settings.ENCOMPASS_CLIENT_ID,
            client_secret=settings.ENCOMPASS_CLIENT_SECRET,
            username=settings.ENCOMPASS_USERNAME,
            password=settings.ENCOMPASS_PASSWORD,
            instance_id=settings.ENCOMPASS_INSTANCE_ID,
        )

    async def get_encompass_field_details(self, canonical_names: list[str]) -> list[dict]:
        token = await self.get_auth_token()
        headers = {"Content-Type": "application/json", "Authorization": token}

        async def fetch_chunk(chunk):
            params = {"canonicalNames": ",".join(chunk)}
            try:
                response = await self.client.get(constants.CANONICAL_FIELDS_URL, headers=headers, params=params)
                response.raise_for_status()
                return response.json()
            except HTTPStatusError as e:
                logger.error(f"Failed to fetch options for chunk {chunk}: {e}")
                return []
            except Exception as e:
                logger.error(f"Unexpected error fetching field options: {e}")
                return []

        if len(canonical_names) <= 50:
            return await fetch_chunk(canonical_names)

        tasks = []
        for i in range(0, len(canonical_names), 50):
            end = i + 50
            chunk = canonical_names[i:end]
            tasks.append(fetch_chunk(chunk))

        results = await asyncio.gather(*tasks)

        field_data = [item for result in results for item in result]

        return field_data

    async def get_dropdown_values(self, api_field: str) -> list[str]:
        """
        Fetch dropdown values for a specific field from Encompass.
        """

        await self.initialize_redis()
        dropdown_values = await self.redis.get("encompass_fields_with_options")

        if dropdown_values:
            dropdown_values = json.loads(dropdown_values)
            return dropdown_values.get(api_field, [])

        return []


loan_utils = LoanUtils()
