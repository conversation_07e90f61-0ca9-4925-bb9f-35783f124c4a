import asyncio
import json
import math
import operator
from base64 import b64encode
from datetime import datetime, timedelta

from config import settings
from dateutil.parser import parse
from db.models import (
    CustomLoanOfficerMapping,
    DataField,
    TotalExpertInsight,
    TotalExpertInsightType,
    TotalExpertJourney,
    TotalExpertToken,
    User,
)
from db.session import session_manager
from fastapi import HTTPException, status
from httpx import AsyncClient, HTTPStatusError
from loguru import logger
from redis import asyncio as aioredis
from schema.enums import DataFieldFolder, LoanOfficerMappingType, ReadStatus, TEJourneyTypes
from sqlalchemy import and_, func, or_, select, update
from sqlalchemy.ext.asyncio import AsyncSession
from starlette.status import HTTP_400_BAD_REQUEST
from tools.fields.totalexpert import fetch_all_fields
from tools.fields.totalexpert._default import JOURNEY_PREFIXES, NOTE_TYPE_IDS_MAPPING
from tools.functions.total_expert.constants import (
    <PERSON><PERSON><PERSON>_LIMIT,
    DATE_OPERATORS,
    MAP_DATAFIELD_TO_TYPE,
    NUMERIC_OPERATORS,
    STRING_OPERATORS,
)
from tools.utils.total_expert_token import TotalExpertTokenManager
from utils.division import get_assistant_division_credentials
from utils.type_conversion import type_converter


class NotFoundError(Exception):
    pass


def decode_if_bytes(value):
    return value.decode("utf-8") if isinstance(value, bytes) else value


class TotalExpertUtils:
    def __init__(self):
        self.base_url = settings.TOTAL_EXPERT_BASE_URL
        self.client = AsyncClient(timeout=None)
        self.page_size = 100
        self.redis = None
        # concurrency limit for fetching data
        self.concurrent_processes = 100

    async def initialize_redis(self):
        self.redis = await aioredis.from_url(settings.REDIS_URL)

    async def map_user(self, user, assistant_id=None, mapping_type=LoanOfficerMappingType.TOTALEXPERT_GENERAL) -> str:
        """Maps the given user to a custom loan officer email if exists, otherwise returns the user's email."""
        lo_email = user.email
        logger.info(
            f"Checking mapping for user email: {lo_email} with mapping type: {mapping_type}"
            f"for assistant_id {assistant_id}"
        )
        async with session_manager() as session:
            query = select(CustomLoanOfficerMapping.lo_email).where(
                func.lower(CustomLoanOfficerMapping.email) == lo_email.lower(),
                CustomLoanOfficerMapping.mapping_type == mapping_type,
                CustomLoanOfficerMapping.assistant_id == assistant_id,
            )
            if settings.ENABLE_TOTAL_EXPERT_OAUTH:
                query = query.where(CustomLoanOfficerMapping.active)
            result = await session.execute(query)
            custom_mapping = result.scalar()
            if custom_mapping:
                logger.info(f"Custom mapping found: {custom_mapping} for user email: {lo_email}")
                return custom_mapping.lower()
            logger.info(f"No mapping found. Using default user email: {lo_email}")
            return lo_email.lower()

    async def is_admin(
        self, user, assistant_id=None, return_email: bool = False, token: str | None = None
    ) -> bool | str:
        """Determines if the specified user is an admin in TotalExpert."""
        # user can also be email
        if isinstance(user, str):
            lo_email = user
        else:
            lo_email = user.email
            lo_email = await self.map_user(user, assistant_id)
        logger.info(f"Checking admin status for user email: {lo_email}")
        token = token or await self.get_token(assistant_id, user=user)
        user_details = await self.get_user_details(token, lo_email)
        admin_roles = ["admin"]
        is_admin = user_details.get("role", {}).get("role_name", "").lower() in admin_roles
        logger.info(f"User email: {lo_email} is admin: {is_admin}")
        return lo_email if return_email else is_admin

    async def get_owner_filter(self, user, assistant_id=None) -> str:
        """Returns the filter string for owner email if the user is not an admin, else returns an empty string."""
        if await self.is_admin(user, assistant_id):
            return ""
        lo_email = await self.map_user(user=user, assistant_id=assistant_id)
        logger.info(f"Owner email filter returned for user: owner_email={lo_email}")
        return f"owner_email={lo_email}"

    async def _decode_data(self, response) -> dict | None:
        response_data = None
        try:
            response_text = response.text
            response_data = json.loads(response_text)
        except json.JSONDecodeError as e:
            logger.error(f"JSON Decode error when decoding output from total expert: {e}")
            try:
                response_text = response.content.decode("utf-8", errors="ignore")
                response_data = json.loads(response_text)
            except Exception as e:
                logger.error(f"Some error occured while converting total expert response to JSON: {e}")
                return None

        return response_data

    @staticmethod
    async def get_basic_auth_token(assistant_id: str) -> str | None:
        try:
            credentials = await get_assistant_division_credentials(assistant_id)
            if (
                credentials
                and credentials.get("total_expert_client_id")
                and credentials.get("total_expert_client_secret")
            ):
                total_expert_client_id = decode_if_bytes(credentials.get("total_expert_client_id"))
                total_expert_client_secret = decode_if_bytes(credentials.get("total_expert_client_secret"))
            else:
                total_expert_client_id = settings.TOTAL_EXPERT_CLIENT_ID
                total_expert_client_secret = settings.TOTAL_EXPERT_CLIENT_SECRET
            token = b64encode(f"{total_expert_client_id}:{total_expert_client_secret}".encode()).decode("ascii")
            return f"Basic {token}"
        except Exception as e:
            logger.error(f"Error while fetching basic auth credentials:: {e}")
            return None

    async def get_token(self, assistant_id: str, user: User | None = None) -> str | None:
        if not self.redis:
            await self.initialize_redis()

        if settings.ENABLE_TOTAL_EXPERT_OAUTH and user:
            lo_email = await self.map_user(user=user, assistant_id=assistant_id)
            user_id = user.id if user.email == lo_email else None

            # Use OAuth token flow
            async with session_manager() as session:
                if user_id is None:
                    # fetch impersonate user id
                    query = select(User.id).where(User.email == lo_email)
                    result = await session.execute(query)
                    user_id = result.scalar_one_or_none()
                    query = select(TotalExpertToken).where(
                        TotalExpertToken.user_id == user_id, TotalExpertToken.assistant_id == assistant_id
                    )
                    result = await session.execute(query)
                    token_record = result.scalar_one_or_none()
                    if not token_record:
                        return None

                token_manager = TotalExpertTokenManager(session, self.redis)
                token = await token_manager.get_oauth_te_token(user_id, assistant_id)
                if token:
                    if isinstance(token, dict):
                        return f"{token.get('token_type', 'bearer')} {token.get('access_token')}"
                    elif isinstance(token, str):
                        try:
                            token_dict = json.loads(token)
                            return f"{token_dict.get('token_type', 'bearer')} {token_dict.get('access_token')}"
                        except json.JSONDecodeError:
                            logger.error("Invalid token format in Redis")
                            return None
                    else:
                        return f"{token.token_type} {token.access_token}"
                return None
        else:
            # Use existing client credentials flow
            cached_token = await self.redis.get(f"total_expert_token:{assistant_id}")
            if cached_token:
                return cached_token.decode() if isinstance(cached_token, bytes) else cached_token

            basic_auth_token = await self.get_basic_auth_token(assistant_id)

            if not basic_auth_token:
                return None

            payload = json.dumps({"grant_type": "client_credentials"})

            headers = {
                "Authorization": basic_auth_token,
                "Content-Type": "application/json",
            }
            url = f"{self.base_url}/token"
            try:
                response = await self.client.post(url=url, headers=headers, data=payload)
                response.raise_for_status()
            except HTTPStatusError as e:
                logger.error(
                    f"Error while fetching token. Status Code: {e.response.status_code} Error:{e.response.text}"
                )
                return None
            except Exception as e:
                logger.error(f"Error while fetching token:: {e}")
                return None

            json_response = await self._decode_data(response)

        token = f"{json_response.get('token_type')} {json_response.get('access_token')}"
        await self.redis.setex(f"total_expert_token:{assistant_id}", timedelta(minutes=59), token)
        # cache for 59 minutes; expires after an hour
        return token

    async def fetch_data_from_api(self, url: str, method: str, **kwargs) -> list:
        # Initialize the list to store API data
        api_data = []
        # Stopping Condition :
        # {
        # field_name: "Name of the field",
        # field_value:"value for the field",
        # field_type:"The data type of the condition",
        # }
        stopping_condition = kwargs.pop("stopping_condition", None)
        # stopping_condition = {
        #     "field_name": "creation_date",
        #     "field_value": datetime.now() - relativedelta(months=1),
        #     "field_type": datetime,
        # }
        # Extract and clean up the `params` from kwargs, ensuring no trailing commas in filter
        params = kwargs.get("params", {})
        if "filter" in params:
            params["filter"] = params["filter"].strip(",")

        # Extract optional arguments from kwargs
        limit = kwargs.pop("limit", None)
        # limit = None
        page_size = params.get("page[size]", self.page_size)
        return_page_count = kwargs.pop("return_page_count", False)

        # Adjust the page size based on limit
        if limit:
            params["page[size]"] = min(limit, page_size, self.page_size)

        # Validate and enforce HTTP method
        self._validate_http_method(method)

        # Determine total pages to fetch based on limit if provided, else fetch all pages

        total_pages = math.ceil(limit / self.page_size) if limit is not None else float("inf")

        try:
            # Fetch first page to determine number of items and consider pagination
            response = await self.client.request(url=url, method=method, **kwargs)
            response.raise_for_status()
        except HTTPStatusError as e:
            logger.error(f"Error with status: {e.response.status_code}:: {e.response.json()}")
            return ([], 1) if return_page_count else []
        except Exception as e:
            logger.error(f"Error Occured while fetching data:{e}")
            return ([], 1) if return_page_count else []

        # Decode and process the JSON response
        json_response = await self._decode_data(response)
        if not json_response:
            logger.warning("Empty response from total expert!")
            return ([], 1) if return_page_count else []

        # Extend api_data with the first page of items
        api_data.extend(json_response.get("items", []))

        links = json_response.get("links", {})

        # Determine total pages from response if limit is not set or return_page_count is set
        if not limit:
            total_pages = int(links.get("last", 1))

        semaphore = asyncio.Semaphore(self.concurrent_processes)

        async def fetch_data(url, method, **kwargs):
            async with semaphore:
                try:
                    # Fetch data for each page asynchronously
                    response = await self.client.request(url=url, method=method, **kwargs)
                    response.raise_for_status()
                    return response
                except HTTPStatusError as e:
                    logger.error(f"Error with status: {e.response.status_code}:: {e.response.json()}")
                    raise Exception(f"Error fetching data: {e}")
                except Exception as e:
                    logger.error(f"Error Occured while fetching data:{e}")
                    raise Exception(f"Error fetching data: {e}")

        # Prepare tasks to fetch remaining pages
        # tasks = []
        # for page in range(2, min(total_pages + 1, int(links.get("last", 1)) + 1)):
        #     params["page[number]"] = page
        #     kwargs.update({"params": params.copy()})
        #     tasks.append(fetch_data(url, method=method, **kwargs))

        # New Logic for batch Update
        batch_size = 3
        current_page = 2
        last_page = min(total_pages + 1, links.get("last", 1) + 1)
        # if tasks:
        # Gather responses for all tasks
        while current_page < last_page:
            batch_tasks = []
            end_page = min(current_page + batch_size, last_page)
            for page in range(current_page, end_page):
                params["page[number]"] = page
                if stopping_condition:
                    params["sort"] = f'-{stopping_condition.get("field_name")}'.strip(",")
                kwargs.update({"params": params.copy()})
                batch_tasks.append(fetch_data(url, method=method, **kwargs))
            responses = await asyncio.gather(*batch_tasks, return_exceptions=True)
            successful_responses = [res for res in responses if not isinstance(res, Exception)]
            for response in responses:
                if isinstance(response, Exception):
                    logger.error(f"Error during fetching pages: {str(response)}")
                    # since its error throw empty resposne
                    return ([], 1) if return_page_count else []

            stop = False
            for response in successful_responses:
                json_response = await self._decode_data(response)
                if json_response:
                    items = json_response.get("items", [])
                    api_data.extend(items)
                    if stopping_condition:
                        for item in items:
                            if await self.check_stopping_condition(item, stopping_condition):
                                logger.info("Stopping condition met.Halting further data fetch.")
                                stop = True
                                break
                if stop:
                    break
            if stop:
                break
            current_page = end_page
        # Clip the results to the limit if specified
        if limit:
            api_data = api_data[:limit]

        if return_page_count:
            total_pages = int(links.get("last", 1))

        # Return results with or without total page count
        return (api_data, total_pages) if return_page_count else api_data

    def _validate_http_method(self, method: str):
        if method.upper() not in ["GET", "POST", "PUT", "PATCH", "DELETE"]:
            logger.error(f"HTTP method not supported: {method}")
            raise ValueError(f"Unsupported HTTP method: {method}")

    async def is_team_manager(self, user, **kwargs):
        assistant_id = kwargs.get("assistant_id")
        owner_email = await self.map_user(user=user, assistant_id=assistant_id)
        token = kwargs.get("token") or await self.get_token(assistant_id, user=user)
        if not token:
            logger.error("Empty auth token")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED, detail="Could not authenticate user into Total Expert!"
            )

        user_id = await self.get_user_id(email=owner_email, token=token)
        if not user_id:
            logger.error("User ID not found. Impersonate user.")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=(
                    "It seems like there is not an account associated with your email. Please contact admin"
                    " for assistance!"
                ),
            )

        managed_teams = await self._fetch_managed_teams(user_id, owner_email, token)
        return managed_teams

    async def _fetch_managed_teams(self, user_id: str, owner_email: str, token: str) -> list:
        url = f"{self.base_url}/users/{user_id}"
        headers = {
            "Authorization": token,
            "Content-Type": "application/json",
        }
        try:
            response = await self.client.get(url=url, headers=headers)
            response.raise_for_status()
            json_response = await self._decode_data(response)
        except HTTPStatusError as e:
            logger.error(f"Error while fetching user. Status Code: {e.response.status_code}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Some unexpected error occurred while fetching user!",
            )

        return [
            team.get("team_name")
            for team in json_response.get("teams", [])
            if owner_email in [manager.get("email") for manager in team.get("managers", [])]
        ]

    async def fetch_all_users(self, assistant_id, user: User | None = None) -> list:
        cached_users = await self.redis.get(f"total_expert_users:{assistant_id}")
        if cached_users:
            return json.loads(cached_users)

        token = await self.get_token(assistant_id, user=user)
        if not token:
            logger.error("Empty auth token")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED, detail="Could not authenticate user into total expert!"
            )

        url = f"{self.base_url}/users"
        headers = {
            "Authorization": token,
            "Content-Type": "application/json",
        }
        params = {"page[number]": 1, "page[size]": self.page_size}
        try:
            users = await self.fetch_data_from_api(url, method="GET", headers=headers, params=params)
            await self.redis.setex(f"total_expert_users:{assistant_id}", timedelta(days=1), json.dumps(users))
            return users
        except HTTPStatusError as e:
            logger.error(f"Error while fetching users. Status Code: {e.response.status_code}")
            raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Error while fetching users")
        except Exception as e:
            logger.error(f"Unexpected error while fetching users: {e}")
            raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Error while fetching users")

    async def fetch_team_members(self, team_names: list[str], user: User | None = None, **kwargs) -> list:
        # TODO: fetching team wont give members but only managers.
        # One option is to fetch all users and check team names and if matches then return the user details.
        # BAD WAY: HAHAHA. SO SAD
        # Maybe store in db????
        token = kwargs.get("token")
        assistant_id = kwargs.get("assistant_id")
        if not token:
            token = await self.get_token(assistant_id, user=user)
            if not token:
                logger.error("Empty auth token")
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED, detail="Could not authenticate user into total expert!"
                )
        # initialize redis
        await self.initialize_redis()
        users = await self.fetch_all_users(assistant_id, user=user)
        team_members = []
        for user in users:
            for team in user.get("teams", []):
                if team.get("team_name", "") in team_names:
                    team_members.append(user)
        return team_members

    async def fetch_contacts(
        self, user, filter: str = "", limit: int | None = None, token: str | None = None, **kwargs
    ) -> list:
        assistant_id = kwargs.pop("assistant_id")
        token = token or await self.get_token(assistant_id, user=user)
        if not token:
            logger.error("Empty auth token")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED, detail="Could not authenticate user into total expert!"
            )
        url = f"{self.base_url}/contacts"
        headers = {
            "Authorization": token,
            "Content-Type": "application/json",
        }
        team_members = [None]
        if not settings.ENABLE_TOTAL_EXPERT_OAUTH:
            team_members = await self._get_team_member_emails(user, token, assistant_id=assistant_id)
        tasks = []
        for team_member in team_members:
            params = {
                "page[number]": 1,
                "page[size]": self.page_size,
                "filter": filter,
                "sort": "-internal_created_at",
            }
            if team_member:
                team_filter = f"owner_email={team_member}"
                params["filter"] = f"{params['filter']},{team_filter}".strip(",")
            tasks.append(
                self.fetch_data_from_api(url, method="GET", headers=headers, params=params, limit=limit, **kwargs)
            )
        # fetch contacts for each team member
        contacts = await asyncio.gather(*tasks, return_exceptions=True)
        contacts = [contact for sublist in contacts for contact in sublist if not isinstance(contact, Exception)]
        return contacts

    async def fetch_contact_id(
        self,
        user,
        first_name: str | None = None,
        last_name: str | None = None,
        email: str | None = None,
        phone_cell: str | None = None,
        **kwargs,
    ):
        if not any([email, phone_cell, first_name, last_name]):
            logger.error("Either email or phone cell or first name or last name is required!")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=(
                    "To proceed, please provide at least one of the following: email, phone number, first name, or"
                    " last name."
                ),
            )
        filters = {"email": email, "phone_cell": phone_cell, "first_name": first_name, "last_name": last_name}
        filter = ",".join(f"{key}={value}" for key, value in filters.items() if value)
        contacts = await self.fetch_contacts(user, filter=filter, assistant_id=kwargs.get("assistant_id"))
        if not contacts:
            logger.warning(f"Contact not found with the given details: {filter}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Contact not found with the given details. Please recheck the values and try again!",
            )
        if len(contacts) > 1:
            logger.warning("Multiple contacts found for the given details.")
            contact_information = "\n".join(
                [
                    f"**Contact ID**: {contact.get('id')}\n"
                    f"**First Name**: {contact.get('first_name')}\n"
                    f"**Last Name**: {contact.get('last_name')}\n"
                    f"**Email**: {contact.get('email')}\n"
                    f"**Phone Number**: {contact.get('phone_cell')}\n"
                    "----------------------------------------\n"
                    for contact in contacts
                ]
            )
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=(
                    f"Multiple contacts match the details provided. \n{contact_information}\n Please review the list"
                    " above and provide the contact ID for precise identification."
                ),
            )

        return contacts[0].get("id")

    async def fetch_contact(
        self, user, contact_id: str, lo_email: list | None = None, assistant_id: str | None = None, **kwargs
    ) -> dict:
        url = f"{self.base_url}/contacts/{contact_id}"
        token = kwargs.get("token") or await self.get_token(assistant_id, user=user)
        if not lo_email:  # TODO: Remove after team permission implementation
            lo_email = [await self.map_user(user=user, assistant_id=assistant_id)]

        if not token:
            logger.error("Empty auth token")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED, detail="Could not authenticate user into total expert!"
            )
        headers = {
            "Authorization": token,
            "Content-Type": "application/json",
        }
        try:
            response = await self.client.get(url=url, headers=headers)
            response.raise_for_status()
        except HTTPStatusError as e:
            logger.error(f"Error while fetching contact. Status Code: {e.response.status_code}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Some unexpected error occured while while fetching contact",
            )
        json_response = await self._decode_data(response)
        if settings.ENABLE_TOTAL_EXPERT_OAUTH:
            return json_response
        owner = json_response.get("owner")
        if not lo_email[0] or owner.get("email") in lo_email:  # lo_email[0] is None if admin
            return json_response

        logger.error(f"Users {','.join(lo_email)} does not have permission to access contact {contact_id}")
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You do not have permission to access this contact. Please recheck the values and try again!",
        )

    async def check_permission(self, user, contact_id: str, **kwargs):
        return await self.fetch_contact(user, contact_id, **kwargs)
        # lo_email = await self.map_user(user)
        # return user and user.get("owner", {}).get("email") == lo_email

    async def update_contact(self, user, contact_id: str, payload: dict, **kwargs):
        assistant_id = kwargs.get("assistant_id")
        token = await self.get_token(assistant_id, user=user)
        if not token:
            logger.error("Empty auth token")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Could not authenticate user into total expert!",
            )

        headers = {
            "Authorization": token,
            "Content-Type": "application/json",
        }

        if not settings.ENABLE_TOTAL_EXPERT_OAUTH:
            # Determine team members' emails
            team_members = await self._get_team_member_emails(user, token, **kwargs)

            kwargs["lo_email"] = team_members
            # Check if user has permission to update the contact
            await self.check_permission(user, contact_id, **kwargs)

        # Prepare and send the request to update contact
        return await self._send_update_contact_request(contact_id, headers, payload)

    async def _get_team_member_emails(
        self, user, token, assistant_id: str | None = None, admin_email: bool = False, **kwargs
    ):
        lo_admin = await self.is_admin(user, assistant_id=assistant_id, return_email=admin_email)
        if lo_admin:
            return [lo_admin if admin_email else None]

        # fetch teams that user is manager of
        managed_teams = await self.is_team_manager(user, assistant_id=assistant_id, token=token)
        if managed_teams:
            team_members = await self.fetch_team_members(
                managed_teams, assistant_id=assistant_id, token=token, user=user
            )
            return [member.get("email") for member in team_members]

        return [await self.map_user(user=user, assistant_id=assistant_id)]

    async def _send_update_contact_request(self, contact_id: str, headers: dict, payload: dict) -> dict:
        url = f"{self.base_url}/contacts/{contact_id}"
        try:
            # Prepare the payload without modifying the original
            request_payload = payload.copy()
            request_payload["custom"] = request_payload.pop("custom_fields", [])

            response = await self.client.patch(url=url, headers=headers, data=json.dumps(request_payload))
            response.raise_for_status()
            return await self._decode_data(response)
        except HTTPStatusError as e:
            logger.error(f"Error while updating contact. Status Code: {e.response.status_code}:: {e.response.json()}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Some unexpected error occurred while updating contact",
            )

    async def bulk_contact_update(self, user, contact_ids: list[str], payload: dict, **kwargs):
        tasks = [
            self.update_contact(user=user, contact_id=contact_id, payload=payload.copy(), **kwargs)
            for contact_id in contact_ids
        ]
        responses = await asyncio.gather(*tasks, return_exceptions=True)
        # Check for any exceptions and collect failed contact_ids
        failed_contact_ids = [
            contact_id for contact_id, response in zip(contact_ids, responses) if isinstance(response, Exception)
        ]
        if failed_contact_ids:
            logger.error(f"Some errors occurred while updating contacts: {failed_contact_ids}")
            return failed_contact_ids
        logger.info("Successfully updated all contacts.")
        return True

    async def add_note(self, user, note_title: str, note: str, type_id: str, contact_id: str, **kwargs):
        url = f"{self.base_url}/contact-notes"
        assistant_id = kwargs.get("assistant_id")
        token = await self.get_token(assistant_id, user=user)
        if not token:
            logger.error("Empty auth token")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED, detail="Could not authenticate user into total expert!"
            )
        headers = {
            "Authorization": token,
            "Content-Type": "application/json",
        }
        # check if user has permission to add or not
        # find the original owner of the contact
        if not settings.ENABLE_TOTAL_EXPERT_OAUTH:
            team_members = await self._get_team_member_emails(user, token, assistant_id=assistant_id)
            contact_info = await self.fetch_contact(
                user, contact_id, lo_email=team_members, assistant_id=assistant_id
            )  # Permission thing here also
            admin_email = await self.is_admin(user, assistant_id=assistant_id, return_email=True)
            contact_owner = contact_info.get("owner", {}).get("email")
            if admin_email is None and contact_owner not in team_members:
                # if user is not admin or not team manager and its not his contact, he cannot add note
                logger.warning(f"User {user.email} does not have permission to add note to contact {contact_id}")
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="You do not have permission to add note to this contact",
                )

        payload = {
            "title": note_title,
            "note": note,
            "type_id": type_id,
            "contact": {"id": contact_id},
        }
        if not settings.ENABLE_TOTAL_EXPERT_OAUTH:
            payload.update(
                {
                    "owner": {
                        "email": contact_owner,  # needs contact owners email
                    },
                }
            )
        try:
            response = await self.client.post(url=url, headers=headers, data=json.dumps(payload))
            response.raise_for_status()
            # TODO: return actual response
            return response.status_code == 201
        except HTTPStatusError as e:
            logger.error(f"Error while adding note. Status Code: {e.response.status_code}:: {e.response.json()}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Some unexpected error occured while while adding note",
            )

    async def fetch_contact_notes(self, user, contact_id: str, type_id=None, **kwargs) -> list:
        url = f"{self.base_url}/contact-notes"
        assistant_id = kwargs.get("assistant_id")
        token = await self.get_token(assistant_id, user=user)
        if not token:
            logger.error("Empty auth token")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED, detail="Could not authenticate user into total expert!"
            )
        if not settings.ENABLE_TOTAL_EXPERT_OAUTH:
            lo_emails = await self._get_team_member_emails(user, token, **kwargs)
        headers = {
            "Authorization": token,
            "Content-Type": "application/json",
        }
        notes = []
        filter_type_id = [type_id] if type_id else NOTE_TYPE_IDS_MAPPING.values()
        # Define a task for each note type and gather results asynchronously
        tasks = [
            self.fetch_data_from_api(
                url,
                method="GET",
                headers=headers,
                params={"page[number]": 1, "page[size]": self.page_size, "filter": f"type_id={type_id}"},
            )
            for type_id in filter_type_id
        ]

        # Execute tasks concurrently and process the results
        fetched_notes_lists = await asyncio.gather(*tasks)

        # Flatten the list of lists into a single list
        for fetched_notes in fetched_notes_lists:
            notes.extend(fetched_notes)

        if not notes:
            logger.warning("No notes found for the given contact! Empty notes from total expert.")
            return []

        filtered_notes = []
        REVERSE_NOTE_TYPE_IDS_MAPPING = {str(v): k for k, v in NOTE_TYPE_IDS_MAPPING.items()}

        async def is_valid_note(note):
            note_type_id = note.get("type_id", "1")
            is_correct_contact = note.get("contact", {}).get("id") == str(contact_id)
            is_user_authorized = True
            if not settings.ENABLE_TOTAL_EXPERT_OAUTH:
                owner_email = note.get("owner", {}).get("email")
                is_user_authorized = owner_email in lo_emails or await self.is_admin(user, assistant_id=assistant_id)
            is_valid_note_type = note_type_id in {"1", "2"}
            return is_correct_contact and is_user_authorized and is_valid_note_type

        def format_note_data(note, note_type_id):
            return {
                "id": note.get("id"),
                "title": note.get("title"),
                "note": note.get("note"),
                "note_type": REVERSE_NOTE_TYPE_IDS_MAPPING.get(note_type_id),
                "owner_email": note.get("owner", {}).get("email"),
            }

        for note in notes:
            if await is_valid_note(note):
                filtered_notes.append(format_note_data(note, note.get("type_id", "1")))
        return filtered_notes

    async def _fetch_insight_types_from_te(self, assistant_id: str):
        url = f"{self.base_url}/insight-types"
        token = await self.get_token(assistant_id)
        if not token:
            logger.error("Empty auth token")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED, detail="Could not authenticate user into total expert!"
            )
        headers = {
            "Authorization": token,
            "Content-Type": "application/json",
        }
        params = {
            "page[number]": 1,
            "page[size]": self.page_size,
        }
        insight_types = await self.fetch_data_from_api(url, method="GET", headers=headers, params=params)
        return insight_types

    async def list_published_insight_types(self, assistant_id: str):
        async with session_manager() as session:
            query = select(TotalExpertInsightType).where(
                TotalExpertInsightType.assistant_id == assistant_id, TotalExpertInsightType.is_published
            )
            result = await session.execute(query)
            insight_types = result.scalars().all()
            if insight_types:
                return {insight_type.id: insight_type.name for insight_type in insight_types}
            return {}

    async def fetch_all_insights(
        self,
        user,
        pagination: tuple[int, int],
        status: ReadStatus | None = None,
        type: str | None = None,
        session: AsyncSession = None,
        **kwargs,
    ):
        url = f"{self.base_url}/insights"
        assistant_id = kwargs.get("assistant_id")
        token = await self.get_token(assistant_id, user=user)

        if not token:
            logger.error("Empty auth token")
            raise HTTPException(status_code=401, detail="Could not authenticate user into total expert!")

        headers = {"Authorization": token, "Content-Type": "application/json"}
        page, size = pagination
        start, end = (page - 1) * size, page * size
        lo_email = await self.map_user(user, assistant_id=assistant_id)

        DATA_MULTIPLIER = 5
        BATCH_SIZE = size * DATA_MULTIPLIER  # Fetch 5x the requested page size to compensate for read insights

        data_filter = f"type_id={type}" if type else ""
        if not settings.ENABLE_TOTAL_EXPERT_OAUTH:
            data_filter = f"{data_filter},owner_email={lo_email}".strip(",")
        params = {
            "page[number]": math.ceil(page / DATA_MULTIPLIER),
            "page[size]": BATCH_SIZE,
            "sort": "-insight_date",
            "filter": data_filter,
        }

        valid_insights = []
        total_data_count = 0

        published_insight_types = await self.list_published_insight_types(assistant_id)

        while True:
            insights, total_page_count = await self.fetch_data_from_api(
                url, method="GET", headers=headers, params=params, return_page_count=True, limit=BATCH_SIZE
            )

            # handle case where total insights are less than BATCH_SIZE
            if total_page_count == 1 and len(insights) <= BATCH_SIZE:
                total_data_count = len(insights)

            # Add new insights to the DB if they don't exist
            await self._add_new_insights_to_db(user, insights, session)
            # Filter insights based on the read status
            insights = await self._filter_insights_based_on_status(user, insights, status, session)
            # also need to filter from type
            insights = [
                insight for insight in insights if insight.get("type", {}).get("id") in published_insight_types
            ]

            valid_insights.extend(insights)

            if len(valid_insights[start:end]) >= size or params["page[number]"] >= total_page_count:
                break

            params["page[number]"] += 1
            logger.info(f"Fetched {len(valid_insights)} insights from {total_page_count} pages. Fetching next page...")
        # Fetch contact details for valid insights
        tasks = [
            self.fetch_contact(
                user, item.get("contact", {}).get("id"), assistant_id=assistant_id, lo_email=[lo_email], token=token
            )
            for item in valid_insights[start:end]
            if item.get("contact", {}).get("id")
        ]
        contact_details = await asyncio.gather(*tasks, return_exceptions=True)
        filtered_insights = await self._process_insights(valid_insights[start:end], contact_details, assistant_id)
        total_data_count = total_data_count if total_data_count else total_page_count * BATCH_SIZE
        if len(filtered_insights) == 0:
            return [], 0
        return filtered_insights, total_data_count

    async def _process_insights(self, insights, contacts, assistant_id: str):
        """Process the insights by merging them with contact details and preparing the final data structure."""
        filtered_insights = []

        # Fetch insight type descriptions in advance
        insight_types = await self.list_published_insight_types(assistant_id)

        for item, contact in zip(insights, contacts):
            if isinstance(contact, Exception):
                logger.warning(f"Error fetching contact details: {contact}")
                continue

            attributes = self._clean_attributes(item.get("attributes", "[]"))
            insight_type_id = item.get("type", {}).get("id", "")
            insight_type = insight_types.get(insight_type_id, item.get("type", {}).get("type"))
            contact["id"] = item.get("contact", {}).get("id")

            filtered_insights.append(
                {
                    "id": item.get("id"),
                    "attributes": attributes,
                    "description": item.get("data", {}).get("description"),
                    "expires_at": item.get("expires_at"),
                    "insight_date": item.get("insight_date"),
                    "contact": contact,
                    "insight_type": insight_type,
                }
            )
        return filtered_insights

    def _clean_attributes(self, attributes_json):
        """Parses and cleans the attributes JSON data."""
        attributes = json.loads(attributes_json or "[]")

        def clean_attribute(attribute):
            return attribute.get("field_name", "").replace("standardized_", "").strip()

        return [
            {**attr, "field_name": clean_attribute(attr)}
            for attr in attributes
            if attr.get("field_name") and attr.get("field_value")
        ]

    async def _add_new_insights_to_db(self, user, insights, session):
        """Add new insights to the database if they do not already exist."""
        tasks = []

        async def _process_insight(insight):
            insight_id = str(insight.get("id"))
            query = select(TotalExpertInsight).where(
                TotalExpertInsight.id == insight_id, TotalExpertInsight.user_id == user.id
            )
            result = await session.execute(query)
            db_insight = result.scalar_one_or_none()
            if db_insight is None:
                # get type id
                type_id = insight.get("type", {}).get("id")
                if type_id is None:
                    return
                # Add if it doesn't exist in the db
                new_insight = TotalExpertInsight(id=insight_id, user_id=user.id, is_read=False)
                session.add(new_insight)

        for insight in insights:
            tasks.append(_process_insight(insight))

        await asyncio.gather(*tasks)
        await session.commit()

    async def _filter_insights_based_on_status(self, user, insights, status, session):
        """Filter insights based on their read status."""
        insight_ids = (str(insight.get("id")) for insight in insights)
        query = select(TotalExpertInsight.id).where(
            TotalExpertInsight.id.in_(insight_ids), TotalExpertInsight.user_id == user.id
        )

        if status == ReadStatus.READ:
            query = query.where(TotalExpertInsight.is_read)
        elif status == ReadStatus.UNREAD:
            query = query.where(~TotalExpertInsight.is_read)

        result = await session.execute(query)
        desired_insight_ids = {str(insight_id[0]) for insight_id in result.all()}

        return [insight for insight in insights if str(insight.get("id")) in desired_insight_ids]

    async def fetch_insight(self, insight_id: str, token: str | None = None, user: User | None = None):
        url = self.base_url + f"/insights/{insight_id}"
        # TODO: Not Called from anywhere
        token = token or await self.get_token(user=user)
        if not token:
            logger.error("Empty auth token")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED, detail="Could not authenticate user into total expert!"
            )
        headers = {
            "Authorization": token,
            "Content-Type": "application/json",
        }

        try:
            response = await self.client.get(url, headers=headers)
            response.raise_for_status()
        except HTTPStatusError as e:
            logger.error(f"Failed to fetch insight {insight_id}: {e}")
            return None

        return response.json()

    async def add_contact(
        self,
        user,
        source: str,
        first_name: str,
        last_name: str,
        email: str | None = None,
        phone_cell: str | None = None,
        **kwargs,
    ):
        url = f"{self.base_url}/contacts"
        assistant_id = kwargs.get("assistant_id")
        token = await self.get_token(assistant_id, user=user)

        if settings.ENABLE_TOTAL_EXPERT_OAUTH:
            lo_email = await self.map_user(user=user, assistant_id=assistant_id)
        else:
            lo_email = await self.is_admin(user, assistant_id=assistant_id, return_email=True)
            if lo_email == user.email:
                # not admin
                lo_email = await self.map_user(user=user, assistant_id=assistant_id)

        if not token:
            logger.error("Empty auth token")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED, detail="Could not authenticate user into total expert!"
            )
        headers = {
            "Authorization": token,
            "Content-Type": "application/json",
        }
        payload = {
            "source": source,
            "first_name": first_name,
            "last_name": last_name,
        }
        if not settings.ENABLE_TOTAL_EXPERT_OAUTH:
            payload.update(
                {
                    "owner": {"email": lo_email},
                }
            )
        if not any([email, phone_cell]):
            logger.error("Either email or phone_cell is required")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Please provide either email or Phone Number to add contact.",
            )

        payload["email"] = email or ""
        payload["phone_cell"] = phone_cell or ""

        # Add any additional fields from kwargs
        payload.update(kwargs.get("extra_fields", {}))
        request_payload = payload.copy()
        request_payload["custom"] = request_payload.pop("custom_fields", [])
        try:
            response = await self.client.post(url=url, headers=headers, data=json.dumps(request_payload))
            response.raise_for_status()
            json_response = await self._decode_data(response)
            return json_response
        except HTTPStatusError as e:
            logger.error(f"Error while adding contact. Status Code: {e.response.status_code}:: {e.response.json()}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Some unexpected error occured while while adding contact",
            )

    async def fetch_contact_groups(self, **kwargs) -> list:
        url = f"{self.base_url}/contact-groups"
        assistant_id = kwargs.get("assistant_id")
        user = kwargs.get("user")
        token = await self.get_token(assistant_id, user=user)
        if not token:
            logger.error("Empty auth token")
            return HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Could not authenticate! Please check your email and try again!",
            )
        # team_members = await self._get_team_member_emails(
        #     user, token=token, assistant_id=assistant_id, admin_email=True
        # )
        # is_admin = await self.is_admin(user, assistant_id=assistant_id)

        if not settings.ENABLE_TOTAL_EXPERT_OAUTH:
            contact_groups = await self.fetch_published_journeys(assistant_id)
            return [{"id": group.id, "group_name": group.name} for group in contact_groups]

        lo_email = await self.map_user(user, assistant_id=assistant_id)
        headers = {
            "Authorization": token,
            "Content-Type": "application/json",
        }
        params = {"page[number]": 1, "page[size]": self.page_size}
        api_payload = {"headers": headers, "params": params}

        try:
            contact_groups = await self.fetch_data_from_api(url, method="GET", **api_payload)
        except Exception as e:
            logger.error(f"Error while fetching data: {e}")
            return HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=(
                    "Some error occured while fetching contact groups. Please try agai. If the issue persists please"
                    " contact admin!"
                ),
            )

        if not contact_groups:
            logger.warning("Empty Contact Groups!")
            return HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=(
                    "There seems to be no contact groups associated with your account. Please check your contact"
                    " groups. If this is a mistake contact admin!"
                ),
            )

        # Filter contact groups if the user is not an admin
        # if not is_admin:
        #     contact_groups = [
        #         contact_group
        #         for contact_group in contact_groups
        #         if (not is_admin or contact_group.get("owner", {}).get("email") in team_members)
        #         and any(prefix.lower() in contact_group.get("group_name", "").lower() for prefix in JOURNEY_PREFIXES)
        #     ]

        # Filter contact groups where the admin is the owner
        filtered_contact_groups = []

        def is_journey_group(contact_group):
            # Check if the contact group's name contains any known journey prefix
            if settings.ENABLE_TOTAL_EXPERT_OAUTH:
                return True
            group_name = contact_group.get("group_name", "").lower()
            return any(prefix.lower() in group_name for prefix in JOURNEY_PREFIXES)

        async def is_owner(contact_group, email) -> bool:
            # Check if the contact group's owner email matches the given email
            if settings.ENABLE_TOTAL_EXPERT_OAUTH:
                return True
            return contact_group.get("owner", {}).get("email") == email

        async def filter_groups_by_owner(contact_groups):
            tasks = []
            for group in filter(is_journey_group, contact_groups):
                # Add a task for each group to check owner status
                tasks.append(is_owner(group, lo_email))

            # Run all tasks concurrently and get results
            owner_status_results = await asyncio.gather(*tasks)

            # Filter and add groups that are owned by an admin
            for group, owner_status in zip(filter(is_journey_group, contact_groups), owner_status_results):
                if owner_status is True:
                    filtered_contact_groups.append(group)

        # Call the asynchronous function to filter groups
        await filter_groups_by_owner(contact_groups)
        return filtered_contact_groups

    async def create_contact_group(self, user, group_name: str, assistant_id: str):
        url = f"{self.base_url}/contact-groups"
        token = await self.get_token(assistant_id, user=user)

        if not token:
            logger.error("Empty auth token")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED, detail="Could not authenticate user into total expert!"
            )

        headers = {
            "Authorization": token,
            "Content-Type": "application/json",
        }

        if settings.ENABLE_TOTAL_EXPERT_OAUTH:
            payload = {
                "group_name": group_name,
            }
        else:
            payload = {
                "group_name": group_name,
                "owner": {"email": await self.map_user(user=user, assistant_id=assistant_id)},
                "user_editable": True,
            }

        try:
            response = await self.client.post(url=url, headers=headers, json=payload)
            response.raise_for_status()
            response = await self._decode_data(response)
            if "duplicate" in response:
                logger.warning(f"Contact group with name '{group_name}' already exists.")
                raise HTTPException(
                    status_code=status.HTTP_409_CONFLICT,
                    detail=f"Contact group with name '{group_name}' already exists.",
                )
            return response
        except HTTPStatusError as e:
            logger.error(f"Error while creating contact group : {e.response.status_code}:: {e.response.json()}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="An unexpected error occurred while creating contact group",
            )

    async def fetch_published_journeys(self, assistant_id: str) -> list:
        async with session_manager() as session:
            query = select(TotalExpertJourney).where(
                TotalExpertJourney.assistant_id == assistant_id, TotalExpertJourney.is_published
            )
            result = await session.execute(query)
            journeys = result.scalars().all()
            return journeys

    async def fetch_contact_note(self, user, note_id: str | int, **kwargs) -> dict:
        url = f"{self.base_url}/contact-notes/{note_id}"
        assistant_id = kwargs.get("assistant_id")
        token = await self.get_token(assistant_id, user=user)
        lo_email = await self.map_user(user=user, assistant_id=assistant_id)
        if not token:
            logger.error("Empty auth token")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED, detail="Could not authenticate user into total expert!"
            )
        headers = {
            "Authorization": token,
            "Content-Type": "application/json",
        }
        try:
            response = await self.client.get(url=url, headers=headers)
            response.raise_for_status()
        except HTTPStatusError as e:
            logger.error(f"Error while fetching Notes. {e.response.status_code} :: {e.response.json()}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Some error occured while deleting contact note. Please try again!",
            )
        response_json = await self._decode_data(response)
        if settings.ENABLE_TOTAL_EXPERT_OAUTH:
            return response_json
        owner_email = response_json.get("owner", {}).get("email")
        if owner_email != lo_email:
            logger.error("User doesnot have permission to delete note!")
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN, detail="You do not have permission to delete this note!"
            )
        return response_json

    async def check_contact_note_permission(self, user, note_id: str | int, **kwargs):
        return await self.fetch_contact_note(user, note_id, **kwargs)

    async def update_contact_note(self, user, note_id: str | int, payload: dict, **kwargs) -> dict:
        url = f"{self.base_url}/contact-notes/{note_id}/"
        assistant_id = kwargs.get("assistant_id")
        token = await self.get_token(assistant_id, user=user)
        if not token:
            logger.error("Empty auth token")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED, detail="Could not authenticate user into total expert!"
            )
        headers = {
            "Authorization": token,
            "Content-Type": "application/json",
        }
        if not settings.ENABLE_TOTAL_EXPERT_OAUTH:
            await self.check_contact_note_permission(user, note_id, assistant_id=assistant_id)
        try:
            response = await self.client.patch(url, headers=headers, data=json.dumps(payload))
            response.raise_for_status()
        except HTTPStatusError as e:
            logger.error(f"Error while updating note: {e.response.status_code}:: {e.response.json()}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Some error occured while updating contact note. Please try again!",
            )

        json_response = await self._decode_data(response)
        return json_response

    async def delete_note(self, user, note_id: str | int, **kwargs):
        url = f"{self.base_url}/contact-notes/{note_id}"
        assistant_id = kwargs.get("assistant_id")
        token = await self.get_token(assistant_id, user=user)
        lo_email = await self.map_user(user=user, assistant_id=assistant_id)
        if not token:
            logger.error("Empty auth token")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED, detail="Could not authenticate user into total expert!"
            )
        headers = {
            "Authorization": token,
            "Content-Type": "application/json",
        }
        if not settings.ENABLE_TOTAL_EXPERT_OAUTH:
            try:
                response = await self.client.get(url=url, headers=headers)
                response.raise_for_status()
            except HTTPStatusError as e:
                logger.error(f"Error while fetching Notes. {e.response.status_code} :: {e.response.json()}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Some error occured while deleting contact note. Please try again!",
                )
            response_json = await self._decode_data(response)

            owner_email = response_json.get("owner", {}).get("email")
            if owner_email != lo_email:
                logger.error("User doesnot have permission to delete note!")
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN, detail="You do not have permission to delete this note!"
                )

        try:
            response = await self.client.delete(url, headers=headers)
            response.raise_for_status()
        except HTTPStatusError as e:
            logger.error(f"Error while deleting note: {e.response.status_code}:: {e.response.json()}")
            # TODO: Maybe Return 401 instead of 500  (in case of unauthorized)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Some error occured while deleting contact note. Please try again!",
            )

        json_response = await self._decode_data(response)
        return json_response

    async def fetch_activities(self, user, filter: str = "", limit: int | None = None, **kwargs) -> dict:
        url = f"{self.base_url}/activity"
        assistant_id = kwargs.get("assistant_id")
        token = await self.get_token(assistant_id, user=user)
        if not token:
            logger.error("Empty auth token")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED, detail="Could not authenticate user into total expert!"
            )
        headers = {
            "Authorization": token,
            "Content-Type": "application/json",
        }
        if settings.ENABLE_TOTAL_EXPERT_OAUTH:
            lo_owner_ids = [None]
            lo_emails = None
        else:
            is_admin = await self.is_admin(user, assistant_id=assistant_id)
            lo_emails = await self._get_team_member_emails(user, token, **kwargs)
            if is_admin:
                lo_owner_ids = [None]
            else:
                tasks = [self.get_user_id(token, email) for email in lo_emails]

                lo_owner_ids = await asyncio.gather(*tasks, return_exceptions=True)
                lo_owner_ids = [owner_id for owner_id in lo_owner_ids if owner_id is not None]
                if not lo_owner_ids:
                    logger.error("Could not find user id")
                    raise HTTPException(
                        status_code=status.HTTP_404_NOT_FOUND,
                        detail=(
                            "Could not find user in total expert! "
                            "Please check your email and try again or contact admin."
                        ),
                    )

        tasks = []
        limit = limit or DATA_LIMIT // len(lo_owner_ids) or 1
        for owner_id in lo_owner_ids:
            owner_filter = f"owner_id={owner_id}" if owner_id else ""
            params = {
                "page[number]": 1,
                "page[size]": self.page_size,
                "filter": f"{owner_filter},{filter}".strip(",") if filter else owner_filter,
                "sort": "-internal_created_at",  # doesn't look to be supported
            }
            tasks.append(self.fetch_data_from_api(url, method="GET", headers=headers, params=params, limit=limit))

        activities = await asyncio.gather(*tasks, return_exceptions=True)
        activities = [activity for activity in activities if activity is not None]
        activities = [item for sublist in activities for item in sublist]

        # Convert internal_created_at to datetime for sorting, handling missing microseconds
        def _to_sortable_date(val):
            if isinstance(val, datetime):
                return val
            if isinstance(val, str):
                try:
                    return datetime.strptime(val, "%Y-%m-%dT%H:%M:%S.%fZ")
                except ValueError:
                    try:
                        return datetime.strptime(val, "%Y-%m-%dT%H:%M:%SZ")
                    except Exception:
                        return datetime.min
            return datetime.min

        # iso 8601 can be sorted by string itself
        activities.sort(key=lambda x: _to_sortable_date(x.get("internal_created_at")), reverse=True)

        if limit:
            activities = activities[:limit]
        tasks = []
        for item in activities:
            contact_id = item.get("contact_id")
            if contact_id:
                tasks.append(self.fetch_contact(user, contact_id, assistant_id=assistant_id, lo_email=lo_emails))
        FIELD_MAPPING, FIELD_TYPE_MAPPING, DISPLAY_FIELDS, SEARCH_FIELDS = await fetch_all_fields(
            kwargs.get("assistant_id"), folder=DataFieldFolder.ACTIVITY
        )
        contact_details = await asyncio.gather(*tasks, return_exceptions=True)
        mapped_keys = FIELD_MAPPING.keys()
        filtered_data = []

        for item, contact_detail in zip(activities, contact_details):
            if isinstance(contact_detail, Exception):
                continue
            contact_id = item.get("contact_id")
            email_file = None
            email_details = item.get("details", {})
            if email_details:
                email_file = email_details.get("filename", "")

            item["email_content"] = (
                f"<a href='https://totalexpert.net/email/archive/{email_file}' target='_blank'>View Message</a>"
                if email_file
                else ""
            )
            item["contact_name"] = (
                f"{contact_detail.get('first_name', '')} {contact_detail.get('last_name', '')}".strip()
                if contact_id
                else ""
            )
            filtered_data.append(
                {
                    FIELD_MAPPING[k]: (
                        type_converter.parse_date(item[k], time=True) if k == "internal_created_at" else item[k]
                    )
                    for k in mapped_keys
                    if k in item
                }
            )

        return filtered_data

    async def get_user_details(self, token, email: str) -> dict:
        url = f"{self.base_url}/users"
        headers = {
            "Authorization": token,
            "Content-Type": "application/json",
        }
        params = {"page[number]": 1, "page[size]": 1, "filter": f"email={email}"}
        json_response = await self.fetch_data_from_api(url, method="GET", headers=headers, params=params)
        if len(json_response) == 1:
            return json_response[0]

        logger.warning("Multiple or no users found with the given email!")
        return {}

    async def get_user_id(self, token, email: str) -> bool:
        user_details = await self.get_user_details(token=token, email=email)
        return user_details.get("id")

    async def get_data_type(self, assistant_id: str, field_name: str, folder: DataFieldFolder) -> type:
        async with session_manager() as session:
            # logger.info(f"Fetching field type for field :{field_name}")
            query = select(DataField.type).where(
                and_(
                    DataField.assistant_id == assistant_id,
                    DataField.field_id == field_name,
                    DataField.folder == folder,
                )
            )
            query_res = await session.execute(query)
            value = query_res.scalar_one_or_none()
            if not value:
                # TODO Temporary
                return MAP_DATAFIELD_TO_TYPE.get("text", str)
                raise HTTPException(status_code=400, detail="The field doesnot exist.")
            # TODO Temporary Fix
            return MAP_DATAFIELD_TO_TYPE.get(value, str)

    def get_operator_func(self, field_type, operator_name):
        if field_type == str:
            return STRING_OPERATORS.get(operator_name)
        if field_type == float or field_type == int:
            return NUMERIC_OPERATORS.get(operator_name)
        if field_type == datetime:
            return DATE_OPERATORS.get(operator_name)
        return None

    async def filter_data(self, data, filter, assistant_id: str, folder: DataFieldFolder = DataFieldFolder.LOAN):
        # Filter data based on the provided filter
        # OPERATOR_MAPPING = {
        #     "GreaterThanEquals": operator.ge,
        #     "LessThanEquals": operator.le,
        #     "Equal": operator.eq,
        #     "NotEqual": operator.ne,
        #     "GreaterThan": operator.gt,
        #     "LessThan": operator.lt,
        #     "Exact": lambda a, b: str(a).lower() == str(b).lower() if a is not None and b is not None else False,
        #     "Contains": lambda a, b: str(b).lower() in str(a).lower() if a is not None and b is not None else False,
        # }
        field_types = {}
        for key, _ in data.items():
            field_types[key] = await self.get_data_type(assistant_id=assistant_id, field_name=key, folder=folder)
        data = {key: type_converter.convert_string_data(field_types, key, val) for key, val in data.items()}
        matched = True
        for f in filter:
            field = f["field"]
            filter_operator = f["operator"]
            value = f["value"]
            filter_value = type_converter.convert_string_data(field_types, field, value)
            if field in data.keys() and data.get(field) is not None:
                compare = self.get_operator_func(field_types[field], filter_operator)
                # data_value = type_converter.convert_string_data(field_types, field, data.get(field))
                data_value = data.get(field)
                if field_types[field] == datetime:
                    data_value = parse(data_value)
                    filter_value = parse(filter_value)
                if not compare:
                    matched = False
                    break
                if not compare(data_value, filter_value):
                    matched = False
                    break
            else:
                matched = False
                break
        if matched:
            return data
        return None

    async def fetch_db_journeys(self, assistant_id: str) -> list:
        async with session_manager() as session:
            query = select(TotalExpertJourney).where(TotalExpertJourney.assistant_id == assistant_id)
            result = await session.execute(query)
            journeys = result.scalars().all()
            return journeys

    async def fetch_contact_group(self, id: str | int, assistant_id, user: User | None = None) -> dict:
        url = f"{self.base_url}/contact-groups/{id}"
        token = await self.get_token(assistant_id=assistant_id, user=user)
        if not token:
            logger.error("Empty auth token")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED, detail="Could not authenticate user into total expert!"
            )
        headers = {
            "Authorization": token,
            "Content-Type": "application/json",
        }
        try:
            response = await self.client.get(url=url, headers=headers)
            response.raise_for_status()
            contact_group = await self._decode_data(response)
            return contact_group
        except HTTPStatusError as e:
            if e.response.status_code == 404:
                raise NotFoundError("Contact group not found")
            logger.error(f"Error while fetching contact group: {e.response.status_code}:: {e.response.text}")
            return {}
        except Exception as e:
            logger.error(f"Some unexpected error occured while fetching contact group: {e}")
            return {}

    async def create_journey(
        self, assistant_id: str, journey_id: str, session: AsyncSession, user: User | None = None
    ):
        try:
            journey = await self.fetch_contact_group(journey_id, assistant_id=assistant_id, user=user)
        except NotFoundError:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Contact Group not found in TotalExpert. Please recheck contact group id and try again!",
            )

        if not journey:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Could not fetch journey from TotalExpert. Please try again after some time.",
            )
        is_custom_journey = "custom" in journey.get("group_name").lower()

        # check if journey exists with same id or name or not
        query = select(TotalExpertJourney).where(
            and_(
                or_(TotalExpertJourney.id == str(journey.get("id")), TotalExpertJourney.name == journey.get("name")),
                TotalExpertJourney.assistant_id == assistant_id,
            )
        )
        result = await session.execute(query)
        existing_journey = result.scalar_one_or_none()
        if existing_journey:
            logger.warning(f"Contact Group Already Exists: {journey.get('id')}")
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail=(
                    "Contact group already exists in the system with given name or id. Please recheck id and try"
                    " again."
                ),
            )

        new_journey = TotalExpertJourney(
            id=str(journey.get("id")),
            assistant_id=assistant_id,
            name=journey.get("group_name"),
            type=TEJourneyTypes.STANDARD,
            is_custom=is_custom_journey,
        )
        session.add(new_journey)
        await session.commit()
        await session.refresh(new_journey)
        return new_journey

    async def delete_journey(self, assistant_id: str, journey_id: str):
        async with session_manager() as session:
            query = select(TotalExpertJourney).where(
                TotalExpertJourney.assistant_id == assistant_id,
                TotalExpertJourney.id == journey_id,
            )
            journey = await session.execute(query)
            journey = journey.scalar_one_or_none()
            if not journey:
                logger.warning(f"Journey Not Found: {journey_id}:: Assistant ID: {assistant_id}")
                raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Journey Not Found!")
            await session.delete(journey)
            await session.commit()
            return journey

    async def update_journey(
        self,
        id: str,
        assistant_id: str,
        data: dict,
        session: AsyncSession,
        **kwargs,
    ) -> TotalExpertJourney:
        user = kwargs.get("user")
        query = select(TotalExpertJourney).where(
            TotalExpertJourney.assistant_id == assistant_id,
            TotalExpertJourney.id == id,
        )
        journey = await session.execute(query)
        journey = journey.scalar_one_or_none()
        if not journey:
            logger.warning(f"Journey with id: {id} not found in DB!")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Journey not found",
            )

        user = kwargs.get("user")
        user_info = {
            "user_id": str(user.id),
            "user_name": user.username,
            "email": user.email,
            "name": user.name,
        }

        # update all data dict
        for key, value in data.items():
            setattr(journey, key, value)

        if journey.meta:
            journey.meta = {
                **journey.meta,
                "user_info": {**journey.meta["user_info"], "updated_by": user_info},
            }
        else:
            journey.meta = {"user_info": {"updated_by": user_info}}

        if data.get("meta"):
            journey.meta.update(data.meta)

        await session.commit()
        await session.refresh(journey)
        return journey

    # journeys
    async def sync_journeys(self, user_id: str, assistant_id: str) -> None:
        async with session_manager() as session:
            query = select(User).where(User.id == user_id)
            user = await session.execute(query)
            user = user.scalar_one_or_none()
            if not user:
                raise ValueError("User not found in the database. Please check your user ID.")
            # or query by email if needed

            # fetch journeys from db
            db_journeys = await self.fetch_db_journeys(assistant_id=assistant_id)

            # Parallelize fetching journeys from TE
            async def process_db_journey(db_journey):
                try:
                    journey = await self.fetch_contact_group(db_journey.id, user=user)
                except NotFoundError as e:
                    logger.error(str(e))
                    # if journey not found in te, delete it from db
                    await self.delete_journey(assistant_id, db_journey.id)
                    return
                except Exception:
                    return
                # now compare name
                if journey.get("name") != db_journey.name:
                    updated_data = {"name": journey.get("name")}
                    await self.update_journey(db_journey.id, assistant_id, updated_data, session)

            await asyncio.gather(*(process_db_journey(db_journey) for db_journey in db_journeys))

    async def fetch_journeys(self, assistant_id: str):
        async with session_manager() as session:
            journeys = await session.execute(
                select(TotalExpertJourney).where(TotalExpertJourney.assistant_id == assistant_id)
            )
            return [journey.__dict__ for journey in journeys.scalars().all()]

    async def sync_insight_types(self, assistant_id: str, session: AsyncSession) -> None:
        insight_types = await self._fetch_insight_types_from_te(assistant_id)

        # now check db and if there are any new insight types, add them to the db
        query = select(TotalExpertInsightType).where(TotalExpertInsightType.assistant_id == assistant_id)
        result = await session.execute(query)
        db_insight_types = result.scalars().all()
        db_insight_type_ids = [insight_type.id for insight_type in db_insight_types]
        for insight_type in insight_types:
            # if new add to db
            if str(insight_type.get("id")) not in db_insight_type_ids:
                new_insight_type = TotalExpertInsightType(
                    id=str(insight_type.get("id")),
                    name=insight_type.get("description"),
                    type=insight_type.get("type"),
                    is_published=True,
                    assistant_id=assistant_id,
                )
                session.add(new_insight_type)
        await session.commit()

        # now check if any insight types have been removed
        for insight_type in db_insight_types:
            if insight_type.id not in [str(insight_type.get("id")) for insight_type in insight_types]:
                await session.delete(insight_type)
        await session.commit()
        return None

    async def fetch_journey_details(self, assistant_id: str, journey_name: str = None, journey_id: str = None) -> dict:
        async with session_manager() as session:
            query = select(TotalExpertJourney).where(
                TotalExpertJourney.assistant_id == assistant_id,
            )
            if journey_id:
                query = query.where(TotalExpertJourney.id == journey_id)
            elif journey_name:
                query = query.where(TotalExpertJourney.name == journey_name)

            result = await session.execute(query)
            journey = result.scalar_one_or_none()
            if journey:
                return journey.__dict__
            return {}

    async def check_stopping_condition(self, item, stopping_condition):
        field_name = stopping_condition.get("field_name", None)
        field_value = stopping_condition.get("field_value", None)
        field_type = stopping_condition.get("field_type", None)
        try:
            if field_name and field_value and field_type:
                field_mapping = {field_name: field_type}
                actual_value = item.get(field_name, None)

                field_value_parsed = field_value
                actual_value_parsed = actual_value

                if not isinstance(field_value_parsed, field_type):
                    field_value_parsed = type_converter.convert_to_type(field_mapping, field_name, field_value_parsed)

                if not isinstance(actual_value_parsed, field_type):
                    actual_value_parsed = type_converter.convert_to_type(
                        field_mapping, field_name, actual_value_parsed
                    )

                if field_type == datetime:
                    # In come cases getting timezone information that breaks the comparision logic
                    field_value_parsed = field_value_parsed.replace(tzinfo=None)
                    actual_value_parsed = actual_value_parsed.replace(tzinfo=None)

                if actual_value_parsed:
                    if operator.lt(actual_value_parsed, field_value_parsed):
                        logger.info(
                            f"Early Stopping. Field Name:{field_name}, Filter_value:{field_value_parsed}"
                            f"and Stopping Value: {actual_value_parsed}"
                        )
                        return True
        except Exception as e:
            logger.info(f"Error Occured:{e}")
        return False

    async def handle_multi_impersonation(self, user: User, selected_user_id: str, assistant_id: str):
        if selected_user_id and settings.ENABLE_TOTAL_EXPERT_OAUTH:
            async with session_manager() as session:
                if str(selected_user_id) == str(user.id):
                    await session.execute(
                        update(CustomLoanOfficerMapping)
                        .where(CustomLoanOfficerMapping.active)
                        .where(CustomLoanOfficerMapping.email == user.email)
                        .where(CustomLoanOfficerMapping.assistant_id == assistant_id)
                        .values(active=False)
                    )
                    await session.commit()
                    return

                query = select(User.email).where(User.id == selected_user_id)
                result = await session.execute(query)
                impersonation_email = result.scalar_one_or_none()
                if not impersonation_email:
                    raise HTTPException(
                        status_code=HTTP_400_BAD_REQUEST, detail="The impersonation_id is not valid or doesnot exist"
                    )
                query = select(CustomLoanOfficerMapping).where(
                    CustomLoanOfficerMapping.email == user.email,
                    CustomLoanOfficerMapping.lo_email == impersonation_email,
                    CustomLoanOfficerMapping.assistant_id == assistant_id,
                )
                result = await session.execute(query)
                custom_lo_mapping = result.scalar_one_or_none()
                if not custom_lo_mapping:
                    raise HTTPException(
                        status_code=HTTP_400_BAD_REQUEST,
                        detail="You cannot select a user that has not been impersonated.",
                    )
                logger.info(f"Found the email of the impersonating_user {custom_lo_mapping.lo_email}")
                if not custom_lo_mapping.active:
                    await session.execute(
                        update(CustomLoanOfficerMapping)
                        .where(CustomLoanOfficerMapping.active)
                        .where(CustomLoanOfficerMapping.email == user.email)
                        .where(CustomLoanOfficerMapping.assistant_id == assistant_id)
                        .where(CustomLoanOfficerMapping.lo_email != custom_lo_mapping.lo_email)
                        .values(active=False)
                    )
                    await session.execute(
                        update(CustomLoanOfficerMapping)
                        .where(CustomLoanOfficerMapping.email == user.email)
                        .where(CustomLoanOfficerMapping.assistant_id == assistant_id)
                        .where(CustomLoanOfficerMapping.lo_email == impersonation_email)
                        .values(active=True)
                    )
                    await session.commit()


total_expert_utils = TotalExpertUtils()
