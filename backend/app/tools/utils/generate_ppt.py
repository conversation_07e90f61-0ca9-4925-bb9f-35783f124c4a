import json
import os
from datetime import datetime
from io import BytesIO

from config import settings
from hooks.s3 import get_s3_hook
from loguru import logger
from openai import AsyncOpenAI
from pptx import Presentation

client = AsyncOpenAI(api_key=settings.OPENAI_API_KEY, organization=settings.OPENAI_ORG_ID)


async def generate_ppt_content(title, context: str, num_pages: int) -> str:
    if context:
        prompt = (
            f"Generate {num_pages} slides for a presentation on the topic `{title}` "
            f"based on the following context `{context}`. Produce 100 to 150 words per slide. Return as JSON."
        )
    else:
        prompt = (
            f"Generate {num_pages} slides for a presentation on the topic `{title}`. "
            f"Produce 100 to 150 words per slide. Return as JSON."
        )

    completion = await client.chat.completions.create(
        model="gpt-4o",
        response_format={"type": "json_object"},
        messages=[
            {
                "role": "system",
                "content": """
                You are a helpful assistant that presentation slide based on a topic. \
                Respond with title (without side number) and content for each slide.
                Generate content in 2 to 3 sentences with 50 to 60 words per sentence.  \
                Provide your answer in JSON structure like this:
                ```
                    {
                        "slides": [
                            {
                                "title": "title for page 1",
                                "content": {
                                    "sentences": ["sentence 1", "sentence 2", "sentence 3"],
                                }
                            },
                            {
                                "title": "title for page 2",
                                "content": {
                                    "sentences": ["sentence 1", "sentence 2", "sentence 3"],
                                }
                            }
                        ]
                    }
                ```
            """,
            },
            {"role": "user", "content": prompt},
        ],
    )

    response = completion.choices[0].message.content
    logger.info(f"Generated ppt content: {response}")
    return response


def save_and_upload_presentation(s3_hook, response: str, ppt_title: str) -> tuple[str, str]:
    try:
        response = json.loads(response)
        slide_data = response["slides"]
        prs = Presentation()

        font_path = os.path.join(os.path.abspath(settings.BASE_DIR), "font", "Calibri Regular.ttf")
        for slide in slide_data:
            slide_layout = prs.slide_layouts[1]
            new_slide = prs.slides.add_slide(slide_layout)

            if slide.get("title"):
                title = new_slide.shapes.title
                title.text = slide["title"]

            if points := slide.get("content", {}).get("sentences"):
                shapes = new_slide.shapes
                body_shape = shapes.placeholders[1]
                tf = body_shape.text_frame
                tf.text = points[0]
                for point in points[1:]:
                    p = tf.add_paragraph()
                    p.text = point
                    p.level = 0
                    tf.fit_text(font_family="Calibri", max_size=18, bold=True, font_file=font_path)

        # Save the presentation to a byte stream
        presentation_stream = BytesIO()
        prs.save(presentation_stream)
        presentation_stream.seek(0)

        filename = f"{ppt_title}_{datetime.now().strftime('%Y%m%d%H%M%S')}.pptx"
        response = s3_hook.put_object(object_name=filename, file=presentation_stream)

        if response["ResponseMetadata"]["HTTPStatusCode"] == 200:
            ppt_url = s3_hook.get_presigned_url_for_download(filename)
            return (ppt_url, slide_data)
        else:
            raise Exception("Failed to upload ppt file.")

    except Exception:
        raise Exception("Error occurred while generating and uploading presentation.")


async def generate_ppt(title: str, context: str, num_pages: int, assistant_id: str) -> tuple[str, str] | None:
    try:
        response = await generate_ppt_content(title, context, num_pages)
        s3_hook = await get_s3_hook(assistant_id)
        ppt_url, slide_data = save_and_upload_presentation(s3_hook, response, title)
        return (ppt_url, slide_data)
    except Exception as e:
        logger.info(f"Error occurred while generating ppt: {e}")
        return None
