import json
from datetime import datetime

import chardet
import pandas as pd
from clients import client_config
from config import settings
from db.models import Assistant
from db.session import get_session
from hooks.s3 import get_s3_hook
from httpx import AsyncClient, HTTPStatusError
from loguru import logger
from openai import OpenAI
from schema.enums import AssistantSubTypes
from sqlalchemy import select
from tools.fields.encompass import fetch_all_fields
from tools.loan_data.category_data import fetch_context_data
from tools.payloads import (
    get_active_loan_terms,
    get_closed_loan_terms,
    get_locked_loan_terms,
    get_purchase_loan_terms,
    get_restricted_terms,
    get_terms_for_epo_data,
)
from tools.schema import Component, ComponentEnum, ToolResponse
from tools.utils.encompass.loan import loan_utils
from utils.type_conversion import type_converter

from .constants import VALID_MATCH_TYPES_NON_STR, VALID_MATCH_TYPES_STR

# TODO: Fetch result from all pages and not just 1000 results.


class LoanFunction:
    NO_CONTEXT_LOAN_MESSAGE = "There are {total_loans} loans on your system that matches the given criteria."

    IN_CONTEXT_LOAN_MESSAGE = (
        "Provide the number of loans fetched and summarize some important features of all these loans like total loan"
        " amount, highest and lowest loan amount, interest rate etc. but don't display all the data. The summary must"
        " be 2-3 sentences long. Here are all the loan details:\n\n{loan_details}."
    )

    def __init__(self) -> None:
        self.client = AsyncClient(timeout=None)
        self.url = "https://api.elliemae.com/encompass/v1/loanPipeline"
        self.NUMBER_OF_LOANS: int = 1

    async def initialize_fields(self, assistant_id: str):
        """Fetch all field-related attributes"""
        data_fields = await fetch_all_fields(assistant_id=assistant_id)
        FIELD_MAPPING = data_fields.get("FIELD_MAPPING", {})
        DISPLAY_FIELDS = data_fields.get("DISPLAY_FIELDS", [])
        SEARCH_FIELDS = data_fields.get("SEARCH_FIELDS", [])
        FIELD_TYPE_MAPPING = data_fields.get("FIELD_TYPE_MAPPING", {})
        EXTRA_FIELDS = data_fields.get("EXTRA_FIELDS", {})
        return (
            FIELD_MAPPING,
            DISPLAY_FIELDS,
            SEARCH_FIELDS,
            FIELD_TYPE_MAPPING,
            EXTRA_FIELDS,
        )

    async def format_encompass_data(self, field: str, value: str, **kwargs) -> str | int | float | None:
        """Formats encompass data for easy readability.
        Like: Converting floating values into 2 decimal point. Convert date to specific format."""
        FIELD_TYPE_MAPPING = kwargs.get("field_type_mapping")
        return type_converter.convert_string_data(FIELD_TYPE_MAPPING, field, value)

    def list_functions(self) -> dict:
        functions = {
            "get_loan_details_by_loan_number": self.get_loan_details_by_loan_number,
            "get_loan_details_by_name": self.get_loan_details_by_name,
            "get_loan_details": self.get_loan_details,
            "get_current_interest_rates": self.get_current_interest_rates,
            "get_refi_report": self.get_refi_report,
            "get_property_valuation_details": self.get_property_valuation_details,
            "convert_field_name_to_api_field_name": self.convert_field_name_to_api_field_name,
            "convert_input_to_payload": self.fetch_loan_details,
            "categorize_loan_type": self.categorize_loan_type,
            "get_open_condition": self.get_open_condition,
        }
        return functions

    async def _decode_loan_details(self, response) -> dict | None:
        response_data = None
        try:
            response_text = response.text
            response_data = json.loads(response_text)
        except json.JSONDecodeError as e:
            logger.error(f"JSON Decode error when decoding output from encompass: {e}")
            try:
                response_text = response.content.decode("utf-8", errors="ignore")
                response_data = json.loads(response_text)
            except Exception as e:
                logger.error(f"Some error occured while converting encompass response to JSON: {e}")
                return None

        return response_data

    async def categorize_loan_type(self, **kwargs):
        loan_type = kwargs.get("loan_type", "other")
        # Fetch context data from the database
        logger.info(f"Loan Category: {loan_type}")
        context_data = fetch_context_data(loan_type.lower())
        return context_data

    async def get_loan_details_by_loan_number(self, **kwargs) -> dict | None:
        loan_number = kwargs.get("loan_number")
        assistant_id = kwargs.get("assistant_id")
        data_only = kwargs.get("data_only", False)
        user = kwargs.get("user")
        FIELD_MAPPING, DISPLAY_FIELDS, SEARCH_FIELDS, FIELD_TYPE_MAPPING, EXTRA_FIELDS = await self.initialize_fields(
            assistant_id
        )
        token = await loan_utils.get_impersonation_auth_token(**kwargs)
        if not token:
            return ToolResponse(
                message=(
                    "Could not verify user. Please make sure your email"
                    "address match the email address in your loan account."
                )
            )

        payload = {
            "filter": {
                "operator": "AND",
                "terms": [
                    {
                        "canonicalName": "Fields.364",
                        "value": str(loan_number),
                        "matchType": "exact",
                    }
                ],
            },
            "fields": list(FIELD_MAPPING.keys()),
            "includeArchivedLoans": True,
        }
        # client config must have restriction enabled and if the user doesn't have impersonation access
        # (restriction enabled)
        lo_details = {}
        if client_config.ENCOMPASS_LOAN_IMPERSONATION_RESTRICTION and not user.encompass_impersonation_access:
            lo_details = await loan_utils.get_loan_officer_details(**kwargs)

        payload["filter"]["terms"].extend(get_restricted_terms(**lo_details))
        headers = {"Content-Type": "application/json", "Authorization": token}
        logger.info(f"Requesting loan details for loan number {loan_number}...")
        try:
            response = await self.client.post(self.url, headers=headers, json=payload)
            response.raise_for_status()
        except HTTPStatusError as e:
            logger.error(f"Request failed with status {e.response.status_code} {e.response.content}")
            return ToolResponse(
                message=(
                    "Sorry, I could not process your request. Please check if the loan number exists in your loan"
                    " file."
                )
            )

        response_data = await self._decode_loan_details(response)

        if not response_data:
            logger.warning("No response data.")

            return ToolResponse(
                message=(
                    "It seems like there is no loan with the given loan number. Recheck the loan number and try again"
                )
            )

        fields = response_data[0].get("fields", {})
        mapped_fields = {
            FIELD_MAPPING.get(k): await self.format_encompass_data(k, v, field_type_mapping=FIELD_TYPE_MAPPING)
            for k, v in fields.items()
            if k in FIELD_MAPPING
        }
        logger.info(f"Successfully retrieved loan details for loan number {loan_number}.")
        mapped_fields["Lock Status"] = False if mapped_fields.get("Lock Status") else True
        if settings.CLIENT_NAME.lower() == "synergpt":
            message = (
                f"Loan Details: {mapped_fields}\n\n PAYMENT DETAILS:"
                f" {await loan_utils.payment_details_synergpt(fields)}"
            )
        else:
            message = mapped_fields
        HEADERS = [field for field in DISPLAY_FIELDS if field in mapped_fields.keys()]

        if data_only:
            return mapped_fields

        return ToolResponse(
            message=message,
            payload=payload,
            component=Component(
                component_name=ComponentEnum.LoanDetails,
                component_props={
                    "data": [mapped_fields],
                    "headers": HEADERS,
                    "search_fields": [field for field in SEARCH_FIELDS if field in HEADERS],
                },
            ),
        )

    async def get_loan_details_by_name(self, **kwargs) -> list[dict] | None:
        assistant_id = kwargs.get("assistant_id")
        FIELD_MAPPING, DISPLAY_FIELDS, SEARCH_FIELDS, FIELD_TYPE_MAPPING, EXTRA_FIELDS = await self.initialize_fields(
            assistant_id
        )
        first_name = kwargs.get("first_name", "")
        last_name = kwargs.get("last_name", "")
        user = kwargs.get("user")

        token = await loan_utils.get_impersonation_auth_token(**kwargs)
        if not token:
            return ToolResponse(
                message=(
                    "Could not verify user. Please make sure your email"
                    "address match the email address in your loan account."
                )
            )

        payload = {
            "filter": {
                "operator": "AND" if first_name and last_name else "OR",
                "terms": [
                    {
                        "canonicalName": "Fields.4000",
                        "value": first_name or last_name,
                        "matchType": "contains",
                    },
                    {
                        "canonicalName": "Fields.4002",
                        "value": last_name or first_name,
                        "matchType": "contains",
                    },
                ],
            },
            "fields": list(FIELD_MAPPING.keys()),
            "sortOrder": [{"canonicalName": "fields.3142", "order": "desc"}],
            "includeArchivedLoans": True,
        }

        # client config must have restriction enabled and if the user doesn't have impersonation access
        # (restriction enabled)
        lo_details = {}
        if client_config.ENCOMPASS_LOAN_IMPERSONATION_RESTRICTION and not user.encompass_impersonation_access:
            lo_details = await loan_utils.get_loan_officer_details(**kwargs)

        payload["filter"]["terms"].extend(get_restricted_terms(**lo_details))
        headers = {"Content-Type": "application/json", "Authorization": token}
        params = {"limit": 2}
        logger.info(f"Requesting loan details for {first_name or last_name}...")
        try:
            response = await self.client.post(self.url, headers=headers, json=payload, params=params)
            response.raise_for_status()
        except HTTPStatusError as e:
            logger.error(f"Request failed with status {e.response.status_code}:: {response.json().get('details', '')}")
            return ToolResponse(
                message=(
                    "Sorry, I could not process your request. Please check if the Borrower exists in your loan file."
                )
            )

        response_data = await self._decode_loan_details(response)

        if not response_data:
            logger.warning("No response data.")
            return ToolResponse(
                message="It seems like there is no loan with the given details. Recheck the details and try again"
            )
        all_mapped_fields = []
        for record in response_data:
            fields = record.get("fields", {})
            mapped_fields = {
                FIELD_MAPPING.get(k): await self.format_encompass_data(k, v, field_type_mapping=FIELD_TYPE_MAPPING)
                for k, v in fields.items()
                if k in FIELD_MAPPING
            }
            logger.info(f"Successfully retrieved loan details for {first_name or last_name}.")
            mapped_fields["Lock Status"] = False if mapped_fields.get("Lock Date") else True
            all_mapped_fields.append(mapped_fields)

        HEADERS = [field for field in DISPLAY_FIELDS if field in mapped_fields.keys()]
        return ToolResponse(
            message=json.dumps(all_mapped_fields),
            payload=payload,
            component=Component(
                component_name=ComponentEnum.LoanDetails,
                component_props={
                    "data": all_mapped_fields,
                    "headers": HEADERS,
                    "search_fields": [field for field in SEARCH_FIELDS if field in HEADERS],
                },
            ),
        )

    async def get_loan_details_by_phone_number(self, **kwargs) -> dict | None:
        phone_number = kwargs.get("phone_number")
        division_id = kwargs.get("division_id")
        assistant_id = None
        async for session in get_session():
            query = select(Assistant.id).where(Assistant.sub_type == AssistantSubTypes.ENCOMPASS)
            if division_id:
                query = query.where(Assistant.division_id == division_id)
            result = await session.execute(query)
            assistant_id = result.scalars().first()
            if assistant_id:
                break

        FIELD_MAPPING, DISPLAY_FIELDS, SEARCH_FIELDS, FIELD_TYPE_MAPPING, EXTRA_FIELDS = await self.initialize_fields(
            assistant_id
        )
        token = await loan_utils.get_auth_token(assistant_id)

        borrower_phone_field = next((k for k, v in FIELD_MAPPING.items() if v == "Borrower Phone"), None)

        payload = {
            "filter": {
                "operator": "AND",
                "terms": [
                    {
                        "canonicalName": borrower_phone_field,
                        "value": str(phone_number),
                        "matchType": "exact",
                    }
                ],
            },
            "fields": list(FIELD_MAPPING.keys()),
            "includeArchivedLoans": True,
        }
        headers = {"Content-Type": "application/json", "Authorization": token}
        try:
            response = await self.client.post(self.url, headers=headers, json=payload)
            response.raise_for_status()
        except HTTPStatusError as e:
            logger.error(f"Request failed with status {e.response.status_code} {e.response.content}")
            return {"message": "Some error occured while fetching loan details for the user"}

        response_data = await self._decode_loan_details(response)

        if not response_data:
            logger.warning("No response data.")

            return {"message": "No loan details found for the user"}

        fields = response_data[0].get("fields", {})
        mapped_fields = {
            FIELD_MAPPING.get(k): await self.format_encompass_data(k, v, field_type_mapping=FIELD_TYPE_MAPPING)
            for k, v in fields.items()
            if k in FIELD_MAPPING
        }
        logger.info(f"Successfully retrieved loan details for phone number {phone_number}.")
        mapped_fields["Lock Status"] = False if mapped_fields.get("Lock Status") else True

        return mapped_fields

    async def get_loan_details(self, **kwargs) -> dict | list[dict] | None:
        loan_number = kwargs.get("loan_number")
        first_name = kwargs.get("first_name", "")
        last_name = kwargs.get("last_name", "")

        if loan_number:
            return await self.get_loan_details_by_loan_number(**kwargs)

        elif first_name or last_name:
            return await self.get_loan_details_by_name(**kwargs)

        else:
            return ToolResponse(
                message="Sorry! Could not find the loan details. Please check your query and try again"
            )

    async def get_current_interest_rates(self, **kwargs) -> dict | None:
        rates = await loan_utils.get_interest_rates()
        return rates

    async def get_refi_report(self, **kwargs) -> ToolResponse | str | None:
        user = kwargs.get("user")
        fha_rate = kwargs.get("fha_rate")
        va_rate = kwargs.get("va_rate")
        conv_rate = kwargs.get("conv_rate")
        conv_non_rate = kwargs.get("conv_non_rate")
        rate_t = kwargs.get("rate_t")
        if not fha_rate or not va_rate or not conv_rate or not conv_non_rate or not rate_t:
            return "Need all rates i.e FHA, VA, Conventional, Conventional Non, and Rate Type. to generate the report."
        report = await loan_utils.get_refi_report(
            user, fha_rate, va_rate, conv_rate, conv_non_rate, rate_t, assistant_id=kwargs.get("assistant_id")
        )

        if report:
            s3_hook = await get_s3_hook(assistant_id=kwargs.get("assistant_id"))
            excel_url = (
                loan_utils.generate_and_upload_excel(s3_hook=s3_hook, prefix="refi_report", report_data=report)
                if client_config.DISPLAY_EXCEL_LINK_ON_LOAN_TABLE
                else None
            )

            HEADERS = list(report[0].keys())

            component = Component(
                component_name=ComponentEnum.LoanDetails,
                component_props={
                    "headers": HEADERS,
                    "search_fields": HEADERS,
                    "link": excel_url,
                    "data": report,
                    "is_downloadable": client_config.DISPLAY_EXCEL_LINK_ON_LOAN_TABLE,
                },
            )
            message = "Your Refi Report has been generated."
        else:
            message = (
                "It seems there are no loans eligible for refinancing. Please try again with some other parameters. If"
                " you think this is a mistake, Contact admin."
            )

        return ToolResponse(
            message=message,
            component=component if report else None,
        )

    async def get_property_valuation_details(self, **kwargs) -> ToolResponse | None:
        address1 = kwargs.get("street_address")
        address2 = kwargs.get("city_state")

        if not address1 or not address2:
            return ToolResponse(message="Both street address and city, state are required.")

        params = {"address1": address1, "address2": address2}
        headers = {"APIKey": settings.ATTOM_API_KEY, "accept": "application/json"}

        logger.info(f"Requesting property valuation details for address: {address1}, {address2}...")
        try:
            response = await self.client.get(settings.ATTOM_API_URL, headers=headers, params=params)
            response.raise_for_status()
        except HTTPStatusError as e:
            logger.error(f"Request failed with status {e.response.status_code}.")
            return ToolResponse(message="Failed to fetch property details. Please verify the addresses and try again.")

        response_data = response.json()
        if not response_data:
            logger.error("No response data.")
            return ToolResponse(message="Failed to fetch property details. Please verify the addresses and try again.")

        try:
            property_data = response_data.get("property", [])[0]
            property_details = property_data.get("address", {})
            summary = property_data.get("summary", {})
            lot = property_data.get("lot", {})
            avm = property_data.get("avm", {}).get("amount", {})
            sale = property_data.get("sale", {}).get("amount", {})
            assessment = property_data.get("assessment", {})

            property_info = {
                "Property Details": {
                    "Full Address": property_details.get("oneLine"),
                    "Property Type": summary.get("propertyType"),
                    "Year Built": summary.get("yearbuilt"),
                    "Lot Size (sqft)": lot.get("lotsize2"),
                },
                "Market Value": assessment.get("market", {}).get("mktttlvalue"),
                "Assessed Value": assessment.get("assessed", {}).get("assdttlvalue"),
                "AVM Value": {
                    "Estimated Value": avm.get("value"),
                    "High Estimate": avm.get("high"),
                    "Low Estimate": avm.get("low"),
                },
                "Owner Information": property_data.get("owner", {}),
                "Last Sale Amount": sale.get("saleamt"),
            }
        except KeyError as e:
            logger.error(f"Error parsing response data: {e}")
            return ToolResponse(message="Failed to parse property details. Please verify the addresses and try again.")

        logger.info(f"Successfully retrieved property valuation details for addresses: {address1}, {address2}.")
        return ToolResponse(
            message=(
                f"Property Valuation Details:\n\n{property_info}.\n\n"
                "You can also view the Property Valuation Details in the sidebar."
            ),
            component=Component(
                component_name=ComponentEnum.PropertyAVMComponent,
                component_props=property_info,
            ),
        )

    async def convert_field_name_to_api_field_name(self, **kwargs) -> list | ToolResponse | None:
        assistant_id = kwargs.get("assistant_id")
        FIELD_MAPPING, DISPLAY_FIELDS, SEARCH_FIELDS, FIELD_TYPE_MAPPING, EXTRA_FIELDS = await self.initialize_fields(
            assistant_id
        )
        field_names = kwargs.get("field_names", [])
        api_field_names = []
        REVERSE_FIELD_MAPPING = {val.lower(): key for key, val in FIELD_MAPPING.items()}

        for field_name in field_names:
            field_name = field_name.lower()
            api_field = ""
            if field_name in REVERSE_FIELD_MAPPING:
                api_field = REVERSE_FIELD_MAPPING.get(field_name)
            else:
                prompt = (
                    "You are a helpful assistant. You help me choose similar field that represents the field name"
                    " given to you. You will be given a list of fields and your task is to return the most similar"
                    " field based on the given field. If there are no match. return Nothing. Only return the field"
                    f" name\n AVAILABLE_FIELD_NAMES: {list(FIELD_MAPPING.values())}"
                )
                user_prompt = f"Given the field: {field_name}, return the most similar field."
                messages = [{"role": "system", "content": prompt}, {"role": "user", "content": user_prompt}]

                # similar_field = get_ai_response(messages, model="gpt-3.5-turbo")
                client = OpenAI()
                try:
                    response = client.chat.completions.create(
                        messages=messages, model="gpt-4o-mini-2024-07-18", temperature=0
                    )
                except Exception as e:
                    logger.error(str(e))
                    return ToolResponse("I did not get the query. Could you please elaborate it.")

                similar_field = response.choices[0].message.content

                if similar_field in REVERSE_FIELD_MAPPING:
                    api_field = REVERSE_FIELD_MAPPING.get(field_name)

            if api_field:
                valid_match_type = (
                    VALID_MATCH_TYPES_NON_STR if field_name in FIELD_TYPE_MAPPING else VALID_MATCH_TYPES_STR
                )
                is_datetime_field = (
                    isinstance(FIELD_TYPE_MAPPING.get(api_field), type)
                    and FIELD_TYPE_MAPPING.get(api_field) == datetime
                )
                result = {"field_name": field_name, "api_field_name": api_field}
                result.update({"valid_match_types": valid_match_type} if valid_match_type else {})

                # if field is datetime field, prompt llm to get both start and end date.
                if is_datetime_field:
                    result.update(
                        {
                            "additional_instructions": (
                                "Strictly use both start and end date for this field if user is not asking for"
                                " specific single date to get better results."
                            )
                        }
                    )  # add date_range for datetime field

                dropdown_values = await loan_utils.get_dropdown_values(api_field=api_field)

                if dropdown_values:
                    result.update(
                        {
                            "valid_values": dropdown_values,
                            "additional_instructions": (
                                "You have been provided with the valid values for this field."
                                " If the user has entered something else, let them know that it was"
                                " most likely a typo and map the user input to the closest valid value."
                            ),
                        }
                    )

                api_field_names.append(result)
        logger.info(f"Function returned value:  {api_field_names}")
        return api_field_names

    async def verify_input_fields(self, input_fields: list, sorting: bool = False, **kwargs) -> list:
        FIELD_MAPPING = kwargs.get("field_mapping")
        # verify input fields
        verified_fields = []
        REVERSE_FIELD_MAPPING = {}
        for key, val in FIELD_MAPPING.items():
            val_lower = val.lower()
            if val_lower in REVERSE_FIELD_MAPPING:
                REVERSE_FIELD_MAPPING[val_lower].append(key)
            else:
                REVERSE_FIELD_MAPPING[val_lower] = [key]
        for idx, i_field in enumerate(input_fields):
            if (
                i_field.get("field_name") not in FIELD_MAPPING.keys()
                and i_field.get("field_name") not in FIELD_MAPPING.values()
            ):
                if sorting:
                    input_fields.pop(idx)
                    continue
                else:
                    logger.error("LLM Couldn't extract input fields.")
                    raise ValueError(f"Field name {i_field} not valid. ")

            elif i_field.get("field_name") in FIELD_MAPPING.values():
                temp_mapping = {val: key for key, val in FIELD_MAPPING.items()}
                corrected_field = temp_mapping.get(i_field.get("field_name"))
                if corrected_field:
                    input_fields[idx]["field_name"] = corrected_field

        for idx, i_field in enumerate(input_fields):
            desc_name = FIELD_MAPPING.get(i_field.get("field_name"))
            desc_value_count = list(FIELD_MAPPING.values()).count(desc_name)
            fields = [i_field.get("field_name")]
            if desc_value_count > 1:
                matching_keys = [
                    key
                    for key, value in FIELD_MAPPING.items()
                    if value == desc_name and key != i_field.get("field_name")
                ]
                logger.info(f"Multiple fields found for {desc_name}, keys: {matching_keys}")
                fields.extend(matching_keys)

            i_field.pop("field_name", None)
            verified_fields.append({"field_name": fields, **i_field})

        return verified_fields

    async def post_process_data(self, post_processing: list[dict], response_data: list[dict], **kwargs):
        """Post Process Loan data to answer questions, with optional group_by support using pandas"""
        assistant_id = kwargs.get("assistant_id")
        FIELD_MAPPING, DISPLAY_FIELDS, SEARCH_FIELDS, FIELD_TYPE_MAPPING, EXTRA_FIELDS = await self.initialize_fields(
            assistant_id
        )

        # Prepare loan_details
        loan_details = []
        for response in response_data:
            loan_data = {}
            for k, v in response.get("fields", {}).items():
                formatted_value = await self.format_encompass_data(k, v, field_type_mapping=FIELD_TYPE_MAPPING)
                loan_data[FIELD_MAPPING.get(k)] = formatted_value
            loan_details.append(loan_data)

        if not loan_details:
            return {"message": "No loan details to process.", "grouped_data": None}

        POST_PROCESSING_FUNCTIONS = {
            "sum": "sum",
            "average": "mean",
            "min": "min",
            "max": "max",
            "count": "count",
        }

        message = ""
        grouped_data = []
        processing_fields = await self.verify_input_fields(post_processing, field_mapping=FIELD_MAPPING)
        logger.info("Post Processing Results!")

        # check if processing fields has group_by
        group_by = []
        fields_to_remove = []
        for field in processing_fields:
            result_type = field.get("result_type")
            if result_type.lower() == "group_by":
                field_name = field.get("field_name")
                if isinstance(field_name, list):
                    field_name = field_name[0]
                if field_name in tuple(FIELD_MAPPING.keys()):
                    field_name = FIELD_MAPPING.get(field_name)
                elif field_name in tuple(FIELD_MAPPING.values()):
                    pass
                else:
                    raise ValueError(
                        f"Field Name {field_name} is invalid. Please recheck the available fields and try again."
                    )
                group_by.append(field_name)
                fields_to_remove.append(field)

        # Remove group_by fields from processing_fields
        for field in fields_to_remove:
            processing_fields.remove(field)

        # load data in pandas
        df = pd.DataFrame(loan_details)
        if not group_by:  # not group by
            for field in processing_fields:
                field_name = field.get("field_name")
                if isinstance(field_name, list):
                    field_name = field_name[0]
                result_type = field.get("result_type")
                agg_func = POST_PROCESSING_FUNCTIONS.get(result_type)
                display_field = FIELD_MAPPING.get(field_name, field_name)
                if display_field not in df.columns or agg_func is None:
                    logger.warning(f"{display_field} not in dataframe.")
                    continue
                try:
                    result = getattr(df[display_field], agg_func)()
                except Exception:
                    result = "N/A"

                message += f"The {result_type} of {display_field} is {result}"
            return {"message": message, "grouped_data": []}

        select_fields = {}
        # now do post processing (sum avg or other)
        for field in processing_fields:
            field_name = field.get("field_name")
            if isinstance(field_name, list):
                field_name = field_name[0]
            result_type = field.get("result_type")
            agg_func = POST_PROCESSING_FUNCTIONS.get(result_type)
            display_field = FIELD_MAPPING.get(field_name, field_name)
            if display_field in select_fields:
                existing_operation = select_fields.get(display_field)
                if isinstance(existing_operation, list):
                    select_fields[display_field].append(agg_func)
                elif isinstance(existing_operation, str):
                    select_fields[display_field] = [existing_operation, agg_func]
                else:
                    pass
            else:
                select_fields[display_field] = agg_func
        if not select_fields:
            # if no operations are available to perform on group by throw error
            raise ValueError("Please select atleast one operation on grouping function for any fields.")

        grouped_df = df.groupby(group_by).agg(select_fields).reset_index()
        # Example flat index column: Index([('Loan Type', ''),('Purpose of Loan', ''), ('Appraised value', 'sum'),
        # ('Interest Rate', 'mean'),])
        # If columns are not MultiIndex, convert to tuples for uniform processing
        if not isinstance(grouped_df.columns, pd.MultiIndex):
            grouped_df.columns = pd.MultiIndex.from_tuples(
                [(col, select_fields.get(col, "")) for col in grouped_df.columns]
            )

        # Flatten MultiIndex columns if present
        grouped_df.columns = [
            f"{col[1].capitalize()} {col[0]}" if col[1] else col[0] for col in grouped_df.columns.to_flat_index()
        ]
        # ('Interest Rate', 'mean') -> Mean Interest Rate
        # Convert the grouped DataFrame to a list of dicts
        grouped_data = grouped_df.to_dict(orient="records")

        # Format the output as requested
        for row in grouped_data:
            for k, v in row.items():
                if "count" in k.lower() and v is not None:
                    try:
                        row[k] = int(v)
                    except (ValueError, TypeError):
                        pass
                elif "interest rate" in k.lower() and v is not None:
                    row[k] = f"{v:.3f}%"
                elif any(
                    balance_key.lower() in k.lower()
                    for balance_key in [
                        "appraised value",
                        "amount",
                        "mopymtpi",
                        "down payment",
                        "balance",
                        "price",
                    ]
                ):
                    if isinstance(v, (int, float)):
                        row[k] = f"${v:,.3f}"
        return {"message": message, "grouped_data": grouped_data}

    async def fetch_loan_details(self, **kwargs) -> ToolResponse | None:
        """This function tries to fetch loan data from each given query"""
        assistant_id = kwargs.get("assistant_id")
        FIELD_MAPPING, DISPLAY_FIELDS, SEARCH_FIELDS, FIELD_TYPE_MAPPING, EXTRA_FIELDS = await self.initialize_fields(
            assistant_id
        )
        user = kwargs.get("user")
        global_operator = kwargs.get("operator", "AND")
        input_fields = kwargs.get("input_fields", [])
        loan_type = kwargs.get("loan_type", "other")
        sorting = kwargs.get("sorting", [])
        post_processing = kwargs.get("post_processing", [])
        output_fields = kwargs.get("output_fields", [])
        # verify output fields
        try:
            output_fields = await self._verify_output_fields(output_fields, FIELD_MAPPING, EXTRA_FIELDS)
        except ValueError as e:
            logger.error(f"Output Field not recognized: {e}")
            return ToolResponse(message=str(e))

        # initialize payload
        payload = {}

        terms_mapping = {
            "active": get_active_loan_terms,
            "pipeline": get_active_loan_terms,
            "closed": get_closed_loan_terms,
            "funded": get_closed_loan_terms,
            "epo": get_terms_for_epo_data,
            "locked": get_locked_loan_terms,
            "purchase": get_purchase_loan_terms,
        }

        if loan_type not in ["active", "closed", "funded", "epo", "pipeline", "purchase"] and not input_fields:
            logger.error("LLM Couldn't extract input fields.")
            return ToolResponse(
                message="I didn't exactly get your query. Could you please elaborate more on which fields do you want?"
            )

        # verify input fields
        try:
            input_fields = await self.verify_input_fields(input_fields, field_mapping=FIELD_MAPPING)
        except ValueError as e:
            logger.error(f"INPUT FIELDS: {input_fields}:: {e}")
            return ToolResponse(
                message=(
                    f"{e}. Here are the similar fields that I can pull from. Could you please check and confirm which"
                    " field you want to use? If the field is not listed there,Could you check if the field is made"
                    " available to pull data from encompass? Please ask admin to add that field."
                )
            )

        token = await loan_utils.get_impersonation_auth_token(**kwargs)

        if not token:
            return ToolResponse(
                message=(
                    "Could not verify user. Please make sure your email"
                    "address match the email address in your loan account."
                )
            )

        loan_filter = {
            "operator": global_operator,
            "terms": [],
        }

        loan_term_fn = terms_mapping.get(loan_type)
        if loan_term_fn:
            loan_term = loan_term_fn()
            loan_filter["terms"].extend(loan_term)

        def create_term(field_name, field_value, match_types, terms):
            try:
                match_type = match_types[idx]
            except IndexError as e:
                logger.error(str(e))
                match_type = match_types[-1]

            if field_name.lower() in [key.lower() for key in FIELD_TYPE_MAPPING.keys()]:
                if match_type.lower() not in [mt.lower() for mt in VALID_MATCH_TYPES_NON_STR]:
                    match_type = "equals"
            else:
                if match_type.lower() not in [mt.lower() for mt in VALID_MATCH_TYPES_STR]:
                    match_type = "exact"
                    # For string values the gpt mostly returns equals, which we will convert to exact.

            if field_name in FIELD_TYPE_MAPPING and FIELD_TYPE_MAPPING.get(field_name) == float:
                field_value = type_converter.convert_to_float(field_value)

            terms["terms"].append({"canonicalName": field_name, "value": field_value, "matchType": match_type})
            return terms

        include_archived_loans = True
        for loan_field in input_fields:
            field_term = {"operator": loan_field.get("operator", "AND"), "terms": []}
            field_name = loan_field.get("field_name")
            match_types = loan_field.get("matchType", [])
            values = loan_field.get("values", [])
            for idx, field_value in enumerate(values):
                if isinstance(field_name, list) and len(field_name) > 1:
                    terms = {"operator": "OR", "terms": []}
                    for field in field_name:
                        await self._handle_date_verification(field, match_types, values, FIELD_TYPE_MAPPING)
                        terms = create_term(field, field_value, match_types, terms)
                    field_term["terms"].append(terms)
                else:
                    single_field = field_name[0] if isinstance(field_name, list) else field_name
                    await self._handle_date_verification(single_field, match_types, values, FIELD_TYPE_MAPPING)
                    field_term = create_term(single_field, field_value, match_types, field_term)
            loan_filter["terms"].append(field_term)

        payload = {"filter": loan_filter, "fields": list(FIELD_MAPPING.keys()), "sortOrder": []}
        if sorting:
            sorting_fields = await self.verify_input_fields(sorting, sorting=True, field_mapping=FIELD_MAPPING)
            for field in sorting_fields:
                field_name = field.get("field_name")
                if isinstance(field_name, list):
                    for s_field in field_name:
                        payload["sortOrder"].append({"canonicalName": s_field, "order": field.get("ordering", "asc")})
                else:
                    payload["sortOrder"].append(
                        {
                            "canonicalName": field.get("field_name"),
                            "order": field.get("ordering", "asc"),
                        }
                    )

        headers = {"Content-Type": "application/json", "Authorization": token}
        logger.info(f"Filtering loans for user with email {user.email}...")
        params = {"cursortype": "randomAccess", "limit": 1}

        # client config must have restriction enabled and if the user doesn't have impersonation access
        # (restriction enabled)
        lo_details = {}
        if client_config.ENCOMPASS_LOAN_IMPERSONATION_RESTRICTION and not user.encompass_impersonation_access:
            lo_details = await loan_utils.get_loan_officer_details(**kwargs)

        payload["filter"]["terms"].extend(get_restricted_terms(**lo_details))
        #####
        # for Archieved Loans
        #####
        # go through payload and find if fields.1393 has active loan or "", if yes then return True

        def check_active_loan_in_terms(terms):
            if isinstance(terms, list):
                for term in terms:
                    if isinstance(term, dict):
                        # Direct term with canonicalName
                        if term.get("canonicalName", "").lower() == "fields.1393":
                            value = term.get("value", "")
                            if value == "active loan" or value == "":
                                return True
                        # Nested terms (AND/OR)
                        if "terms" in term:
                            if check_active_loan_in_terms(term["terms"]):
                                return True
            return False

        if check_active_loan_in_terms(payload["filter"].get("terms", [])):
            include_archived_loans = client_config.ARCHIVED_LOANS_FOR_ACTIVE_LOANS

        # always include archived loans
        payload["includeArchivedLoans"] = include_archived_loans
        # if loan_type.lower() in ["active", "pipeline", "locked"]:
        #     # if active loans, some clients prefer not to display archived loans
        #     payload["includeArchivedLoans"] = client_config.ARCHIVED_LOANS_FOR_ACTIVE_LOANS
        logger.info(f"Encompass Payload: {payload}")
        try:
            initial_response = await self.client.post(self.url, headers=headers, json=payload, params=params)
            initial_response.raise_for_status()
        except HTTPStatusError as e:
            logger.error(
                f"Request failed with status {e.response.status_code} {initial_response.json().get('details', '')}."
            )
            logger.warning(f"PAYLOAD: {payload}")
            return ToolResponse(
                message=(
                    "There was some issue with fetching loan details from Encompass."
                    f" {initial_response.json().get('details', '')}"
                ),
                payload=payload,
            )

        loans_count = int(initial_response.headers.get("x-total-count", 0))
        if loans_count == 0:
            logger.warning("No Data found in encompass.")
            logger.warning(f"PAYLOAD: {payload}")
            return ToolResponse(message="No loans found for the given query.", paylaod=payload)
        cursor_id = initial_response.headers.get("x-cursor")

        try:
            params = {"cursor": cursor_id, "start": 0, "limit": 1000}
            response = await self.client.post(
                self.url, headers=headers, json={"fields": list(FIELD_MAPPING.keys())}, params=params
            )
            response.raise_for_status()
        except HTTPStatusError as e:
            logger.error(f"Request failed with status {e.response.status_code} {response.json().get('details', '')}.")
            logger.warning(f"PAYLOAD: {payload}")
            return ToolResponse(
                message=(
                    f"Encountered an Error while fetching data from Encompass. Please try again. {e.response.text}"
                ),
                payload=payload,
            )
        except Exception as e:
            logger.error(f"Error While fetching data from Encompass: {e}")
            return ToolResponse(
                message=(
                    "Some internal error occured while fetching data from encompass. Please try again after some time."
                    " or contact Admin for more details"
                ),
                payload=payload,
            )

        try:
            response_text = response.text
            response_data = json.loads(response_text)
        except json.JSONDecodeError as e:
            logger.error(f"JSON Deacode error when decoding output from encompass: {e}")
            try:
                encoding_format = chardet.detect(response.content)  # detect and use encoding
                response_text = response.content.decode(encoding_format["encoding"])
                response_data = json.loads(response_text)

            except Exception as e:
                logger.error(f"Some error occured while converting encompass response into json: {e}")
                return ToolResponse(
                    message=(
                        "Couldn't process the data due to some unexpected problem in our system. Please contact admin."
                    ),
                    payload=payload,
                )
        if not response_data:
            logger.warning("Encompass did not return any response.")
            logger.warning(f"PAYLOAD: {payload}")
            return ToolResponse(
                message=(
                    "Could not fetch loans! It looks like there are no loans with the given criteria. Please recheck"
                    " your criteria."
                ),
                payload=payload,
            )

        message = ""
        if post_processing:
            try:
                post_process_result = await self.post_process_data(
                    post_processing, response_data, assistant_id=assistant_id
                )
            except ValueError as e:
                return ToolResponse(message=f"Error while processing data: {e}")

            message += post_process_result.get("message", "")
            grouped_data = post_process_result.get("grouped_data")
            if grouped_data:
                component = Component(
                    component_name=ComponentEnum.LoanDetails,
                    component_props={
                        "link": "",
                        "is_downloadable": False,
                        "data": grouped_data,
                        "headers": list(grouped_data[0].keys()),
                        "search_fields": list(grouped_data[0].keys()),
                    },
                )
                return ToolResponse(message=message, component=component)

        try:
            loan_details = []
            for response in response_data:
                loan_data = {}
                for k, v in response.get("fields", {}).items():
                    if k in FIELD_MAPPING:
                        field_key = EXTRA_FIELDS.get(k) if k in EXTRA_FIELDS else FIELD_MAPPING.get(k)
                        formatted_value = await self.format_encompass_data(k, v, field_type_mapping=FIELD_TYPE_MAPPING)
                        loan_data[field_key] = formatted_value
                loan_details.append(loan_data)

            s3_hook = await get_s3_hook(assistant_id=assistant_id)
            excel_url = (
                loan_utils.generate_and_upload_excel(s3_hook=s3_hook, prefix="loan_details", report_data=loan_details)
                if client_config.DISPLAY_EXCEL_LINK_ON_LOAN_TABLE
                else None
            )

            HEADERS = await self._determine_headers(loan_details, output_fields, DISPLAY_FIELDS, FIELD_MAPPING)

            # Check if some fields are not available to display
            # TODO: Maybe move this a bit higher so that we won't have to fetch loans if no display headers are
            # present??
            if output_fields and not set(output_fields).issubset(HEADERS):
                # if not all items of output_fields are in HEADERS
                msg, should_return = await self._handle_missing_output_fields(output_fields, HEADERS)
                message = f"{message}\n{msg}"
                if should_return:
                    return ToolResponse(message=message, payload=payload)

            component = Component(
                component_name=ComponentEnum.LoanDetails,
                component_props={
                    "link": excel_url,
                    "is_downloadable": client_config.DISPLAY_EXCEL_LINK_ON_LOAN_TABLE,
                    "data": loan_details,
                    "headers": HEADERS,
                    "search_fields": [field for field in SEARCH_FIELDS if field in HEADERS],
                },
            )
            if len(loan_details) > self.NUMBER_OF_LOANS:
                message_copy = (
                    f"{message}\n{self.NO_CONTEXT_LOAN_MESSAGE.format(total_loans=loans_count)}\n"
                    f"{'I can only display first 1000 loans. Please use proper filters to get better responses.' if loans_count > 1000 else ''}"  # noqa
                )
                return ToolResponse(
                    message=message_copy,
                    component=component,
                    payload=payload,
                )
            else:
                message_copy = f"{message}\n{self.IN_CONTEXT_LOAN_MESSAGE.format(loan_details=loan_details)}"
                return ToolResponse(message=message_copy, component=component, payload=payload)

        except Exception as e:
            logger.error(f"Some unexpected error occured! {e}")
            return ToolResponse(
                message="Some unexpected error occured in our server. Please try again!", payload=payload
            )

    async def get_open_condition(self, **kwargs) -> ToolResponse | None:
        if not client_config.ENCOMPASS_LOAN_OPEN_CONDITION:
            return ToolResponse(message="This feature is not available at the moment.")

        loan_number = kwargs.get("loan_number")
        user = kwargs.get("user")

        if not loan_number:
            return ToolResponse(message="Loan number is required to fetch open condition.")

        token = await loan_utils.get_impersonation_auth_token(**kwargs)

        if not token:
            return ToolResponse(
                message=(
                    "Could not verify user. Please make sure your email"
                    "address match the email address in your loan account."
                )
            )

        payload = {
            "filter": {
                "operator": "AND",
                "terms": [{"canonicalName": "Fields.364", "value": str(loan_number), "matchType": "exact"}],
            },
            "fields": ["Fields.GUID"],
            "includeArchivedLoans": True,
        }

        # client config must have restriction enabled and if the user doesn't have impersonation access
        # (restriction enabled)
        lo_details = {}
        if client_config.ENCOMPASS_LOAN_IMPERSONATION_RESTRICTION and not user.encompass_impersonation_access:
            lo_details = await loan_utils.get_loan_officer_details(**kwargs)

        payload["filter"]["terms"].extend(get_restricted_terms(**lo_details))

        headers = {"Content-Type": "application/json", "Authorization": token}

        try:
            response = await self.client.post(self.url, headers=headers, json=payload)
            response.raise_for_status()
        except HTTPStatusError as e:
            logger.error(f"Request failed with status {e.response.status_code} {e.response.content}")
            return ToolResponse(
                message=(
                    "Sorry, I could not process your request. Please check if the loan number exists in your loan"
                    " file."
                )
            )

        loan_details = await self._decode_loan_details(response)

        if not loan_details:
            logger.warning("No response data.")
            return ToolResponse(
                message=(
                    "Sorry, I could not process your request. Please check if the loan number exists in your loan"
                    " file."
                )
            )

        guid = loan_details[0].get("fields").get("Fields.GUID")

        base_url = "https://api.elliemae.com/encompass/v1/loans/"
        params = {"filter": "status:!=:Cleared,status:!=:Waived"}

        try:
            response = await self.client.get(
                f"{base_url}{guid}/conditions/underwriting",
                params=params,
                headers=headers,
            )
            response.raise_for_status()

        except HTTPStatusError as e:
            logger.error(f"Request failed with status {e.response.status_code} {e.response.content}")
            return ToolResponse(
                message=(
                    "Sorry, I could not process your request. Please check if the loan number exists in your loan"
                    " file."
                )
            )

        response_data = await self._decode_loan_details(response)

        if not response_data:
            logger.warning("No response data.")
            return ToolResponse(
                message="""It seems like there are no open conditions for the given loan number.
                Recheck the details and try again"""
            )

        open_conditions = []
        try:
            for data in response_data:
                open_condition = {
                    "Title": data.get("title", "N/A"),
                    "Category": data.get("category", "N/A"),
                    "Prior To": data.get("priorTo", "N/A"),
                    "Days to receive": str(data.get("daysToReceive", "N/A")),
                    "Expected Date": data.get("expectedDate", "N/A"),
                    "Description": data.get("description", "N/A"),
                    "Source": data.get("source", "N/A"),
                    "Status": data.get("status", "N/A"),
                    "Application Pair": (
                        "For all borrowers"
                        if data.get("forAllApplications")
                        else data.get("application", {}).get("entityName", "N/A")
                    ),
                    "Owner Role": data.get("ownerRole", {}).get("entityName", "N/A"),
                    "Allowed to clear": str(data.get("allowToClear", "N/A")),
                    "Comments": "<br>".join([comment["comments"] for comment in data.get("comments", [])]) or "N/A",
                    "Documents": (
                        "<br>".join([document["entityName"] for document in data.get("documents", [])]) or "N/A"
                    ),
                }
                open_conditions.append(open_condition)

            HEADERS = list(open_conditions[0].keys())
            return ToolResponse(
                message="Successfully fetched open conditions.",
                component=Component(
                    component_name=ComponentEnum.LoanDetails,
                    component_props={
                        "data": open_conditions,
                        "headers": HEADERS,
                        "search_fields": HEADERS,
                    },
                ),
            )
        except Exception as e:
            logger.error(f"Error parsing response data: {e}")
            return ToolResponse(message="Failed to parse open conditions. ")

    async def _determine_headers(self, loan_details, output_fields, DISPLAY_FIELDS, FIELD_MAPPING):
        # Determine headers by intersecting DISPLAY_FIELDS and the fields present in the first loan_details entry
        HEADERS = [field for field in DISPLAY_FIELDS if field in loan_details[0]]
        if output_fields:
            # FIELDS CAN BE mapping with field it too. So lets fix that
            output_fields = [FIELD_MAPPING.get(field) if field in FIELD_MAPPING else field for field in output_fields]

            HEADERS = [field for field in output_fields if field in HEADERS]
            # Ensure that fields are both correct and available to display as per settings/docs admin

        return HEADERS

    async def _handle_missing_output_fields(self, output_fields, HEADERS) -> tuple[str, bool]:
        missing_fields = set(output_fields) - set(HEADERS)  # Check missing fields
        plural = len(missing_fields) > 1
        fields_list = " ".join(missing_fields)
        verb = "are" if plural else "is"
        message = (
            f"\nIt seems the requested field{'s' if plural else ''} "
            f"{fields_list} {verb} not available to show here. "
            "Please contact admin to add them or make them available for display."
        )
        if not HEADERS:
            return message, True  # whether to return the message or not

        return message, False

    @staticmethod
    async def _handle_date_verification(
        field_name: str, values: list, match_types: list, FIELD_TYPE_MAPPING: dict
    ) -> ToolResponse | None:
        # check if field is datetime field
        if isinstance(FIELD_TYPE_MAPPING.get(field_name), type) and FIELD_TYPE_MAPPING.get(field_name) == datetime:
            # For each value, if it's single and match type not in [exact, equals]
            if len(values) == 1 and match_types[0].lower() not in ["exact", "equals"]:
                today = datetime.now().strftime("%Y-%m-%d")
                message = (
                    "Wait Let me recheck the date range again. Since you have not mentioned exact"
                    " date I need to use both start and end date. Let me calculate it again by"
                    f" including today's date i.e. {today}"
                )
                return ToolResponse(message=message)

    @staticmethod
    async def _verify_output_fields(output_fields: list, FIELD_MAPPING: dict, EXTRA_FIELDS: dict) -> list:
        valid_fields = []
        field_mapping = FIELD_MAPPING.copy()
        field_mapping.update(EXTRA_FIELDS)
        for output_field in output_fields:
            if output_field in field_mapping.keys():
                output_field = field_mapping.get(output_field)
                valid_fields.append(output_field)
            elif output_field in field_mapping.values():
                valid_fields.append(output_field)
            else:
                # Wrong output field
                raise ValueError(
                    f"Wrong output Field: {output_field}. Did you miss some details? Check available fields and recall"
                    " the tool or ask for confirmation with user."
                )
        return valid_fields


loan_function = LoanFunction()
