from schema.enums import DataFieldTypes

VALID_MATCH_TYPES_STR = ("Exact", "Contains", "StartsWith", "IsEmpty", "IsNotEmpty", "NotEquals")
VALID_MATCH_TYPES_NON_STR = (
    "Equals",
    "NotEquals",
    "GreaterThan",
    "GreaterThanOrEquals",
    "LessThan",
    "LessThanOrEquals",
    "IsEmpty",
    "IsNotEmpty",
)
LOAN_PIPELINE_URL = "https://api.elliemae.com/encompass/v1/loanPipeline"
DB_FIELD_TYPE_MAP = {
    "STRING": DataFieldTypes.TEXT,
    "DATE": DataFieldTypes.DATE,
    "INTEGER": DataFieldTypes.INTEGERNUMBER,
    "DECIMAL_2": DataFieldTypes.DECIMALNUMBER,
    "DECIMAL_3": DataFieldTypes.DECIMALNUMBER,
    "DECIMAL_4": DataFieldTypes.DECIMALNUMBER,
    "DECIMAL_5": DataFieldTypes.DECIMALNUMBER,
    "DECIMAL_6": DataFieldTypes.DECIMALNUMBER,
    "DECIMAL_7": DataFieldTypes.DECIMALNUMBER,
}

CANONICAL_FIELDS_URL = "https://api.elliemae.com/encompass/v3/loanPipeline/canonicalFields"
LOAN_URL = "https://api.elliemae.com/encompass/v1/loans/"
