import xml.sax.saxutils as saxutils
from datetime import datetime, timedelta, timezone

import redis.asyncio as aioredis
from config import settings
from httpx import AsyncClient
from tools.functions.encompass import loan_function
from tools.schema import Component, ComponentEnum, ToolResponse
from tools.templates import client_templates
from tools.utils.encompass.loan import loan_utils
from tools.utils.encompass.sales import sales_utils
from twilio.rest import Client


class SalesFunction:
    def __init__(self) -> None:
        self.client = AsyncClient(timeout=None)
        self.encompass_url = "https://api.elliemae.com/encompass/v1/loanPipeline"
        self.redis = None

    async def initialize_redis(self):
        if self.redis is None:
            self.redis = await aioredis.from_url(settings.REDIS_URL)

    def list_functions(self) -> dict:
        functions = {
            "rate_term_refi": self.generate_auto_quote_email,
            "cash_out_refi": self.cash_out_refi,
            "get_purchase_offer": self.get_purchase_offer,
            "generate_open_house_flyer": self.generate_open_house_flyer,
            "generate_pre_qualification_letter": self.generate_pre_qualification_letter,
            "find_lending_opportunity": self.find_lending_opportunity,
            "make_refinance_call": self.make_call,
        }
        return functions

    async def generate_auto_quote_email(self, **kwargs) -> ToolResponse | None:
        auth_token = await loan_utils.get_impersonation_auth_token(**kwargs)
        if not auth_token:
            return ToolResponse(
                message=(
                    "Sorry, you are not authorized to access this feature."
                    "Please make sure your email address matches the email address in your loan account."
                )
            )
        user = kwargs.get("user")
        loan_number = kwargs.get("loan_number")
        first_name = kwargs.get("first_name")
        last_name = kwargs.get("last_name")
        loan_amount = kwargs.get("loan_amount")
        interest_rate = kwargs.get("interest_rate")
        mopymtpi = kwargs.get("monthly_payment_pi")
        closed_date = kwargs.get("closed_date")
        loan_term = int(float(kwargs.get("loan_term", 360)))
        closing_fees = kwargs.get("closing_fees", 5000)
        assistant_id = kwargs.get("assistant_id")

        mandatory_params = [first_name, last_name, loan_amount, interest_rate, closed_date, loan_term]
        if not (all(mandatory_params) or loan_number):
            return ToolResponse(
                message=(
                    "Thank you for your interest in generating a rate term refinance offer."
                    " Please select an existing or a new customer to proceed."
                ),
                component=Component(
                    component_name=ComponentEnum.SalesEmail,
                    component_props={
                        "operation": "auto_quote",
                        "require_extra_options": client_templates.REQUIRE_ALL_OPTION_FIELDS,
                        "fields": {},
                    },
                ),
            )

        loan_details = {}

        options = kwargs.get("options", [])
        if not options:
            return ToolResponse(
                message=("No valid options provided. Please provide at least one complete option."),
                component=Component(
                    component_name=ComponentEnum.SalesEmail,
                    component_props={
                        "operation": "auto_quote",
                        "require_extra_options": client_templates.REQUIRE_ALL_OPTION_FIELDS,
                        "fields": {},
                    },
                ),
            )

        valid_options = []
        invalid_options = []

        all_option_fields = ["term", "rate", "points", "loan_type"]
        required_fields = ["term", "rate"] if not client_templates.REQUIRE_ALL_OPTION_FIELDS else all_option_fields
        for i, option_data in enumerate(options, 1):
            rate = option_data.get("rate")
            if rate == 0:
                continue  # Skip completely if rate is 0 (Avoid division by zero)

            option = {
                "term": option_data.get("term"),
                "rate": rate,
                "points": option_data.get("points") if "points" in required_fields else None,
                "mortgage_insurance_rate": option_data.get("mortgage_insurance_rate", 0.0),
                "loan_type": option_data.get("loan_type") if "loan_type" in required_fields else None,
                "va_funding_fee": option_data.get("va_funding_fee", 0.0),
            }

            if all(option[field] is not None for field in required_fields):
                valid_options.append(option)
            elif any(option[field] is not None for field in required_fields):
                missing_fields = [field for field in required_fields if option[field] is None]
                invalid_options.append(f"option_{i}: {missing_fields}")

        if invalid_options:
            return ToolResponse(
                message=(
                    f"Incomplete option data provided. Missing fields: {', '.join(invalid_options)}."
                    "Please provide all required fields for each option."
                ),
                component=Component(
                    component_name=ComponentEnum.SalesEmail,
                    component_props={
                        "operation": "auto_quote",
                        "require_extra_options": client_templates.REQUIRE_ALL_OPTION_FIELDS,
                        "fields": {},
                    },
                ),
            )

        if all(value is not None for value in mandatory_params):
            loan_details = {
                "Loan Number": loan_number,
                "First Name": first_name,
                "Last Name": last_name,
                "Loan Amount": loan_amount,
                "Interest Rate": interest_rate,
                "MoPymtPI": (
                    mopymtpi
                    if mopymtpi
                    else await loan_utils.calculate_monthly_payment(
                        principal=loan_amount,
                        rate=interest_rate,
                        term_months=loan_term,
                    )
                ),
                "Loan Term": loan_term,
                "Fund Released Date": closed_date,
                "Closing Fees": closing_fees,
            }
        elif loan_number:
            loan_details = await loan_function.get_loan_details_by_loan_number(
                user=user,
                loan_number=loan_number,
                assistant_id=assistant_id,
                data_only=True,
            )
            if isinstance(loan_details, ToolResponse):
                return {"error": "Couldn't fetch the loan details"}
            if not loan_details.get("Fund Released Date"):
                return ToolResponse(
                    message="Loan is not yet closed! Please use loan number whose loan is already closed",
                    component=Component(
                        component_name=ComponentEnum.SalesEmail,
                        component_props={
                            "operation": "auto_quote",
                            "require_extra_options": client_templates.REQUIRE_ALL_OPTION_FIELDS,
                        },
                    ),
                )
        else:
            required_fields = {
                "first_name": first_name,
                "last_name": last_name,
                "loan_amount": loan_amount,
                "interest_rate": interest_rate,
                "closed_date": closed_date,
            }
            missing_fields = [field_name for field_name, value in required_fields.items() if not value]
            message = (
                "Thank you for your interest in generating a rate term refinance offer."
                " To proceed, please fill out the necessary form with relevant details."
                f" Missing {missing_fields} parameters for generating rate term refinance email."
            )
            return ToolResponse(
                message=message,
                component=Component(
                    component_name=ComponentEnum.SalesEmail,
                    component_props={
                        "operation": "auto_quote",
                        "require_extra_options": client_templates.REQUIRE_ALL_OPTION_FIELDS,
                        "fields": required_fields,
                    },
                ),
            )

        # refinance options
        refinance_details = await sales_utils.generate_refinance_options(loan_details, valid_options)

        # Fetch loan officer details
        if loan_number:
            loan_details = await loan_function.get_loan_details_by_loan_number(
                user=user,
                loan_number=loan_number,
                assistant_id=assistant_id,
                data_only=True,
            )
            if isinstance(loan_details, ToolResponse):
                return {"error": "Couldn't fetch the loan details"}
            loid = loan_details.get("Loan Officer Login ID")
            lo_email = loan_details.get("Loan Officer Email")
            lo_details = await loan_utils.fetch_loan_officer_details(loid, lo_email, **kwargs)
        else:
            lo_details = await loan_utils.fetch_loan_officer_details(**kwargs)

        email_subject = client_templates.RATE_TERM_EMAIL_SUBJECT.format(
            first_name=loan_details.get("First Name"),
            last_name=loan_details.get("Last Name"),
        )

        now = datetime.now(timezone.utc)
        disclaimer = client_templates.DISCLAIMER_TEMPLATE_REFI.format(
            year=now.year, date=now.strftime("%m/%d/%Y"), nmls_number=lo_details.get("nmls_number", "000000")
        )
        show_signature = client_templates.SHOW_SIGNATURE

        message_head = client_templates.RATE_TERM_EMAIL_BODY_HEAD.format(
            first_name=loan_details.get("First Name"),
            closed_date=loan_details.get("Fund Released Date"),
            interest_rate=f"{loan_details.get('Interest Rate', 0):.3f}",
        )
        message_tail = client_templates.RATE_TERM_EMAIL_BODY_TAIL.format()
        email_body_json = {
            "message_head": message_head,
            "table": refinance_details,
            "message_tail": message_tail,
        }

        component_props = {
            "refi_type": "rate_term",
            "is_display": "True",
            "show_signature": show_signature,
            "subject": email_subject,
            "body": email_body_json,
            "signature": lo_details,
            "disclaimer": disclaimer,
        }

        if client_templates.DISABLE_SEND_EMAIL_BUTTON or (
            not lo_details.get("nmls_number") and not client_templates.FORCE_SEND_EMAIL_BUTTON
        ):
            component_props["is_display"] = "False"

        return ToolResponse(
            message="Here's the Rate Term Refi Email based on your inputs.",
            component=Component(
                component_name=ComponentEnum.SalesEmail,
                component_props=component_props,
            ),
        )

    async def cash_out_refi(self, **kwargs) -> ToolResponse | None:
        auth_token = await loan_utils.get_impersonation_auth_token(**kwargs)
        if not auth_token:
            return ToolResponse(
                message=(
                    "Sorry, you are not authorized to access this feature."
                    "Please make sure your email address matches the email address in your loan account."
                )
            )
        user = kwargs.get("user")
        loan_number = kwargs.get("loan_number")
        first_name = kwargs.get("first_name")
        last_name = kwargs.get("last_name")
        loan_amount = kwargs.get("loan_amount")
        interest_rate = kwargs.get("interest_rate")
        mopymtpi = kwargs.get("monthly_payment_pi")
        closed_date = kwargs.get("closed_date")
        loan_term = int(float(kwargs.get("loan_term", 360)))
        assistant_id = kwargs.get("assistant_id")

        address = kwargs.get("street_address")
        city = kwargs.get("city")
        state = kwargs.get("state")
        zip_code = kwargs.get("zip_code")

        debt_amount = kwargs.get("debt_amount", 0)
        debt_payment = kwargs.get("debt_payment", 0)

        desired_cashout = kwargs.get("desired_cashout", 0)
        closing_fees = kwargs.get("closing_fees", 5000)
        apr_fees = closing_fees

        mandatory_params = [
            first_name,
            last_name,
            loan_amount,
            interest_rate,
            closed_date,
            loan_term,
            address,
            city,
            state,
            zip_code,
        ]
        if not (all(mandatory_params) or loan_number):
            return ToolResponse(
                message=(
                    "Thank you for your interest in generating a cash out refinance offer."
                    " Please select an existing or a new customer to proceed."
                ),
                component=Component(
                    component_name=ComponentEnum.SalesEmail,
                    component_props={
                        "operation": "cash_out_refi",
                        "require_extra_options": client_templates.REQUIRE_ALL_OPTION_FIELDS,
                        "fields": {},
                    },
                ),
            )

        loan_details = {}

        options = kwargs.get("options", [])
        if not options:
            return ToolResponse(
                message=("No valid options provided. Please provide at least one complete option."),
                component=Component(
                    component_name=ComponentEnum.SalesEmail,
                    component_props={
                        "operation": "cash_out_refi",
                        "require_extra_options": client_templates.REQUIRE_ALL_OPTION_FIELDS,
                        "fields": {},
                    },
                ),
            )

        valid_options = []
        invalid_options = []

        all_option_fields = ["term", "rate", "points", "loan_type"]
        required_fields = ["term", "rate"] if not client_templates.REQUIRE_ALL_OPTION_FIELDS else all_option_fields
        for i, option_data in enumerate(options, 1):
            rate = option_data.get("rate")
            if rate == 0:
                continue  # Skip completely if rate is 0 (Avoid division by zero)

            option = {
                "term": option_data.get("term"),
                "rate": rate,
                "points": option_data.get("points") if "points" in required_fields else None,
                "mortgage_insurance_rate": option_data.get("mortgage_insurance_rate", 0.0),
                "loan_type": option_data.get("loan_type") if "loan_type" in required_fields else None,
                "va_funding_fee": option_data.get("va_funding_fee", 0.0),
            }

            if all(option[field] is not None for field in required_fields):
                valid_options.append(option)
            elif any(option[field] is not None for field in required_fields):
                missing_fields = [field for field in required_fields if option[field] is None]
                invalid_options.append(f"option_{i}: {missing_fields}")

        if invalid_options:
            return ToolResponse(
                message=(
                    f"Incomplete option data provided. Missing fields: {', '.join(invalid_options)}."
                    "Please provide all required fields for each option."
                ),
                component=Component(
                    component_name=ComponentEnum.SalesEmail,
                    component_props={
                        "operation": "cash_out_refi",
                        "require_extra_options": client_templates.REQUIRE_ALL_OPTION_FIELDS,
                        "fields": {},
                    },
                ),
            )

        if all(value is not None for value in mandatory_params):
            loan_details = {
                "Loan Number": loan_number,
                "First Name": first_name,
                "Last Name": last_name,
                "Loan Amount": loan_amount,
                "Interest Rate": interest_rate,
                "MoPymtPI": (
                    mopymtpi
                    if mopymtpi
                    else await loan_utils.calculate_monthly_payment(
                        principal=loan_amount, rate=interest_rate, term_months=loan_term
                    )
                ),
                "Loan Term": loan_term,
                "Fund Released Date": closed_date,
                "Address": address,
                "City": city,
                "State": state,
                "Zip Code": zip_code,
            }
        elif loan_number:
            loan_details = await loan_function.get_loan_details_by_loan_number(
                user=user,
                loan_number=loan_number,
                assistant_id=assistant_id,
                data_only=True,
            )
            if isinstance(loan_details, ToolResponse):
                return {"error": "Couldn't fetch the loan details"}
            if not loan_details.get("Fund Released Date"):
                return ToolResponse(
                    message="Loan is not yet closed! Please use loan number whose loan is already closed",
                    component=Component(
                        component_name=ComponentEnum.SalesEmail,
                        component_props={
                            "operation": "cash_out_refi",
                            "require_extra_options": client_templates.REQUIRE_ALL_OPTION_FIELDS,
                        },
                    ),
                )
        else:
            required_fields = {
                "first_name": first_name,
                "last_name": last_name,
                "loan_amount": loan_amount,
                "interest_rate": interest_rate,
                "closed_date": closed_date,
                "address": address,
                "city": city,
                "state": state,
                "zip_code": zip_code,
            }
            missing_fields = [field_name for field_name, value in required_fields.items() if not value]
            message = (
                "Thank you for your interest in generating a cash out refinance offer."
                " To proceed, please fill out the necessary form with relevant details."
                f" Missing {missing_fields} parameters for generating cash out refinance email."
            )
            return ToolResponse(
                message=message,
                component=Component(
                    component_name=ComponentEnum.SalesEmail,
                    component_props={
                        "operation": "cash_out_refi",
                        "require_extra_options": client_templates.REQUIRE_ALL_OPTION_FIELDS,
                        "fields": required_fields,
                    },
                ),
            )

        # Calculations
        appraised_value = kwargs.get("appraised_value") or await sales_utils.get_property_estimated_value(loan_details)
        if appraised_value:
            estimated_payoff = await sales_utils.get_estimated_payoff(loan_details)
            max_cashout = kwargs.get("max_cashout") or ((appraised_value * 0.8) - estimated_payoff)
            if max_cashout < 0:
                return ToolResponse(
                    message=(
                        f"You are not eligible for a cash-out refinance due to the negative maximum cashout amount of {max_cashout}."  # noqa
                        " This situation typically indicates that the current loan balance exceeds the appraised value of the property,"  # noqa
                        " making it ineligible for cash-out refinancing."
                    )
                )
        else:
            return ToolResponse(
                message=f"Sorry, for address {address}, {city}, {state}, {zip_code}, we could not calculate the appraised value."  # noqa
            )

        # Cashout options
        if debt_amount > 0:
            # Check if debt amount is less than max cashout
            if debt_amount > max_cashout:
                return ToolResponse(
                    message=f"Debt amount is greater than the maximum cashout amount. Maximum cashout amount is {max_cashout}"  # noqa
                )
            else:
                remaining_cashout = round(max_cashout - debt_amount, 2)
                if desired_cashout <= remaining_cashout:
                    cashout_details = await sales_utils.generate_cashout_details(
                        loan_details,
                        valid_options,
                        estimated_payoff,
                        debt_amount=debt_amount,
                        debt_payment=debt_payment,
                        desired_cashout=desired_cashout,
                        closing_fees=closing_fees,
                        apr_fees=apr_fees,
                    )
                    email_subject = client_templates.CASHOUT_DEBTCON_EMAIL_SUBJECT.format(
                        first_name=loan_details.get("First Name"),
                        last_name=loan_details.get("Last Name"),
                    )
                    message_head = client_templates.CASHOUT_DEBTCON_EMAIL_BODY_HEAD.format(
                        first_name=loan_details.get("First Name"),
                    )
                    message_tail = client_templates.CASHOUT_DEBTCON_EMAIL_BODY_TAIL.format()
                else:
                    return ToolResponse(
                        message=f"Desired cashout amount is greater than the remaining cashout amount. Remaining cashout amount is {remaining_cashout}"  # noqa
                    )
        elif desired_cashout > 0:
            # Check if desired cashout is less than max cashout
            if desired_cashout > max_cashout:
                return ToolResponse(
                    message=f"Desired cashout amount is greater than the maximum cashout amount. Maximum cashout amount is {max_cashout}"  # noqa
                )
            else:
                cashout_details = await sales_utils.generate_cashout_details(
                    loan_details,
                    valid_options,
                    estimated_payoff,
                    desired_cashout=desired_cashout,
                    closing_fees=closing_fees,
                    apr_fees=apr_fees,
                )
                email_subject = client_templates.CASHOUT_DESIRED_EMAIL_SUBJECT.format(
                    first_name=loan_details.get("First Name"),
                    last_name=loan_details.get("Last Name"),
                )
                message_head = client_templates.CASHOUT_DESIRED_EMAIL_BODY_HEAD.format(
                    first_name=loan_details.get("First Name"),
                )
                message_tail = client_templates.CASHOUT_DESIRED_EMAIL_BODY_TAIL.format()
        else:
            return ToolResponse(message="You need to choose a refinancing option before proceeding.")

        # Fetch loan officer details
        if loan_number:
            loan_details = await loan_function.get_loan_details_by_loan_number(
                user=user,
                loan_number=loan_number,
                assistant_id=assistant_id,
                data_only=True,
            )
            if isinstance(loan_details, ToolResponse):
                return {"error": "Couldn't fetch the loan details"}
            loid = loan_details.get("Loan Officer Login ID")
            lo_email = loan_details.get("Loan Officer Email")
            lo_details = await loan_utils.fetch_loan_officer_details(loid, lo_email, **kwargs)
        else:
            lo_details = await loan_utils.fetch_loan_officer_details(**kwargs)

        now = datetime.now(timezone.utc)
        disclaimer = client_templates.DISCLAIMER_TEMPLATE_REFI.format(
            year=now.year, date=now.strftime("%m/%d/%Y"), nmls_number=lo_details.get("nmls_number", "000000")
        )
        show_signature = client_templates.SHOW_SIGNATURE

        email_body_json = {"message_head": message_head, "table": cashout_details, "message_tail": message_tail}

        component_props = {
            "refi_type": "cash_out",
            "is_display": "True",
            "show_signature": show_signature,
            "subject": email_subject,
            "body": email_body_json,
            "signature": lo_details,
            "disclaimer": disclaimer,
        }

        if client_templates.DISABLE_SEND_EMAIL_BUTTON or (
            not lo_details.get("nmls_number") and not client_templates.FORCE_SEND_EMAIL_BUTTON
        ):
            component_props["is_display"] = "False"

        return ToolResponse(
            message="Here's the Cash Out Refi Email based on your inputs.",
            component=Component(
                component_name=ComponentEnum.SalesEmail,
                component_props=component_props,
            ),
        )

    async def get_purchase_offer(self, **kwargs) -> ToolResponse | None:
        auth_token = await loan_utils.get_impersonation_auth_token(**kwargs)
        if not auth_token:
            return ToolResponse(
                message=(
                    "Sorry, you are not authorized to access this feature."
                    "Please make sure your email address matches the email address in your loan account."
                )
            )
        user = kwargs.get("user")
        loan_number = kwargs.get("loan_number")
        assistant_id = kwargs.get("assistant_id")

        first_name = kwargs.get("first_name")
        last_name = kwargs.get("last_name")
        address = kwargs.get("street_address", "TBD")
        city = kwargs.get("city", "TBD")
        state = kwargs.get("state", "TBD")
        zip_code = kwargs.get("zip_code", "TBD")

        purchase_price = kwargs.get("purchase_price")
        closing_fees = kwargs.get("closing_fees", 5000)
        property_tax_rate = kwargs.get("property_tax_rate", 1)
        annual_hoi = kwargs.get("annual_hoi", 1500)
        lender_concession = kwargs.get("lender_concession", 0)
        dpa_grant = kwargs.get("dpa_grant", 0)

        time_frame = kwargs.get("time_frame")
        property_appreciation_rate = kwargs.get("property_appreciation_rate", 5)
        investment_roi = kwargs.get("investment_roi", 9)

        mandatory_params = [first_name, last_name, purchase_price, time_frame]
        if not (all(mandatory_params)):
            return ToolResponse(
                message=(
                    "Thank you for your interest in generating a purchase offer."
                    " Please select an existing or a new customer to proceed."
                ),
                component=Component(
                    component_name=ComponentEnum.SalesEmail,
                    component_props={
                        "operation": "purchase_offer",
                        "fields": {},
                    },
                ),
            )

        options = []
        for i in range(1, 5):
            option_data = kwargs.get(f"option_{i}", {})
            option = {
                "dp_percent": option_data.get("dp_percent"),
                "term": option_data.get("term"),
                "rate": option_data.get("rate"),
                "points": option_data.get("points"),
                "mortgage_insurance_rate": option_data.get("mortgage_insurance_rate", 0.0),
                "loan_type": option_data.get("loan_type", "Conventional"),
                "va_funding_fee": option_data.get("va_funding_fee", 0.0),
                "desc": option_data.get("desc", f"Down Payment {i}"),
            }

            if all(option[field] is not None for field in ["dp_percent", "term", "rate", "points"]):
                options.append(option)
            elif any(option[field] is not None for field in ["dp_percent", "term", "rate", "points"]):
                missing_fields = [
                    f"option_{i}.{field}"
                    for field in ["dp_percent", "term", "rate", "points"]
                    if option[field] is None
                ]
                return ToolResponse(
                    message=(
                        "Incomplete option data provided. "
                        f"Missing {missing_fields} for option {i}. "
                        "Please provide all required fields for each option."
                    ),
                    component=Component(
                        component_name=ComponentEnum.SalesEmail,
                        component_props={"operation": "purchase_offer", "fields": {f"option_{i}": option}},
                    ),
                )

        if not options:
            return ToolResponse(
                message=(
                    "No valid options provided. "
                    "Please provide at least one complete option with down payment percentage, term, rate, points, and description."  # noqa
                ),
                component=Component(
                    component_name=ComponentEnum.SalesEmail,
                    component_props={"operation": "purchase_offer", "fields": {}},
                ),
            )

        if all(value is not None for value in mandatory_params):
            purchase_details = {
                "First Name": first_name,
                "Last Name": last_name,
                "Address": address,
                "City": city,
                "State": state,
                "Zip Code": zip_code,
                "Purchase Price": purchase_price,
                "Closing Fees": closing_fees,
                "Property Tax Rate": property_tax_rate,
                "Annual HOI": annual_hoi,
                "Lender Concession": lender_concession,
                "DPA Grant": dpa_grant,
                "Options": options,
                "Time Frame": time_frame,
                "Property Appreciation Rate": property_appreciation_rate,
                "Investment ROI": investment_roi,
            }
        else:
            required_fields = {
                "First Name": first_name,
                "Last Name": last_name,
                "Purchase Price": purchase_price,
                "Time Frame": time_frame,
            }
            missing_fields = [field_name for field_name, value in required_fields.items() if not value]
            message = (
                "Thank you for your interest in generating a purchase offer."
                " To proceed, please fill out the necessary form with relevant details."
                f" Missing {missing_fields} parameters for generating purchase offer email."
            )
            return ToolResponse(
                message=message,
                component=Component(
                    component_name=ComponentEnum.SalesEmail,
                    component_props={"operation": "purchase_offer", "fields": required_fields},
                ),
            )

        loan_table, investment_table, graph_json = await sales_utils.generate_purchase_offer_details(purchase_details)

        # Check if any of the address fields are 'TBD'
        if any(field.lower() == "tbd" for field in [address, city, state, zip_code]):
            property_address = "your property"
        else:
            property_address = f"{address}, {city}, {state}, {zip_code}"

        # Fetch loan officer details
        if loan_number:
            loan_details = await loan_function.get_loan_details_by_loan_number(
                user=user,
                loan_number=loan_number,
                assistant_id=assistant_id,
                data_only=True,
            )
            if isinstance(loan_details, ToolResponse):
                return {"error": "Couldn't fetch the loan details"}
            lender_name = loan_details.get("Lender Name")
            loid = loan_details.get("Loan Officer Login ID")
            lo_email = loan_details.get("Loan Officer Email")
            lo_details = await loan_utils.fetch_loan_officer_details(loid, lo_email, **kwargs)
        else:
            lender_name = None
            lo_details = await loan_utils.fetch_loan_officer_details(**kwargs)

        now = datetime.now(timezone.utc)
        disclaimer = client_templates.DISCLAIMER_TEMPLATE.format(
            year=now.year, date=now.strftime("%m/%d/%Y"), nmls_number=lo_details.get("nmls_number", "000000")
        )
        show_signature = client_templates.SHOW_SIGNATURE

        lender_str = f"from {lender_name}" if lender_name else ""
        email_subject = client_templates.PURCHASE_EMAIL_SUBJECT.format(
            first_name=purchase_details.get("First Name"),
            last_name=purchase_details.get("Last Name"),
            lender_str=lender_str,
        )

        message_head = client_templates.PURCHASE_OFFER_EMAIL_BODY_HEAD.format(
            first_name=purchase_details.get("First Name"),
            property_address=property_address,
        )
        message_mid = client_templates.PURCHASE_OFFER_EMAIL_BODY_MID.format(
            time_frame=purchase_details.get("Time Frame"),
        )
        message_tail = client_templates.PURCHASE_OFFER_EMAIL_BODY_TAIL.format(
            property_address=property_address,
        )
        email_body_json = {
            "message_head": message_head,
            "loan_table": loan_table,
            "message_mid": message_mid,
            "investment_table": investment_table,
            "message_tail": message_tail,
        }

        division_details = client_templates.get_division_theme(user)
        division_details.update({"Equal Housing Lender Logo": client_templates.EQUAL_HOUSING_LENDER_LOGO})

        component_props = {
            "refi_type": "purchase_offer",
            "is_display": "True",
            "show_signature": show_signature,
            "subject": email_subject,
            "body": email_body_json,
            "graph": graph_json,
            "signature": lo_details,
            "disclaimer": disclaimer,
            "division_details": division_details,
        }

        if client_templates.DISABLE_SEND_EMAIL_BUTTON or (
            not lo_details.get("nmls_number") and not client_templates.FORCE_SEND_EMAIL_BUTTON
        ):
            component_props["is_display"] = "False"

        return ToolResponse(
            message="Here's the Purchase Offer Email based on your inputs.",
            component=Component(
                component_name=ComponentEnum.SalesEmail,
                component_props=component_props,
            ),
        )

    async def generate_open_house_flyer(self, **kwargs) -> ToolResponse | None:
        if not client_templates.OPEN_HOUSE_FLYER_ENABLED:
            return ToolResponse(message=("Sorry, you are not allowed to access this feature right now."))

        property_image = kwargs.get("property_image")
        address = kwargs.get("street_address")
        city = kwargs.get("city")
        state = kwargs.get("state")
        zip_code = kwargs.get("zip_code")
        purchase_price = kwargs.get("purchase_price")
        property_tax_rate = kwargs.get("property_tax_rate", 1)
        annual_hoi = kwargs.get("annual_hoi", 1500)

        interest_rate = kwargs.get("interest_rate")
        point = kwargs.get("point")

        bedrooms_number = kwargs.get("bedrooms_number")
        bathrooms_number = kwargs.get("bathrooms_number")
        amenities = kwargs.get("amenities")
        status = kwargs.get("status")

        agent_name = kwargs.get("agent_name")
        agent_cell_phone = kwargs.get("agent_cell_phone")
        company_name = kwargs.get("company_name")
        company_office_phone = kwargs.get("company_office_phone")

        source = kwargs.get("source")
        mls_number = kwargs.get("mls_number")
        assistant_id = kwargs.get("assistant_id")

        mandatory_params = [
            property_image,
            address,
            city,
            state,
            zip_code,
            purchase_price,
            interest_rate,
            point,
            bedrooms_number,
            bathrooms_number,
            amenities,
            status,
            agent_name,
            agent_cell_phone,
            company_name,
            company_office_phone,
            source,
            mls_number,
        ]
        if not (all(mandatory_params)):
            return ToolResponse(
                message=(
                    "Thank you for your interest in generating an open house flyer."
                    " Please fill out the necessary form with relevant details to proceed."
                ),
                component=Component(
                    component_name=ComponentEnum.OpenHouseFlyer,
                    component_props={
                        "operation": "open_house_flyer",
                        "fields": {},
                    },
                ),
            )

        if all(value is not None for value in mandatory_params):
            flyer_details = {
                "Property Image": property_image,
                "Address": address,
                "City": city,
                "State": state,
                "Zip Code": zip_code,
                "Purchase Price": purchase_price,
                "Property Tax Rate": property_tax_rate,
                "Annual HOI": annual_hoi,
                "Interest Rate": interest_rate,
                "Point": point,
                "Bedrooms Number": bedrooms_number,
                "Bathrooms Number": bathrooms_number,
                "Amenities": amenities,
                "Status": status,
                "Agent Name": agent_name,
                "Agent Cell Phone": agent_cell_phone,
                "Company Name": company_name,
                "Company Office Phone": company_office_phone,
                "Source": source,
                "MLS Number": mls_number,
            }
        else:
            required_fields = {
                "Property Image": property_image,
                "Address": address,
                "City": city,
                "State": state,
                "Zip Code": zip_code,
                "Purchase Price": purchase_price,
                "Interest Rate": interest_rate,
                "Point": point,
                "Bedrooms Number": bedrooms_number,
                "Bathrooms Number": bathrooms_number,
                "Amenities": amenities,
                "Status": status,
                "Agent Name": agent_name,
                "Agent Cell Phone": agent_cell_phone,
                "Company Name": company_name,
                "Company Office Phone": company_office_phone,
                "Source": source,
                "MLS Number": mls_number,
            }
            missing_fields = [field_name for field_name, value in required_fields.items() if not value]
            message = (
                "Thank you for your interest in obtaining an open house flyer."
                " To proceed, please fill out the necessary form with relevant details."
                f" Missing {missing_fields} parameters for generating open house flyer."
            )
            return ToolResponse(
                message=message,
                component=Component(
                    component_name=ComponentEnum.OpenHouseFlyer,
                    component_props={"operation": "open_house_flyer", "fields": required_fields},
                ),
            )

        flyer_url = await sales_utils.generate_open_house_flyer(assistant_id, flyer_details)
        return ToolResponse(
            message="Here's the Open House Flyer based on your inputs.",
            component=Component(
                component_name=ComponentEnum.OpenHouseFlyer,
                component_props={"link": flyer_url},
            ),
        )

    async def generate_pre_qualification_letter(self, **kwargs) -> ToolResponse | None:
        auth_token = await loan_utils.get_impersonation_auth_token(**kwargs)
        user = kwargs.get("user")
        if not auth_token:
            return ToolResponse(
                message=(
                    "Sorry, you are not authorized to access this feature."
                    "Please make sure your email address matches the email address in your loan account."
                )
            )

        if not client_templates.PRE_QUALIFICATION_LETTER_ENABLED:
            return ToolResponse(message=("Sorry, you are not allowed to access this feature."))

        user = kwargs.get("user")
        assistant_id = kwargs.get("assistant_id")
        loan_number = kwargs.get("loan_number")

        date = datetime.now().strftime("%m/%d/%Y")
        borrower_first_name = kwargs.get("borrower_first_name")
        borrower_last_name = kwargs.get("borrower_last_name")
        coborrower_first_name = kwargs.get("coborrower_first_name")
        coborrower_last_name = kwargs.get("coborrower_last_name")
        loan_type = kwargs.get("loan_type")

        address = kwargs.get("street_address")
        city = kwargs.get("city")
        state = kwargs.get("state")
        zip_code = kwargs.get("zip_code")
        purchase_price = kwargs.get("purchase_price")
        down_payment_percent = kwargs.get("down_payment_percent")
        property_loan_amount = kwargs.get("property_loan_amount")

        mandatory_params = [
            borrower_first_name,
            borrower_last_name,
            loan_type,
            address,
            city,
            state,
            zip_code,
            purchase_price,
            down_payment_percent,
        ]
        if not (all(mandatory_params)):
            return ToolResponse(
                message=(
                    "Thank you for your interest in obtaining a pre-qualification letter."
                    " Please select an existing or a new customer to proceed."
                ),
                component=Component(
                    component_name=ComponentEnum.PreQual,
                    component_props={
                        "operation": "pre_qualification_letter",
                        "fields": {},
                    },
                ),
            )

        if all(value is not None for value in mandatory_params):
            division_details = client_templates.get_division_theme(user)
            pre_qualification_details = {
                "Loan Number": loan_number,
                "Date": date,
                "First Name": borrower_first_name,
                "Last Name": borrower_last_name,
                "Co-Borrower First Name": coborrower_first_name,
                "Co-Borrower Last Name": coborrower_last_name,
                "Loan Type": loan_type,
                "Address": address,
                "City": city,
                "State": state,
                "Zip Code": zip_code,
                "Purchase Price": f"${purchase_price:,.2f}",
                "Company Logo": division_details.get("logo"),
                "Equal Housing Lender Logo": client_templates.EQUAL_HOUSING_LENDER_LOGO,
                "Primary Color": division_details.get("primary_color"),
                "Secondary Color": division_details.get("secondary_color"),
            }
        else:
            required_fields = {
                "First Name": borrower_first_name,
                "Last Name": borrower_last_name,
                "Loan Type": loan_type,
                "Address": address,
                "City": city,
                "State": state,
                "Zip Code": zip_code,
                "Purchase Price": purchase_price,
                "Down Payment Percent": down_payment_percent,
            }
            missing_fields = [field_name for field_name, value in required_fields.items() if not value]
            message = (
                "Thank you for your interest in obtaining a pre-qualification letter."
                " To proceed, please fill out the necessary form with relevant details."
                f" Missing {missing_fields} parameters for generating pre-qualification letter."
            )
            return ToolResponse(
                message=message,
                component=Component(
                    component_name=ComponentEnum.PreQual,
                    component_props={"operation": "pre_qualification_letter", "fields": required_fields},
                ),
            )

        if loan_number:
            loan_details = await loan_function.get_loan_details_by_loan_number(
                user=user,
                loan_number=loan_number,
                assistant_id=assistant_id,
                data_only=True,
            )
            if isinstance(loan_details, ToolResponse):
                return {"error": "Couldn't fetch the loan details"}
            loid = loan_details.get("Loan Officer Login ID")
            lo_email = loan_details.get("Loan Officer Email")
            maxprequal_amount = loan_details.get("Max to be approved for", 0)
            loan_amount = loan_details.get("Loan Amount", 0)

            office_phone = loan_details.get("Office Phone")
            pre_qualification_details["Office Phone"] = office_phone

            lo_details = await loan_utils.fetch_loan_officer_details(loid, lo_email, prequal=True, **kwargs)
        else:
            maxprequal_amount = 0
            loan_amount = 0
            lo_details = await loan_utils.fetch_loan_officer_details(prequal=True, **kwargs)
            pre_qualification_details["Office Phone"] = lo_details.get("office_phone")

        pre_qualification_details.update(
            {
                "Loan Officer": lo_details.get("name"),
                "Loan Officer Email": lo_details.get("email"),
                "Cell Phone": lo_details.get("phone"),
                "NMLS Number": lo_details.get("nmls_number"),
                "Broker Lender Address": lo_details.get("broker_lender_address"),
                "Broker Lender City": lo_details.get("broker_lender_city"),
                "Broker Lender State": lo_details.get("broker_lender_state"),
                "Broker Lender Zip": lo_details.get("broker_lender_zip"),
            }
        )

        # Calculations
        if not property_loan_amount:
            down_payment_amount = (down_payment_percent / 100) * purchase_price
            property_loan_amount = purchase_price - down_payment_amount
        pre_qualification_details["Property Loan Amount"] = f"${property_loan_amount:,.2f}"

        # Loan limit checks
        if maxprequal_amount > 0 or loan_amount > 0:
            max_allowed = maxprequal_amount if maxprequal_amount > 0 else loan_amount
            if property_loan_amount > max_allowed:
                return ToolResponse(
                    message=(
                        "Sorry, you are not qualified for this loan amount. "
                        f"The requested loan amount (${property_loan_amount:,.2f}) exceeds the maximum allowed (${max_allowed:,.2f})."  # noqa
                    )
                )

        return ToolResponse(
            message="Here's the Pre-Qualification Letter based on your inputs.",
            component=Component(
                component_name=ComponentEnum.PreQual,
                component_props={
                    "pre_qualification_details": pre_qualification_details,
                },
            ),
        )

    async def find_lending_opportunity(self, **kwargs) -> ToolResponse | None:
        """
        Find a lending opportunity for a client.
        """
        if not client_templates.FIND_LENDING_OPPORTUNITY_ENABLED:
            return ToolResponse(
                message=(
                    "We apologize, but this feature is currently restricted. Please contact your administrator for access."  # noqa
                )
            )

        return ToolResponse(
            message="To proceed, please upload the file to process.",
            component=Component(
                component_name=ComponentEnum.FindLendingOpportunity,
                component_props={"operation": "find_lending_opportunity"},
            ),
        )

    async def make_call(self, **kwargs) -> ToolResponse | None:
        """Make an outbound call."""
        auth_token = await loan_utils.get_impersonation_auth_token(**kwargs)
        loan_number = kwargs.get("loan_number")
        first_name = kwargs.get("first_name")
        last_name = kwargs.get("last_name")
        loan_amount = kwargs.get("loan_amount")
        interest_rate = kwargs.get("interest_rate")
        mopymtpi = kwargs.get("monthly_payment_pi")
        closed_date = kwargs.get("closed_date")
        loan_term = int(float(kwargs.get("loan_term", 360)))
        closing_fees = kwargs.get("closing_fees", 5000)
        outlook_token = kwargs.get("outlook_token")
        user = kwargs.get("user")

        if not auth_token:
            return ToolResponse(
                message=(
                    "Sorry, you are not authorized to access this feature."
                    "Please make sure your email address matches the email address in your loan account."
                )
            )

        await self.initialize_redis()

        if outlook_token:
            await self.redis.setex(f"outlook_token-{user.email}", timedelta(hours=1), outlook_token)

        loan_number = kwargs.get("loan_number")
        closing_fees = kwargs.get("closing_fees", 5000)

        options = kwargs.get("options", [])
        if not options:
            return ToolResponse(
                message=("No valid options provided. Please provide at least one complete option."),
                component=Component(
                    component_name=ComponentEnum.SalesEmail,
                    component_props={
                        "operation": "auto_quote",
                        "require_extra_options": client_templates.REQUIRE_ALL_OPTION_FIELDS,
                        "fields": {},
                    },
                ),
            )

        valid_options = []
        invalid_options = []

        all_option_fields = ["term", "rate", "points", "loan_type"]
        required_fields = ["term", "rate"] if not client_templates.REQUIRE_ALL_OPTION_FIELDS else all_option_fields
        for i, option_data in enumerate(options, 1):
            rate = option_data.get("rate")
            if rate == 0:
                continue

            option = {
                "term": option_data.get("term"),
                "rate": rate,
                "points": option_data.get("points") if "points" in required_fields else None,
                "mortgage_insurance_rate": option_data.get("mortgage_insurance_rate", 0.0),
                "loan_type": option_data.get("loan_type") if "loan_type" in required_fields else None,
                "va_funding_fee": option_data.get("va_funding_fee", 0.0),
            }

            if all(option[field] is not None for field in required_fields):
                valid_options.append(option)
            elif any(option[field] is not None for field in required_fields):
                missing_fields = [field for field in required_fields if option[field] is None]
                invalid_options.append(f"option_{i}: {missing_fields}")

        if invalid_options:
            return ToolResponse(
                message=(
                    f"Incomplete option data provided. Missing fields: {', '.join(invalid_options)}."
                    "Please provide all required fields for each option."
                ),
                component=Component(
                    component_name=ComponentEnum.SalesEmail,
                    component_props={
                        "operation": "auto_quote",
                        "require_extra_options": client_templates.REQUIRE_ALL_OPTION_FIELDS,
                        "fields": {},
                    },
                ),
            )

        loan_details = {
            "Loan Number": loan_number,
            "First Name": first_name,
            "Last Name": last_name,
            "Loan Amount": loan_amount,
            "Interest Rate": interest_rate,
            "MoPymtPI": (
                mopymtpi
                if mopymtpi
                else await loan_utils.calculate_monthly_payment(
                    principal=loan_amount,
                    rate=interest_rate,
                    term_months=loan_term,
                )
            ),
            "Loan Term": loan_term,
            "Fund Released Date": closed_date,
            "Closing Fees": closing_fees,
        }

        refinance_details = await sales_utils.generate_refinance_options(loan_details, valid_options)

        header = refinance_details[0][1:]
        detailed_paragraph = []

        current = [row[1] for row in refinance_details[1:5]]
        detailed_paragraph.append(
            f"You currently have a {current[0]} mortgage with a loan amount of {current[1]}, "
            f"at an interest rate of {current[2]}. Your monthly principal and interest payment is {current[3]}."
        )

        for i, option in enumerate(header[1:], start=2):
            details = [row[i] for row in refinance_details[1:]]
            detailed_paragraph.append(
                f"\n- {option} offers a {details[0]} mortgage with a loan amount of {details[1]}, "
                f"at an interest rate of {details[2]}. Your monthly payment would be {details[3]}, "
                f"resulting in monthly savings of {details[4]} and total savings of {details[5]}."
            )

        refinance_description = "\n".join(detailed_paragraph)

        phone_number_to_call = kwargs.get("phone_number")
        current_loan_details = (
            f"{loan_details['First Name']} {loan_details['Last Name']} (Loan Number: {loan_details['Loan Number']}) took a loan of "  # noqa: E501
            f"${loan_details['Loan Amount']:,} at an interest rate of {loan_details['Interest Rate']}% for a term of "
            f"{loan_details['Loan Term']} months. The monthly principal and interest payment is ${loan_details['MoPymtPI']:.2f}. "  # noqa: E501
        )

        if not phone_number_to_call:
            raise ValueError("Please provide a phone number to call.")

        current_loan_details_escaped = saxutils.escape(current_loan_details)
        refinance_description_escaped = saxutils.escape(refinance_description)

        outbound_twiml = (
            '<?xml version="1.0" encoding="UTF-8"?>'
            "<Response>"
            "  <Connect>"
            f'    <Stream url="{settings.DOMAIN_URL}/api/twilio-call/refinancing_option/outbound-call">'
            f'      <Parameter name="current_loan_details" value="{current_loan_details_escaped}"/>'
            f'      <Parameter name="refinance_description" value="{refinance_description_escaped}"/>'
            f'      <Parameter name="user_email" value="{user.email}"/>'
            "    </Stream>"
            "  </Connect>"
            "</Response>"
        )

        client = Client(settings.TWILIO_ACCOUNT_SID, settings.TWILIO_AUTH_TOKEN)

        call = client.calls.create(to=phone_number_to_call, from_=settings.TWILIO_PHONE_NUMBER, twiml=outbound_twiml)

        return ToolResponse(
            message=f"Call has been started with SID: {call.sid}. You can find the recording of the call in the Twilio console."  # noqa: E501
        )


sales_function = SalesFunction()
