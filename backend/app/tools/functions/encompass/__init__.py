from .loan import loan_function
from .sales import sales_function


class EncompassFunction:

    def list_functions(self) -> dict:
        return {
            **loan_function.list_functions(),
            **sales_function.list_functions(),
        }

    @staticmethod
    def function_status_mapping() -> dict:
        return {
            "_default": "Processing the Request.",
        }


encompass_function = EncompassFunction()
