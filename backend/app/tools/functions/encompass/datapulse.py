import base64
import copy
import json

import redis.asyncio as aioredis
from config import settings
from db.models import User
from fastapi import HTT<PERSON>Ex<PERSON>, status
from hooks.s3 import get_s3_hook
from httpx import AsyncClient
from loguru import logger
from tools.fields.encompass import fetch_all_fields
from tools.utils.encompass.datapulse import datapulse_utils
from tools.utils.encompass.loan import loan_utils


class DataPulseFunctions:
    def __init__(self) -> None:
        self.client = AsyncClient(timeout=None)
        self.redis = None

    async def initialize_redis(self):
        if self.redis is None:
            try:
                self.redis = await aioredis.from_url(settings.REDIS_URL)
            except Exception as e:
                logger.error(f"Redis initialization failed: {e}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Redis service is unavailable.",
                )

    async def get_reports(self, assistant_id: str) -> list:
        reports = await datapulse_utils.get_reports_from_db(assistant_id)

        if not reports:
            logger.info("No reports found in the database.")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="No reports available.",
            )

        return [
            {
                "name": report.get("name"),
                "description": report.get("description"),
                "id": report.get("id"),
                "requires_custom_date": report.get("requires_custom_date"),
                "order": report.get("order"),
            }
            for report in reports
            if report.get("is_visible")
        ]

    async def generate_report(
        self, assistant_id: str, user: User, report_id: str, start_date: str | None, end_date: str | None
    ) -> dict:
        await self.initialize_redis()

        reports = await datapulse_utils.get_reports_from_db(assistant_id=assistant_id)

        if not reports:
            logger.info("No reports found in the database.")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="No reports available.",
            )

        try:
            report = list(filter(lambda r: r.get("id") == report_id, reports))[0]
            report = copy.deepcopy(report)
        except Exception as e:
            logger.info(f"Report with ID {report_id} not found: {e}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Report not found.",
            )

        try:
            data_fields = await fetch_all_fields(assistant_id=assistant_id)
            payload, start_date, end_date = datapulse_utils.parse_payload(report, start_date, end_date)
            s3_hook = await get_s3_hook(assistant_id=assistant_id)

            FIELD_MAPPING = data_fields.get("FIELD_MAPPING", {})
            FIELD_TYPE_MAPPING = data_fields.get("FIELD_TYPE_MAPPING", {})

            payload["fields"].extend(list(FIELD_MAPPING.keys()))

            cache_key = await datapulse_utils.get_cache_key(assistant_id, user, report_id, start_date, end_date)
            cached_data = await self.redis.get(cache_key)
            if cached_data:
                result = json.loads(cached_data)
                for key in [
                    "primary_display_fields",
                    "excel_url",
                    "loan_details",
                    "secondary_display_fields",
                    "summary",
                ]:
                    result.pop(key, None)
                return result

            all_response_data = await datapulse_utils.fetch_loan_data(
                assistant_id=assistant_id,
                user=user,
                payload=payload,
            )

            if not all_response_data:
                logger.warning("No data found for the report.")
                return {
                    "name": report.get("name"),
                    "description": report.get("description"),
                    "report_key": None,
                }

            loan_details = await datapulse_utils.process_loan_data(
                all_response_data, FIELD_MAPPING, FIELD_TYPE_MAPPING
            )

            excel_url = loan_utils.generate_and_upload_excel(
                s3_hook=s3_hook, prefix=f"{report.get('id')}", report_data=loan_details, folder="datapulse"
            )

            if report.get("requires_condition_pending", False):
                report["primary_display_fields"].append("View Conditions Pending")

                try:
                    loan_details = await datapulse_utils.add_open_conditions(
                        loan_details=loan_details,
                        assistant_id=assistant_id,
                        user=user,
                    )
                except Exception as e:
                    logger.error(f"Unexpected error fetching conditions {str(e)}")

            summary = await datapulse_utils.generate_summary(loan_details)

            # To ensure "all" is always at the top of the list
            officers = [{"name": loan["Loan Officer"], "loid": loan["Loan Officer Login ID"]} for loan in summary]
            officers.sort(key=lambda x: x["loid"] != "all")

            result = {
                "name": report.get("name", "Report"),
                "description": report.get("description"),
                "report_key": base64.b64encode(cache_key.encode()).decode(),
                "summary": summary,
                "excel_url": excel_url,
                "loan_details": loan_details,
                "primary_display_fields": await datapulse_utils.convert_cannonical_to_display(
                    data=report.get("primary_display_fields"), FIELD_MAPPING=FIELD_MAPPING
                ),
                "secondary_display_fields": await datapulse_utils.convert_cannonical_to_display(
                    data=report.get("secondary_display_fields"), FIELD_MAPPING=FIELD_MAPPING
                ),
                "available_officers": officers,
            }

            await self.redis.setex(cache_key, report.get("ttl", 86400), json.dumps(result))

            for key in ["primary_display_fields", "excel_url", "loan_details", "secondary_display_fields", "summary"]:
                result.pop(key, None)

            return result

        except HTTPException as e:
            logger.error(f"HTTPException occurred: {e.detail}")
            raise e

        except Exception as e:
            logger.error(f"Error generating report: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to generate report. Please try again later.",
            )

    async def get_summary(
        self, user: User, report_key: str, loid: str, page: int, size: int, assistant_id: str
    ) -> dict:

        await self.initialize_redis()

        # we might not need this anymore
        if not loid:
            loan_officer = await loan_utils.get_loan_officer_details(
                assistant_id=assistant_id,
                user=user,
            )
            loid = loan_officer.get("loan_id")

        try:
            cache_key = base64.b64decode(report_key).decode()
        except Exception as e:
            logger.error(f"Failed to decode cache key: {e}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid cache key format.",
            )

        cached_data = await self.redis.get(cache_key)

        if not cached_data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Report data not found or expired. Please generate a new report.",
            )

        result = json.loads(cached_data)

        result["summary"] = list(filter(lambda x: x["Loan Officer Login ID"] == loid, result["summary"]))

        start_idx = (page - 1) * size
        end_idx = start_idx + size

        if loid != "all":
            result["loan_details"] = filter(lambda x: x.get("Loan Officer Login ID") == loid, result["loan_details"])

        result["loan_details"] = [
            {k: v for k, v in loan.items() if k in result["secondary_display_fields"]}
            for loan in result["loan_details"]
        ]

        result["loan_details"] = result["loan_details"][start_idx:end_idx]

        for key in ["primary_display_fields", "excel_url", "available_officers"]:
            result.pop(key, None)

        return result

    async def get_complete_report(self, report_key: str) -> dict:
        await self.initialize_redis()

        try:
            cache_key = base64.b64decode(report_key).decode()
        except Exception as e:
            logger.error(f"Failed to decode cache key: {e}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid cache key format.",
            )

        cached_data = await self.redis.get(cache_key)

        if not cached_data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Report data not found or expired. Please generate a new report.",
            )

        result = json.loads(cached_data)

        result["loan_details"] = [
            {k: v for k, v in loan.items() if k in result["primary_display_fields"]} for loan in result["loan_details"]
        ]

        for key in ["secondary_display_fields", "summary", "available_officers"]:
            result.pop(key, None)

        return result

    async def regenerate_report(
        self, assistant_id: str, user: User, report_id: str, start_date: str | None, end_date: str | None
    ) -> dict:
        await self.initialize_redis()
        cache_key = await datapulse_utils.get_cache_key(assistant_id, user, report_id, start_date, end_date)
        await self.redis.delete(cache_key)
        data = await self.generate_report(
            assistant_id=assistant_id, user=user, report_id=report_id, start_date=start_date, end_date=end_date
        )
        return data


datapulse_function = DataPulseFunctions()
