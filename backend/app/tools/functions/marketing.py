import base64
from datetime import datetime

from hooks.s3 import get_s3_hook
from loguru import logger
from tools.schema import Component, ComponentEnum, ToolResponse


class MarketingFunction:
    def list_functions(self) -> dict:
        return {
            "generate_marketing_image": self.generate_marketing_image,
        }

    def upload_image_to_s3(self, s3_hook, assistant_id: str, b64_json_image: str):
        try:
            logger.info(f"Uploading image to S3 bucket for assistant: {assistant_id}")
            file = base64.b64decode(b64_json_image)
            filename = f"{assistant_id}/image__{datetime.now().strftime('%Y%m%d%H%M%S')}.png"
            response = s3_hook.put_object(object_name=filename, file=file, content_type="image/png")

            if response["ResponseMetadata"]["HTTPStatusCode"] == 200:
                logger.info(f"Image uploaded to S3 bucket: {filename}")
                image_url = s3_hook.get_presigned_url_for_download(filename)
                return image_url
            else:
                raise Exception("Failed to store image in S3 bucket")
        except Exception as e:
            logger.error(f"An error occurred: {e}")
            return None

    async def generate_marketing_image(self, **kwargs) -> ToolResponse:
        """
        Generate an image for the given title.
        """
        try:
            prompt = kwargs.get("prompt")
            assistant_id = kwargs.get("assistant_id")
            size = kwargs.get("size") or "1024x1024"

            logger.info(f"Generating image for prompt: {prompt}")
            from hooks.openai_hook import oai_hook

            b64_json_image = await oai_hook.generate_image(prompt=prompt, model="dall-e-3", size=size)
            s3_hook = await get_s3_hook(assistant_id)
            image_url = self.upload_image_to_s3(s3_hook, assistant_id, b64_json_image)
            if image_url:
                message = "Image has been successfully generated."
                component = Component(component_name=ComponentEnum.Image, component_props={"link": image_url})
            else:
                message = "Image could not be generated."
            return ToolResponse(message=message, component=component if image_url else None)
        except Exception as e:
            logger.error(f"An error occurred: {e}")
            return ToolResponse(message="An error occurred.")


marketing_function = MarketingFunction()
