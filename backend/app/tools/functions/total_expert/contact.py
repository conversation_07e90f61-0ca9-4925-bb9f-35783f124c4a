from textwrap import dedent

from config import settings
from fastapi import HTTPException
from httpx import AsyncClient
from loguru import logger
from schema.enums import DataFieldFolder
from tools.fields.totalexpert import fetch_all_fields
from tools.fields.totalexpert._default import (
    CONTACT_CUTTOFF_FIELDS,
    CONTACT_FIELDS,
    CONTACT_SEARCH_CUSTOM_FIELDS,
    CONTACT_SEARCH_FIELDS,
)
from tools.schema import Component, ComponentEnum, ToolResponse
from tools.utils.total_expert import total_expert_utils
from utils.type_conversion import is_valid_iso8601, type_converter

from .constants import DATA_LIMIT


class ContactFunction:
    def __init__(self):
        self.client = AsyncClient(timeout=None)

    def list_functions(self) -> dict:
        return {
            "fetch_contacts": self.fetch_contacts,
            "add_contact": self.add_contact,
            "update_contact": self.update_contact,
        }

    async def fetch_contacts(self, **kwargs):
        user = kwargs.get("user")
        input_fields = kwargs.get("input_fields", [])
        assistant_id = kwargs.get("assistant_id")
        data_only = kwargs.get("data_only", False)
        # verify input fields
        for i_field in input_fields:
            field_name = i_field.get("field_name", "")
            if field_name not in CONTACT_SEARCH_FIELDS:
                logger.error("LLM Couldn't extract input fields.")
                return ToolResponse(message=f"Field name {i_field} not valid")

        # filter fields
        filter_criteria = []
        contact_id = None
        custom_filters = []
        stopping_condition_added = False
        stopping_condition = {}
        for contact_field in input_fields:
            field_name = contact_field.get("field_name", "")
            value = contact_field.get("value", "")
            if field_name not in CONTACT_SEARCH_FIELDS:
                return ToolResponse(
                    message=(
                        f"Field name {field_name} is not available for search. Did you mean any of these? Check values"
                        f" and confirm with user if needed. {CONTACT_SEARCH_FIELDS}"
                    )
                )
            if field_name == "id":
                contact_id = value
            elif field_name in CONTACT_SEARCH_CUSTOM_FIELDS:
                custom_filters.append(
                    {"field": field_name, "operator": contact_field.get("matchType"), "value": value}
                )
                if (
                    not stopping_condition_added
                    and field_name in CONTACT_CUTTOFF_FIELDS
                    and (
                        contact_field.get("matchType") == "GreaterThan"
                        or contact_field.get("matchType") == "GreaterThanEquals"
                    )
                ):
                    field_type = await total_expert_utils.get_data_type(
                        assistant_id=assistant_id, field_name=field_name, folder=DataFieldFolder.CONTACT
                    )
                    stopping_condition = {
                        "field_name": field_name,
                        "field_value": value,
                        "field_type": field_type,
                    }
                    stopping_condition_added = True

            elif field_name in ["internal_updated_at_start", "internal_updated_at_end"]:
                # convert to iso format
                if not is_valid_iso8601(value):
                    logger.warning(f"Invalid date format: {field_name}={value}")
                    return ToolResponse(
                        message=f"Invalid date format for field {field_name}. Please use YYYY-MM-DDTHH:MM:SSZ format."
                    )
                filter_criteria.append(f"{field_name}={value}")
            else:
                filter_criteria.append(f"{field_name}={value}")

        filter = ",".join(filter_criteria)
        # fetch token
        token = await total_expert_utils.get_token(assistant_id, user=user)
        if not token:
            logger.error("Empty auth token")
            return ToolResponse(message="Could not authenticate! Please check your email and try again!")

        # TODO: if owner email is used, do not fetch team members and bypass admin check
        if settings.ENABLE_TOTAL_EXPERT_OAUTH:
            team_members = [None]
        else:
            team_members = await total_expert_utils._get_team_member_emails(user, token, assistant_id=assistant_id)
        if contact_id:
            try:
                contact = await total_expert_utils.fetch_contact(
                    user, contact_id, assistant_id=assistant_id, lo_email=team_members, token=token
                )
                if contact:
                    contacts = [contact]
            except Exception as e:
                logger.error(f"Error while fetching contact {contact_id}: {e}")
                return ToolResponse(
                    message=(
                        "Some error occured while fetching contact. Please try again. If the issue persists please"
                        " contact admin!"
                    ),
                )

        else:
            try:
                contacts = await total_expert_utils.fetch_contacts(
                    user,
                    filter=filter,
                    assistant_id=assistant_id,
                    token=token,
                    limit=DATA_LIMIT // len(team_members) or 1,
                    stopping_condition=stopping_condition,
                )
            except (Exception, HTTPException) as e:
                logger.error(f"Error while fetching contacts: {e}")
                return ToolResponse(
                    message=(
                        "Some error occured while fetching contacts. Please try again. If the issue persists please"
                        " contact admin!"
                    ),
                    payload={"filter": filter},
                )

        if not contacts:
            logger.warning("Empty Contacts!")
            return ToolResponse(
                message=(
                    "No associated contacts found. Please check your contact list."
                    " If you believe this is an error, contact the administrator."
                ),
                payload={"filter": filter},
            )
        filtered_contact_data = []
        for contact in contacts:
            contact["owner_email"] = contact.get("owner", {}).get("email", "")
            contact["owner_id"] = contact.get("owner", {}).get("id", "")

            contact.pop("owner", None)
            if not data_only:
                contact.pop("contact_groups", None)
            if custom_filters:
                if filtered_data := await total_expert_utils.filter_data(
                    data=contact, filter=custom_filters, assistant_id=assistant_id, folder=DataFieldFolder.CONTACT
                ):
                    filtered_contact_data.append(filtered_data)
                else:
                    continue
            else:
                filtered_contact_data.append(contact)

        FIELD_MAPPING, FIELD_TYPE_MAPPING, DISPLAY_FIELDS, SEARCH_FIELDS = await fetch_all_fields(
            kwargs.get("assistant_id"), folder=DataFieldFolder.CONTACT
        )

        if data_only:
            return contacts, DISPLAY_FIELDS, SEARCH_FIELDS

        contact_details = [
            {
                field_name: type_converter.convert_string_data(
                    FIELD_TYPE_MAPPING, field_key, contact.get(field_key, "")
                )
                for field_key, field_name in FIELD_MAPPING.items()
            }
            for contact in filtered_contact_data
        ]

        # also send borrower_contact_id for FE to handle selection and journey trigger
        for contact in contact_details:
            contact["borrower_contact_id"] = contact.get("id", None)

        if len(contact_details) == 0:
            logger.warning("Empty Contacts!")
            return ToolResponse(
                message=(
                    "No associated contacts found. Please check your contact list."
                    " If you believe this is an error, contact the administrator."
                ),
                payload={"filter": filter},
            )
        elif len(contact_details) == 1:
            message = f"Contact Details fetched: {contact_details[0]}"
        else:
            message = "Contacts fetched Successfully!"

        return ToolResponse(
            message=message,
            component=Component(
                component_name=ComponentEnum.TotalExpert,
                component_props={
                    "data": contact_details,
                    "headers": DISPLAY_FIELDS,
                    "search_fields": SEARCH_FIELDS,
                    "is_trigger_journey": False,
                },
            ),
            payload={"filter": filter_criteria},
        )

    async def add_contact(self, **kwargs):
        input_fields = kwargs.get("input_fields", [])
        user = kwargs.get("user")
        # verify input fields
        provided_fields = {}
        for i_field in input_fields:
            field_name = i_field.get("field_name", "")
            value = i_field.get("value", "")
            if field_name in CONTACT_FIELDS and value != "":
                provided_fields[field_name] = value

        provided_fields["source"] = provided_fields.get("source", settings.CLIENT_NAME)

        required_fields = ["source", "first_name", "last_name"]
        for field in required_fields:
            if field not in provided_fields:
                logger.warning(f"Field name {field} not provided!")
                return ToolResponse(
                    message=(
                        "Some required fields are missing. Please fill the form to add contact. Remember: Source,"
                        " First Name, Last Name, and either of Email or Phone is required."
                    ),
                    component=Component(
                        component_name=ComponentEnum.TotalExpert,
                        component_props={"operation": "add_contact", "fields": provided_fields},
                    ),
                )

        email_provided = "email" in provided_fields
        phone_cell_provided = "phone_cell" in provided_fields

        if not email_provided and not phone_cell_provided:
            logger.warning("Neither email nor phone_cell provided!")
            return ToolResponse(
                message=(
                    "Some required fields are missing. Please fill the form to add contact. Remember: Source, First"
                    " Name, Last Name, and either of Email or Phone is required."
                ),
                component=Component(
                    component_name=ComponentEnum.TotalExpert,
                    component_props={"operation": "add_contact", "fields": provided_fields},
                ),
            )

        # now call api to add contact
        response = await total_expert_utils.add_contact(user, **provided_fields)
        if response:
            return ToolResponse(
                message=dedent(
                    f"""
                    Contact added successfully with following details:
                    **Contact ID**: {response.get('id')}
                    {provided_fields}
                    """
                ).strip()
            )
        else:
            return ToolResponse(
                message=(
                    "Some required fields are missing. Please fill the form to add contact. Remember: Source, First"
                    " Name, Last Name, and either of Email or Phone is required."
                ),
                component=Component(
                    component_name=ComponentEnum.TotalExpert,
                    component_props={"operation": "add_contact", "fields": provided_fields},
                ),
            )

    async def update_contact(self, **kwargs) -> ToolResponse:
        return ToolResponse(
            message="Please fill the given form to update contact.",
            component=Component(
                component_name=ComponentEnum.TotalExpert,
                component_props={"operation": "update_contact"},
            ),
        )


contact_function = ContactFunction()
