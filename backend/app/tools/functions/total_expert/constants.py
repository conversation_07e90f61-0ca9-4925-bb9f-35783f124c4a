import difflib
import operator
from collections.abc import Callable
from datetime import datetime
from typing import Any

from config import settings

BASE_URL = settings.TOTAL_EXPERT_BASE_URL
PAGE_SIZE = 100
DATA_LIMIT = 500

NUMERIC_OPERATORS: dict[str, Callable[[Any, Any], bool]] = {
    "GreaterThanEqual": operator.ge,
    "LessThanEqual": operator.le,
    "Equals": operator.eq,
    "NotEqual": operator.ne,
    "GreaterThan": operator.gt,
    "LessThan": operator.lt,
}

# Operators for string values
STRING_OPERATORS: dict[str, Callable[[str, str], bool]] = {
    "Equals": lambda a, b: False if a is None or b is None else str(a).lower() == str(b).lower(),
    "NotEqual": lambda a, b: False if a is None or b is None else str(a).lower() != str(b).lower(),
    "Exact": lambda a, b: False if a is None or b is None else str(a).lower() == str(b).lower(),
    "Contains": lambda a, b: False if a is None or b is None else str(b).lower() in str(a).lower(),
    "StartsWith": lambda a, b: False if a is None or b is None else str(a).lower().startswith(str(b).lower()),
    "EndsWith": lambda a, b: False if a is None or b is None else str(a).lower().endswith(str(b).lower()),
    "FuzzyMatch": lambda a, b: (
        False
        if a is None or b is None
        else difflib.SequenceMatcher(None, str(a).lower(), str(b).lower()).ratio() >= 0.75
    ),
}
# Operators for datetime values
DATE_OPERATORS: dict[str, Callable[[datetime, datetime], bool]] = {
    "GreaterThanEquals": operator.ge,
    "LessThanEquals": operator.le,
    "Equal": operator.eq,
    "NotEqual": operator.ne,
    "GreaterThan": operator.gt,
    "LessThan": operator.lt,
}

MAP_DATAFIELD_TO_TYPE = {"text": str, "decimalnumber": float, "integernumber": int, "date": datetime}
