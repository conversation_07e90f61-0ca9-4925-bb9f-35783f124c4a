import asyncio
import difflib
import json

from config import settings
from httpx import AsyncClient, HTTPStatusError
from loguru import logger
from schema.enums import DataFieldFolder
from tools.fields.totalexpert import fetch_all_fields
from tools.functions.total_expert.contact import contact_function
from tools.schema import Component, ComponentEnum, ToolResponse
from tools.utils.total_expert import total_expert_utils
from utils.type_conversion import type_converter

from .constants import BASE_URL


class ContactGroupFunction:
    def __init__(self):
        self.client = AsyncClient(timeout=None)

    def list_functions(self) -> dict:
        return {
            # "add_to_contact_group": self.add_to_contact_group,
            "add_remove_contact_to_group": self.add_remove_contact_to_group,
        }

    async def add_contact_group_to_contact(self, lo_email: str, headers: dict, payload: dict, contact_id: str):
        """Adds a contact to contact group"""
        url = f"{BASE_URL}/contacts/{contact_id}"

        # First check if user is owner in given contact group or not
        try:
            logger.info(f"Checking if {lo_email} is owner of contact {contact_id} !")
            response = await self.client.get(url=url, headers=headers)
            response.raise_for_status()
            json_response = await total_expert_utils._decode_data(response)
            if json_response:
                owner_email = json_response.get("owner").get("email")
                if owner_email != lo_email:
                    logger.error(
                        f"Loan officer does not have permission to update {contact_id}: Owner is {owner_email}"
                    )
                    return False
            else:
                logger.error(f"Could not find contact with id: {contact_id}")
                return False
        except HTTPStatusError as e:
            logger.error(f"Error while fetching contact {contact_id}:: {e.response.status_code}:: {e.response.json()}")
            return False

        # now update contact to add it to contact group
        try:
            logger.info(f"Adding Contact {contact_id} to contact group.")
            response = await self.client.patch(url=url, headers=headers, data=json.dumps(payload))
            response.raise_for_status()
            return contact_id
        except HTTPStatusError as e:
            logger.error(f"Error while adding contact {contact_id}:: {e.response.status_code}:: {e.response.json()}")
            return False

    async def add_to_contact_group(self, **kwargs) -> ToolResponse:
        user = kwargs.get("user")
        assistant_id = kwargs.get("assistant_id")
        contact_ids = kwargs.get("contact_ids", [])
        contact_group = kwargs.get("contact_group", "")
        if not contact_ids:
            logger.error("Contact id not provided!")
            return ToolResponse(message="Please provide contacts of which you want to update contact groups!")

        if not contact_group:
            logger.error("Contact group is not provided!")
            return ToolResponse(
                message="Contact group name is not provided. Please provide contact group name to continue!"
            )

        lo_email = await total_expert_utils.map_user(user)
        token = await total_expert_utils.get_token(assistant_id, user)
        if not token:
            logger.error("Empty auth token")
            return ToolResponse(message="Could not authenticate! Please check your email and try again!")

        headers = {
            "Authorization": token,
            "Content-Type": "application/json",
        }

        payload = {"contact_groups": [{"group_name": contact_group}]}
        tasks = [
            self.add_contact_group_to_contact(lo_email, headers, payload, contact_id) for contact_id in contact_ids
        ]
        responses = await asyncio.gather(*tasks)
        completed_responses = [response for response in responses if response]
        # Check if completed responses is equal to original contact ids or not
        if len(contact_ids) != len(completed_responses):
            # find failed contact ids
            failed_responses = list(set(completed_responses))
            failed_responses = [response for response in failed_responses if response]
            return ToolResponse(
                message=(
                    f"Some error occured while adding contacts {failed_responses} to {contact_group}. Please try"
                    " again!"
                )
            )
        return ToolResponse(
            message=f"Successfully added all contacts to contact group {contact_group}!",
            payload=payload,
        )

    async def add_remove_contact_to_group(self, **kwargs) -> ToolResponse:
        user = kwargs.get("user")
        assistant_id = kwargs.get("assistant_id")
        operation = kwargs.get("operation", "")
        contact_group = kwargs.get("contact_group", "")
        input_fields = kwargs.get("input_fields", [])
        create_new_group = kwargs.get("create_new_group", False)

        FIELD_MAPPING, FIELD_TYPE_MAPPING, DISPLAY_FIELDS, SEARCH_FIELDS = await fetch_all_fields(
            kwargs.get("assistant_id"), folder=DataFieldFolder.CONTACT
        )

        if not input_fields:
            return ToolResponse(message="Please provide input fields to identify the contact.")

        if not contact_group:
            return ToolResponse(message="Contact group name is not provided.")

        operation = operation.lower().strip()
        if operation not in ("add", "remove"):
            return ToolResponse(message="Invalid operation. Must be 'add' or 'remove'.")

        contact_result = await contact_function.fetch_contacts(
            user=user,
            assistant_id=assistant_id,
            input_fields=input_fields,
            data_only=True,
        )

        if isinstance(contact_result, ToolResponse):
            return contact_result

        message = ""

        contacts, DISPLAY_FIELDS, SEARCH_FIELDS = contact_result

        if len(contacts) > 1:
            message += "Multiple contacts found. Please select the desired contacts. "

        if settings.ENABLE_TOTAL_EXPERT_OAUTH:
            available_contact_groups = await total_expert_utils.fetch_contact_groups(
                user=user, assistant_id=assistant_id
            )
        else:
            available_contact_groups = await total_expert_utils.fetch_journeys(
                assistant_id,
            )
            available_contact_groups = [
                {"group_name": group.get("name", ""), "id": group.get("id", "")} for group in available_contact_groups
            ]

        group_names = [group["group_name"] for group in available_contact_groups]

        if create_new_group:
            if contact_group in group_names:
                return ToolResponse(
                    message=f"Contact group '{contact_group}' already exists."
                    " Please choose a different name or confirm if you want to use existing group."
                )
            new_group = {"group_name": contact_group}
            matched_groups = [new_group]
            message += f"A new contact group '{contact_group}' will be created. "

        else:
            closest_names = difflib.get_close_matches(contact_group, group_names, n=5, cutoff=0.6)

            if not closest_names:
                return ToolResponse(
                    message=f"No contact groups match '{contact_group}'. Please check the name and try again."
                )

            matched_groups = [group for group in available_contact_groups if group["group_name"] in closest_names]

            if len(closest_names) > 1:
                message += (
                    f"Multiple contact groups match '{contact_group}': {closest_names}"
                    " Please select the correct one from the dropdown."
                )

        # additional validation before removing
        if operation == "remove":
            if len(contacts) > 1:
                contact_names = [
                    f"{contact.get('first_name', '')} {contact.get('last_name', '')}".strip() for contact in contacts
                ]
                message += (
                    f"Multiple contacts found ({', '.join(contact_names)}). "
                    "Please be more specific in your search criteria."
                )

            if len(matched_groups) > 1:
                group_names_list = [group["group_name"] for group in matched_groups]
                message += (
                    f"Multiple contact groups match '{contact_group}': {', '.join(group_names_list)}."
                    " Please be more specific about which contact group you want to remove the contact from."
                )

            if message:
                return ToolResponse(message=message)

            contact = contacts[0]
            target_group_name = matched_groups[0]["group_name"]
            contact_group_names = [cg.get("group_name") for cg in contact.get("contact_groups", [])]

            if target_group_name not in contact_group_names:
                contact_name = f"{contact.get('first_name', '')} {contact.get('last_name', '')}".strip()
                return ToolResponse(
                    message=f"Contact '{contact_name}' is not currently in the contact group '{target_group_name}'."
                    " No removal needed."
                )

        contact_details = [
            {
                field_name: type_converter.convert_string_data(
                    FIELD_TYPE_MAPPING, field_key, contact.get(field_key, "")
                )
                for field_key, field_name in FIELD_MAPPING.items()
            }
            for contact in contacts
        ]

        for contact in contact_details:
            contact.pop("contact_groups", None)
            contact["borrower_contact_id"] = contact.get("id", None)

        message += (
            f"The contacts to {operation} to the contact group '{contact_group}' are displayed below. "
            " Please review the details and confirm the operation."
        )
        return ToolResponse(
            message=message,
            component=Component(
                component_name=ComponentEnum.TotalExpert,
                component_props={
                    "data": contact_details,
                    "headers": DISPLAY_FIELDS,
                    "search_fields": SEARCH_FIELDS,
                    "contact_groups": matched_groups,
                    "is_add_operation": operation == "add",
                },
            ),
            payload={"filter": input_fields},
        )


contact_group_function = ContactGroupFunction()
