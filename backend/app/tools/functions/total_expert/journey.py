import asyncio
import json
import re
import string
from datetime import datetime, timedelta

from dateutil.relativedelta import relativedelta
from db.models import User
from httpx import AsyncClient, HTTPStatusError
from loguru import logger
from schema.total_expert import TEJourneyTypes
from tools.fields.totalexpert import CLOSED_LOAN_STATUS_FIELD
from tools.fields.totalexpert._default import (
    CASHOUT_REFI_VALID_FIELDS,
    DEFAULT_JOURNEY_LOAN_FILTER_FIELDS,
    RATE_TERM_REFI_VALID_FIELDS,
)
from tools.schema import Component, ComponentEnum, ToolResponse
from tools.templates import client_templates
from tools.utils.encompass.loan import loan_utils as encompass_loan_utils
from tools.utils.encompass.sales import sales_utils as encompass_sales_utils
from tools.utils.total_expert import total_expert_utils

from .constants import BASE_URL
from .loan import loan_function as te_loan_function


class JourneyFunction:
    def __init__(self):
        self.base_url = BASE_URL
        self.client = AsyncClient(timeout=None)
        self.page_size = 100

    def list_functions(self) -> dict:
        return {
            "trigger_journey": self.trigger_journey,
            "calculate_rate_term_refi": self.calculate_rate_term_refi,
            "calculate_cashout_refi": self.calculate_cashout_refi,
        }

    async def _fetch_loan_metadata(self, token: str, endpoint: str, key: str):
        url = self.base_url + endpoint
        headers = {
            "Authorization": token,
            "Content-Type": "application/json",
        }
        params = {"page[number]": 1, "page[size]": self.page_size}
        try:
            items = await total_expert_utils.fetch_data_from_api(url, method="GET", headers=headers, params=params)
            return [item.get(key) for item in items]
        except Exception as e:
            logger.error(f"Error while fetching {key.replace('_', ' ')}: {e}")
            return []

    async def fetch_loan_types(self, token: str):
        return await self._fetch_loan_metadata(token, "/loan-types", "loan_type")

    async def fetch_loan_status(self, token: str):
        return await self._fetch_loan_metadata(token, "/loan-statuses", "loan_status")

    async def fetch_loan_programs(self, token: str):
        return await self._fetch_loan_metadata(token, "/loan-programs", "loan_program")

    async def fetch_loan_purposes(self, token: str):
        return await self._fetch_loan_metadata(token, "/loan-purposes", "loan_purpose")

    async def _field_mapping(self, journey_type: TEJourneyTypes, assistant_id: str, user: User) -> list:
        if journey_type == TEJourneyTypes.RATE_TERM_REFI:
            default_fields = RATE_TERM_REFI_VALID_FIELDS

        elif journey_type in [TEJourneyTypes.CASHOUT_REFI]:
            default_fields = CASHOUT_REFI_VALID_FIELDS
        else:
            default_fields = DEFAULT_JOURNEY_LOAN_FILTER_FIELDS

        # fetch token
        token = await total_expert_utils.get_token(assistant_id, user=user)
        if not token:
            logger.warning("Could not fetch access token")
            return default_fields

        loan_program_field = default_fields.get("loan_program")
        if loan_program_field:
            default_fields["loan_program"]["values"] = await self.fetch_loan_programs(token)

        loan_purpose_field = default_fields.get("loan_purpose")
        if loan_purpose_field:
            default_fields["loan_purpose"]["values"] = await self.fetch_loan_purposes(token)

        loan_status_field = default_fields.get("loan_status")
        if loan_status_field:
            default_fields["loan_status"]["values"] = await self.fetch_loan_status(token)

        loan_type_field = default_fields.get("loan_type")
        if loan_type_field:
            default_fields["loan_type"]["values"] = await self.fetch_loan_types(token)

        return default_fields

    async def is_valid_funded_date(self, funded_date: str) -> bool:
        # parse date of str YYYY-MM-DDTHH:MM:SSZ
        try:
            date_obj = datetime.strptime(funded_date, "%Y-%m-%dT%H:%M:%SZ")
            # Now check if its 6 months old or not
            six_months_ago = datetime.now() - timedelta(days=180)
            if date_obj < six_months_ago:
                return True

            logger.warning(f"Loan is not 6 months old. Funded date: {funded_date}")
            return False
        except ValueError:
            logger.error(f"Invalid date format: {funded_date}")
            return False

    async def trigger_journey(self, **kwargs):
        """Lists the journeys for a contact"""
        journey_id = kwargs.get("journey_id")
        assistant_id = kwargs.get("assistant_id")
        input_fields = kwargs.get("input_fields", [])
        user = kwargs.get("user")
        if not journey_id:
            # we need to send component with list of journeys
            return ToolResponse(
                message="Please select a journey from the given dropdown!",
                component=Component(
                    component_name=ComponentEnum.TotalExpertJourneys,
                    component_props={"operation": "list"},
                ),
            )
        else:
            journey_details = await total_expert_utils.fetch_journey_details(
                assistant_id=assistant_id, journey_id=journey_id
            )
            if not journey_details:
                logger.error(f"Journey not found for id: {journey_id}")
                return ToolResponse(message="Journey not found!")

            journey_type = journey_details.get("type")
            # now Check if we have loan conditions or not
            if input_fields:
                # maybe let's try to fetch from our existing function first
                from .loan import loan_function

                if journey_type in [
                    TEJourneyTypes.RATE_TERM_REFI,
                    TEJourneyTypes.CASHOUT_REFI,
                ]:
                    # if input_fields doesnot have loan status field, we should add it
                    has_loan_status = False
                    for i_field in input_fields:
                        if i_field.get("field_name") == "loan_status":
                            has_loan_status = True
                            break
                        if i_field.get("field_name") in ["funded_date_start", "funded_date_end"]:
                            if not await self.is_valid_funded_date(i_field.get("value", "")):
                                return ToolResponse(
                                    message=(
                                        "Loan must be atleast 6 months old to trigger rate term refinance or"
                                        " Cashout Refinance journies."
                                    )
                                )
                    if not has_loan_status:
                        input_fields.append(
                            {"field_name": "loan_status", "value": CLOSED_LOAN_STATUS_FIELD, "matchType": "Equals"}
                        )

                    # ALL Rate term refi loans must be FirstLien Loans. So, we also need to compare Lien_position field
                    input_fields.append({"field_name": "lien_position", "value": "FirstLien", "matchType": "Exact"})

                loan_data = await loan_function.fetch_loan_details(
                    assistant_id=assistant_id,
                    input_fields=input_fields,
                    user=user,
                    filter_multiple_loans=True,
                )
                if isinstance(loan_data, ToolResponse):
                    # check component
                    if loan_data.component:
                        # get all loan numbers and generate new question to ask
                        component = loan_data.component
                        component.component_props["journey_name"] = journey_details.get("name")
                        component.component_props["journey_id"] = journey_id
                        component.component_props["journey_type"] = journey_details.get("type", "Standard")
                        component.component_props["is_trigger_journey"] = True
                        return ToolResponse(
                            message=(
                                "Here are the loans that matches your criteria. Select the loans to proceed further"
                                " and trigger the journey. Alternatively, you can also trigger the journey for all"
                                " loans without selecting any."
                            ),
                            component=component,
                        )
                    else:
                        return loan_data
                else:
                    logger.error("Error while fetching loan details.")
                    return ToolResponse(
                        message="Something went wrong while fetching loan details. Please try again!",
                    )
            else:
                # if we have name but no input_fields, we need to send component with input fields
                # lets fetch required fields of that journey
                filterable_fields = journey_details.get("fields", [])
                if not filterable_fields and journey_type == TEJourneyTypes.STANDARD:
                    filterable_fields = list(DEFAULT_JOURNEY_LOAN_FILTER_FIELDS.keys())

                field_mapping = await self._field_mapping(journey_type, assistant_id, user)
                form_fields = {fields: field_mapping.get(fields) for fields in filterable_fields}
                return ToolResponse(
                    message="Please use the below fields to filter the loans to trigger the journey.",
                    component=Component(
                        component_name=ComponentEnum.TotalExpertJourneys,
                        component_props={
                            "operation": "loan_filter_input",
                            "journey_name": journey_details.get("name"),
                            "journey_id": journey_details.get("id"),
                            "fields": form_fields,
                        },
                    ),
                )

    async def _update_loan(self, loan_id, payload, assistant_id, user: User | None = None):
        """Updates the loan with calculated values"""
        token = await total_expert_utils.get_token(assistant_id, user)
        if not token:
            logger.error("Empty auth token")
            raise Exception("Empty auth token")

        # Validate and filter custom fields in the payload
        custom_fields = payload.get("custom", [])

        validated_payload = [
            {
                "field_name": field["field_name"],
                "value": float(re.sub(r"[^\d.]", "", str(field["value"]))),
            }  # remove ,%$
            for field in custom_fields
            if field.get("field_name") and field.get("value")
        ]

        payload = {"custom": validated_payload}

        headers = {
            "Authorization": token,
            "Content-Type": "application/json",
        }

        url = f"{self.base_url}/loans/{loan_id}"
        try:
            # Prepare the payload without modifying the original
            request_payload = payload.copy()

            response = await self.client.patch(url=url, headers=headers, data=json.dumps(request_payload))
            response.raise_for_status()
            return await total_expert_utils._decode_data(response)
        except HTTPStatusError as e:
            logger.error(f"Error while updating loan. Status Code: {e.response.status_code}:: {e.response.text}")
            raise Exception(
                "Some unexpected error occurred while updating the loan values",
            )

    @staticmethod
    def _get_loan_term_mapping():
        # Mapping of loan terms to their respective duration in months
        return {
            "thirty_year_fixed": 30 * 12,
            "twenty_five_year_fixed": 25 * 12,
            "twenty_year_fixed": 20 * 12,
            "fifteen_year_fixed": 15 * 12,
        }

    async def _process_loan(self, user, assistant_id, rates, loan_number, custom_field_mapping, closing_fee: float):
        """
        Processes a single loan by fetching details, performing refinance calculations,
        and updating the loan with calculated values.
        """
        loan_data = {"Loan Number": loan_number}
        loan_term_mapping = self._get_loan_term_mapping()
        custom_field_payload = []

        # Fetch loan details
        loan_details = await te_loan_function._fetch_loan_details_using_loan_number(
            loan_number, assistant_id, user=user
        )
        if not loan_details:
            return None, f"Loan details not found for loan number: {loan_number}"

        contact_id = loan_details.get("borrower", {}).get("id")
        if not contact_id:
            return None, f"Contact not found for loan number: {loan_number}"

        # Fetch contact details
        contact_details = await total_expert_utils.fetch_contact(user, contact_id, assistant_id=assistant_id)
        if not contact_details:
            return None, f"Contact details not found for contact ID: {contact_id}"

        # Filter out zero rates
        filtered_rates = {k: v for k, v in rates.items() if v != 0}

        # Perform refinance calculations
        refinance_results, new_loan_amount = await self._perform_refinance_calculations(
            loan_details, filtered_rates, loan_term_mapping, custom_field_mapping, custom_field_payload, closing_fee
        )

        # Prepare loan data
        loan_data.update(self._prepare_loan_data(loan_details, contact_details, refinance_results, new_loan_amount))

        # Update loan with calculated values
        try:
            await self._update_loan(loan_details.get("id"), {"custom": custom_field_payload}, assistant_id, user=user)
        except Exception as e:
            logger.error(f"Error while updating loan: {e}")
            return (
                None,
                "Could not update Loan in Totalexpert. Do you have correct field mappings? Please check and confirm.",
            )

        return loan_data, None

    async def _calculate_refinance_details(
        self,
        original_loan_amount,
        current_rate,
        current_payment,
        closed_date,
        current_term,
        new_rate,
        new_term,
        closing_fees=5000,
        apr_fees=5000,
        separate_apr=False,
    ):
        # Calculate months left on the current mortgage
        date_formats = [
            "%m/%d/%Y %I:%M:%S %p",  # 02/23/2024 05:00:00 AM
            "%Y-%m-%dT%H:%M:%S.%fZ",  # 2024-09-27T17:05:07.000000Z,
            "%Y/%m/%d",  # 2024/09/27
            "%m/%d/%Y",  # 09/27/2024
            "%Y-%m-%d %H:%M:%S",  # 2024-09-27 17:05:07 FOR TE
        ]
        d1 = None
        for date_format in date_formats:
            try:
                d1 = datetime.strptime(closed_date, date_format)
            except Exception:
                continue
        if not d1:
            logger.warning(f"Could not format date: {closed_date}")
            return {
                "term": "N/A",
                "rate": "N/A",
                "payment": "N/A",
                "total_savings": "N/A",
            }
        d2 = datetime.now()
        delta = relativedelta(d2, d1)
        months_diff = delta.years * 12 + delta.months
        months_left = current_term - months_diff

        new_loan_amount = (
            await encompass_loan_utils.remaining_balance(
                original_loan_amount, current_rate, current_payment, months_diff
            )
            + closing_fees
        )
        new_payment = await encompass_loan_utils.calculate_monthly_payment(new_loan_amount, new_rate, new_term)
        apr = await encompass_loan_utils.calculate_apr_rate(new_rate, apr_fees, new_loan_amount, new_term)

        total_savings = encompass_loan_utils.calculate_total_savings(
            current_payment, months_left, new_payment, new_term
        )
        monthly_savings = current_payment - new_payment

        if client_templates.APPLY_SAVINGS_LOGIC and monthly_savings > 0 and total_savings < 0:
            total_savings = ""
        else:
            total_savings = f"${total_savings:,.2f}"

        if client_templates.ROUNDOFF_UPB_TO_NEARNEST_10:
            new_loan_amount = f"${round(new_loan_amount, -1):,.2f} *"
        else:
            new_loan_amount = f"${new_loan_amount:,.2f}"

        final_data = {
            "term": f"{new_term // 12}-year fixed",
            "new_loan_amount": new_loan_amount,
            "rate": f"{new_rate:,.3f}% ({apr:,.3f}% APR)",
            "payment": f"${new_payment:,.2f}",
            "monthly_savings": f"${monthly_savings:,.2f}",
            "total_savings": total_savings,
        }
        if separate_apr:
            final_data["apr"] = f"{apr:,.3f}%"
            final_data["rate"] = f"{new_rate:,.3f}%"
        return final_data

    async def _perform_refinance_calculations(
        self, loan_details, filtered_rates, loan_term_mapping, custom_field_mapping, custom_field_payload, closing_fee
    ):
        """
        Performs refinance calculations for the given loan details and rates.
        Returns the refinance results and the new loan amount.
        """
        original_loan_amount = float(loan_details.get("loan_amount", 0))
        current_rate = float(loan_details.get("loan_rate", 0))
        current_term = int(loan_details.get("loan_term", 360))
        current_pi = (
            loan_details.get("monthly_pi_payment")
            or await encompass_loan_utils.calculate_monthly_payment(original_loan_amount, current_rate, current_term)
            or 0
        )
        current_pi = float(current_pi)
        current_apr_rate = await encompass_loan_utils.calculate_apr_rate(
            current_rate, closing_fee, original_loan_amount, current_term
        )

        custom_field_payload.append(
            {
                "field_name": custom_field_mapping.get("Current Mortgage APR Rate"),
                "value": f"{current_apr_rate:,.3f}",
            }
        )

        closed_date = loan_details.get("funded_date")

        # Prepare refinance calculation tasks
        tasks = [
            self._calculate_refinance_details(
                original_loan_amount,
                current_rate,
                current_pi,
                closed_date,
                current_term,
                rate,
                loan_term_mapping[loan_term],
                closing_fees=closing_fee,
                apr_fees=closing_fee,
                separate_apr=True,
            )
            for loan_term, rate in filtered_rates.items()
        ]

        # Execute tasks concurrently
        refinance_results = await asyncio.gather(*tasks)
        new_loan_amount = original_loan_amount

        # Process refinance results
        for idx, (loan_term, refi_data) in enumerate(zip(filtered_rates.keys(), refinance_results)):
            term_descriptor = loan_term.replace("_", " ").title()

            # Map fields to custom field payload
            fields_to_map = {
                "Loan Amount": "loan_amount",
                "Rate": "rate",
                "APR Rate": "apr",
                "Principal and Interest": "payment",
                "Monthly Savings": "monthly_savings",
                "Total Savings": "total_savings",
            }
            custom_field_payload.extend(
                {
                    "field_name": custom_field_mapping.get(f"{term_descriptor} {field_display}"),
                    "value": refi_data.get(field_key),
                }
                for field_display, field_key in fields_to_map.items()
                if custom_field_mapping.get(f"{term_descriptor} {field_display}") is not None
            )

            new_loan_amount = refi_data["new_loan_amount"]

        return refinance_results, new_loan_amount

    def _prepare_loan_data(self, loan_details, contact_details, refinance_results, new_loan_amount):
        """Prepares loan data for the response."""
        loan_data = {
            "Borrower First Name": contact_details.get("first_name"),
            "Borrower Last Name": contact_details.get("last_name"),
            "Current Loan Amount": float(loan_details.get("loan_amount", 0)),
            "New Loan Amount": new_loan_amount,
            "borrower_contact_id": contact_details.get("id"),
        }

        for idx, refi_data in enumerate(refinance_results):
            term = refi_data.get("term")
            loan_data[f"Option {string.ascii_uppercase[idx]} ({term})"] = {
                field.replace("_", " ").capitalize(): value for field, value in refi_data.items()
            }

        return loan_data

    async def calculate_rate_term_refi(self, **kwargs):
        """Calculates the rate term refinance options"""
        loan_numbers = kwargs.get("loan_numbers", [])
        rates = kwargs.get("rates", {})
        journey_id = kwargs.get("journey_id")
        user = kwargs.get("user")
        assistant_id = kwargs.get("assistant_id")
        closing_fee = kwargs.get("closing_fees", 5000)

        # Validate inputs
        journey_details = await total_expert_utils.fetch_journey_details(
            assistant_id=assistant_id, journey_id=journey_id
        )
        if not journey_details:
            return ToolResponse(message="Journey not found!")
        if journey_details["type"] != TEJourneyTypes.RATE_TERM_REFI:
            return ToolResponse(message="Journey type is not RATE_TERM_REFI!")
        if not loan_numbers:
            return ToolResponse(message="Loan numbers not provided!")

        custom_field_mapping = journey_details.get("custom_field_mapping") or {}

        # Run all loan processing tasks concurrently
        tasks = [
            self._process_loan(user, assistant_id, rates, loan_number, custom_field_mapping, closing_fee)
            for loan_number in loan_numbers
        ]
        results = await asyncio.gather(*tasks)
        # Collect data and messages
        data = []
        message = ""
        for loan_data, error in results:
            if loan_data:
                data.append(loan_data)
            if error:
                logger.error(error)
                message += f"\n{error}"

        if not data:
            return ToolResponse(message="No valid loans processed!")

        # Extract headers for the response
        headers = {key: list(value.keys()) if isinstance(value, dict) else [] for key, value in data[0].items()}

        # Return response
        return ToolResponse(
            message=(
                "Refinance calculations completed successfully! Please review the values and select the ones you want"
                f" to trigger the journey. You can also trigger journey for all loans without selecting any. {message}"
            ),
            component=Component(
                component_name=ComponentEnum.TotalExpertJourneys,
                component_props={
                    "data": data,
                    "headers": headers,
                    "journey": {"id": journey_details.get("id"), "name": journey_details.get("name")},
                },
            ),
        )

    def extract_rate_apr(self, rate: str, apr: bool = False) -> tuple:
        # fmt = "8.750% (8.750% APR)"
        import re

        # extract both rate and APR from string
        rate = re.findall(r"\d+\.\d+", rate)
        if apr:
            return float(rate[1])
        return float(rate[0])

    @staticmethod
    def validate_amount(amount, max_allowed, label):
        if amount > max_allowed:
            return (
                None,
                f"{label} is greater than the maximum cashout amount. Maximum cashout amount is {max_allowed}",
            )
        return True, None

    async def _map_cashout_json(self, cashout_json, custom_field_payload, custom_field_mapping, loan_data):
        CASHOUT_FIELDS = client_templates.CASHOUT_DESIRED_DETAILS
        CASHOUT_FIELD_MAPPING = client_templates.CASHOUT_DESIRED_MAPPING

        term_value_mapping = {
            "your current mortgage": "Current Mortgage",
            "30-year fixed": "Thirty Year Fixed",
            "25-year fixed": "Twenty Five Year Fixed",
            "20-year fixed": "Twenty Year Fixed",
            "15-year fixed": "Fifteen Year Fixed",
        }

        field_mapping_sales_to_TE = {
            "rates": "Rate",
            "apr_rates": "APR Rate",
            "payments": "Principal and Interest",
            "total_loan_amounts": "Total Loan Amount",
            "desired_cashout_amount": "Desired Cashout Amount",
        }

        for option_data in cashout_json["options"]:
            # Map cashout_json to custom field payload
            if option_data["option_name"].lower() == "your current mortgage":
                term_descriptor = term_value_mapping["your current mortgage"]
            else:
                term_descriptor = term_value_mapping.get(option_data["terms"].lower())

            for option_key, field_display in field_mapping_sales_to_TE.items():
                if option_key in option_data:
                    value = option_data.get(option_key)
                    if value is not None and value != "--":
                        field_name = custom_field_mapping.get(f"{term_descriptor} {field_display}")
                        if field_name is not None:
                            custom_field_payload.append({"field_name": field_name, "value": value})

            # Map cashout_json to loan_data
            option_name = option_data["option_name"]
            term = option_data["terms"]
            option_details = {}
            for field_key in CASHOUT_FIELDS:
                if field_key == "options":
                    continue
                loan_data_key = CASHOUT_FIELD_MAPPING.get(field_key)
                if field_key == "rates":
                    rate = option_data.get("rates")
                    apr_rate = option_data.get("apr_rates")
                    if rate is not None and rate != "--" and apr_rate is not None and apr_rate != "--":
                        option_details[loan_data_key] = f"{rate} ({apr_rate})"
                else:
                    loan_data_value = option_data.get(field_key)
                    if loan_data_value is not None and loan_data_value != "--":
                        option_details[loan_data_key] = loan_data_value
            loan_data[f"{option_name} ({term})"] = option_details

        return loan_data, custom_field_payload

    async def _generate_cashout_json(
        self,
        loan_details,
        new_loan_amount,
        estimated_payoff,
        interest_rates,
        apr_fees=5000,
        desired_cashout=0,
    ):
        original_loan_amount = float(loan_details.get("Loan Amount"))
        current_rate = float(loan_details.get("Interest Rate"))
        current_payment = float(loan_details.get("MoPymtPI"))
        current_term = int(loan_details.get("Loan Term"))

        # Remove options with value 0
        filtered_rates = {k: v for k, v in interest_rates.items() if v != 0}

        loan_term_mapping = {
            "thirty_year_fixed": 30 * 12,
            "twenty_five_year_fixed": 25 * 12,
            "twenty_year_fixed": 20 * 12,
            "fifteen_year_fixed": 15 * 12,
        }
        current_apr = await encompass_loan_utils.calculate_apr_rate(
            current_rate, apr_fees, original_loan_amount, current_term
        )

        # Initialize JSON structure
        cashout_json = {
            "options": [
                {
                    "option_name": "Your Current Mortgage",
                    "terms": f"{current_term // 12}-year fixed",
                    "loan_amounts": f"${original_loan_amount:,.2f}",
                    "est_current_loan_amounts": f"${estimated_payoff:,.2f}",
                    "rates": f"{current_rate:,.3f}%",
                    "apr_rates": f"{current_apr:,.3f}% APR",
                    "payments": f"${current_payment:,.2f}",
                    "total_loan_amounts": f"${original_loan_amount:,.2f}",
                    "desired_cashout_amount": "--",
                }
            ]
        }

        # Calculate refinance details for each option
        for idx, (loan_term, rate) in enumerate(filtered_rates.items()):
            loan_term_months = loan_term_mapping[loan_term]
            new_payment = await encompass_loan_utils.calculate_monthly_payment(new_loan_amount, rate, loan_term_months)
            apr = await encompass_loan_utils.calculate_apr_rate(rate, apr_fees, new_loan_amount, loan_term_months)

            amount = estimated_payoff if client_templates.DISPLAY_ESTIMATED_PAYOFF else original_loan_amount
            if client_templates.ROUNDOFF_UPB_TO_NEARNEST_10:
                loan_amount = f"${round(amount, -1):,.2f} *"
            else:
                loan_amount = f"${amount:,.2f}"

            option_data = {
                "option_name": f"Option {string.ascii_uppercase[idx]}",
                "terms": f"{loan_term_months // 12}-year fixed",
                "loan_amounts": loan_amount,
                "est_current_loan_amounts": f"${estimated_payoff:,.2f}",
                "rates": f"{rate:,.3f}%",
                "apr_rates": f"{apr:,.3f}% APR",
                "payments": f"${new_payment:,.2f}",
                "total_loan_amounts": f"${new_loan_amount:,.2f}",
                "desired_cashout_amount": f"${desired_cashout:,.2f}",
            }
            cashout_json["options"].append(option_data)

        return cashout_json

    async def _process_loan_for_cashout_refi(
        self,
        user,
        assistant_id: str,
        loan_number: str,
        rates: dict,
        custom_field_mapping: dict,
        max_cashout: float | None = None,
        closing_fees: float = 5000,
    ):
        loan_details = await te_loan_function._fetch_loan_details_using_loan_number(
            loan_number, assistant_id, user=user
        )
        if not loan_details:
            logger.error(f"Loan details not found: {loan_number}")
            return None, f"Loan details not found for loan number: {loan_number}"

        contact_id = loan_details.get("borrower", {}).get("id")
        if not contact_id:
            logger.error(f"contact not found for Loan number: {loan_number}")
            return None, f"Contact not found for loan number: {loan_number}"

        contact_details = await total_expert_utils.fetch_contact(user, contact_id, assistant_id=assistant_id)
        if not contact_details:
            return None, f"Contact details not found for contact ID: {contact_id}"

        loan_number = loan_details.get("loan_number")
        address = contact_details.get("address") or loan_details.get("address")
        city = contact_details.get("city", "") or loan_details.get("city", "")
        state = contact_details.get("state", "") or loan_details.get("state", "")
        zip_code = contact_details.get("zip_code", "") or loan_details.get("zip_code", "")

        loan_data = {"Loan Number": loan_number}
        original_loan_amount = float(loan_details.get("loan_amount", 0))
        current_rate = float(loan_details.get("loan_rate", 0))
        current_term = int(loan_details.get("loan_term", 360))
        current_pi = loan_details.get("monthly_pi_payment") or await encompass_loan_utils.calculate_monthly_payment(
            original_loan_amount, current_rate, current_term
        )
        current_pi = float(current_pi)
        current_apr_rate = await encompass_loan_utils.calculate_apr_rate(
            current_rate, closing_fees, original_loan_amount, current_term
        )
        custom_field_payload = [
            {
                "field_name": custom_field_mapping.get("Current Mortgage APR Rate"),
                "value": f"{current_apr_rate:,.3f}",
            }
        ]

        # Fetch estimated property value
        appraised_value = await self._get_appraised_value(address, city, state, zip_code)
        if not appraised_value:
            err_msg = (
                f"Sorry, for address {address}, {city}, {state}, {zip_code}, we could not calculate the appraised"
                " value."
            )
            logger.error(err_msg)
            return None, err_msg

        estimated_payoff = await self._get_estimated_payoff(
            original_loan_amount, current_rate, current_pi, loan_details.get("funded_date")
        )
        max_cashout = max_cashout or ((appraised_value * 0.8) - estimated_payoff)
        desired_cashout = max_cashout
        if max_cashout < 0:
            err_message = (
                "You are not eligible for a cash-out refinance due to the negative maximum cashout amount of"
                f" {max_cashout}. This situation typically indicates that the current loan balance exceeds the"
                " appraised value of the property, making it ineligible for cash-out refinancing."
            )
            logger.warning(err_message)
            return None, err_message

        loan_data.update(
            {
                "Borrower First Name": contact_details.get("first_name"),
                "Borrower Last Name": contact_details.get("last_name"),
                "Current Loan Amount": float(loan_details.get("loan_amount", 0)),
                "borrower_contact_id": contact_details.get("id"),
            }
        )

        current_loan_details = {
            "Loan Amount": original_loan_amount,
            "Interest Rate": current_rate,
            "MoPymtPI": current_pi,
            "Loan Term": current_term,
        }

        if desired_cashout > 0:
            valid, err = self.validate_amount(desired_cashout, max_cashout, "Desired cashout amount")
            if not valid:
                return None, err
            new_loan_amount = estimated_payoff + desired_cashout + closing_fees
            cashout_json = await self._generate_cashout_json(
                current_loan_details,
                new_loan_amount,
                estimated_payoff,
                rates,
                apr_fees=closing_fees,
                desired_cashout=desired_cashout,
            )
            loan_data, custom_field_payload = await self._map_cashout_json(
                cashout_json, custom_field_payload, custom_field_mapping, loan_data
            )
            await self._update_loan(loan_details.get("id"), {"custom": custom_field_payload}, assistant_id, user=user)
            return loan_data, None
        else:
            return (
                None,
                (
                    "You can only generate Cashout Refinance offer for Desired Cashout with Desired Cashout amount"
                    " equals to Max Cashout."
                ),
            )

    async def _get_appraised_value(self, address, city, state, zip_code):
        params = {
            "Address": address,
            "City": city,
            "State": state,
            "Zip Code": zip_code,
        }
        return await encompass_sales_utils.get_property_estimated_value(params)

    async def _get_estimated_payoff(self, loan_amount, interest_rate, monthly_pi, funded_date):
        params = {
            "Loan Amount": loan_amount,
            "Interest Rate": interest_rate,
            "MoPymtPI": monthly_pi,
            "Fund Released Date": funded_date,
        }
        return await encompass_sales_utils.get_estimated_payoff(params)

    async def calculate_cashout_refi(self, **kwargs):
        """Calculates the cashout refinance options"""
        loan_numbers = kwargs.get("loan_numbers", [])
        journey_id = kwargs.get("journey_id")
        user = kwargs.get("user")
        assistant_id = kwargs.get("assistant_id")
        max_cashout = kwargs.get("max_cashout")
        rates = kwargs.get(
            "rates",
            {"thirty_year_fixed": 0, "twenty_five_year_fixed": 0, "twenty_year_fixed": 0, "fifteen_year_fixed": 0},
        )
        closing_fees = kwargs.get("closing_fees", 5000)

        # Validate inputs
        journey_details = await total_expert_utils.fetch_journey_details(
            assistant_id=assistant_id, journey_id=journey_id
        )
        if not journey_details:
            return ToolResponse(message="Journey not found!")
        if journey_details["type"] not in [
            TEJourneyTypes.CASHOUT_REFI,
        ]:
            return ToolResponse(message="Journey type is not Cashout Refinance!")
        if not loan_numbers:
            return ToolResponse(message="Loan numbers not provided!")

        custom_field_mapping = journey_details.get("custom_field_mapping") or {}

        # Run all loan processing tasks concurrently
        tasks = [
            self._process_loan_for_cashout_refi(
                user, assistant_id, loan_number, rates, custom_field_mapping, max_cashout, closing_fees=closing_fees
            )
            for loan_number in loan_numbers
        ]
        results = await asyncio.gather(*tasks)
        # Collect data and messages
        data = []
        message = ""
        for loan_data, error in results:
            if loan_data:
                data.append(loan_data)
            if error:
                logger.error(error)
                message += f"\n{error}"

        if not data:
            return ToolResponse(message=message)

        # Extract headers for the response
        headers = {key: list(value.keys()) if isinstance(value, dict) else [] for key, value in data[0].items()}

        # Return response
        return ToolResponse(
            message=(
                "Cashout Refinance calculations completed successfully! Please review the values and select the ones"
                " you want to trigger the journey. You can also trigger journey for all loans without selecting any."
                f" {message}"
            ),
            component=Component(
                component_name=ComponentEnum.TotalExpertJourneys,
                component_props={
                    "data": data,
                    "headers": headers,
                    "journey": {"id": journey_details.get("id"), "name": journey_details.get("name")},
                },
            ),
        )


journey_function = JourneyFunction()
