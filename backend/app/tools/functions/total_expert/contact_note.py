from textwrap import dedent

from httpx import AsyncClient
from loguru import logger
from schema.enums import DataFieldFolder
from tools.fields.totalexpert import fetch_all_fields
from tools.fields.totalexpert._default import NOTE_TYPE_IDS_MAPPING
from tools.schema import Component, ComponentEnum, ToolResponse
from tools.utils.total_expert import total_expert_utils


class ContactNoteFunction:
    def __init__(self):
        self.client = AsyncClient(timeout=None)

    def list_functions(self) -> dict:
        return {
            "add_contact_note": self.add_contact_note,
            "fetch_contact_notes": self.fetch_contact_notes,
        }

    async def add_contact_note(self, **kwargs) -> ToolResponse:
        contact_information = kwargs.get("contact_information", {})
        if not contact_information:
            logger.warning("Contact information is not provided!")
            return ToolResponse(
                message=(
                    "Could you tell me on which contact would you like to add this note to. Mention one of email, or"
                    " phone number or contact id"
                ),
                component=Component(
                    component_name=ComponentEnum.TotalExpert,
                    component_props={
                        "operation": "add_note",
                        "fields": {key: value for key, value in kwargs.items() if key != "user"},
                    },
                ),
            )
        contact_id = contact_information.get("contact_id")
        email = contact_information.get("email")
        first_name = contact_information.get("first_name")
        last_name = contact_information.get("last_name")
        phone_number = contact_information.get("phone_number")
        user = kwargs.get("user")

        if not any([contact_id, email, phone_number, first_name, last_name]):
            # if not first_name or not last_name:
            logger.info("No Contact information is provided")
            return ToolResponse(
                message=(
                    "Could you tell me on which contact would you like to add this note to. Mention one of email,"
                    " phone number or contact id, first name or last name"
                ),
                component=Component(
                    component_name=ComponentEnum.TotalExpert,
                    component_props={
                        "operation": "add_note",
                        "fields": {
                            key: value for key, value in {**kwargs, "contact_id": contact_id}.items() if key != "user"
                        },
                    },
                ),
            )
        else:
            if not contact_id:
                try:
                    contact_id = await total_expert_utils.fetch_contact_id(
                        user,
                        first_name=first_name,
                        last_name=last_name,
                        email=email,
                        phone_cell=phone_number,
                        assistant_id=kwargs.get("assistant_id"),
                    )
                except Exception as e:
                    logger.warning(f"Error while fetching contact id: {e}")
                    return ToolResponse(message=str(e))

        note_title = kwargs.get("note_title")
        note = kwargs.get("note")
        if not all([note_title, note]):
            logger.warning("Note title, note or note type is not provided")
            return ToolResponse(
                message=(
                    "Note title, note and note type are required! Please provide a title, note and note type for the"
                    " note."
                ),
                component=Component(
                    component_name=ComponentEnum.TotalExpert,
                    component_props={
                        "operation": "add_note",
                        "fields": {
                            key: value for key, value in {**kwargs, "contact_id": contact_id}.items() if key != "user"
                        },
                    },
                ),
            )
        # type_id = NOTE_TYPE_IDS_MAPPING.get(note_type, 1)
        type_id = 1  # Support Only General Notes

        try:
            response = await total_expert_utils.add_note(
                user, note_title, note, type_id, contact_id, assistant_id=kwargs.get("assistant_id")
            )
            if response:
                return ToolResponse(
                    message=dedent(
                        f"""
                        Note added successfully with the following details:
                        **Note Title**: {note_title}
                        **Note**: {note}
                        """
                    ).strip()
                )
            else:
                return ToolResponse(
                    message=dedent(
                        f"""
                        Could not add note to contact! Please check all the values and try again!
                        **Note Title**: {note_title}
                        **Note**: {note}
                        **Contact ID**: {contact_id}
                        **Email**: {email}
                        **Phone Number**: {phone_number}
                        """
                    ).strip(),
                    component=Component(
                        component_name=ComponentEnum.TotalExpert,
                        component_props={
                            "operation": "add_note",
                            "fields": {
                                key: value
                                for key, value in {**kwargs, "contact_id": contact_id}.items()
                                if key != "user"
                            },
                        },
                    ),
                )
        except Exception as e:
            logger.error(f"Error while adding note:: {e}")
            return ToolResponse(
                message=dedent(
                    f"""
                        Could not add note to contact! Please check all the values and try again!
                        **Note Title**: {note_title}
                        **Note**: {note}
                        **Contact ID**: {contact_id}
                        **Email**: {email}
                        **Phone Number**: {phone_number}
                        """
                ).strip(),
                component=Component(
                    component_name=ComponentEnum.TotalExpert,
                    component_props={
                        "operation": "add_note",
                        "fields": {
                            key: value for key, value in {**kwargs, "contact_id": contact_id}.items() if key != "user"
                        },
                    },
                ),
            )

    async def fetch_contact_notes(self, **kwargs):
        contact_id = kwargs.get("contact_id")
        email = kwargs.get("email")
        phone_number = kwargs.get("phone_number")
        first_name = kwargs.get("first_name")
        last_name = kwargs.get("last_name")
        user = kwargs.get("user")
        note_type = kwargs.get("note_type")
        if not any([contact_id, email, phone_number, first_name, last_name]):
            logger.info("No Contact information is provided")
            return ToolResponse(
                message=(
                    "Could you tell me on which contact would you like to fetch notes to. Mention one of email, or"
                    " phone number or contact id or first name or last name"
                )
            )
        else:
            if not contact_id:
                try:
                    contact_id = await total_expert_utils.fetch_contact_id(
                        user,
                        email=email,
                        phone_cell=phone_number,
                        first_name=first_name,
                        last_name=last_name,
                        assistant_id=kwargs.get("assistant_id"),
                    )
                except Exception as e:
                    logger.warning(f"Error while fetching contact id: {e}")
                    return ToolResponse(message=(str(e)))

        try:
            type_id = NOTE_TYPE_IDS_MAPPING.get(note_type, None)
            notes = await total_expert_utils.fetch_contact_notes(
                user=user, contact_id=contact_id, type_id=type_id, assistant_id=kwargs.get("assistant_id")
            )
            FIELD_MAPPING, FIELD_TYPE_MAPPING, DISPLAY_FIELDS, SEARCH_FIELDS = await fetch_all_fields(
                kwargs.get("assistant_id"), folder=DataFieldFolder.CONTACT_NOTES
            )
            if notes:
                return ToolResponse(
                    message="Notes fetched successfully!",
                    component=Component(
                        component_name=ComponentEnum.TotalExpertNotes,
                        component_props={"data": notes, "headers": DISPLAY_FIELDS, "search_fields": SEARCH_FIELDS},
                    ),
                )
            else:
                return ToolResponse(
                    message=dedent(
                        f"""
                    No notes found for contact {contact_id}!
                    **Contact ID**: {contact_id}
                    **Email**: {email}
                    **Phone Number**: {phone_number}
                        """
                    ).strip()
                )
        except Exception as e:
            logger.error(f"Error while fetching notes:: {e}")
            return ToolResponse(
                message=dedent(
                    f"""
                    Could not fetch notes for contact {contact_id}! Please check all the values and try again!
                    **Contact ID**: {contact_id}
                    **Email**: {email}
                    **Phone Number**: {phone_number}
                    """
                ).strip()
            )


contact_note_function = ContactNoteFunction()
