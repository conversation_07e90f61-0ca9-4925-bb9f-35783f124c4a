from textwrap import dedent

from httpx import AsyncClient
from loguru import logger
from schema.enums import DataFieldFolder
from tools.fields.totalexpert import fetch_all_fields
from tools.schema import Component, ComponentEnum, ToolResponse
from tools.utils.total_expert import total_expert_utils


class ActivityFunction:
    def __init__(self):
        self.client = AsyncClient(timeout=None)

    def list_functions(self) -> dict:
        return {
            "fetch_activities": self.fetch_activities,
        }

    async def fetch_activities(self, **kwargs):
        contact_id = kwargs.get("contact_id")
        email = kwargs.get("email")
        phone_number = kwargs.get("phone_number")
        first_name = kwargs.get("first_name")
        last_name = kwargs.get("last_name")
        start_date = kwargs.get("start_date")
        end_date = kwargs.get("end_date")
        activity_type = kwargs.get("activity_type")
        user = kwargs.get("user")
        if any([email, phone_number, first_name, last_name]) and not contact_id:
            try:
                contact_id = await total_expert_utils.fetch_contact_id(
                    user,
                    email=email,
                    phone_cell=phone_number,
                    first_name=first_name,
                    last_name=last_name,
                    assistant_id=kwargs.get("assistant_id"),
                )
            except Exception as e:
                logger.warning(f"Error while fetching contact id: {e}")
                return ToolResponse(message=(str(e)))
        filters = {
            "internal_created_at_start": start_date,
            "internal_created_at_end": end_date,
            "type": activity_type,
            "contact_id": contact_id,
        }

        filter = ",".join(f"{key}={value}" for key, value in filters.items() if value)
        try:
            activities = await total_expert_utils.fetch_activities(
                user, filter, assistant_id=kwargs.get("assistant_id")
            )
            if activities:
                FIELD_MAPPING, FIELD_TYPE_MAPPING, DISPLAY_FIELDS, SEARCH_FIELDS = await fetch_all_fields(
                    kwargs.get("assistant_id"), folder=DataFieldFolder.ACTIVITY
                )
                return ToolResponse(
                    message="Activities fetched successfully!",
                    component=Component(
                        component_name=ComponentEnum.TotalExpertActivities,
                        component_props={
                            "data": activities,
                            "headers": DISPLAY_FIELDS,
                            "search_fields": SEARCH_FIELDS,
                        },
                    ),
                )
            else:
                return ToolResponse(
                    message=dedent(
                        f"""
                    Activities not found for contact with id: {contact_id}!
                    **Contact ID**: {contact_id}
                    **First Name**: {first_name}
                    **Last Name**: {last_name}
                    **Email**: {email}
                    **Phone Number**: {phone_number}
                        """
                    ).strip()
                )
        except Exception as e:
            logger.error(f"Error while fetching activities:: {e}")
            return ToolResponse(
                message=dedent(
                    f"""
                    Activities not found for contact with id: {contact_id}!
                    **Contact ID**: {contact_id}
                    **First Name**: {first_name}
                    **Last Name**: {last_name}
                    **Email**: {email}
                    **Phone Number**: {phone_number}
                        """
                ).strip()
            )


activity_function = ActivityFunction()
