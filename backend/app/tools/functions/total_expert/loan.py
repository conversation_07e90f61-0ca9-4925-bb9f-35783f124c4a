import asyncio

from config import settings
from db.models import User
from httpx import AsyncClient, HTTPStatusError
from loguru import logger
from schema.enums import DataFieldFolder
from tools.fields.totalexpert import ACTIVE_LOAN_STATUS_FIELD, CLOSED_LOAN_STATUS_FIELD, fetch_all_fields
from tools.fields.totalexpert._default import LOAN_CUTOFF_FIELDS, LOAN_SEARCH_CUSTOM_FIELDS, LOAN_SEARCH_FIELDS
from tools.schema import Component, ComponentEnum, ToolResponse
from tools.utils.total_expert import total_expert_utils
from utils.type_conversion import is_valid_iso8601, type_converter

from .constants import BASE_URL, DATA_LIMIT, PAGE_SIZE


class LoanFunction:
    def __init__(self):
        self.client = AsyncClient(timeout=None)

    def list_functions(self) -> dict:
        return {
            "fetch_loan_details": self.fetch_loan_details,
        }

    async def fetch_loan_details(self, **kwargs) -> ToolResponse:
        user = kwargs.get("user")
        assistant_id = kwargs.get("assistant_id")
        loan_type = kwargs.get("loan_type", "other")
        input_fields = kwargs.get("input_fields", [])
        output_fields = kwargs.get("output_fields", [])
        # this will remove loans which are not eligible for refinance offer
        # if the contact has another active loan, we will not show the refinance offer for that loan.
        filter_multiple_loans = kwargs.get("filter_multiple_loans", False)
        url = f"{BASE_URL}/loans"

        # fetch token
        token = await total_expert_utils.get_token(assistant_id, user=user)
        if not token:
            logger.error("Empty auth token")
            return ToolResponse(message="Could not authenticate! Please check your email and try again!")

        headers = {
            "Authorization": token,
            "Content-Type": "application/json",
        }

        # verify input fields
        for i_field in input_fields:
            field_name = i_field.get("field_name", "")
            if field_name not in LOAN_SEARCH_FIELDS:
                logger.error("LLM Couldn't extract input fields.")
                return ToolResponse(message=f"Field name {i_field} not valid")

        # prepare api_query
        # if fields can be directly used, then we find those fields and then call api
        # if not then filter after receiving query
        # filter fields
        filter_criteria = []
        contact_filters = {}
        custom_filters = []

        stopping_condition_added = False
        stopping_condition = {}

        owner_filter = None

        for loan_field in input_fields:
            field_name = loan_field.get("field_name", "")
            value = loan_field.get("value", "")
            if field_name.lower() == "owner_email":
                if settings.ENABLE_TOTAL_EXPERT_OAUTH:
                    return ToolResponse("Owner email is not supported in OAuth mode")
                owner_filter = value

            if field_name not in LOAN_SEARCH_FIELDS:
                return ToolResponse(
                    message=(
                        f"Field name {field_name} is not available for search. Did you mean any of these? Check values"
                        f" and confirm with user if needed. {LOAN_SEARCH_FIELDS}"
                    )
                )
            if field_name in ["first_name", "last_name", "email", "phone"]:
                contact_filters[field_name] = value
            elif field_name in LOAN_SEARCH_CUSTOM_FIELDS:
                custom_filters.append({"field": field_name, "operator": loan_field.get("matchType"), "value": value})
                if (
                    not stopping_condition_added
                    and field_name in LOAN_CUTOFF_FIELDS
                    and (
                        loan_field.get("matchType") == "GreaterThan"
                        or loan_field.get("matchType") == "GreaterThanEquals"
                    )
                ):
                    field_type = await total_expert_utils.get_data_type(
                        assistant_id=assistant_id, field_name=field_name, folder=DataFieldFolder.CONTACT
                    )
                    stopping_condition = {
                        "field_name": field_name,
                        "field_value": value,
                        "field_type": field_type,
                    }
                    stopping_condition_added = True

            elif field_name in [
                "internal_updated_at_start",
                "internal_updated_at_end",
                "internal_created_at_start",
                "internal_created_at_end",
                "funded_date_start",
                "funded_date_end",
            ]:
                # convert to iso format
                if not is_valid_iso8601(value):
                    logger.warning(f"Invalid date format: {field_name}={value}")
                    return ToolResponse(
                        message=f"Invalid date format for field {field_name}. Please use YYYY-MM-DDTHH:MM:SSZ format."
                    )
                filter_criteria.append(f"{field_name}={value}")
            else:
                filter_criteria.append(f"{field_name}={value}")

        # for active loans
        if loan_type == "active":
            active_loan_filter = f"loan_status={ACTIVE_LOAN_STATUS_FIELD}"
            filter_criteria.append(active_loan_filter)
        elif loan_type == "closed":
            closed_loan_filter = f"loan_status={CLOSED_LOAN_STATUS_FIELD}"
            filter_criteria.append(closed_loan_filter)

        if contact_filters:
            contact_filters["phone_cell"] = contact_filters.pop("phone", None)
            try:
                contact_id = await total_expert_utils.fetch_contact_id(
                    user, **contact_filters, assistant_id=kwargs.get("assistant_id")
                )
            except Exception as e:
                logger.warning("Some error occured while fetching contact details!")
                return ToolResponse(message=str(e))

            filter_criteria.append(f"borrower_id={contact_id}")

        team_members = [None]
        if not settings.ENABLE_TOTAL_EXPERT_OAUTH and not owner_filter:
            team_members = await total_expert_utils._get_team_member_emails(
                user, token, assistant_id=kwargs.get("assistant_id")
            )

        if owner_filter:
            team_members = [owner_filter]

        # Now fetch data for each team member:
        # lets do it in parallel using asyncio
        # for each team member, fetch loans and append to loan_data
        filter_criteria = ",".join(filter_criteria).strip(",")
        tasks = []
        for team_member in team_members:
            # For USER LEVEL AUTH this loop runs one time for which team_members=None
            # --> filter_string = filter_criteria
            filter_string = filter_criteria + f",owner_email={team_member}" if team_member else filter_criteria
            params = {
                "page[number]": 1,
                "page[size]": PAGE_SIZE,
                "filter": filter_string,
                "sort": "-internal_updated_at",
            }
            tasks.append(
                total_expert_utils.fetch_data_from_api(
                    url=url,
                    method="GET",
                    headers=headers,
                    params=params,
                    limit=DATA_LIMIT // len(team_members) or 1,
                    stopping_condition=stopping_condition,
                )
            )

        loan_data = await asyncio.gather(*tasks, return_exceptions=True)
        loan_data = [item for sublist in loan_data for item in sublist if not isinstance(item, Exception)]
        # sort loan_data by created_at in descending order
        sorted_key = "funded_date" if loan_type == "closed" else "created_date"
        loan_data = sorted(loan_data, key=lambda k: k[sorted_key], reverse=True)
        # if loan_data is empty, return empty message
        if not loan_data:
            logger.warning("Empty loan details!")
            return ToolResponse(
                message=(
                    "No loans found for the given criteria. Please try again with different criteria or contact admin!"
                )
            )
        # TODO: will define certain fields and only return them after testing
        # TODO For Division As No Admin Access
        # if settings.ENABLE_TOTAL_EXPERT_OAUTH:
        #     token = await total_expert_utils.get_token(assistant_id=assistant_id)
        #     if not token:
        #         logger.error("Empty auth token")
        #         return ToolResponse(message="Could not authenticate! Please check your email and try again!")
        #     owner_email = await total_expert_utils.map_user(user=user, assistant_id=assistant_id)
        #     user_details = await total_expert_utils.get_user_details(token=token, email=owner_email)
        filtered_loan_data = []
        for loan in loan_data:
            loan["account_class_name"] = loan.get("account_class", {}).get("class_name")
            loan["account_class_type"] = loan.get("account_class", {}).get("class_type")
            loan["borrower_details"] = loan.get("borrower", {}).get("id", "")
            loan["borrower_contact_id"] = loan["borrower_details"]
            loan["loan_program"] = loan.get("loan_program", {}).get("loan_program", "")
            loan["loan_purpose"] = loan.get("loan_purpose", {}).get("loan_purpose", "")
            loan["loan_status"] = loan.get("loan_status", {}).get("loan_status", "")
            loan["loan_type"] = loan.get("loan_type", {}).get("loan_type", "")
            loan["owner_email"] = loan.get("owner", {}).get("email", "")
            loan["owner_id"] = loan.get("owner", {}).get("id", "")

            # filter here
            if custom_filters:
                if filtered_data := await total_expert_utils.filter_data(
                    data=loan, filter=custom_filters, assistant_id=assistant_id, folder=DataFieldFolder.LOAN
                ):
                    filtered_loan_data.append(filtered_data)
                else:
                    continue
            else:
                filtered_loan_data.append(loan)

        if filter_multiple_loans:
            # filter out ineligible loans for Refinance Offer. If the contact has another active loan, we will not show
            # the refinance offer for that loan.
            inelligible_loans = await self.fetch_inelligible_loans(filtered_loan_data, assistant_id, user=user)
            filtered_loan_data = [
                loan for loan in filtered_loan_data if loan.get("loan_number") not in inelligible_loans
            ]
        FIELD_MAPPING, FIELD_TYPE_MAPPING, DISPLAY_FIELDS, SEARCH_FIELDS = await fetch_all_fields(
            kwargs.get("assistant_id"), folder=DataFieldFolder.LOAN
        )

        # add borrower contact_id to mapping
        FIELD_MAPPING["borrower_contact_id"] = "borrower_contact_id"

        loan_details = [
            {
                field_name: type_converter.convert_string_data(FIELD_TYPE_MAPPING, field_key, loan.get(field_key, ""))
                for field_key, field_name in FIELD_MAPPING.items()
            }
            for loan in filtered_loan_data
        ]
        if len(loan_details) == 0:
            logger.warning("Empty Loans!")
            return ToolResponse(
                message=(
                    "No associated loans found. Please check your contact list."
                    " If you believe this is an error, contact the administrator."
                )
            )
        elif len(loan_details) == 1:
            message = f"Loan details fetched: {loan_details[0]} You can add these loans to your journey."
        else:
            message = "Loan details fetched successfully! You can add these loans to your journey."

        if output_fields:
            mapped_output_fields = [
                FIELD_MAPPING.get(field_name) for field_name in output_fields if field_name in FIELD_MAPPING
            ]

            output_fields_set = set(mapped_output_fields)
            display_fields_set = set(DISPLAY_FIELDS)
            search_fields_set = set(SEARCH_FIELDS)

            DISPLAY_FIELDS = list(display_fields_set & output_fields_set)
            SEARCH_FIELDS = list(search_fields_set & output_fields_set)

            missing_from_display = output_fields_set - display_fields_set

            if missing_from_display:
                message += (
                    f" The following fields are not available for display: {', '.join(missing_from_display)}"
                    "Please add it to the system and set it to be displayed"
                )

        return ToolResponse(
            message=message,
            component=Component(
                component_name=ComponentEnum.TotalExpert,
                component_props={
                    "data": loan_details,
                    "headers": DISPLAY_FIELDS,
                    "search_fields": SEARCH_FIELDS,
                    "is_trigger_journey": False,
                },
            ),
            payload=params,
        )

    async def _fetch_loan_details_using_loan_number(
        self, loan_number: str, assistant_id: str, user: User | None = None
    ):
        url = f"{BASE_URL}/loans"

        # fetch token
        token = await total_expert_utils.get_token(assistant_id, user=user)
        if not token:
            logger.error("Empty auth token")
            return ToolResponse(message="Could not authenticate! Please check your email and try again!")

        headers = {
            "Authorization": token,
            "Content-Type": "application/json",
        }

        filter = f"loan_number={loan_number}"

        try:
            response = await self.client.get(url, headers=headers, params={"filter": filter})
            response.raise_for_status()
            return response.json().get("items")[0] or ToolResponse(message="Loan not found!")
        except HTTPStatusError as e:
            logger.error(f"HTTP error occurred: {e.response.status_code} - {e.response.text}")
            return ToolResponse(message="Error fetching loan details! Please try again!")
        except Exception as e:
            logger.error(f"Error fetching loan details: {e}")
            return ToolResponse(message="Error fetching loan details! Please try again!")

    async def fetch_inelligible_loans(self, loan_data, assistant_id: str, user: User | None = None):
        # Filter out ineligible loans for Refinance Offer. If the contact has another active loan, we will not show
        # the refinance offer for that loan.
        async def check_ineligibility(loan, assistant_id):
            contact_id = loan.get("borrower_contact_id")
            params = {
                "filter": f"borrower_id={contact_id},loan_status={ACTIVE_LOAN_STATUS_FIELD}",
                "page[number]": 1,
                "page[size]": 1,
            }
            try:
                response = await self.client.get(
                    f"{BASE_URL}/loans",
                    headers={"Authorization": await total_expert_utils.get_token(assistant_id, user=user)},
                    params=params,
                )
                response.raise_for_status()
                active_loans = response.json().get("items", [])
                if active_loans:
                    return loan.get("loan_number")
            except HTTPStatusError as e:
                logger.error(f"HTTP error occurred: {e.response.status_code} - {e.response.text}")
                return None

        tasks = [check_ineligibility(loan, assistant_id) for loan in loan_data]
        inelligible_loans = await asyncio.gather(*tasks)
        return [loan_number for loan_number in inelligible_loans if loan_number]


loan_function = LoanFunction()
