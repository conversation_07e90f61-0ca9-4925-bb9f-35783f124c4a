from .activity import activity_function
from .contact import contact_function
from .contact_group import contact_group_function
from .contact_note import contact_note_function
from .journey import journey_function
from .loan import loan_function


class TotalExpertFunction:

    def list_functions(self) -> dict:
        return {
            **loan_function.list_functions(),
            **contact_function.list_functions(),
            **contact_note_function.list_functions(),
            **activity_function.list_functions(),
            **contact_group_function.list_functions(),
            **journey_function.list_functions(),
        }


total_expert_function = TotalExpertFunction()
