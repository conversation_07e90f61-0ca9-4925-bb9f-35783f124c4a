import base64
import re
from datetime import datetime

import faiss
import numpy as np
from config import settings
from db.models import Assistant, Document
from db.session import session_manager
from fastapi import HTTPException
from hooks.s3 import get_s3_hook
from hooks.voyage_hook import voyage_embedder
from loguru import logger
from schema.enums import AssistantSubTypes, DocumentTypes
from sqlalchemy import case, or_, select
from tools.fields.encompass import fetch_encompass_assistants
from tools.schema import Component, ComponentEnum, ToolResponse
from tools.utils.generate_ppt import generate_ppt
from tools.utils.generic import get_context, get_filtered_matches, remove_duplicate_documents


class GenericFunction:
    def list_functions(self) -> dict:
        return {
            "get_relevant_documents": self.get_relevant_documents,
            "get_relevant_user_documents": self.get_relevant_user_documents,
            "generate_ppt": self.generate_ppt,
            "get_relevant_images": self.get_relevant_images,
            "generate_image": self.generate_image,
            "out_of_scope_query": self.out_of_scope_query,
            "web_search": self.search_in_web,
        }

    async def get_reranked_contexts(self, matches, user_query):
        if not matches:
            return []
        text_to_context = {context.get("metadata").get("text"): context for context in matches}
        documents = [context.get("metadata").get("text") for i, context in enumerate(matches)]
        contexts = await voyage_embedder.get_reranked_context(user_query, documents, settings.RERANK_TOP_K)
        irrelevant_context = all(item.relevance_score < 0.57 for item in contexts.results)

        top_k = [item.document for item in contexts.results]
        top_k_contexts = [text_to_context.get(text) for text in top_k]

        if irrelevant_context:
            top_k_contexts = await get_filtered_matches(top_k_contexts, user_query=user_query)

        return top_k_contexts

    async def get_relevant_documents(self, **kwargs) -> ToolResponse:
        """
        Retrieve relevant documents based on the query
        """

        try:
            user_query = kwargs.get("user_query")
            queries = kwargs.get("query")
            categories = kwargs.get("categories") or []
            assistant_id = kwargs.get("assistant_id")

            async with session_manager() as session:
                query = select(Assistant.enable_hard_filter).where(Assistant.id == assistant_id)
                result = await session.execute(query)
                enable_hard_filter = result.scalar_one_or_none() or False
            if not categories:
                async with session_manager() as session:
                    assistant = (
                        await session.execute(select(Assistant).where(Assistant.id == assistant_id))
                    ).scalars().first() or HTTPException(status_code=404, detail=f"Assistant {assistant_id} not found")

                    # Check if advance filtering is On.(is_category_filtered=True)
                    if hasattr(assistant, "is_category_filtered") and assistant.is_category_filtered:
                        # If advance filtering, then return all assistant categories
                        categories = (
                            sorted(map(str.strip, assistant.categories), key=str.lower)
                            if hasattr(assistant, "categories") and isinstance(assistant.categories, list)
                            else assistant.categories
                        )
                    else:
                        # If advance filtering is Off(is_category_filtered=False), return empty list
                        categories = []

            if not queries:
                return {"message": "No query provided."}

            if isinstance(queries, str):
                queries = [queries]

            logger.info(f"Retrieving relevant documents from namespace: {assistant_id}")
            context = await get_context(
                queries, assistant_id=assistant_id, namespace=assistant_id, categories=categories
            )
            matches = context.get("matches")

            if settings.RERANKER_IMPLEMENTATION:
                logger.info("Reranking contexts")
                matches = await self.get_reranked_contexts(matches, user_query)
            else:
                logger.info("Filtering the contexts")
                matches = await get_filtered_matches(matches, user_query)

            matches = await remove_duplicate_documents(matches)
            if not matches:
                logger.info("No relevant documents found in the knowledge base")
                if not enable_hard_filter:
                    logger.info("Searching in web")
                    return await self.search_in_web(fallback=True, **kwargs)
                message = "No relevant documents found in the knowledge base"  # noqa
                return ToolResponse(message=message)

            for i, context in enumerate(matches):
                logger.info(f"Relevant context's Id: {context.id}")

            if matches:
                # Updated context_message to group by categories and include sources.
                context_message = (  # noqa
                    f"The following contexts are provided to help answer the query. These are from your knowledge base. "  # noqa
                    f"When generating your answer, please organize the information by categories. "
                    + (
                        f"Specifically focus on these categories: {categories}. "
                        f"When organizing, **prioritize the document's own metadata categories when they exist within this list.** "  # noqa
                        if categories
                        else "Use the document categories from metadata where available. "
                    )
                    + f"If some documents don't have specific categories or if a document covers multiple categories, use your judgment to organize the information logically. "  # noqa
                    f"For each section, include the relevant filename and page number sources that support the information. "  # noqa
                    f"Make sure to cite the specific sources (filename and page number) for each piece of information you provide.\n\n"  # noqa
                    + "\n".join(
                        [
                            f"Context {i+1}:\n{context.get('metadata').get('text')} "
                            f"### Source: "
                            f"Filename: {context.get('metadata').get('filename')} "
                            f"Page: {context.get('metadata').get('page_number', 'N/A')} "
                            f"Categories: {context.get('metadata').get('categories', 'N/A')} "
                            for i, context in enumerate(matches)
                        ]
                    )
                )
            else:
                context_message = ""

            metadata = {"knowledgebase_citation": [m.get("metadata") for m in matches]}

            if not enable_hard_filter:
                web_search_content = await self.search_in_web(**kwargs)
                web_context_message = getattr(web_search_content, "message", "")
                context_message = f"{context_message}\n\n{web_context_message}"
                web_component = getattr(web_search_content, "component", None)
                web_metadata = []
                if web_component and hasattr(web_component, "component_props"):
                    web_metadata = web_component.component_props
                metadata["web_citation"] = web_metadata

            component = Component(component_name=ComponentEnum.SourceCitation, component_props=metadata)
            return ToolResponse(message=context_message, component=component)
        except Exception as e:
            logger.error(f"An error occurred: {e}")
            return {"message": "An error occurred."}

    async def generate_ppt(self, **kwargs) -> ToolResponse:
        """
        Generate a presentation slide for the given title.
        """
        try:
            title = kwargs.get("title")
            num_pages = kwargs.get("num_pages") or 5
            context = kwargs.get("context") or ""
            assistant_id = kwargs.get("assistant_id")

            logger.info(f"Generating presentation for title: {title}")
            ppt_url, data = await generate_ppt(title, context, num_pages, assistant_id)
            if ppt_url:
                message = "Presentation has been successfully generated."
                component = Component(
                    component_name=ComponentEnum.SlidePresentation, component_props={"link": ppt_url, "data": data}
                )
            else:
                message = "Presentation could not be generated."
            return ToolResponse(message=message, component=component if ppt_url else None)
        except Exception as e:
            logger.error(f"An error occurred: {e}")
            return ToolResponse(message="An error occurred.")

    async def get_relevant_user_documents(self, **kwargs) -> ToolResponse:
        """
        Retrieve relevant documents based on the query
        """
        try:
            user_query = kwargs.get("user_query")
            query = kwargs.get("query")
            categories = kwargs.get("categories") or []
            assistant_id = kwargs.get("assistant_id")
            user = kwargs.get("user")
            if not query:
                return {"message": "No query provided."}

            if isinstance(query, str):
                query = [query]

            namespace = f"{assistant_id}_{str(user.id)}"
            logger.info(f"Retrieving relevant documents from namespace: {namespace}")
            context = await get_context(query, assistant_id=assistant_id, namespace=namespace, categories=categories)
            matches = context.get("matches")
            if not matches:
                return {"message": "No relevant documents found."}

            if settings.RERANKER_IMPLEMENTATION:
                logger.info("Reranking contexts")
                matches = await self.get_reranked_contexts(matches, user_query)
            else:
                logger.info("Filtering the contexts")
                matches = await get_filtered_matches(matches, user_query)

            matches = await remove_duplicate_documents(matches)
            if not matches:
                return {"message": "No relevant documents found after filtering."}

            for i, context in enumerate(matches):
                logger.info(f"Relevant context's Id: {context.id}")

            if matches:
                context_message = "The following contexts are provided to help answer the query:\n\n" + "\n".join(
                    [
                        f"Context {i+1}:\n{context.get('metadata').get('text')}\n" for i, context in enumerate(matches)
                    ]  # noqa: E501
                )
            else:
                context_message = ""
            metadata = [m.get("metadata") for m in matches]
            component = Component(component_name=ComponentEnum.SourceCitation, component_props=metadata)
            return ToolResponse(message=context_message, component=component)
        except Exception as e:
            logger.error(f"An error occurred: {e}")
            return {"message": "An error occurred."}

    async def get_relevant_images(self, **kwargs) -> ToolResponse:
        img_file_name = kwargs.get("img_file_name")
        if not img_file_name:
            return ToolResponse(message="Image file name is required.")

        terms = re.split(r"[\s_]+", img_file_name.lower())
        if not terms:
            return ToolResponse(message="Provide more specific search terms.")

        try:
            async with session_manager() as session:
                ilike_conds = []
                for t in terms:
                    ilike_conds += [Document.title.ilike(f"%{t}%"), Document.title.ilike(f'%{"%".join(t)}%')]

                relevance = sum(case((Document.title.ilike(f"%{t}%"), 1), else_=0) for t in terms).label("relevance")
                query = (
                    select(Document, relevance)
                    .where(Document.type.in_([DocumentTypes.IMAGE, DocumentTypes.SHAREPOINT_IMAGE]), or_(*ilike_conds))
                    .order_by(relevance.desc())
                    .limit(20)
                )
                res = await session.execute(query)
                rows = res.all()
        except Exception:
            logger.error("Database query failed", exc_info=True)
            return ToolResponse(message="Couldn't fetch documents due to DB error.")

        if not rows:
            return ToolResponse(message="No images found.")

        docs, ilike_scores = zip(*rows)

        try:
            from hooks.openai_hook import oai_hook

            title_texts = [d.title for d in docs]
            title_embs_resp = await oai_hook.embed_texts(title_texts)
            title_embs = np.array(title_embs_resp, dtype="float32")
            query_emb_resp = await oai_hook.embed_texts(img_file_name)
            query_emb = np.array(query_emb_resp[0], dtype="float32")
        except Exception:
            return ToolResponse(message="Failed to compute semantic embedding.")

        try:
            embs_mat = np.vstack(title_embs)
            faiss.normalize_L2(embs_mat)
            dim = embs_mat.shape[1]
            index = faiss.IndexFlatIP(dim)
            index.add(embs_mat)
            qe = query_emb.copy().reshape(1, -1)
            faiss.normalize_L2(qe)
            _, indices = index.search(qe, k=min(10, len(docs)))
        except Exception:
            logger.error("FAISS indexing/search failed", exc_info=True)
            indices = np.arange(min(10, len(docs))).reshape(1, -1)  # fallback to top candidates

        results = []
        for idx in indices[0]:
            sim = float(
                np.dot(title_embs[idx], query_emb) / (np.linalg.norm(title_embs[idx]) * np.linalg.norm(query_emb))
            )
            results.append((docs[idx], ilike_scores[idx], sim))

        ranked = sorted(results, key=lambda x: 0.5 * x[1] + (1 - 0.5) * x[2], reverse=True)[:4]

        images = []
        for doc, *_ in ranked:
            try:
                hook = await get_s3_hook(doc.assistant_id)
                url = hook.get_presigned_url_from_link(doc.link)
                images.append({"image_file_name": doc.title, "link": url})
            except Exception:
                logger.error(f"S3 error for {doc.title}", exc_info=True)

        if not images:
            return ToolResponse(message="No images could be retrieved from storage.")

        return ToolResponse(
            message=f"Found {len(images)} images (hybrid search).",
            component=Component(
                component_name=ComponentEnum.Image,
                component_props={"images": images, "search_terms": terms, "type": "hybrid_semantic_image_retrieval"},
            ),
        )

    async def generate_image(self, **kwargs) -> ToolResponse:
        """Generate an image for the given title."""

        try:
            prompt = kwargs.get("prompt")
            assistant_id = kwargs.get("assistant_id")
            aspect_ratio = kwargs.get("aspectRatio") or "16:9"
            model = settings.IMAGE_GENERATION_MODEL

            if not prompt:
                logger.warning("Prompt is not passed")
                return ToolResponse(message="Could you describe again on what kind of image do you want?")

            from hooks.gemini_hook import gemini_hook

            b64_image = await gemini_hook.query_image_model(
                user_input=prompt, model_name=model, aspect_ratio=aspect_ratio
            )

            # upload image to s3
            file = base64.b64decode(b64_image)

            filename = f"{assistant_id}/image__{datetime.now().strftime('%Y%m%d%H%M%S')}.jpg"
            s3_hook = await get_s3_hook(assistant_id)
            image_url = s3_hook.upload_file(filename, file, content_type="image/jpg")
            if image_url:
                message = "Image has been successfully generated."
                component = Component(component_name=ComponentEnum.Image, component_props={"link": image_url})
            else:
                message = "Sorry, I coundn't generate image at the moment. Could you please try again after sometime?"
            return ToolResponse(message=message, component=component if image_url else None)
        except Exception as e:
            logger.warning(f"An error occurred: {e}")
            return ToolResponse(message="An error occurred.")

    async def out_of_scope_query(self, **kwargs) -> ToolResponse | None:
        assistant_id = kwargs.get("assistant_id")
        sales_assistants = await fetch_encompass_assistants()

        is_sales = any(
            a.id == assistant_id and a.sub_type == AssistantSubTypes.ENCOMPASS_SALES for a in sales_assistants
        )

        message = (
            "Sorry the query is beyond my scope. I have no answer to this query. "
            "But I have other capabilities:\n"
            "- Generate a rate term refinance offer\n"
            "- Generate a cashout refinance offer\n\n"
            "- Generate a purchase offer\n\n"
            "If this is not what you are looking for, maybe you can explore other assistants."
            if is_sales
            else "Sorry the query is beyond my scope. I have no answer to this query. But i have other capabilities and mention the capabilities (functionality) you have strictly in bullet points. If this is not what you are looking for, maybe you can explore other assistants"  # noqa
        )

        return ToolResponse(message=message)

    async def search_in_web(self, fallback=False, **kwargs):
        """
        Performs a web search using OpenAI's Responses API and returns the result.

        Parameters:
        - prompt (str): The query or question to search.

        Returns:
        - str: The AI-generated response incorporating web search results.
        """
        from hooks.openai_hook import oai_hook

        try:
            user_query = kwargs.get("query")
            if isinstance(user_query, str):
                user_query = [user_query]

            system_prompt = (
                "When providing information from web searches, always include the source URLs (annotations) and titles "  # noqa
                "for each piece of information. Structure your response to be concise yet informative, "
                "focusing on the most relevant details that directly answer the query. "
                "For each claim or piece of information, cite the specific source it came from. "
                "Synthesize information from multiple sources when appropriate, but maintain clarity "
                "about which sources support which points. Provide responses that are "
                "factual, well-organized, and easy to understand."
            )
            response = await oai_hook.create_response(
                prompt=[{"role": "system", "content": system_prompt}, {"role": "user", "content": user_query[0]}],
                model="gpt-4.1",
                tools=[{"type": "web_search"}],
            )

            metadata = []
            if hasattr(response, "output") and response.output:
                content_output = None
                for output in response.output:
                    if hasattr(output, "content") and output.content:
                        content_output = output
                        break
                if content_output:
                    response_text = content_output.content[0].text

                if hasattr(content_output.content[0], "annotations"):
                    seen_urls = set()
                    for annotation in content_output.content[0].annotations:
                        if hasattr(annotation, "title") and hasattr(annotation, "url"):
                            if annotation.url not in seen_urls:
                                seen_urls.add(annotation.url)
                                metadata.append(
                                    {
                                        "assistant_id": kwargs.get("assistant_id"),
                                        "categories": [],
                                        "element_type": "link",
                                        "domain": annotation.url,
                                        "url": annotation.url,
                                        "text": annotation.title,
                                        "title": annotation.title,
                                        "type": "website",
                                    }
                                )

                fallback_message = (
                    "The following contexts are provided from the internet(web). Clearly state that you donot find any information from your knowledge base but found some information from the web. Use these contexts to answer the query."  # noqa
                    if fallback
                    else ""
                )
                context_message = (
                    f"{fallback_message}"
                    f"The following are the web search results for the query:\n"
                    f"Always mention about the source of the information in the answer. "
                    f"{response_text}"
                )
                return ToolResponse(
                    message=context_message,
                    component=(
                        Component(component_name=ComponentEnum.SourceCitation, component_props=metadata)
                        if metadata
                        else None
                    ),
                )
            else:
                return ToolResponse(message="No web search results found.")

        except Exception as e:
            logger.error(f"Error in web search: {e}")
            return ToolResponse(message="Sorry, I encountered an error while searching the web.")


generic_function = GenericFunction()
