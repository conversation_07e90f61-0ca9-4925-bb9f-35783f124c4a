from config import settings

BASE_URL = settings.POLLY_BASE_URL
AUTH_URL = BASE_URL + "auth/token/"
BASE_RATE_SET_URL = BASE_URL + "pe/rates/"
PRICING_REQUEST_URL = BASE_URL + "pe/pricing-scenario/"
CUSTOM_PARMETERS_URL = BASE_URL + "pe/custom-parameters/"
CITIZENSHIP_MAP = {
    "N/A": "ForeignNational",
    "Non-Permanent Resident Alien": "NonPermanentResidentAlien",
    "Permanent Resident Alien": "PermanentResidentAlien",
    "USCitizen": "USCitizen",
    "": "None",  # If empty
    None: "None",
}

PURPOSE_MAP = {
    "Purchase": "Purchase",
    "Refinance": "Refinance",
    "Construction": "Construction",
    "Construction - Perm": "ConstructionPerm",
    "Other": "Other",
    "NoCash-Out Refinance": "NoCashOutRefinance",
    "Cash-Out Refinance": "CashOutRefinance",
    "": "Refinance",  # If empty
    None: "None",
}

IMPOUNDS_MAP = {
    "Taxes and Insurance": "Full",
    "Taxes only": "Partial",
    "Insurance only": "Partial",
    "No Impounds": "None",
    "": "None",  # If empty
    None: "None",
}

AUS_MAP = {
    "Manual": "Manual",
    "DU": "DU",
    "LP": "LP",
    "N/A": "Other",
    "": "NotSpecified",  # If empty
    None: "None",
}

PROPERTY_TYPE_MAP = {
    "Detached": "SFR",
    "Condominium": "Condominium",
    "High Rise Condominium": "Condominium",
    "Detached Condo": "Condominium",
    "PUD": "PUD",
    "Manufactured Housing": "Mobile",
    "Mfd Home/Condo/PUD/Co-Op": "Mobile",
    "Co-Operative": "Cooperative",
    "TwoFourUnit": "TwoFourUnit",
    "Attached": "Townhome",
    "Multifamily": "Multifamily",
    "Commercial": "Commercial",
    "MixedUse": "MixedUse",
    "Farm": "Farm",
    "HomeAndBusiness": "HomeAndBusiness",
    "Land": "Land",
    "ManufacturedSingleWide": "ManufacturedSingleWide",
    "ManufacturedDoubleWide": "ManufacturedDoubleWide",
    "": "SFR",  # Default
    None: "SFR",
}

OCCUPANCY_MAP = {
    "PrimaryResidence": "PrimaryResidence",
    "SecondHome": "SecondHome",
    "Investor": "InvestmentProperty",
    "": "PrimaryResidence",  # Default
    None: "PrimaryResidence",
}
