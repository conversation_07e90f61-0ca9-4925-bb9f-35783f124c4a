import ast
from datetime import timed<PERSON><PERSON>

import redis.asyncio as aioredis
from config import settings
from fastapi import HTT<PERSON>Ex<PERSON>, status
from httpx import AsyncClient, HTTPStatusError
from loguru import logger
from redis.exceptions import RedisError
from schema.polly import Price, PricingRequestBody, PricingRequestResponse
from tools.functions.polly.constants import (
    AUS_MAP,
    AUTH_URL,
    BASE_RATE_SET_URL,
    BASE_URL,
    CITIZENSHIP_MAP,
    CUSTOM_PARMETERS_URL,
    IMPOUNDS_MAP,
    OCCUPANCY_MAP,
    PRICING_REQUEST_URL,
    PROPERTY_TYPE_MAP,
    PURPOSE_MAP,
)


class PollyFunction:
    def __init__(self) -> None:
        self.client = AsyncClient(timeout=None)
        self.url = BASE_URL
        self.redis = None

    async def initialize_redis(self):
        if self.redis is None:
            try:
                self.redis = await aioredis.from_url(settings.REDIS_URL)
                await self.redis.ping()
            except RedisError as e:
                logger.error(f"Redis error: {e}")
            except Exception as e:
                logger.exception(f"Unexpected error during Redis initialization:${e}")

    async def fetch_access_token(self, payload: dict) -> str | None:
        auth_url = AUTH_URL
        headers = {
            "Content-Type": "application/x-www-form-urlencoded",
            "organization-ticker-symbol": settings.POLLY_ORG_TICKER,
        }

        try:
            response = await self.client.post(auth_url, headers=headers, data=payload)
            response.raise_for_status()
            response_data = response.json()
            return response_data.get("access_token")
        except HTTPStatusError as e:
            logger.error(f"Token request failed with status {e.response.status_code}: {e.response.text}")
        except Exception as e:
            logger.error(f"Unexpected exception during token fetch: {e}")
        return None

    async def authenticate(self):
        if not self.redis:
            await self.initialize_redis()
        cached_token = await self.redis.get("polly_token")
        if cached_token:
            return cached_token.decode() if isinstance(cached_token, bytes) else cached_token
        payload = {
            "username": settings.POLLY_USERNAME,
            "password": settings.POLLY_PASSWORD,
            "grant_type": "password",
            "client_id": settings.POLLY_CLIENT_ID,
            "client_secret": settings.POLLY_CLIENT_SECRET,
        }
        token = await self.fetch_access_token(payload)
        if not token:
            logger.error("Empty auth token")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED, detail="Could not authenticate user into polly!"
            )
        await self.redis.setex("polly_token", timedelta(minutes=59), token)
        return token

    async def get_auth_header(self):
        token = await self.authenticate()
        return {"authorization": f"Bearer {token}"}

    async def _authenticated_get(self, path: str):
        full_url = path
        try:
            headers = await self.get_auth_header()
            response = await self.client.get(full_url, headers=headers)
            response.raise_for_status()
            return response.json()
        except HTTPStatusError as e:
            logger.error(f"Request failed: {e.response.status_code} - {e.response.text}")
            raise HTTPException(
                status_code=e.response.status_code,
                detail=f"Request failed: {e.response.text}",
            )
        except HTTPException as e:
            logger.error(f"Authorization failed: {e.detail}")
            raise  # re-raise same exception
        except Exception as e:
            logger.exception(f"Unexpected error during GET {path}: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Unexpected error while making request to {path}",
            )

    async def get_base_rate_sets(self):
        return await self._authenticated_get(BASE_RATE_SET_URL)

    async def get_base_rate(self, base_rate_set_id):
        return await self._authenticated_get(BASE_RATE_SET_URL + base_rate_set_id)

    async def request_pricing(self, pricing_req_body):
        # logger.info(f"Sending pricing request with body: {pricing_req_body}")
        request_pricing_url = PRICING_REQUEST_URL

        try:
            headers = await self.get_auth_header()
            headers["Content-Type"] = "application/json"
            response = await self.client.post(request_pricing_url, headers=headers, json=pricing_req_body)
            response.raise_for_status()

        except HTTPStatusError as e:
            logger.error(f"Pricing request failed: {e.response.status_code} - {e.response.text}")
            if response.status_code == 422:
                self._handle_validation_error(e.response.json())
            raise HTTPException(status_code=e.response.status_code, detail="Pricing request failed!")
        except Exception as e:
            logger.error(f"Unexpected error in request_pricing:{e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Unexpected error while fetching pricing data",
            )
        return response.json()["data"]["results"]

    async def fetch_custom_parameters(self):
        # logger.info(f"Sending pricing request with body: {pricing_req_body}")
        try:
            headers = await self.get_auth_header()
            headers["Content-Type"] = "application/json"
            response = await self.client.get(CUSTOM_PARMETERS_URL, headers=headers)
            response.raise_for_status()

        except HTTPStatusError as e:
            logger.error(f"Fetching Custom Parameters Failed: {e.response.status_code} - {e.response.text}")
            if response.status_code == 422:
                self._handle_validation_error(e.response.json())
            raise HTTPException(status_code=e.response.status_code, detail="Internal Server Error")
        except Exception as e:
            logger.error(f"Unexpected error in request_pricing:{e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Unexpected error while fetching pricing data",
            )
        return response.json()["parameters"]

    async def fetch_valid_branches(self):
        custom_params = await self.fetch_custom_parameters()
        # Iterate to get the dict with name Branch and return allowedValues if exist else return []
        return next((item for item in custom_params if item.get("name", "") == "Branch"), {}).get("allowedValues", [])

    async def validate_branch(self, branch):
        return branch.split()[0] in await self.fetch_valid_branches()

    def _handle_validation_error(self, error_data):
        validation_details = ast.literal_eval(error_data["detail"])
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=f"{','.join(item.get('field') for item in validation_details)} are not valid.",
        )

    def parse_price_request_result(self, response: dict) -> list[PricingRequestResponse]:
        final_result = []
        for item in response:
            for price in item.get("prices", []):
                parsed_price = Price(
                    rate=price["rate"],
                    price=price["price"],
                    apr=price["apr"],
                    lockPeriod=price["lockPeriod"],
                    investor=price["investor"],
                    principalAndInterest=price["principalAndInterest"],
                    discount=price["discount"],
                )
                parsed_result = PricingRequestResponse(
                    name=item["name"],
                    id=item["id"],
                    code=item["code"],
                    amortizationTermMonths=item["amortizationTermMonths"],
                    amortization=item["amortization"],
                    loanType=item["loanType"],
                    ltv=item["ltv"],
                    prices=parsed_price,
                )
                final_result.append(parsed_result)
        return final_result

    def _map_value(self, value, mapping, default=None):
        return mapping.get(value, default or value)

    def loan_details_to_pricing_request_body(self, pricing_req_body: PricingRequestBody):
        loan_request_body = {
            "borrower": {
                "fico": pricing_req_body.borrower.fico,
                "citizenship": self._map_value(pricing_req_body.borrower.citizenship, CITIZENSHIP_MAP, "None"),
            },
            "loan": {
                "amount": pricing_req_body.loan.amount,
                "purpose": self._map_value(pricing_req_body.loan.purpose, PURPOSE_MAP, "None"),
                "ltv": pricing_req_body.loan.ltv,
                "impounds": self._map_value(pricing_req_body.loan.impounds, IMPOUNDS_MAP, "Partial"),
                "aus": self._map_value(
                    pricing_req_body.loan.aus,
                    AUS_MAP,
                    "NotSpecified",
                ),
            },
            "property": {
                "propertyType": self._map_value(pricing_req_body.property.propertyType, PROPERTY_TYPE_MAP, "SFR"),
                "occupancy": self._map_value(pricing_req_body.property.occupancy, OCCUPANCY_MAP, "PrimaryResidence"),
                "state": pricing_req_body.property.state,
                "county": pricing_req_body.property.county + " County",
            },
            "search": {
                "loanTypes": [pricing_req_body.search.loanType] if pricing_req_body.search.loanType else [],
                "loanTerms": [pricing_req_body.search.loanTerms * 12] if pricing_req_body.search.loanTerms else [],
                "amortizationTypes": [pricing_req_body.search.armType],
                "desiredLockPeriod": pricing_req_body.search.desiredLockPeriod,
                "desiredPrice": 100 - pricing_req_body.search.desiredPrice,
            },
            "settings": {
                "returnIneligibleProducts": False,
            },
            "customValues": [
                {
                    "name": "Branch",
                    "value": pricing_req_body.branch.split()[0],
                },
            ],
            "audienceId": settings.POLLY_AUDIENCE_ID,
        }
        return loan_request_body

    def parse_base_rates(self, data: dict):
        return data["data"].get("baseRates", [])


polly_function = PollyFunction()
