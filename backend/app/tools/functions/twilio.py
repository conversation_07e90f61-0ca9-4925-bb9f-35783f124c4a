import uuid
from datetime import datetime, timedelta

import redis.asyncio as aioredis
from config import settings
from hooks.connectors.outlook_hook import outlook_hook
from httpx import AsyncClient
from loguru import logger
from tools.templates import client_templates
from tools.utils.encompass.loan import loan_utils
from twilio.rest import Client


class TwilioUtils:
    def __init__(self):
        self.client = AsyncClient(timeout=None)
        self.redis = None

    def get_end_call_tool(self):
        return {
            "type": "function",
            "name": "end_call",
            "description": "End the call. This is the final gratitude from the user, use this tool to end the call.",  # noqa: E501
        }

    def get_send_refinance_email_tool(self):
        return {
            "type": "function",
            "name": "send_refinance_email",
            "description": "Send a refinance email to the user. Use this tool when the user confirms the refinance option",  # noqa: E501
            "parameters": {
                "type": "object",
                "properties": {
                    "borrower_name": {"type": "string", "description": "borrower name from loan details"},
                    "closed_date": {"type": "string", "description": "closed date from loan details"},
                    "interest_rate": {"type": "number", "description": "interest rate from loan details"},
                    "current_loan_details": {
                        "type": "string",
                        "description": "detailed information of the current loan like loan number, borrower name, loan amount, interest rate, monthly payment, monthly savings, total savings, etc.",  # noqa: E501
                    },
                    "recipient_email": {"type": "string", "description": "user email provided by the user"},
                    "refinance_details": {
                        "type": "string",
                        "description": "detailed information of the refinance options like loan amount, interest rate, monthly payment, monthly savings, total savings, etc.",  # noqa: E501
                    },
                },
                "required": ["current_loan_details", "refinance_details"],
            },
        }

    def get_send_message_to_user_tool(self):
        return {
            "type": "function",
            "name": "send_message_to_user",
            "description": "Send a message to the user. Use this tool when the user requests to know the loan details or any other information regarding loans through text",  # noqa: E501
            "parameters": {
                "type": "object",
                "properties": {
                    "sms_body": {
                        "type": "string",
                        "description": "sms body to be sent to the user (i.e information requested by the user)",
                    },
                },
                "required": ["sms_body"],
            },
        }

    def get_transfer_call_tool(self):
        return {
            "type": "function",
            "name": "transfer_call",
            "description": "Transfer the call to a human agent. Use this tool when the user requests to speak with a human agent",  # noqa: E501
            "parameters": {
                "type": "object",
                "properties": {
                    "forward_to_number": {"type": "string"},
                },
                "required": ["forward_to_number"],
            },
        }

    def get_interest_rates_tool(self):
        return {
            "type": "function",
            "name": "get_interest_rates",
            "description": "Get the current interest rates while calculating the refinance options for the user.",
        }

    def get_send_probate_mail_tool(self):
        return {
            "type": "function",
            "name": "send_probate_mail",
            "description": "Send a probate mail to the user. When use is interested in the probate details and has some plans for the estate, use this tool to send the mail",  # noqa: E501
            "parameters": {
                "type": "object",
                "properties": {
                    "email_address": {"type": "string", "description": "mail address mentioned by the probate user"},
                    "first_name": {"type": "string", "description": "first name of the user"},
                    "last_name": {"type": "string", "description": "last name of the user"},
                    "appointment_date": {
                        "type": "string",
                        "description": "appointment date for the user strictly in this format: year-month-dayTtime (i.e 2025-03-15T12:00:00)",  # noqa: E501
                    },
                    "probate_details": {
                        "type": "string",
                        "description": "probate details like plans and followup mail",
                    },
                },
                "required": ["user_email", "first_name", "last_name", "probate_details"],
            },
        }

    async def initialize_redis(self):
        if not self.redis:
            self.redis = await aioredis.from_url(settings.REDIS_URL)

    def list_functions(self) -> dict:
        return {
            "get_interest_rates": self.get_interest_rates,
            "send_refinance_email": self.send_refinance_email,
            "send_message_to_user": self.send_message_to_user,
            "send_probate_mail": self.send_probate_mail,
        }

    async def send_refinance_email(self, **kwargs) -> str:
        try:
            await self.initialize_redis()
            outlook_token = await self.redis.get(f"outlook_token-{kwargs.get('user_email')}")
        except Exception as e:
            logger.error(f"Failed to initialize redis: {e}")
            return "Sorry, we were not able to send the refinance email to you right now. But we will send it to you soon."  # noqa: E501

        recipient_email = kwargs.get("recipient_email")
        refinance_details = kwargs.get("refinance_details")
        current_loan_details = kwargs.get("current_loan_details")
        borrower_name = kwargs.get("borrower_name")
        closed_date = kwargs.get("loan_closed_date")
        interest_rate = kwargs.get("interest_rate")
        if outlook_token:
            outlook_token = outlook_token.decode() if isinstance(outlook_token, bytes) else outlook_token
            try:
                subject = "Selected Refinance Option"
                message_head = client_templates.RATE_TERM_EMAIL_BODY_HEAD.format(
                    first_name=borrower_name,
                    closed_date=closed_date,
                    interest_rate=f"{interest_rate:.3f}",
                )
                message_tail = client_templates.RATE_TERM_EMAIL_BODY_TAIL.format()
                body = (
                    f"{message_head}\n\n"
                    f"**Current Loan Details:**\n\n{current_loan_details.strip()}\n\n"
                    f"**Selected Refinance Option:**\n\n{refinance_details.strip()}\n\n"
                    f"{message_tail}"
                )
                logger.info(f"Sending refinance email to {recipient_email} with body: {body}")
                await outlook_hook.send_email(
                    recipients=[recipient_email],
                    subject=subject,
                    body=body,
                    token=outlook_token,
                )
                return "Refinance email sent successfully"
            except Exception as e:
                logger.error(f"Failed to send refinance email: {e}")
                return "Sorry, we were not able to send the refinance email to you right now. But we will send it to you soon."  # noqa: E501
        else:
            return "Sorry, we were not able to send the refinance email to you right now. But we will send it to you soon."  # noqa: E501

    def create_calendar_event(self, start_date, first_name, last_name, email, title):
        """
        Create a calendar event dict for Outlook.
        start_date: string in format "YYYY-MM-DDTHH:MM:SS"
        first_name, last_name: attendee's name
        email: attendee's email
        title: event subject/title
        """
        try:
            dt_start = datetime.strptime(start_date, "%Y-%m-%dT%H:%M:%S") if start_date else None
            dt_end = (dt_start + timedelta(hours=1)).strftime("%Y-%m-%dT%H:%M:%S") if dt_start else None
        except Exception as e:
            logger.error(f"Failed to parse start_date for calendar event: {e}")
            dt_start = start_date
            dt_end = start_date

        return {
            "subject": title,
            "body": {"contentType": "HTML", "content": ""},
            "start": {"dateTime": start_date, "timeZone": "Eastern Standard Time"},
            "end": {"dateTime": dt_end, "timeZone": "Eastern Standard Time"},
            "location": {"displayName": "Office"},
            "attendees": [
                {"emailAddress": {"address": email, "name": f"{first_name} {last_name}"}, "type": "required"}
            ],
            "allowNewTimeProposals": True,
            "transactionId": str(uuid.uuid4()),
        }

    async def send_probate_mail(self, **kwargs) -> str:
        outlook_token = None
        try:
            await self.initialize_redis()
            outlook_token = await self.redis.get(f"outlook_token-{kwargs.get('user_email')}")
            outlook_token = outlook_token.decode() if isinstance(outlook_token, bytes) else outlook_token
        except Exception as e:
            logger.error(f"Failed to initialize redis: {e}")
            return "Sorry, we were not able to send the probate mail to you right now. But we will send it to you soon."  # noqa: E501

        recipient_email = kwargs.get("email_address")
        first_name = kwargs.get("first_name")
        last_name = kwargs.get("last_name")
        probate_details = kwargs.get("probate_details")
        appointment_date = kwargs.get("appointment_date")
        if outlook_token:
            try:
                # Use the new function to create the event
                calendar_event = self.create_calendar_event(
                    start_date=appointment_date,
                    first_name=first_name,
                    last_name=last_name,
                    email=recipient_email,
                    title="Probation Appointment (Discussed with the user)",
                )
                subject = client_templates.PROBATE_CALL_EMAIL_SUBJECT.format(
                    first_name=first_name, last_name=last_name
                )
                body = client_templates.PROBATE_CALL_EMAIL_BODY_HEAD.format(first_name=first_name)
                body += f"**Probate Details:**\n\n{probate_details.strip()}\n\n"
                await outlook_hook.send_email(
                    recipients=[recipient_email],
                    subject=subject,
                    body=body,
                    token=outlook_token,
                )
                await outlook_hook.create_event(calendar_event, outlook_token)
                return "Probate mail sent successfully"

            except Exception as e:
                logger.error(f"Failed to send probate mail: {e}")
                return "Sorry, we were not able to send the probate mail to you right now. But we will send it to you soon."  # noqa: E501
        else:
            return "Sorry, we were not able to send the probate mail to you right now. But we will send it to you soon."  # noqa: E501

    async def get_interest_rates(self, **kwargs) -> dict:
        return await loan_utils.get_interest_rates()

    async def send_message_to_user(self, **kwargs) -> str:
        try:
            client = Client(settings.TWILIO_ACCOUNT_SID, settings.TWILIO_AUTH_TOKEN)
            message = client.messages.create(
                from_=kwargs.get("current_phone_number"), to=kwargs.get("phone_number"), body=kwargs.get("sms_body")
            )
            return f"Message sent successfully with message id: {message.sid}"
        except Exception as e:
            logger.error(f"Failed to send message: {e}")
            return "Failed to send message"
