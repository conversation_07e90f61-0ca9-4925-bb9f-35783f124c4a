from httpx import AsyncClient
from tools.schema import Component, ComponentEnum, ToolResponse
from tools.templates import client_templates


class CallCampaignFunction:
    def __init__(self) -> None:
        self.client = AsyncClient(timeout=None)
        self.redis = None

    def list_functions(self) -> dict:
        return {
            "initiate_campaign": self.initiate_campaign,
        }

    async def initiate_campaign(self, **kwargs) -> ToolResponse | None:
        """
        Find a lending opportunity for a client.
        """
        if not client_templates.CALL_CAMPAIGN_ENABLED:
            return ToolResponse(
                message=(
                    "We apologize, but this feature is currently restricted. Please contact your administrator for access."  # noqa
                )
            )

        return ToolResponse(
            message="To proceed a campaign, please upload the file to process.",
            component=Component(
                component_name=ComponentEnum.CallCampaign,
                component_props={"operation": "initiate_campaign"},
            ),
        )


call_campaign_function = CallCampaignFunction()
