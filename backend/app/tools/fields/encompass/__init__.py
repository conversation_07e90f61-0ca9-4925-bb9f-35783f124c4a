import importlib
from datetime import datetime

from config import settings
from db.models import Assistant, <PERSON><PERSON><PERSON>
from db.session import session_manager
from loguru import logger
from schema.enums import AssistantSubTypes
from sqlalchemy import select

# Import default milestones
from tools.fields.encompass._default import ACTIVE_LOAN_MILESTONES

# Find client_name and import fields
client_module = settings.CLIENT_NAME.lower().replace(" ", "_")
try:
    client_fields = importlib.import_module(f"tools.fields.encompass.{client_module}")
except ImportError:
    logger.warning(f"Fields for {client_module} not found! Using default fields")
    client_fields = importlib.import_module("tools.fields.encompass._default")


# Constants
field_type_string_mapping = {"integernumber": int, "decimalnumber": float, "date": datetime}


async def fetch_encompass_assistants():
    async with session_manager() as session:
        assistant_query = select(Assistant).where(
            Assistant.sub_type.in_((AssistantSubTypes.ENCOMPASS, AssistantSubTypes.ENCOMPASS_SALES))
        )
        assistant = await session.execute(assistant_query)
        assistant = assistant.scalars().all()
        return assistant


async def fetch_all_fields(assistant_id: str) -> dict:
    """Async function to fetch all fields from database"""
    async with session_manager() as session:
        # fetch all fields for this assistant
        field_query = (
            select(DataField).where(DataField.assistant_id == assistant_id).order_by(DataField.display_order.asc())
        )
        result = await session.execute(field_query)
        data_fields = result.scalars().all()
        FIELD_TYPE_MAPPING = {
            field.field_id.strip(): field_type_string_mapping.get(field.type)
            for field in data_fields
            if field.type != "text"
        }
        FIELD_MAPPING = {
            field.field_id.strip(): field.merged_name if field.is_merged else field.name for field in data_fields
        }

        # Check for merged fields
        EXTRA_FIELDS = {field.field_id.strip(): field.name for field in data_fields if field.is_merged}

        return {
            "FIELD_MAPPING": FIELD_MAPPING,
            "FIELD_TYPE_MAPPING": FIELD_TYPE_MAPPING,
            "SEARCH_FIELDS": [field.name for field in data_fields if field.is_searchable],
            "DISPLAY_FIELDS": [field.name for field in data_fields if field.display_in_ui],
            "ACTIVE_LOAN_MILESTONES": ACTIVE_LOAN_MILESTONES.extend(
                getattr(client_fields, "ACTIVE_LOAN_MILESTONES", [])
            ),
            "EXTRA_FIELDS": EXTRA_FIELDS or getattr(client_fields, "EXTRA_FIELDS", {}),
        }
