from datetime import datetime

FIELD_MAPPING = {
    "Loan.LoanFolder": "Loan Folder",
    "Fields.4": "Loan Term",
    "Fields.1999": "Fund Released Date",
    "Fields.11": "Address",
    "Fields.12": "City",
    "Fields.317": "Loan Officer",
    "Fields.3968": "Loan Officer Email",
    "Fields.CX.EPO": "EPO Date",
    "Fields.CX.transdate": "Service Transfer Date",
    "Fields.LoanTeamMember.Email.Junior Loan Officer": "Junior Loan Officer Email",
    "Fields.LoanTeamMember.Email.Loan Processor": "Loan Processor Email",
    "Fields.LoanTeamMember.Email.Junior Processor": "Junior Processor Email",
    "Fields.CX.LOCKDISCLOSURESDUE.SENT": "Rate Lock Disclosures Due Sent",
    "Fields.CX.LOCKDISCFAILED.DATE": "Rate Lock Disclosures Failed Date",
    "Fields.CX.LOCKDISCFAILED.REASON": "Rate Lock Disclosures Failed Reason",
    "Fields.CX.ICDDUE.SENT": "Initial CD Due Sent",
    "Fields.CX.ICDFAIL.DATE": "Initial CD Failed Date",
    "Fields.CX.ICDFAIL.REASON": "Initial CD Failed Reason",
    "Fields.LoanTeamMember.Name.Closer": "Closer Name",
    "Fields.1822": "Referral Partner",
    "Fields.VEND.X133": "Referral Partner",
    "Fields.LoanTeamMember.Name.Loan Processor": "Loan Processor",
    "Fields.LoanTeamMember.Name.Underwriter": "Underwriter",
    "Fields.CX.SOFTREVB2": "Borrower Pair Revolving Balance",
    "Fields.CX.SOFTREVC": "Coborrower Revolving Balance",
    "Fields.CX.SOFTREVB": "Borrower Revolving Balance",
    "Fields.guid": "Loan GUID",
    "Fields.2962": "Request Impound Type",
    "Fields.URLA.X1": "Borr Declarations",
    "Fields.AUSF.X1": "AUS Tracking - Latest Submission - Underwriting Risk Assess Type",
    "Fields.1041": "Subject Property Type",
    "Fields.1395": "State FIPS Code",
    "Fields.1396": "County FIPS Code",
    "Fields.CUST13FV": "Branch ID",
    "Fields.13": "Subject Property County",
}

# TODO: Find better solution for this when migrating to db:: Resolves case for referral partner
EXTRA_FIELDS = {"Fields.1822": "Referral Source", "Fields.VEND.X133": "Buyer's Agent"}

FIELD_TYPE_MAPPING = {
    "Fields.1999": datetime,
    "Fields.CX.LOCKDISCLOSURESDUE.SENT": datetime,
    "Fields.CX.LOCKDISCFAILED.DATE": datetime,
    "Fields.CX.ICDDUE.SENT": datetime,
    "Fields.CX.ICDFAIL.DATE": datetime,
    "Fields.CX.transdate": datetime,
    "Fields.CX.EPO": datetime,
    "Fields.CX.SOFTREVB2": float,
    "Fields.CX.SOFTREVC": float,
    "Fields.CX.SOFTREVB": float,
}


SEARCH_FIELDS = [
    "Address",
    "City",
    "Loan Officer",
    "Loan Officer Email",
    "Junior Loan Officer Email",
    "Loan Processor Email",
    "Junior Processor Email",
    "Referral Source",
    "Buyer's Agent",
    "Closer Name",
]

DISPLAY_FIELDS = [
    "Loan Number",
    "Loan Amount",
    "Interest Rate",
    "Loan Term",
    "First Name",
    "Last Name",
    "State",
    "Address",
    "City",
    "Zip Code",
    "Loan Officer",
    "Purpose of Loan",
    "Loan Type",
    "Estimated Closing Date",
    "Borrower Email",
    "Borrower Phone",
    "Co-Borrower Phone",
    "EPO Date",
    "Service Transfer Date",
    "Appraised value",
    "Down Payment Amount",
    "Buyers Agent Name",
    "Buyers Agent Phone",
    "Buyers Agent Email",
    "Current Milestone",
    "Lock Date",
    "Rate Lock Expiry Date",
    "Investor",
    "Servicing Company Name",
    "Fund Released Date",
    "Credit Score",
    "LTV",
    "Referral Source",
    "Buyer's Agent",
    "Loan Processor",
    "Underwriter",
    "Closer Name",
    "Borrower Pair Revolving Balance",
    "Coborrower Revolving Balance",
    "Borrower Revolving Balance",
]
