from datetime import datetime

FIELD_MAPPING = {
    "Fields.364": "Loan Number",
    "Fields.2": "Loan Amount",
    "Fields.3": "Interest Rate",
    "Fields.4": "Loan Term",
    "Fields.4000": "First Name",
    "Fields.4002": "Last Name",
    "Fields.11": "Address",
    "Fields.12": "City",
    "Fields.14": "State",
    "Fields.15": "Zip Code",
    "Fields.19": "Purpose of Loan",
    "Fields.1172": "Loan Type",
    "Fields.763": "Estimated Closing Date",
    "Fields.1240": "Borrower Email",
    "Fields.1490": "Borrower Phone",
    "Fields.1480": "Co-Borrower Phone",
    "Fields.356": "Appraised value",
    "Fields.1335": "Down Payment Amount",
    "Fields.VEND.X139": "Buyers Agent Name",
    "Fields.VEND.X140": "Buyers Agent Phone",
    "Fields.VEND.X141": "Buyers Agent Email",
    "Fields.Log.MS.CurrentMilestone": "Current Milestone",
    "Fields.761": "Lock Date",
    "Fields.762": "Rate Lock Expiry Date",
    "Fields.VEND.X263": "Investor",
    "Fields.VEND.X178": "Servicing Company Name",
    "Fields.5": "MoPymtPI",
    "Fields.1811": "Subject Property Occupancy Status",
    "Fields.3335": "Occupancy Type",
    "Fields.1401": "Loan Program",
    "Fields.VASUMM.X23": "Credit Score",
    "Fields.353": "LTV",
    "Fields.420": "Lien Position",
    "Fields.LOID": "Loan Officer Login ID",
    "Fields.1393": "Loan Status",
    "Loan.LoanFolder": "Loan Folder",
    "Fields.749": "Fund Released Date",
    "Fields.317": "Loan Officer",
    "Fields.1407": "Loan Officer Email",
    "Fields.1822": "Referral Source",
    "Fields.CX.APPRAISALRCVD.1": "Appraisal Received Date",
    "Fields.136": "Purchase Price",
    "Fields.LockRate.CurrentStatus": "Lock Status",
    "Fields.guid": "Loan GUID",
    "Fields.CX.EID": "Entity ID",
    "Fields.1264": "Lender Name",
}


FIELD_TYPE_MAPPING = {
    "Fields.2": float,
    "Fields.3": float,
    "Fields.4": int,
    "Fields.763": datetime,
    "Fields.356": float,
    "Fields.1335": float,
    "Fields.761": datetime,
    "Fields.762": datetime,
    "Fields.VASUMM.X23": int,
    "Fields.353": float,
    "Fields.5": float,
    "Fields.749": datetime,
    "Fields.CX.APPRAISALRCVD.1": datetime,
    "Fields.136": float,
}

SEARCH_FIELDS = [
    "Loan Number",
    "First Name",
    "Last Name",
    "City",
    "State",
    "Zip Code",
    "Purpose of Loan",
    "Loan Type",
    "Borrower Email",
    "Borrower Phone",
    "Co-Borrower Phone",
    "Buyers Agent Name",
    "Buyers Agent Phone",
    "Buyers Agent Email",
    "Current Milestone",
    "Investor",
    "Servicing Company Name",
    "Subject Property Occupancy Status",
    "Occupancy Type",
    "Loan Program",
    "Loan Status",
    "Loan Term",
    "Loan Officer",
    "Lock Status",
    "Entity ID",
    "Lender Name",
]

DISPLAY_FIELDS = [
    "Loan Number",
    "Loan Amount",
    "Interest Rate",
    "Loan Term",
    "First Name",
    "Last Name",
    "Address",
    "City",
    "State",
    "Zip Code",
    "Purpose of Loan",
    "Loan Type",
    "Estimated Closing Date",
    "Borrower Email",
    "Borrower Phone",
    "Co-Borrower Phone",
    "Buyers Agent Name",
    "Buyers Agent Phone",
    "Buyers Agent Email",
    "Current Milestone",
    "Rate Lock Expiry Date",
    "Investor",
    "Servicing Company Name",
    "Subject Property Occupancy Status",
    "Occupancy Type",
    "Loan Program",
    "Loan Status",
    "Fund Released Date",
    "Loan Officer",
    "Appraisal Received Date",
    "Purchase Price",
    "Lock Status",
    "Entity ID",
    "Lender Name",
]
