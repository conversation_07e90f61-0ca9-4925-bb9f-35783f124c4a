from datetime import datetime

FIELD_MAPPING = {
    "Loan.LoanFolder": "Loan Folder",
    "Fields.HMDA.X83": "Loan Term",
    "Fields.MS.FUN": "Fund Released Date",
    "Fields.2626": "Brokered or Banked Loan",
    "Fields.URLA.X73": "Address",
    "Fields.12": "City",
    "Fields.1612": "Loan Officer",
    "Fields.3968": "Loan Officer Email",
    "Fields.CX.VEL.1822": "Payroll Lead Type",
    "Fields.CX.CD.LASTDAY.TOSIGN": "Last day to sign CD",
    "Fields.3142": "Application Date",
    "Fields.CX.READY.FOR.CD.DATE": "CD ordered Date",
    "Fields.3147": "Earliest Possible Closing Date",
    "Fields.1996": "Wire Ordered",
    "Fields.2000": "Fed Ref #",
    "Fields.CX.EPO.END.DATE": "EPO Date",
    "Fields.3977": "CD Sent",
    "Fields.2278": "Committed Investor",
    "Fields.CX.DOC.Expires": "Earliest credit expiration date",
    "Fields.2354": "Appraisal Expiration Date",
    "Fields.QM.X123": "Total Points and Fees",
    "Fields.CX.QCLoan": "Loan selected for QC",
    "Fields.3197": "ITP",
    "Fields.CX.ORIG.WIRE.AMT": "Wire amount",
    "Fields.142": "Cash to Close",
    "Fields.912": "Total PITI",
    "Fields.1393": "Loan Status",
    "Fields.VEND.X293": "Broker Name",
    "Fields.1401": "Loan Program",
    "Fields.Service.X111": "Sub-Servicer Loan Number",
    "Fields.3534": "Servicing Type",
    "Fields.2370": "Purchase Advice Date",
    "Fields.682": "Loan First payment Date",
    "Fields.VEND.X378": "Customer Service Name",
    "Fields.VEND.X384": "Customer Service Phone Number",
    "Fields.VEND.X385": "Customer Service Email",
    "Fields.Service.X14": "Payment Due Date",
    "Fields.3514": "First Payment Due Investor",
    "Fields.LoanTeamMember.Name.Closer": "Closer Name",
    "Fields.1822": "Referral Partner",
    "Fields.VEND.X133": "Referral Partner",
    "Fields.LoanTeamMember.Name.Loan Processor": "Loan Processor",
    "Fields.LoanTeamMember.Name.Underwriter": "Underwriter",
    "Fields.guid": "Loan GUID",
}

# TODO: Find better solution for this when migrating to db:: Resolves case for referral partner
EXTRA_FIELDS = {"Fields.1822": "Referral Source", "Fields.VEND.X133": "Buyer's Agent"}

FIELD_TYPE_MAPPING = {
    "Fields.MS.FUN": datetime,
    "Fields.CX.CD.LASTDAY.TOSIGN": datetime,
    "Fields.CX.ORIG.WIRE.AMT": float,
    "Fields.142": float,
    "Fields.912": float,
    "Fields.3142": datetime,
    "Fields.3147": datetime,
    "Fields.1996": datetime,
    "Fields.CX.EPO.END.DATE": datetime,
    "Fields.3977": datetime,
    "Fields.2370": datetime,
    "Fields.2354": datetime,
    "Fields.3197": datetime,
    "Fields.682": datetime,
    "Fields.Service.X14": datetime,
}


SEARCH_FIELDS = [
    "Brokered or Banked Loan",
    "Address",
    "City",
    "Loan Officer",
    "Loan Officer Email",
    "Payroll Lead Type",
    "Wire Ordered",
    "Fed Ref #",
    "Committed Investor",
    "ITP",
    "Cash to Close",
    "Loan Status",
    "Broker Name",
    "Loan Program",
    "Sub-Servicer Loan Number",
    "Servicing Type",
    "Customer Service Name",
    "Customer Service Phone Number",
    "Customer Service Email",
    "First Payment Due Investor",
    "Referral Source",
    "Buyer's Agent",
    "Closer Name",
]

ACTIVE_LOAN_MILESTONES = [
    "File Started",
    "Sent to Processing",
    "Submitted",
    "Cond. Approval finished",
    "Resubmittal",
    "Approval",
    "Ready for Docs",
    "Docs Out",
    "Funding",
    "Post Close Ready",
    "Ready to Ship expected",
    "Shipping",
    "Purchasing",
    "Reconciliation",
    "Loan Opener finished",
    "Cond Approved",
    "Funding",
]


DISPLAY_FIELDS = [
    "Loan Number",
    "Loan Amount",
    "Interest Rate",
    "First Name",
    "Last Name",
    "Address",
    "City",
    "State",
    "Zip Code",
    "Purpose of Loan",
    "Loan Type",
    "Loan Officer",
    "Loan Folder",
    "Estimated Closing Date",
    "Borrower Email",
    "Borrower Phone",
    "Co-Borrower Phone",
    "Appraised Value",
    "Down Payment Amount",
    "Buyers Agent Name",
    "Buyers Agent Phone",
    "Buyers Agent Email",
    "Current Milestone",
    "Lock Date",
    "Rate Lock Expiry Date",
    "Investor",
    "Servicing Company Name",
    "Service Transfer Date",
    "Fund Released Date",
    "EPO Date",
    "Referral Source",
    "Buyer's Agent",
    "Loan Processor",
    "Underwriter",
    "Closer Name",
]
