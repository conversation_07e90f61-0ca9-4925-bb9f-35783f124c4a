from datetime import datetime

FIELD_MAPPING = {
    "Fields.364": "Loan Number",
    "Fields.2": "Loan Amount",
    "Fields.3": "Interest Rate",
    "Loan.LoanFolder": "Loan Folder",
    "Fields.4": "Loan Term",
    "Fields.4000": "First Name",
    "Fields.4002": "Last Name",
    "Fields.14": "State",
    "Fields.15": "ZIP Code",
    "Fields.19": "Purpose of Loan",
    "Fields.1172": "Loan Type",
    "Fields.1240": "Borrower Email",
    "Fields.1490": "Borrower Phone",
    "Fields.1480": "Co-borrower Phone",
    "Fields.356": "Appraised value",
    "Fields.1335": "Down Payment Amount",
    "Fields.VEND.X139": "Buyers Agent Name",
    "Fields.VEND.X140": "Buyers Agent Phone",
    "Fields.VEND.X141": "Buyers Agent Email",
    "Fields.Log.MS.CurrentMilestone": "Current Milestone",
    "Fields.761": "Lock Date",
    "Fields.762": "Rate Lock Expiry Date",
    "Fields.VEND.X263": "Investor ",
    "Fields.VEND.X178": "Servicing Company Name",
    "Fields.5": "MoPymtPI",
    "Fields.1811": "Subject Property Occupancy Status",
    "Fields.3335": "Occupancy Type",
    "Fields.1401": "Loan Program",
    "Fields.VASUMM.X23": "Credit Score",
    "Fields.353": "LTV",
    "Fields.420": "Lien Position",
    "Fields.2553": "Fund Released Date",
    "Fields.2626": "Brokered or Banked Loan",
    "Fields.11": "Address",
    "Fields.12": "City",
    "Fields.1612": "Loan Officer",
    "Fields.3968": "Loan Officer Email",
    "Fields.3142": "Application Date",
    "Fields.3147": "Earliest Possible Closing Date",
    "Fields.1996": "Wire Ordered",
    "Fields.2000": "Fed Ref #",
    "Fields.3977": "CD Sent",
    "Fields.2278": "Committed Investor",
    "Fields.2354": "Appraisal Expiration Date",
    "Fields.QM.X123": "Total Points and Fees",
    "Fields.3197": "ITP",
    "Fields.142": "Cash to Close",
    "Fields.912": "Total PITI",
    "Fields.1393": "Loan Status",
    "Fields.VEND.X293": "Broker Name",
    "Fields.Service.X111": "Sub-Servicer Loan Number",
    "Fields.3534": "Servicing Type",
    "Fields.2370": "Purchase Advice Date",
    "Fields.682": "Loan First payment date",
    "Fields.VEND.X378": "Customer Service Name",
    "Fields.VEND.X384": "Customer Service Phone Number",
    "Fields.VEND.X385": "Customer Service Email",
    "Fields.Service.X14": "Payment Due Date",
    "Fields.3514": "First Payment Due Investor",
    "Fields.LoanTeamMember.Name.Closer": "Closer Name",
    "Fields.1822": "Referral Partner",
    "Fields.VEND.X133": "Referral Partner",
    "Fields.LoanTeamMember.Name.Loan Processor": "Loan Processor",
    "Fields.LoanTeamMember.Name.Underwriter": "Underwriter",
    "Fields.guid": "Loan GUID",
    "Fields.CX.CLI.FUNDMTH": "Loan Funding Month",
    "Fields.CX.CORE.LOAN.PRO": "Core Loan Program",
    "Fields.2825": "Rate Registration Investor Name",
    "Fields.3332": "Lender Channel",
    "Fields.CX.LM.RECORD.NU": "Leadmaster Record Number",
    "Fields.2976": "Loan Info Lead Source",
    "Fields.LOANFOLDER": "Loan Folder Name",
    "Fields.Log.MS.Date.Closed": "Loan Closed Date",
}


FIELD_TYPE_MAPPING = {
    "Fields.2": float,
    "Fields.3": float,
    "Fields.356": float,
    "Fields.1335": float,
    "Fields.761": datetime,
    "Fields.762": datetime,
    "Fields.VASUMM.X23": int,
    "Fields.353": float,
    "Fields.5": float,
    "Fields.2553": datetime,
    "Fields.CX.CLI.FUNDMTH": datetime,
    "Fields.Log.MS.Date.Closed": datetime,
}


SEARCH_FIELDS = [
    "Loan Number",
    "First Name",
    "Last Name",
    "State",
    "Zip Code",
    "Purpose of Loan",
    "Loan Type",
    "Borrower Email",
    "Borrower Phone",
    "Co-Borrower Phone",
    "Buyers Agent Name",
    "Buyers Agent Phone",
    "Buyers Agent Email",
    "Current Milestone",
    "Investor",
    "Servicing Company Name",
    "Subject Property Occupancy Status",
    "Occupancy Type",
    "Loan Program",
    "Rate Registration Investor Name",
    "Address",
    "City",
    "Loan Officer",
    "Loan Officer Email",
    "Junior Loan Officer Email",
    "Loan Processor Email",
    "Junior Processor Email",
    "Referral Source",
    "Buyer's Agent",
    "Closer Name",
]

DISPLAY_FIELDS = list(FIELD_MAPPING.values())
