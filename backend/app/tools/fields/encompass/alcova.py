from datetime import datetime

FIELD_MAPPING = {
    "Fields.364": "Loan Number",
    "Fields.2": "Loan Amount",
    "Fields.3": "Interest Rate",
    "Fields.HMDA.X83": "Loan Term",
    "Fields.4000": "First Name",
    "Fields.4002": "Last Name",
    "Fields.14": "State",
    "Fields.15": "ZIP Code",
    "Fields.19": "Purpose of Loan",
    "Fields.1172": "Loan Type",
    "Fields.763": "Estimated Closing Date",
    "Fields.1240": "Borrower Email",
    "Fields.1490": "Borrower Phone",
    "Fields.1480": "Co-borrower Phone",
    "Fields.356": "Appraised value",
    "Fields.1335": "Down Payment Amount",
    "Fields.VEND.X139": "Buyers Agent Name",
    "Fields.VEND.X140": "Buyers Agent Phone",
    "Fields.VEND.X141": "Buyers Agent Email",
    "Fields.Log.MS.CurrentMilestone": "Current Milestone",
    "Fields.761": "Lock Date",
    "Fields.762": "Rate Lock Expiry Date",
    "Fields.1811": "Subject Property Occupancy Status",
    "Fields.3335": "Occupancy Type",
    "Fields.1401": "Loan Program",
    "Fields.VASUMM.X23": "Credit Score",
    "Fields.353": "LTV",
    "Fields.420": "Lien Position",
    "Fields.URLA.X73": "Address",
    "Fields.12": "City",
    "Fields.1612": "Loan Officer",
    "Fields.3968": "Loan Officer Email",
    "Fields.3142": "Application Date",
    "Fields.3147": "Earliest Possible Closing Date",
    "Fields.3977": "CD Sent",
    "Fields.142": "Cash to Close",
    "Fields.912": "Total PITI",
    "Fields.1393": "Loan Status",
    "Fields.682": "Loan First Payment Date",
    "Fields.LoanTeamMember.Name.Closer": "Closer Name",
    "Fields.1822": "Referral Partner",
    "Fields.LoanTeamMember.Name.Loan Processor": "Loan Processor",
    "Fields.LoanTeamMember.Name.Underwriter": "Underwriter",
    "Fields.CX.PC.WOD": "Wire Ordered",
    "Fields.CX.ATRQ.DOC.EXP.APPRAISAL.F": "Appraisal Conv Exp Date",
    "Fields.CX.UWD.FHA.APPRAISAL.EXP": "Appraisal FHA Exp Date",
    "Fields.CX.UWD.VA.APPRAISAL.EXP": "Appraisal VA Exp Date",
    "Fields.CX.UWD.USDA.APPRAISAL.EXP": "Appraisal USDA Exp Date",
    "Fields.CX.PC.Closed": "Fund Released Date",
    "Fields.guid": "Loan GUID",
}


FIELD_TYPE_MAPPING = {
    "Fields.2": float,
    "Fields.3": float,
    "Fields.763": datetime,
    "Fields.356": float,
    "Fields.1335": float,
    "Fields.761": datetime,
    "Fields.762": datetime,
    "Fields.VASUMM.X23": int,
    "Fields.353": float,
    "Fields.5": float,
    "Fields.CX.PC.Closed": datetime,
}


SEARCH_FIELDS = [
    "Loan Number",
    "First Name",
    "Last Name",
    "State",
    "Zip Code",
    "Purpose of Loan",
    "Loan Type",
    "Borrower Email",
    "Borrower Phone",
    "Co-Borrower Phone",
    "Buyers Agent Name",
    "Buyers Agent Phone",
    "Buyers Agent Email",
    "Current Milestone",
    "Investor",
    "Servicing Company Name",
    "Subject Property Occupancy Status",
    "Occupancy Type",
    "Loan Program",
]


DISPLAY_FIELDS = [
    "Loan Number",
    "Loan Amount",
    "Interest Rate",
    "Loan Term",
    "First Name",
    "Last Name",
    "State",
    "ZIP Code",
    "Purpose of Loan",
    "Loan Type",
    "Estimated Closing Date",
    "Borrower Email",
    "Borrower Phone",
    "Co-borrower Phone",
    "Appraised value",
    "Down Payment Amount",
    "Buyers Agent Name",
    "Buyers Agent Phone",
    "Buyers Agent Email",
    "Current Milestone",
    "Lock Date",
    "Rate Lock Expiry Date",
    "Subject Property Occupancy Status",
    "Occupancy Type",
    "Loan Program",
    "Credit Score",
    "LTV",
    "Lien Position",
    "Address",
    "City",
    "Loan Officer",
    "Loan Officer Email",
    "Application Date",
    "Earliest Possible Closing Date",
    "CD Sent",
    "Cash to Close",
    "Total PITI",
    "Loan Status",
    "Loan First Payment Date",
    "Closer Name",
    "Referral Partner",
    "Loan Processor",
    "Underwriter",
    "Wire Ordered",
    "Appraisal Conv Exp Date",
    "Appraisal FHA Exp Date",
    "Appraisal VA Exp Date",
    "Appraisal USDA Exp Date",
    "Fund Released Date",
]
