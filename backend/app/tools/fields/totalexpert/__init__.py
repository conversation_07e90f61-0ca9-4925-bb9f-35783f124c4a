import importlib
from datetime import datetime

from config import settings
from db.models import Assistant, <PERSON><PERSON><PERSON>
from db.session import session_manager
from loguru import logger
from schema.enums import AssistantSubTypes, DataFieldFolder
from sqlalchemy import select

# Find client_name and import fields
client_module = settings.CLIENT_NAME.lower().replace(" ", "_")
try:
    client_fields = importlib.import_module(f"tools.fields.totalexpert.{client_module}")
except ImportError:
    logger.warning(f"Fields for {client_module} not found! Using default fields")
    client_fields = importlib.import_module("tools.fields.totalexpert._default")


# Constants
field_type_string_mapping = {"integernumber": int, "decimalnumber": float, "date": datetime}


async def fetch_totalexpert_assistants():
    async with session_manager() as session:
        assistant_query = select(Assistant).where(Assistant.sub_type == AssistantSubTypes.TOTALEXPERT)
        assistant = await session.execute(assistant_query)
        assistant = assistant.scalars().all()
        return assistant


async def fetch_all_fields(assistant_id: str, folder: DataFieldFolder = DataFieldFolder.LOAN) -> dict:
    """Async function to fetch all fields from database"""
    async with session_manager() as session:
        # fetch all fields for this assistant
        field_query = (
            select(DataField)
            .where(
                DataField.assistant_id == assistant_id,
                DataField.folder == folder,
            )
            .order_by(DataField.display_order.asc())
        )
        result = await session.execute(field_query)
        data_fields = result.scalars().all()

        FIELD_TYPE_MAPPING = {
            field.field_id: field_type_string_mapping.get(field.type) for field in data_fields if field.type != "text"
        }
        FIELD_MAPPING = {field.field_id: field.name for field in data_fields}
        SEARCH_FIELDS = [field.name for field in data_fields if field.is_searchable]
        DISPLAY_FIELDS = [field.name for field in data_fields if field.display_in_ui]

        return FIELD_MAPPING, FIELD_TYPE_MAPPING, DISPLAY_FIELDS, SEARCH_FIELDS


ACTIVE_LOAN_STATUS_FIELD = getattr(client_fields, "ACTIVE_LOAN_STATUS_FIELD", "In Process")
CLOSED_LOAN_STATUS_FIELD = getattr(client_fields, "CLOSED_LOAN_STATUS_FIELD", "Funded")
CUSTOM_FIELD_NAME = getattr(client_fields, "CUSTOM_FIELD_NAME", "gpt_custom_message")
