from datetime import datetime

LOAN_SEARCH_FIELDS = [
    "owner_email",
    "application_date",
    "application_received_date",
    "application_sent_date",
    "appraisal_expected_date",
    "appraisal_ordered_date",
    "appraisal_received_date",
    "appraised_value",
    "approval_date",
    "approval_date_estimated",
    "available_line_amount",
    "available_principal_limit",
    "borrower_entity_type",
    "channel",
    "city",
    "closing_date",
    "closing_date_estimated",
    "completion_date",
    "county",
    "created_date",
    "debt_to_income",
    "epo_date",
    "estimated_value",
    "expected_interest_rate",
    "first_payment_date",
    "funded_date",
    "funding_date",
    "guarantee_type",
    "id",
    "investor",
    "legal_entity_name",
    "lien_position",
    "line_amount",
    "loan_amount",
    "loan_application_number",
    "loan_arm_expiration_date",
    "loan_date",
    "loan_name",
    "loan_number",
    "loan_rate",
    "loan_term",
    "loan_to_value",
    "loan_to_value_combined",
    "lock_date",
    "lock_expiration_date",
    "lock_status",
    "margin",
    "monthly_mi",
    "monthly_pi_payment",
    "occupancy_type",
    "pre_approval_issued",
    "pre_approval_issued_date",
    "property_type",
    "purchase_price",
    "purchased_date",
    "source",
    "state",
    "zip_code",
    "loan_program",
    "loan_purpose",
    "loan_status",
    "loan_type",
    "borrower_id",
    "first_name",
    "last_name",
    "email",
    "phone",
    "funded_date_start",
    "funded_date_end",
    "internal_updated_at_start",
    "internal_updated_at_end",
    "internal_created_at_start",
    "internal_created_at_end",
    "closing_disclosure_out_date",
    "max_ltv",
    "referral_source",
]

LOAN_SEARCH_CUSTOM_FIELDS = [
    "referral_source",
    "max_ltv",
    "closing_disclosure_out_date",
    "amort_type",
    "application_date",
    "application_received_date",
    "application_sent_date",
    "appraisal_expected_date",
    "appraisal_ordered_date",
    "appraisal_received_date",
    "appraised_value",
    "approval_date",
    "approval_date_estimated",
    "available_line_amount",
    "available_principal_limit",
    "borrower_entity_type",
    "channel",
    "city",
    "closing_date",
    "closing_date_estimated",
    "completion_date",
    "county",
    "created_date",
    "debt_to_income",
    "epo_date",
    "estimated_value",
    "expected_interest_rate",
    "first_payment_date",
    "funded_date",
    "funding_date",
    "guarantee_type",
    "id",
    "internal_created_at",
    "internal_updated_at",
    "investor",
    "is_first_time_buyer",
    "last_modified_date",
    "legal_entity_name",
    "lien_position",
    "line_amount",
    "loan_amount",
    "loan_application_number",
    "loan_arm_expiration_date",
    "loan_date",
    "loan_name",
    "loan_rate",
    "loan_to_value",
    "loan_to_value_combined",
    "lock_date",
    "lock_expiration_date",
    "lock_status",
    "margin",
    "monthly_mi",
    "monthly_pi_payment",
    "occupancy_type",
    "pre_approval_issued",
    "pre_approval_issued_date",
    "property_type",
    "purchase_price",
    "purchased_date",
    "source",
    "state",
    "zip_code",
    "borrower",
]

LOAN_FIELDS = [
    "address",
    "address_2",
    "adverse_action_date",
    "amort_type",
    "amort_type_arm_desc",
    "annual_review_date",
    "application_date",
    "application_received_date",
    "application_sent_date",
    "appraisal_expected_date",
    "appraisal_ordered_date",
    "appraisal_received_date",
    "appraised_value",
    "approval_date",
    "approval_date_estimated",
    "audits_date",
    "available_line_amount",
    "available_principal_limit",
    "borrower_entity_type",
    "business_partner",
    "buydown_points",
    "channel",
    "city",
    "closing_date",
    "closing_date_estimated",
    "closing_disclosure_out_date",
    "closing_disclosure_signed_date",
    "completion_date",
    "county",
    "created_date",
    "credit_report_date",
    "credit_report_ordered_date",
    "ctc_date",
    "debt_to_income",
    "docs_out_date",
    "documents_signed_date",
    "epo_date",
    "escrow_waived",
    "estimated_value",
    "expected_interest_rate",
    "external_id",
    "first_payment_date",
    "funded_date",
    "funding_date",
    "funds_requested_date",
    "guarantee_type",
    "id",
    "index",
    "internal_created_at",
    "internal_updated_at",
    "investor",
    "is_first_time_buyer",
    "last_modified_date",
    "legal_entity_name",
    "lender_paid_compensation",
    "lien_position",
    "line_amount",
    "loan_amount",
    "loan_application_number",
    "loan_arm_expiration_date",
    "loan_date",
    "loan_name",
    "loan_number",
    "loan_rate",
    "loan_term",
    "loan_to_value",
    "loan_to_value_combined",
    "lock_date",
    "lock_expiration_date",
    "lock_status",
    "margin",
    "master_loc_number",
    "max_acquisition_ltc",
    "max_ltv",
    "max_rehab_ltc",
    "monthly_mi",
    "monthly_pi_payment",
    "monthly_pi_with_mi_payment",
    "monthly_service_fee",
    "occupancy_type",
    "origination_fee",
    "partner_account",
    "post_closed_date",
    "pre_approval_expiration_date",
    "pre_approval_issued",
    "pre_approval_issued_date",
    "pricing_option",
    "principal_limit",
    "processing_start_date",
    "processing_start_date_estimated",
    "property_type",
    "purchase_price",
    "purchased_date",
    "referral_source",
    "resubmittal_date",
    "second_appraisal_ordered_date",
    "second_appraisal_received_date",
    "second_appraisal_scheduled_date",
    "second_appraisal_value",
    "source",
    "state",
    "status_updated_date",
    "tenure_payment",
    "tenure_payment_amount",
    "term_payment_amount",
    "third_party_costs",
    "underwriting_approval_date",
    "underwriting_approval_date_estimated",
    "underwriting_submission_date",
    "underwriting_submission_date_estimated",
    "upfront_mip",
    "zip_code",
    "account_class",
    "borrower",
    "loan_program",
    "loan_purpose",
    "loan_status",
    "loan_type",
]


TE_LOAN_FIELD_MAPPING = {
    "loan_number": "Loan Number",
    "loan_amount": "Loan Amount",
    "loan_rate": "Loan Rate",
    "borrower_details": "Borrower Details",
    "address": "Address",
    "city": "City",
    "state": "State",
    "zip_code": "Zip Code",
    "loan_program": "Loan Program",
    "loan_purpose": "Loan Purpose",
    "loan_status": "Loan Status",
    "loan_type": "Loan Type",
    "loan_term": "Loan Term",
    "closing_date_estimated": "Estimated Closing Date",
    "owner_email": "Owner Email",
    "owner_id": "Owner ID",
    "address_2": "Secondary Address",
    "adverse_action_date": "Adverse Action Date",
    "amort_type": "Amortization Type",
    "amort_type_arm_desc": "ARM Description",
    "annual_review_date": "Annual Review Date",
    "application_date": "Application Date",
    "application_received_date": "Application Received Date",
    "application_sent_date": "Application Sent Date",
    "appraisal_expected_date": "Appraisal Expected Date",
    "appraisal_ordered_date": "Appraisal Ordered Date",
    "appraisal_received_date": "Appraisal Received Date",
    "appraised_value": "Appraised Value",
    "approval_date": "Approval Date",
    "approval_date_estimated": "Estimated Approval Date",
    "audits_date": "Audits Date",
    "available_line_amount": "Available Line Amount",
    "available_principal_limit": "Available Principal Limit",
    "borrower_entity_type": "Borrower Entity Type",
    "business_partner": "Business Partner",
    "buydown_points": "Buydown Points",
    "channel": "Channel",
    "closing_disclosure_out_date": "Closing Disclosure Out Date",
    "closing_disclosure_signed_date": "Closing Disclosure Signed Date",
    "completion_date": "Completion Date",
    "county": "County",
    "created_date": "Created Date",
    "credit_report_date": "Credit Report Date",
    "credit_report_ordered_date": "Credit Report Ordered Date",
    "ctc_date": "CTC Date",
    "debt_to_income": "Debt to Income",
    "docs_out_date": "Docs Out Date",
    "documents_signed_date": "Documents Signed Date",
    "epo_date": "EPO Date",
    "escrow_waived": "Escrow Waived",
    "estimated_value": "Estimated Value",
    "expected_interest_rate": "Expected Interest Rate",
    "first_payment_date": "First Payment Date",
    "funded_date": "Funded Date",
    "funding_date": "Funding Date",
    "funds_requested_date": "Funds Requested Date",
    "guarantee_type": "Guarantee Type",
    "index": "Index",
    "investor": "Investor",
    "is_first_time_buyer": "Is First Time Buyer",
    "legal_entity_name": "Legal Entity Name",
    "lender_paid_compensation": "Lender Paid Compensation",
    "lien_position": "Lien Position",
    "line_amount": "Line Amount",
    "loan_application_number": "Loan Application Number",
    "loan_arm_expiration_date": "Loan ARM Expiration Date",
    "loan_date": "Loan Date",
    "loan_name": "Loan Name",
    "loan_to_value": "Loan to Value",
    "loan_to_value_combined": "Loan to Value Combined",
    "lock_date": "Lock Date",
    "lock_expiration_date": "Lock Expiration Date",
    "lock_status": "Lock Status",
    "margin": "Margin",
    "master_loc_number": "Master LOC Number",
    "max_acquisition_ltc": "Max Acquisition LTC",
    "max_ltv": "Max LTV",
    "max_rehab_ltc": "Max Rehab LTC",
    "monthly_mi": "Monthly MI",
    "monthly_pi_payment": "Monthly PI Payment",
    "monthly_pi_with_mi_payment": "Monthly PI with MI Payment",
    "monthly_service_fee": "Monthly Service Fee",
    "occupancy_type": "Occupancy Type",
    "origination_fee": "Origination Fee",
    "partner_account": "Partner Account",
    "post_closed_date": "Post Closed Date",
    "pre_approval_expiration_date": "Pre-Approval Expiration Date",
    "pre_approval_issued": "Pre-Approval Issued",
    "pre_approval_issued_date": "Pre-Approval Issued Date",
    "pricing_option": "Pricing Option",
    "principal_limit": "Principal Limit",
    "processing_start_date": "Processing Start Date",
    "processing_start_date_estimated": "Processing Start Date Estimated",
    "property_type": "Property Type",
    "purchase_price": "Purchase Price",
    "purchased_date": "Purchased Date",
    "referral_source": "Referral Source",
    "resubmittal_date": "Resubmittal Date",
    "second_appraisal_ordered_date": "Second Appraisal Ordered Date",
    "second_appraisal_received_date": "Second Appraisal Received Date",
    "second_appraisal_scheduled_date": "Second Appraisal Scheduled Date",
    "second_appraisal_value": "Second Appraisal Value",
    "source": "Source",
    "status_updated_date": "Status Updated Date",
    "tenure_payment": "Tenure Payment",
    "tenure_payment_amount": "Tenure Payment Amount",
    "term_payment_amount": "Term Payment Amount",
    "third_party_costs": "Third Party Costs",
    "underwriting_approval_date": "Underwriting Approval Date",
    "underwriting_approval_date_estimated": "Underwriting Approval Date Estimated",
    "underwriting_submission_date": "Underwriting Submission Date",
    "underwriting_submission_date_estimated": "Underwriting Submission Date Estimated",
    "upfront_mip": "Upfront MIP",
    "account_class_name": "Account Class Name",
    "account_class_type": "Account Class Type",
}

TE_LOAN_DISPLAY_FIELDS = list(TE_LOAN_FIELD_MAPPING.values())[:20]
TE_LOAN_SEARCH_FIELDS = [
    "Loan Number",
    "City",
    "State",
    "Loan Program",
    "Loan Purpose",
    "Loan Status",
    "Loan Type",
    "Loan Term",
]

LOAN_CUTOFF_FIELDS = []
LOAN_FIELD_TYPE_MAPPING = {
    "address": "string",
    "address_2": "string",
    "adverse_action_date": datetime,
    "amort_type": "string",
    "amort_type_arm_desc": "string",
    "annual_review_date": datetime,
    "application_date": datetime,
    "application_received_date": datetime,
    "application_sent_date": datetime,
    "appraisal_expected_date": datetime,
    "appraisal_ordered_date": datetime,
    "appraisal_received_date": datetime,
    "appraised_value": float,
    "approval_date": datetime,
    "approval_date_estimated": datetime,
    "audits_date": datetime,
    "available_line_amount": float,
    "available_principal_limit": float,
    "borrower_entity_type": "string",
    "business_partner": "string",
    "buydown_points": float,
    "channel": "string",
    "city": "string",
    "closing_date": datetime,
    "closing_date_estimated": datetime,
    "closing_disclosure_out_date": datetime,
    "closing_disclosure_signed_date": datetime,
    "completion_date": datetime,
    "county": "string",
    "created_date": datetime,
    "credit_report_date": datetime,
    "credit_report_ordered_date": datetime,
    "ctc_date": datetime,
    "debt_to_income": float,
    "docs_out_date": datetime,
    "documents_signed_date": datetime,
    "epo_date": datetime,
    "escrow_waived": "string",
    "estimated_value": float,
    "expected_interest_rate": float,
    "external_id": "string",
    "first_payment_date": datetime,
    "funded_date": datetime,
    "funding_date": datetime,
    "funds_requested_date": datetime,
    "guarantee_type": "string",
    "id": int,
    "index": "string",
    "internal_created_at": datetime,
    "internal_updated_at": datetime,
    "investor": "string",
    "is_first_time_buyer": "string",
    "last_modified_date": datetime,
    "legal_entity_name": "string",
    "lender_paid_compensation": float,
    "lien_position": "string",
    "line_amount": float,
    "loan_amount": float,
    "loan_application_number": "string",
    "loan_arm_expiration_date": datetime,
    "loan_date": datetime,
    "loan_name": "string",
    "loan_number": "string",
    "loan_rate": float,
    "loan_term": int,
    "loan_to_value": float,
    "loan_to_value_combined": float,
    "lock_date": datetime,
    "lock_expiration_date": datetime,
    "lock_status": "string",
    "margin": float,
    "master_loc_number": "string",
    "max_acquisition_ltc": float,
    "max_ltv": float,
    "max_rehab_ltc": float,
    "monthly_mi": float,
    "monthly_pi_payment": float,
    "monthly_pi_with_mi_payment": float,
    "monthly_service_fee": float,
    "occupancy_type": "string",
    "origination_fee": float,
    "partner_account": "string",
    "post_closed_date": datetime,
    "pre_approval_expiration_date": datetime,
    "pre_approval_issued": "string",
    "pre_approval_issued_date": datetime,
    "pricing_option": "string",
    "principal_limit": float,
    "processing_start_date": datetime,
    "processing_start_date_estimated": datetime,
    "property_type": "string",
    "purchase_price": float,
    "purchased_date": datetime,
    "referral_source": "string",
    "resubmittal_date": datetime,
    "second_appraisal_ordered_date": datetime,
    "second_appraisal_received_date": datetime,
    "second_appraisal_scheduled_date": datetime,
    "second_appraisal_value": float,
    "source": "string",
    "state": "string",
    "status_updated_date": datetime,
    "tenure_payment": "string",
    "tenure_payment_amount": float,
    "term_payment_amount": float,
    "third_party_costs": float,
    "underwriting_approval_date": datetime,
    "underwriting_approval_date_estimated": datetime,
    "underwriting_submission_date": datetime,
    "underwriting_submission_date_estimated": datetime,
    "upfront_mip": float,
    "zip_code": "string",
    "account_class": "string",
    "borrower": "string",
    "loan_program": "string",
    "loan_purpose": "string",
    "loan_status": "string",
    "loan_type": "string",
}
CONTACT_FIELDS = [
    "address",
    "address_2",
    "birthday",
    "city",
    "classification",
    "close_date",
    "creation_date",
    "credit_score",
    "credit_score_date",
    "credit_score_expiration_date",
    "email",
    "email_work",
    "employer_address",
    "employer_address_2",
    "employer_city",
    "employer_license_number",
    "employer_name",
    "employer_state",
    "employer_zip",
    "external_id",
    "fax",
    "first_name",
    "id",
    "internal_created_at",
    "internal_updated_at",
    "last_contacted_date",
    "last_modified_date",
    "last_name",
    "license_number",
    "linkedin_url",
    "list_date",
    "m_initial",
    "m_name",
    "nickname",
    "ok_to_call",
    "ok_to_email",
    "ok_to_mail",
    "other_url",
    "phone_cell",
    "phone_home",
    "phone_office",
    "pre_approval_issued_date",
    "referred_by",
    "referred_to",
    "source",
    "state",
    "suffix",
    "title",
    "zip_code",
]

CONTACT_SEARCH_FIELDS = [
    "address",
    "birthday",
    "city",
    "close_date",
    "creation_date",
    "credit_score",
    "email",
    "employer_name",
    "employer_state",
    "employer_zip",
    "external_id",
    "first_name",
    "id",
    "last_contacted_date",
    "last_name",
    "phone_cell",
    "phone_home",
    "phone_office",
    "referred_by",
    "referred_to",
    "source",
    "state",
    "title",
    "zip_code",
    "email_contains",
    "group_name",
    "internal_updated_at_start",
    "internal_updated_at_end",
    "pre_approval_issued_date",
]


TE_CONTACT_FIELD_MAPPING = {
    "id": "id",
    "title": "Title",
    "first_name": "First Name",
    "last_name": "Last Name",
    "email": "Email",
    "phone_cell": "Phone Number",
    "address": "Address",
    "city": "City",
    "state": "State",
    "zip_code": "Zip Code",
    "birthday": "Birthday",
    "source": "Source",
    "owner_id": "Owner ID",
    "owner_email": "Owner Email",
    "address_2": "Street Address Line 2",
    "close_date": "Close Date",
    "creation_date": "Creation Date",
    "credit_score": "Credit Rating",
    "credit_score_date": "Credit Score Date",
    "email_work": "Work Email",
    "employer_address": "Employer Street Address",
    "employer_address_2": "Employer Address Line 2",
    "employer_city": "Employer City",
    "employer_license_number": "Employer License Number",
    "employer_name": "Employer Name",
    "employer_state": "Employer State",
    "employer_zip": "Employer Zip Code",
    "fax": "Fax Number",
    "last_contacted_date": "Last Contacted Date",
    "license_number": "License Number",
    "linkedin_url": "LinkedIn Profile",
    "list_date": "Listing Date",
    "nickname": "Nickname",
    "ok_to_call": "Permission to Call",
    "ok_to_email": "Permission to Email",
    "ok_to_mail": "Permission to Mail",
    "other_url": "Other URL",
    "phone_home": "Home Phone Number",
    "phone_office": "Office Phone Number",
    "pre_approval_issued_date": "Pre-Approval Issue Date",
    "preferences": "Contact Preferences",
    "referred_by": "Referred By",
    "referred_to": "Referred To",
    "suffix": "Suffix",
    "website_url": "Personal Website",
}

TE_CONTACT_DISPLAY_FIELDS = list(TE_CONTACT_FIELD_MAPPING.values())[:14]

TE_CONTACT_SEARCH_FIELDS = TE_CONTACT_DISPLAY_FIELDS


CONTACT_FIELD_TYPE_MAPPING = {
    "address": "string",
    "address_2": "string",
    "birthday": datetime,
    "city": "string",
    "classification": "string",
    "close_date": datetime,
    "creation_date": datetime,
    "credit_score": float,
    "credit_score_date": datetime,
    "credit_score_expiration_date": datetime,
    "email": "string",
    "email_work": "string",
    "employer_address": "string",
    "employer_address_2": "string",
    "employer_city": "string",
    "employer_license_number": "string",
    "employer_name": "string",
    "employer_state": "string",
    "employer_zip": "string",
    "external_id": "string",
    "fax": "string",
    "first_name": "string",
    "id": int,
    "internal_created_at": datetime,
    "internal_updated_at": datetime,
    "last_contacted_date": datetime,
    "last_modified_date": datetime,
    "last_name": "string",
    "license_number": "string",
    "linkedin_url": "string",
    "list_date": datetime,
    "m_initial": "string",
    "m_name": "string",
    "nickname": "string",
    "ok_to_call": int,
    "ok_to_email": int,
    "ok_to_mail": int,
    "other_url": "string",
    "phone_cell": "string",
    "phone_home": "string",
    "phone_office": "string",
    "pre_approval_issued_date": datetime,
    "referred_by": "string",
    "referred_to": "string",
    "source": "string",
    "state": "string",
    "suffix": "string",
    "title": "string",
    "zip_code": "string",
}
CONTACT_SEARCH_CUSTOM_FIELDS = [
    "pre_approval_issued_date",
    "address",
    "birthday",
    "city",
    "close_date",
    "creation_date",
    "credit_score",
    "employer_name",
    "employer_state",
    "employer_zip",
    "external_id",
    "internal_created_at",
    "internal_updated_at",
    "last_contacted_date",
    "last_modified_date",
    "nickname",
    "ok_to_call",
    "ok_to_email",
    "ok_to_mail",
    "referred_by",
    "referred_to",
    "state",
    "title",
    "zip_code",
    "creation_date",
]
CONTACT_CUTTOFF_FIELDS = ["creation_date"]
NOTE_TYPE_IDS_MAPPING = {"general note": 1, "task": 2}

JOURNEY_PREFIXES = ["gpt"]

ACTIVITY_FIELD_MAPPINGS = {
    "title": "Title",
    "notes": "Detail",
    "email_content": "Email Content",
    "internal_created_at": "Activity Date",
    "contact_name": "Contact Name",
    "type": "Activity Type",
    "contact_id": "Contact ID",
}

ACTIVITY_DISPLAY_FIELDS = tuple(ACTIVITY_FIELD_MAPPINGS.values())
ACTIVITY_SEARCH_FIELDS = ACTIVITY_DISPLAY_FIELDS

# TE Contact group creation rules
# - Contact Group and journey name must be same and must contain "Journey" or "GPT"
# - if the journey uses custom_field, then the name of custom_field must be custom_message
# and Journey name must contain "custom"


RATE_TERM_REFI_VALID_FIELDS = {
    "loan_rate": {"name": "Loan Rate", "type": "percentage", "range": True},
    "loan_amount": {"name": "Loan Amount", "type": "currency", "range": True},
    "funded_date": {"name": "Funded Date", "type": "date", "range": True},
}

CASHOUT_REFI_VALID_FIELDS = {
    "loan_rate": {"name": "Loan Rate", "type": "percentage", "range": True},
    "loan_amount": {"name": "Loan Amount", "type": "currency", "range": True},
    "funded_date": {"name": "Funded Date", "type": "date", "range": True},
}

DEFAULT_JOURNEY_LOAN_FILTER_FIELDS = {
    "owner_email": {"name": "Owner Email", "type": "email", "range": False},
    "loan_number": {"name": "Loan Number", "type": "string", "range": False},
    "borrower_id": {"name": "Borrower ID", "type": "string", "range": False},
    "account_class_name": {"name": "Account Class Name", "type": "string", "range": False},
    "account_class_type": {"name": "Account Class Type", "type": "string", "range": False},
    "loan_type": {
        "name": "Loan Type",
        "type": "dropdown",
        "range": False,
        "values": ["Conventional", "Conventional Fixed Rate", "FHA", "VA"],  # can be fetched using API
    },
    "loan_status": {
        "name": "Loan Status",
        "type": "dropdown",
        "range": False,
        "values": ["Clear to Close", "Funded", "In Process", "New", "Pending", "Processing", "Underwriting"],
    },
    "loan_program": {
        "name": "Loan Program",
        "type": "dropdown",
        "range": False,
        "values": [
            "10-year Fixed",
            "Conventional 20 Year Fixed",
            "Conventional 30 Year Fixed",
            "FHA 10 Year Fixed",
            "FHA 20 Year Fixed",
            "FHA 30 Year Fixed",
            "VA 10 Year Fixed",
            "VA 30 Year Fixed",
        ],
    },
    "loan_purpose": {
        "name": "Loan Purpose",
        "type": "dropdown",
        "range": False,
        "values": ["Purchase", "Refinance"],
    },
    "loan_name_contains": {"name": "Loan Name Contains", "type": "string", "range": False},
    "first_name": {"name": "First Name", "type": "string", "range": False},
    "last_name": {"name": "Last Name", "type": "string", "range": False},
    "email": {"name": "Email", "type": "email", "range": False},
    "phone": {"name": "Phone", "type": "phone", "range": False},
    "loan_rate": {"name": "Loan Rate", "type": "percentage", "range": True},
    "loan_amount": {"name": "Loan Amount", "type": "currency", "range": True},
    "funded_date": {"name": "Funded Date", "type": "date", "range": True},
}


RATE_TERM_REFI_CUSTOM_FIELD_MAPPING = [
    "New Loan Amount",
    "Current Mortgage APR Rate",
    "Thirty Year Fixed Rate",
    "Thirty Year Fixed APR Rate",
    "Thirty Year Fixed Principal and Interest",
    "Thirty Year Fixed Monthly Savings",
    "Thirty Year Fixed Total Savings",
    "Twenty Five Year Fixed Rate",
    "Twenty Five Year Fixed APR Rate",
    "Twenty Five Year Fixed Principal and Interest",
    "Twenty Five Year Fixed Monthly Savings",
    "Twenty Five Year Fixed Total Savings",
    "Twenty Year Fixed Rate",
    "Twenty Year Fixed APR Rate",
    "Twenty Year Fixed Principal and Interest",
    "Twenty Year Fixed Monthly Savings",
    "Twenty Year Fixed Total Savings",
    "Fifteen Year Fixed Rate",
    "Fifteen Year Fixed APR Rate",
    "Fifteen Year Fixed Principal and Interest",
    "Fifteen Year Fixed Monthly Savings",
    "Fifteen Year Fixed Total Savings",
]


CASHOUT_REFI_CUSTOM_FIELD_MAPPING = [
    "New Loan Amount",
    # current mortgage
    "Current Mortgage APR Rate",
    "Current Mortgage Principal and Interest",
    "Current Mortgage Total Loan Amount",
    # Thirty Year Fixed
    "Thirty Year Fixed Rate",
    "Thirty Year Fixed APR Rate",
    "Thirty Year Fixed Principal and Interest",
    "Thirty Year Fixed Desired Cashout Amount",
    "Thirty Year Fixed Total Loan Amount",
    # Twenty Five Year Fixed
    "Twenty Five Year Fixed Rate",
    "Twenty Five Year Fixed APR Rate",
    "Twenty Five Year Fixed Principal and Interest",
    "Twenty Five Year Fixed Desired Cashout Amount",
    "Twenty Five Year Fixed Total Loan Amount",
    # Twenty Year Fixed
    "Twenty Year Fixed Rate",
    "Twenty Year Fixed APR Rate",
    "Twenty Year Fixed Principal and Interest",
    "Twenty Year Fixed Desired Cashout Amount",
    "Twenty Year Fixed Total Loan Amount",
    # Fifteen Year Fixed
    "Fifteen Year Fixed Rate",
    "Fifteen Year Fixed APR Rate",
    "Fifteen Year Fixed Principal and Interest",
    "Fifteen Year Fixed Desired Cashout Amount",
    "Fifteen Year Fixed Total Loan Amount",
]
