from db.models import Assistant
from db.session import session_manager
from schema.enums import AssistantSubTypes
from sqlalchemy import select


async def fetch_sidekick_assistants():
    async with session_manager() as session:
        assistant_query = select(Assistant).where(Assistant.sub_type == AssistantSubTypes.SIDEKICK)
        assistants = await session.execute(assistant_query)
        assistants = assistants.scalars().all()
        return assistants
