def get_active_loan_terms():
    terms = [
        {"canonicalName": "Loan.LoanFolder", "value": "My Pipeline", "matchType": "exact"},
    ]
    return terms


def get_closed_loan_terms():
    terms = [
        {
            "canonicalName": "Fields.CX.PC.Closed",
            "value": "",  # value doesn't matter due to matchType
            "matchType": "isNotEmpty",
        },
    ]
    return terms


def get_restricted_terms(**kwargs):
    return [
        {
            "operator": "OR",
            "terms": [
                {"canonicalName": "Loan.LoanFolder", "value": "My Pipeline", "matchType": "exact"},
                {"canonicalName": "Loan.LoanFolder", "value": "Closed_23 Loans", "matchType": "exact"},
                {"canonicalName": "Loan.LoanFolder", "value": "Closed_24 Loans", "matchType": "exact"},
                {"canonicalName": "Loan.LoanFolder", "value": "Closed_25 Loans", "matchType": "exact"},
            ],
        },
    ]


def get_locked_loan_terms():
    return []


def get_purchase_loan_terms():
    return [
        {"canonicalName": "Fields.19", "value": "Purchase", "matchType": "exact"},
    ]
