def get_active_loan_terms():
    PIPELINE_MILESTONES = (
        "Opening - Submission",
        "Pre Processing",
        "Processing",
        "Submittal",
        "Cond. Approval",
        "UTB Approved",
        "Resubmittal",
        "CTC",
        "UTB CTC",
        "Docs Out",
        "Closed",
    )
    terms = [
        {
            "operator": "OR",
            "terms": [
                {"canonicalName": "Fields.Log.MS.CurrentMilestone", "value": milestone, "matchType": "exact"}
                for milestone in PIPELINE_MILESTONES
            ],
        }
    ]
    return terms


def get_closed_loan_terms():
    terms = [
        {
            "canonicalName": "Fields.Log.MS.Date.Funded",
            "value": "",
            "matchType": "IsNotEmpty",
        }
    ]
    return terms


def get_terms_for_epo_data():
    return []


def get_restricted_terms(**kwargs):
    ALLOWED_FOLDERS = ("Completed Loans", "Employee", "Funded Loans", "Pipeline", "Prospect")
    return [
        {
            "operator": "OR",
            "terms": [
                {"canonicalName": "Fields.1393", "value": "Active Loan", "matchType": "exact"},
                {"canonicalName": "Fields.1393", "value": "Loan Originated", "matchType": "exact"},
            ],
        },
        {
            "operator": "OR",
            "terms": [
                {"canonicalName": "Loan.LoanFolder", "value": folder, "matchType": "exact"}
                for folder in ALLOWED_FOLDERS
            ],
        },
    ]


def get_locked_loan_terms():
    return []


def get_purchase_loan_terms():
    PURCHASE_MILESTONES = ("Purchased", "Reconciled", "Completion")
    return [
        {
            "operator": "OR",
            "terms": [
                {"canonicalName": "Fields.Log.MS.CurrentMilestone", "value": milestone, "matchType": "exact"}
                for milestone in PURCHASE_MILESTONES
            ],
        }
    ]
