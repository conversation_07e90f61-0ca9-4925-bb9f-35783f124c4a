from datetime import datetime

from tools.fields.encompass import ACTIVE_LOAN_MILESTONES


def get_active_loan_terms():
    terms = [
        {
            "operator": "OR",
            "terms": [
                {
                    "canonicalName": "Fields.Log.MS.CurrentMilestone",
                    "value": milestone,
                    "matchType": "exact",
                }
                for milestone in ACTIVE_LOAN_MILESTONES
            ],
        },
        {
            "canonicalName": "Loan.LoanFolder",
            "value": "My Pipeline",
            "matchType": "exact",
        },
    ]
    return terms


def get_closed_loan_terms():
    terms = [
        {
            "canonicalName": "Fields.1999",
            "value": f"{datetime.now().isoformat()}",  # value doesn't matter due to matchType
            "matchType": "isNotEmpty",
        }
    ]
    return terms


def get_terms_for_epo_data():
    epo_field = "Fields.CX.EPO"
    terms = [
        {"canonicalName": "Fields.1999", "value": "", "matchType": "IsNotEmpty"},
        {
            "canonicalName": epo_field,
            "value": datetime.now().date().isoformat(),
            "matchType": "GreaterThanOrEquals",
            "precision": "day",
        },
    ]
    return terms


def get_restricted_terms(**kwargs):
    return []


def get_locked_loan_terms():
    return []


def get_purchase_loan_terms():
    return [
        {"canonicalName": "Fields.19", "value": "Purchase", "matchType": "exact"},
    ]
