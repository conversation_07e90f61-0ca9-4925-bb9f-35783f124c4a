from datetime import datetime


def get_active_loan_terms():
    terms = [
        {"canonicalName": "Fields.1393", "value": "Active Loan", "matchType": "exact"},
    ]
    return terms


def get_closed_loan_terms():
    terms = [
        {
            "canonicalName": "Fields.MS.FUN",
            "value": "",  # value doesn't matter due to matchType
            "matchType": "isNotEmpty",
        },
        {"canonicalName": "Loan.LoanFolder", "value": "Funded", "matchType": "exact"},
    ]
    return terms


def get_terms_for_epo_data():
    epo_field = "Fields.CX.EPO.END.DATE"
    terms = [
        {"canonicalName": "Fields.MS.FUN", "value": "", "matchType": "IsNotEmpty"},
        {
            "canonicalName": epo_field,
            "value": datetime.now().date().isoformat(),
            "matchType": "GreaterThanOrEquals",
            "precision": "day",
        },
    ]
    return terms


def get_restricted_terms(**kwargs):
    return []


def get_locked_loan_terms():
    return []


def get_purchase_loan_terms():
    return [
        {"canonicalName": "Fields.19", "value": "Purchase", "matchType": "exact"},
    ]
