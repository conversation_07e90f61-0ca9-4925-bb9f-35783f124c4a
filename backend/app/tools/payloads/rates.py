def get_active_loan_terms():
    terms = [
        {
            "operator": "OR",
            "terms": [
                {
                    "canonicalName": "Fields.1393",
                    "value": "",
                    "matchType": "IsEmpty",
                },
                {
                    "canonicalName": "Fields.1393",
                    "value": "Active Loan",
                    "matchType": "exact",
                },
            ],
        }
    ]
    return terms


def get_closed_loan_terms():
    terms = [
        {
            "canonicalName": "Fields.1393",
            "value": "Loan Originated",
            "matchType": "exact",
        }
    ]
    return terms


def get_terms_for_epo_data():
    return []


def get_locked_loan_terms():
    terms = [
        {
            "operator": "OR",
            "terms": [
                {
                    "canonicalName": "Fields.1393",
                    "value": "",
                    "matchType": "IsEmpty",
                },
                {
                    "canonicalName": "Fields.1393",
                    "value": "Active Loan",
                    "matchType": "exact",
                },
            ],
        }
    ]
    return terms


def get_purchase_loan_terms():
    return [
        {"canonicalName": "Fields.19", "value": "Purchase", "matchType": "exact"},
    ]


def get_restricted_terms(**kwargs):
    lo_email = kwargs.get("lo_email")
    name = kwargs.get("name")
    loan_id = kwargs.get("loan_id")

    if not lo_email and not name and not loan_id:
        return []

    if lo_email:
        lo_email = lo_email.lower()
    if loan_id:
        loan_id = loan_id.lower()
    fields = {
        "email": [
            "Fields.LoanTeamMember.Email.Funder",
        ],
        "user_id": [
            "Fields.LoanTeamMember.UserID.File Starter",
            "Fields.LoanTeamMember.UserID.Loan Officer",
            "Fields.LoanTeamMember.UserID.Mortgage Consultant",
            "Fields.LoanTeamMember.UserID.Underwriting Coord",
            "Fields.LoanTeamMember.UserID.Underwriter",
            "Fields.LoanTeamMember.UserID.Loan Coordinator",
            "Fields.LoanTeamMember.UserID.Closer",
            "Fields.LoanTeamMember.UserID.SaleAssist",
            "Fields.LoanTeamMember.UserID.ProcessManager",
        ],
        "name": [
            "Fields.LoanTeamMember.Name.Closing Coordinator",
            "Fields.CX.COLLATERAL.UW.NAME",
            # "Fields.LoanTeamMember.Name.RegionalManager", # removed since its not in reporting database.
            # "Fields.LoanTeamMember.Name.RegionalOpsManager", # Removed since its not in reporting database.
            # "Fields.LoanTeamMember.Name.DivisionalOpsManager", # Removed since its not in reporting database
        ],
    }

    terms = []
    terms += [{"canonicalName": field, "value": lo_email, "matchType": "exact"} for field in fields["email"]]
    if loan_id:
        terms += [{"canonicalName": field, "value": loan_id, "matchType": "exact"} for field in fields["user_id"]]
    if name:
        terms += [{"canonicalName": field, "value": name, "matchType": "exact"} for field in fields["name"]]

    return [{"operator": "OR", "terms": terms}]
