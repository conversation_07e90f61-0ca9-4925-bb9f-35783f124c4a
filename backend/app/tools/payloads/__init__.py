import importlib

from config import settings
from loguru import logger

# find client_name and import fields
client_module = settings.CLIENT_NAME.lower().replace(" ", "_")
try:
    client_payloads = importlib.import_module(f"tools.payloads.{client_module}")
except ImportError:
    logger.warning(f"File {client_module} not found! Using default payloads")
    from tools.payloads import _default as client_payloads


def get_active_loan_terms():
    return client_payloads.get_active_loan_terms()


def get_closed_loan_terms():
    return client_payloads.get_closed_loan_terms()


def get_terms_for_epo_data():
    return client_payloads.get_terms_for_epo_data()


def get_restricted_terms(**kwargs):
    return client_payloads.get_restricted_terms(**kwargs)


def get_locked_loan_terms():
    return client_payloads.get_locked_loan_terms()


def get_purchase_loan_terms():
    return client_payloads.get_purchase_loan_terms()
