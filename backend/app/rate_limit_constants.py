from enum import Enum

from pydantic import BaseModel


class RateLimitScope(str, Enum):
    USER = "user"
    IP = "ip"


class RateLimitRule(BaseModel):
    path: str
    limit: int
    period: int
    scope: RateLimitScope


# List of rate-limited rules.
# Each rule is a dict: { 'path': str, 'limit': int, 'period': int, 'scope': str }
# - 'path' can be exact, wildcard (e.g., /api/v1/*/stream/), or dynamic (e.g., /api/v1/{id}/stream/)
# - 'limit' is the max requests per period
# - 'period' is the window in seconds
# - 'scope' is either 'user' (rate limit by user id) or 'ip' (rate limit by IP address)
#
# NOTE: Path matching ignores query parameters. Only use the path part (e.g., '/api/user/') in rules.
# For per-user limits, ensure user_id is available in request.state or headers.
# For per-IP fallback, logic is in rate_limit.py

RATE_LIMITED_RULES: list[RateLimitRule] = [
    RateLimitRule(
        path="/api/user/me/", limit=10, period=60, scope=RateLimitScope.IP
    ),  # Limit to 10 requests per minute per IP address (exact path match)
    RateLimitRule(
        path="/api/v1/{id}/stream/", limit=7, period=60, scope=RateLimitScope.USER
    ),  # Limit to 7 requests per minute per user for specific stream endpoints using dynamic {id}
    RateLimitRule(
        path="/api/analytics/recent-messages/*", limit=10, period=60, scope=RateLimitScope.USER
    ),  # Limit to 10 requests every 1 minutes per user for any endpoint under /api/analytics/
    RateLimitRule(
        path="/api/chat/*/stream/", limit=7, period=60, scope=RateLimitScope.USER
    ),  # Limit to 7 requests per minute per user
]
