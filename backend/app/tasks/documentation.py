import asyncio
from datetime import datetime

from celery_app import celery_app
from db.models import Documentation
from db.session import session_manager
from hooks.pinecone_hook import get_generic_hook
from loguru import logger
from sqlalchemy import select


@celery_app.task(bind=True, max_retries=3)
def delete_expired_documentations(self):
    try:
        loop = asyncio.get_event_loop()
        result = loop.run_until_complete(a_delete_expired_documentations())
        return result
    except Exception as e:
        logger.error(f"Error deleting expired documentations: {e}")
        self.retry(countdown=10)


async def a_delete_expired_documentations():
    async with session_manager() as session:
        query = select(Documentation).where(Documentation.expiry_date <= datetime.now())
        result = await session.execute(query)
        documents = result.scalars().all()

        for document in documents:
            await session.delete(document)
            await session.commit()
            generic_hook = await get_generic_hook(document.assistant_id)
            await generic_hook.delete_document(document.id, namespace=document.assistant_id)

    logger.info(f"Deleted {len(documents)} expired documentations")
