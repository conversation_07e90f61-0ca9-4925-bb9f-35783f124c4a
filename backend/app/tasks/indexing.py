import asyncio
import os
from pathlib import Path

import magic
from celery_app import celery_app
from config import settings
from db.models import Document, Documentation, QuestionAnswer, WebsiteContent
from db.session import session_manager
from hooks.openai_hook import oai_hook
from hooks.pinecone_hook import get_generic_hook
from hooks.s3 import get_s3_hook
from hooks.splade_hook import splade_embedder
from hooks.voyage_hook import voyage_embedder
from html2text import html2text
from langchain.text_splitter import RecursiveCharacterTextSplitter
from loguru import logger
from pdfid import pdfid
from pinecone import SparseValues, Vector
from schema.enums import DocumentIndexStatus, DocumentTypes
from sqlalchemy import select
from sqlalchemy.orm import selectinload
from unstructured.staging.base import dict_to_elements
from unstructured_client import UnstructuredClient
from unstructured_client.models import shared
from unstructured_client.models.errors import SDKError
from utils.indexing import Chunk, ElementType, clean_data, create_chunks

unstructured_client = UnstructuredClient(
    api_key_auth=settings.UNSTRUCTURED_API_KEY,
    server_url=settings.UNSTRUCTURED_API_URL,
)


@celery_app.task(bind=True, max_retries=3)
def process_document(self, document_id):
    try:
        loop = asyncio.get_event_loop()
        result = loop.run_until_complete(a_process_document(document_id))
        return result
    except Exception as e:
        logger.error(f"Error processing document: {e}")
        self.retry(countdown=10)


async def a_process_document(document_id):
    async with session_manager() as session:
        query = select(Document).where(Document.id == document_id)
        result = await session.execute(query)
        document = result.scalar_one_or_none()

        if document is None:
            return f"Document {document_id} not found"
        logger.info(f"Processing document: {document.id}")

        text, is_indexed, index_status = await index_document(document, namespace=document.assistant_id)

        document.text = text
        document.is_indexed = is_indexed
        document.index_status = index_status
        await session.commit()
        await session.refresh(document)
    return f"Document {document_id} processed"


async def index_document(document: Document, namespace: str) -> tuple[str, bool]:
    link = document.link

    async def summarize_text(text: str) -> str:
        message = [
            {
                "role": "user",
                "content": (
                    "Generate a concise summary in markdown format for this table, "
                    "highlighting key points suitable for indexing:\n\n" + text
                ),
            }
        ]
        summary = await oai_hook.create_completion(messages=message)
        return summary

    def is_pdf(content: bytes, filename: str) -> bool:
        ext = Path(filename).suffix.lower().lstrip(".")
        mime = magic.Magic(mime=True).from_buffer(content[:4096])
        return ext == "pdf" or mime == "application/pdf"

    def analyze_and_disarm_pdf(s3_hook, buffer: bytes, filename: str, key: str) -> bytes:
        options = pdfid.get_fake_options()
        options.scan = True
        options.json = True

        # Analyze for threats
        analysis = pdfid.PDFiDMain([filename], options, [buffer])["reports"]

        suspicious_keys = [
            "/JS",
            "/JavaScript",
            "/AA",
            "/OpenAction",
            "/AcroForm",
            "/JBIG2Decode",
            "/RichMedia",
            "/Launch",
            "/EmbeddedFile",
        ]
        is_malicious = any(key in analysis[0] and int(analysis[0][key]) > 0 for key in suspicious_keys)
        if is_malicious:
            # Disarm it
            logger.info(f"Malicious PDF detected: {filename}, disarming...")
            disarm_opts = pdfid.get_fake_options()
            disarm_opts.disarm = True
            disarm_opts.return_disarmed_buffer = True
            disarmed = pdfid.PDFiDMain([filename], disarm_opts, [buffer])
            content = disarmed["buffers"][0]
            disarmed_filename = f"{Path(filename).stem}.disarmed.pdf"
            if os.path.exists(disarmed_filename):
                os.remove(disarmed_filename)
            # upload disarmed PDF to S3
            s3_hook.put_object(
                object_name=key,
                file=content,
                content_type="application/pdf",
            )
            logger.info(f"Disarmed PDF uploaded to S3: {filename}")

            return content  # return sanitized PDF bytes
        else:
            logger.info(f"No threats detected in PDF: {filename}")

        return buffer  # no threats, return original

    try:
        if document.type != DocumentTypes.SHAREPOINT_PAGE:
            s3_hook = await get_s3_hook(document.assistant_id)
            key = s3_hook.get_key_from_url(link)
            logger.info(f"Downloading document: {key}")
            content = s3_hook.download_and_read_object_tempfile(key)
            if is_pdf(content, key) and document.type in [DocumentTypes.SHAREPOINT, DocumentTypes.SHAREPOINT_IMAGE]:
                logger.info(f"Analyzing PDF for threats: {key}")
                filename = Path(key).name
                content = analyze_and_disarm_pdf(s3_hook, buffer=content, filename=filename, key=key)
        else:
            content = document.text
            key = f"sharepoint_page_{document.id}.html"
        files = shared.Files(content=content, file_name=key)
        req = shared.PartitionParameters(files=files, strategy="auto", include_page_breaks=True, split_pdf_page=True)
        logger.info(f"Processing document: {document.id} with unstructured API")
        try:
            resp = unstructured_client.general.partition(req)
        except AttributeError as e:
            logger.warning(f"AttributeError occurred with auto strategy, switching to fast: {e}")
            req = shared.PartitionParameters(files=files, strategy="fast")
            resp = unstructured_client.general.partition(req)
        except SDKError as e:
            logger.error(f"Error creating partition parameters: {e}")
            raise e

        logger.info(f"Unstructured processing completed for document: {document.id}")
        text = ""
        # Prepare data for indexing
        metadata = {
            "link": link,
            "type": document.type,
            "title": document.title,
            "document_id": document.id,
            "assistant_id": document.assistant_id,
            "categories": document.categories or [],
        }

        elements = dict_to_elements(resp.elements)
        chunks: list[Chunk] = create_chunks(elements)
        logger.info(f"{len(chunks)} chunks created for document: {document.id}")

        # Filter chunks with less than 40 characters
        chunks: list[Chunk] = [chunk for chunk in chunks if len(chunk.text) >= 40]
        logger.info(f"{len(chunks)} chunks after filtering for document: {document.id}")

        # Generate summary of table chunks
        table_chunks = [chunk for chunk in chunks if chunk.metadata.element_type == ElementType.table]
        if table_chunks:
            logger.info(f"Generating summary for {len(table_chunks)} table chunks")
            chunk_summaries = await asyncio.gather(*(summarize_text(chunk.text) for chunk in table_chunks))
            for chunk, summary in zip(table_chunks, chunk_summaries):
                chunk.metadata.summary = summary
                logger.info(f"Summary for chunk: {chunk.metadata.summary[:50]}...")

        logger.info(f"Indexing document: {document.id} with {len(chunks)} chunks")

        N = settings.CHUNK_SIZE
        chunks: list[list[Chunk]] = [chunks[i : i + N] for i in range(0, len(chunks), N)]  # noqa: E203
        vectors = []
        logger.info(f"Generating dense embeddings {N} chunks...")
        for j, chunk in enumerate(chunks):
            texts: list[str] = []
            for d in chunk:
                if d.metadata.element_type == ElementType.text:
                    texts.append(d.text)
                elif d.metadata.element_type == ElementType.table:
                    texts.append(d.metadata.summary)

            embs = await voyage_embedder.get_embedding(texts=texts, input_type="document")

            sparse_embs = await splade_embedder.encode_documents(texts)

            for i, (d, e, se) in enumerate(zip(chunk, embs, sparse_embs)):
                chunk_metadata = {**d.metadata.model_dump(mode="json"), **metadata}
                chunk_metadata = {k: v for k, v in chunk_metadata.items() if v is not None}
                chunk_metadata["text"] = d.text
                indices = se["indices"]
                values = se["values"]
                if not indices or not values:
                    indices = [0]
                    values = [0.0]
                sparse = SparseValues(indices=indices, values=values)
                text += d.text
                vec = Vector(
                    id=f"{document.id}#{j}_{i}",
                    values=e,
                    sparse_values=sparse,
                    metadata=chunk_metadata,
                )
                vectors.append(vec)

        generic_hook = await get_generic_hook(document.assistant_id)
        await generic_hook.upsert_document(vectors, namespace=namespace)
        logger.info(f"Len of Vectors Indexed: {len(vectors)} ")
        logger.info(f"Document {document.id} indexed in namespace: {namespace}")

        is_indexed = True
        index_status = DocumentIndexStatus.INDEXED

    except Exception as e:
        logger.exception(f"Error processing document: {e}")
        text = ""
        is_indexed = False
        index_status = DocumentIndexStatus.FAILED

    return text, is_indexed, index_status


@celery_app.task(bind=True, max_retries=3)
def index_question_answer(self, qa_id):
    try:
        loop = asyncio.get_event_loop()
        result = loop.run_until_complete(a_index_question_answer(qa_id))
        return result
    except Exception as e:
        logger.error(f"Error processing question answer: {e}")
        self.retry(countdown=10)


async def a_index_question_answer(qa_id):
    async with session_manager() as session:
        query = select(QuestionAnswer).where(QuestionAnswer.id == qa_id)
        result = await session.execute(query)
        qa = result.scalar_one_or_none()

        if qa is None:
            return f"Question Answer {qa_id} not found"

        logger.info(f"Processing question answer: {qa.id}")

        # Index question then index answer+question
        question = qa.question
        answer = qa.answer
        qa_text = f"**{question}**\n{answer}"
        embs = await voyage_embedder.get_embedding(texts=[qa_text], input_type="document")
        sparse_embs = await splade_embedder.encode_documents([qa_text])
        vectors = []
        for i, text in enumerate([qa_text]):
            sparse = sparse_embs[i]
            indices = sparse["indices"]
            values = sparse["values"]
            if not indices or not values:
                indices = [0]
                values = [0.0]
            sparse = SparseValues(indices=indices, values=values)
            vec = Vector(
                id=f"{qa.id}#{i}",
                values=embs[i],
                sparse_values=sparse,
                metadata={
                    "qa_id": qa.id,
                    "text": qa_text,
                    "assistant_id": qa.assistant_id,
                    "categories": qa.categories or [],
                    "type": "question" if i == 0 else "question_answer",
                },
            )
            vectors.append(vec)
        generic_hook = await get_generic_hook(qa.assistant_id)
        await generic_hook.upsert_document(vectors, namespace=qa.assistant_id)

        qa.is_indexed = True
        await session.commit()
        logger.info(f"Question Answer {qa_id} processed")

    return qa.id, qa.is_indexed


@celery_app.task(bind=True, max_retries=3)
def index_documentation(self, doc_id):
    try:
        loop = asyncio.get_event_loop()
        result = loop.run_until_complete(a_index_documentation(doc_id))
        return result
    except Exception as e:
        logger.error(f"Error processing documentation: {e}")
        self.retry(countdown=10)


async def a_index_documentation(doc_id):
    async with session_manager() as session:
        query = select(Documentation).where(Documentation.id == doc_id)
        result = await session.execute(query)
        doc = result.scalar_one_or_none()

        if doc is None:
            return f"Documentation {doc_id} not found"

        logger.info(f"Processing documentation: {doc.id}")

        # Clean text
        doc_text = clean_data(f"{doc.title}\n\n{doc.content}")
        doc_text = html2text(doc_text)

        # Split text into chunks
        splitter = RecursiveCharacterTextSplitter.from_tiktoken_encoder(
            model_name=settings.TOKENIZER_MODEL,
            chunk_size=settings.CHUNK_LENGTH,
            chunk_overlap=settings.CHUNK_OVERLAP,
            separators=["\n\n", "\n", ".", " ", ""],
            keep_separator=True,
        )

        chunks = splitter.split_text(doc_text)

        # Prepare data for indexing
        metadata = {
            "documentation_id": doc.id,
            "title": doc.title,
            "categories": doc.categories or [],
            "assistant_id": doc.assistant_id,
        }

        N = settings.CHUNK_SIZE
        chunks = [chunks[i : i + N] for i in range(0, len(chunks), N)]  # noqa: E203
        vectors = []
        for j, chunk in enumerate(chunks):
            embs = await voyage_embedder.get_embedding(texts=chunk, input_type="document")
            sparse_embs = await splade_embedder.encode_documents(chunk)
            for i, (d, e, se) in enumerate(zip(chunk, embs, sparse_embs)):
                logger.info(f"Indexing chunk {i} with text: {d[:50]}...")
                chunk_metadata = metadata.copy()
                chunk_metadata["text"] = f"## {doc.title}\n\n{d}"
                indices = se["indices"]
                values = se["values"]
                if not indices or not values:
                    indices = [0]
                    values = [0.0]
                sparse = SparseValues(indices=indices, values=values)
                vec = Vector(
                    id=f"{doc.id}#{j}_{i}",
                    values=e,
                    sparse_values=sparse,
                    metadata=chunk_metadata,
                )
                vectors.append(vec)

        generic_hook = await get_generic_hook(doc.assistant_id)
        await generic_hook.upsert_document(vectors, namespace=doc.assistant_id)

        doc.is_indexed = True
        await session.commit()
        logger.info(f"Len of Vectors Indexed: {len(vectors)} ")
        logger.info(f"Documentation {doc.id} indexed")

        return doc.id, doc.is_indexed


@celery_app.task(bind=True, max_retries=3)
def remove_categories_from_related_models(self, assistant_id, removed_categories, assistant_categories):
    try:
        loop = asyncio.get_event_loop()
        result = loop.run_until_complete(
            a_remove_categories_from_related_models(assistant_id, removed_categories, assistant_categories)
        )
        return result
    except Exception as e:
        logger.error(f"Error processing question answer: {e}")
        self.retry(countdown=10)


async def a_remove_categories_from_related_models(
    assistant_id: str, removed_categories: list[str], assistant_categories: list[str]
):
    """
    Remove the specified categories from Document, Documentation, QuestionAnswer, and WebsiteContent models.

    Args:
        removed_categories (List[str]): The categories to be removed.
        session (AsyncSession): The SQLAlchemy asynchronous session.
    """
    async with session_manager() as session:
        models = [Document, Documentation, QuestionAnswer, WebsiteContent]
        for model in models:
            stmt = select(model).options(selectinload(model.assistant)).where(model.assistant_id == assistant_id)
            result = await session.execute(stmt)
            instances = result.scalars().all()

            for instance in instances:
                if instance.categories is not None:
                    instance.categories = [
                        category
                        for category in instance.categories
                        if category not in removed_categories and category in assistant_categories
                    ]
                    if not instance.categories:
                        instance.categories = assistant_categories
                    session.add(instance)
                    # update pinecone metadata
                    generic_hook = await get_generic_hook(assistant_id)
                    await generic_hook.update_metadata(
                        id=str(instance.id), categories=instance.categories, namespace=str(assistant_id)
                    )
                    await session.commit()


@celery_app.task(bind=True, max_retries=3)
def delete_documents(self, assistant_id: str, doc_ids: list[str]):
    try:
        loop = asyncio.get_event_loop()
        result = loop.run_until_complete(a_delete_documents_task(assistant_id, doc_ids))
        return result
    except Exception as e:
        logger.error(f"Error deleting documents: {e}")
        self.retry(countdown=10)


async def a_delete_documents_task(assistant_id: str, doc_ids: list[str]):
    async with session_manager() as session:
        query = select(Document).where(Document.assistant_id == assistant_id).where(Document.id.in_(doc_ids))
        documents = await session.execute(query)
        documents = documents.scalars().all()

        for document in documents:
            await session.delete(document)

            if document.type != DocumentTypes.SHAREPOINT_PAGE:
                # Delete from S3
                s3_hook = await get_s3_hook(document.assistant_id)
                object_name = s3_hook.get_key_from_url(document.link)
                s3_hook.delete_object(object_name=object_name)

            if document.type in [DocumentTypes.IMAGE, DocumentTypes.SHAREPOINT_IMAGE]:
                continue

            # Delete from Pinecone
            generic_hook = await get_generic_hook(assistant_id)
            await generic_hook.delete_document(document.id, namespace=document.assistant_id)

        await session.commit()
    logger.info(f"Documents deleted successfully: {doc_ids}")
    return {"message": f"Documents deleted successfully: {doc_ids}"}


@celery_app.task(bind=True, max_retries=3)
def process_user_document(self, document_id, user_id):
    try:
        loop = asyncio.get_event_loop()
        result = loop.run_until_complete(a_process_user_document(document_id, user_id))
        return result
    except Exception as e:
        logger.error(f"Error processing document: {e}")
        self.retry(countdown=10)


async def a_process_user_document(document_id, user_id):
    async with session_manager() as session:
        query = select(Document).where(Document.id == document_id)
        result = await session.execute(query)
        document = result.scalar_one_or_none()

        if document is None:
            return f"Document {document_id} not found"
        logger.info(f"Processing document: {document.id}")

        text, is_indexed, index_status = await index_document(document, namespace=f"{document.assistant_id}_{user_id}")

        document.text = text
        document.is_indexed = is_indexed
        document.index_status = index_status
        await session.commit()

    return f"Document {document_id} processed"
