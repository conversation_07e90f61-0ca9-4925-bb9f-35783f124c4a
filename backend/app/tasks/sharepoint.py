import asyncio
from datetime import datetime

from celery import chain, group
from celery_app import celery_app
from db.models import Document, SharepointFolder
from db.session import session_manager
from hooks.connectors.sharepoint_hook import sharepoint_hook
from loguru import logger
from schema.enums import DocumentIndexStatus, DocumentTypes
from sqlalchemy import and_, select
from tasks.indexing import process_document
from utils.sharepoint import (
    handle_missing_file,
    handle_modified_file,
    list_new_files,
    upload_and_index_new_file,
    valid_file_mime_types,
    valid_image_mime_types,
)


@celery_app.task(bind=True, max_retries=3)
def sync_sharepoint(self):
    try:
        loop = asyncio.get_event_loop()
        result = loop.run_until_complete(a_sync_sharepoint_task())
        return result
    except Exception as e:
        logger.error(f"Error syncing sharepoint files: {e}")
        self.retry(countdown=10)


async def a_sync_sharepoint_task():
    await sharepoint_hook.get_access_token()
    async with session_manager() as session:
        try:
            query = select(Document).where(
                and_(
                    Document.type.in_([DocumentTypes.SHAREPOINT, DocumentTypes.SHAREPOINT_IMAGE]),
                    Document.meta.op("->")("id").isnot(None),
                )
            )
            result = await session.execute(query)
            documents = result.scalars().all()

            for document in documents:
                try:
                    stored_date = document.meta.get("lastModifiedDateTime", "")
                    file = await sharepoint_hook.get_file(
                        document.meta["site_id"], document.meta["drive_id"], document.meta["id"]
                    )
                    if file:
                        if stored_date != file.get("lastModifiedDateTime", ""):
                            sharepoint_file = {
                                "id": file["id"],
                                "lastModifiedDateTime": file["lastModifiedDateTime"],
                                "name": file["name"],
                                "mimeType": file["file"]["mimeType"],
                                "fullpath": document.meta["fullpath"],
                            }
                            document_id = await handle_modified_file(
                                document, sharepoint_file, document.assistant_id, session, None
                            )
                            if document_id:
                                process_document.delay(document_id)
                    else:
                        await handle_missing_file(document, document.assistant_id, session)

                except Exception as e:
                    logger.error(f"Error syncing document {document.title}: {e}")
                    raise

            folder_query = select(SharepointFolder)
            folder_result = await session.execute(folder_query)
            folders = folder_result.scalars().all()
            for folder in folders:
                try:
                    valid_mime_types = (
                        valid_image_mime_types
                        if folder.type == DocumentTypes.SHAREPOINT_IMAGE
                        else valid_file_mime_types
                    )
                    stored_date = folder.updated_at
                    new_files = await list_new_files(folder, stored_date, valid_mime_types)
                    tasks = []
                    for file in new_files:
                        file_dict = file.model_dump()
                        meta = {**folder.meta, **file_dict}
                        task_chain = chain(
                            upload_and_index_documents.s(
                                file_dict,
                                folder.assistant_id,
                                sharepoint_hook.access_token,
                                folder.meta["user_info"],
                                folder.categories,
                                folder.type,
                                meta,
                            ),
                            process_document.s(),
                        )
                        tasks.append(task_chain)
                    if tasks:
                        group(tasks)()
                    folder.updated_at = datetime.now()
                    session.add(folder)
                    await session.commit()
                    await session.refresh(folder)

                except Exception as e:
                    logger.error(f"Error syncing folder {folder.folder_id}: {e}")
                    raise

            return {"message": "Documents synced"}

        except Exception as e:
            logger.error(f"Failed to sync documents: {e}")


@celery_app.task(bind=True, max_retries=3)
def upload_and_index_documents(
    self, file_dict, assistant_id, sharepoint_token, user_info, categories, type, meta
) -> str:
    try:
        loop = asyncio.get_event_loop()
        result = loop.run_until_complete(
            a_upload_and_index_documents_task(
                file_dict, assistant_id, sharepoint_token, user_info, categories, type, meta
            )
        )
        return result
    except Exception as e:
        logger.error(f"Error uploading and indexing documents: {e}")
        self.retry(countdown=10)


async def a_upload_and_index_documents_task(
    file_dict, assistant_id, sharepoint_token, user_info, categories, type, meta
):
    await sharepoint_hook.set_access_token(sharepoint_token)
    is_indexed, is_modified, document = await sharepoint_hook.check_is_indexed(
        assistant_id, file_dict["id"], file_dict["lastModifiedDateTime"], type
    )

    async with session_manager() as session:
        if is_indexed:
            if not is_modified:
                return None
            else:
                document_id = await handle_modified_file(document, file_dict, assistant_id, session, user_info)
                return document_id
        else:
            document_id = await upload_and_index_new_file(file_dict, assistant_id, session, categories, meta)
            return document_id


@celery_app.task(bind=True, max_retries=3)
def mark_folder_indexed(self, results, folder_id, assistant_id, type):
    try:
        loop = asyncio.get_event_loop()
        result = loop.run_until_complete(a_mark_folder_indexed_task(folder_id, assistant_id, type))
        return result
    except Exception as e:
        logger.error(f"Error marking folder indexed: {e}")
        self.retry(countdown=10)


async def a_mark_folder_indexed_task(folder_id, assistant_id, type):
    async with session_manager() as session:
        folder_query = select(SharepointFolder).where(
            and_(
                SharepointFolder.folder_id == folder_id,
                SharepointFolder.assistant_id == assistant_id,
                SharepointFolder.type == type,
            )
        )
        result = await session.execute(folder_query)
        folder = result.scalars().first()
        folder.is_indexed = True
        folder.index_status = DocumentIndexStatus.INDEXED
        folder.type = type
        session.add(folder)
        await session.commit()
        return {"status": "success", "message": "Folder marked as indexed"}


@celery_app.task(bind=True, max_retries=3)
def upload_and_index_page(self, page_dict, assistant_id, sharepoint_token, user_info, categories, meta) -> str:
    try:
        loop = asyncio.get_event_loop()
        result = loop.run_until_complete(
            a_upload_and_index_page_task(page_dict, assistant_id, sharepoint_token, user_info, categories, meta)
        )
        return result
    except Exception as e:
        logger.error(f"Error uploading and indexing page: {e}")
        self.retry(countdown=10)


async def a_upload_and_index_page_task(page_dict, assistant_id, sharepoint_token, user_info, categories, meta):
    await sharepoint_hook.set_access_token(sharepoint_token)
    await sharepoint_hook.initialize_redis()

    # Check if the page is already indexed
    is_indexed, is_modified, document = await sharepoint_hook.check_is_indexed(
        assistant_id, page_dict["id"], page_dict.get("lastModifiedDateTime", ""), DocumentTypes.SHAREPOINT_PAGE
    )

    async with session_manager() as session:
        if is_indexed:
            if not is_modified:
                return None
            else:
                # Page has been modified, fetch latest content and update
                result = await sharepoint_hook.get_page_content(
                    user_info["user_id"], assistant_id, None, page_dict["site_id"], page_dict["id"], None
                )
                page_content = result.get("page_content", {})

                document.title = page_dict.get("title", "")
                document.link = page_dict.get("webUrl", "")
                document.text = str(page_content.get("text", {}))
                document.is_indexed = False
                document.index_status = DocumentIndexStatus.PENDING
                if user_info is not None:
                    if document.meta:
                        document.meta = {
                            **document.meta,
                            "user_info": {**document.meta["user_info"], "updated_by": user_info},
                        }
                    else:
                        document.meta = {"user_info": {"updated_by": user_info}}

                document.meta["lastModifiedDateTime"] = page_dict["lastModifiedDateTime"]

                session.add(document)
                await session.commit()
                await session.refresh(document)

                from hooks.pinecone_hook import get_generic_hook

                generic_hook = await get_generic_hook(assistant_id)
                await generic_hook.delete_document(document.id, namespace=document.assistant_id)
                return str(document.id)
        else:
            # New page, fetch content and create a new document
            result = await sharepoint_hook.get_page_content(
                user_info["user_id"], assistant_id, None, page_dict["site_id"], page_dict["id"], None
            )
            page_content = result.get("page_content", {})

            new_document = Document(
                assistant_id=assistant_id,
                title=page_dict.get("title", ""),
                type=DocumentTypes.SHAREPOINT_PAGE,
                text=str(page_content.get("text", {})),
                link=page_dict.get("webUrl", ""),
                is_indexed=False,
                index_status=DocumentIndexStatus.PENDING,
                categories=categories,
                meta=meta,
            )
            session.add(new_document)
            await session.commit()
            await session.refresh(new_document)
            return str(new_document.id)
