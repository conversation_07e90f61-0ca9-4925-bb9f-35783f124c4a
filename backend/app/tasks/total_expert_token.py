import asyncio
from datetime import datetime, timedelta

from celery_app import celery_app
from db.models import TotalExpertToken
from db.session import session_manager
from loguru import logger
from sqlalchemy import select
from tools.utils.total_expert_token import TE_REFRESH_TOKEN_THRESHOLD, TotalExpertTokenManager


@celery_app.task(bind=True, max_retries=3)
def refresh_expiring_tokens(self):
    """Sync Celery wrapper for async token refresh logic"""
    asyncio.run(_refresh_expiring_tokens())


async def _refresh_expiring_tokens():
    """
    Background task to refresh Total Expert tokens that are about to expire
    Runs to check for tokens that need refresh
    """
    try:
        async with session_manager() as session:
            expiry_threshold = datetime.utcnow() + timedelta(seconds=TE_REFRESH_TOKEN_THRESHOLD)
            query = select(TotalExpertToken).where(TotalExpertToken.refresh_token_expires_at <= expiry_threshold)
            result = await session.execute(query)
            tokens: list[TotalExpertToken] = result.scalars().all()
            logger.info(f"Starting refresh task for {len(tokens)} tokens")

            if not tokens:
                logger.info("No Total Expert tokens need refresh")
                return

            token_manager = TotalExpertTokenManager(session)
            await token_manager.initialize()
            refreshed_count = 0

            for token in tokens:
                try:
                    await token_manager.refresh_token_if_needed(token)
                    refreshed_count += 1
                except Exception as e:
                    logger.error(f"Error refreshing token for user {token.user_id}: {str(e)}")
                    continue

            logger.info(f"Refreshed {refreshed_count} Total Expert tokens")

    except Exception as e:
        logger.error(f"Error in refresh_expiring_tokens task: {str(e)}")
        raise
