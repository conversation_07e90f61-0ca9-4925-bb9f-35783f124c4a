import asyncio
from collections import defaultdict
from datetime import datetime
from urllib.parse import urlparse

from celery.schedules import crontab
from celery_app import celery_app
from db.models import WebsiteContent
from db.session import session_manager
from fastapi import status
from hooks.pinecone_hook import get_generic_hook
from hooks.website_hook import website_hook
from httpx import HTTPStatusError
from loguru import logger
from redbeat import RedBeatScheduler, RedBeatSchedulerEntry
from sqlalchemy import select
from sqlalchemy.orm import selectinload
from utils.website import clean_markdown, get_redbeat_key_prefix, get_schedules, index_website


@celery_app.task(bind=True, max_retries=3)
def index_website_content(self, website_id, sub_links=[], is_update=False):
    try:
        loop = asyncio.get_event_loop()
        result = loop.run_until_complete(a_index_website_content(website_id, sub_links, is_update))
        return result
    except Exception as e:
        logger.error(f"Error processing website content: {e}")
        self.retry(countdown=10)


async def a_index_website_content(website_id, sub_links, is_update=False):
    async with session_manager() as session:
        query = select(WebsiteContent).where(WebsiteContent.id == website_id)
        result = await session.execute(query)
        website_content = result.scalar_one_or_none()

        if website_content is None:
            return f"Website Content {website_id} not found"

        url = website_content.url
        try:
            response = await website_hook.get_content(url)
        except HTTPStatusError as e:
            logger.error(f"Error processing website content: {e}")
            await session.delete(website_content)
            await session.commit()
            return f"Error processing website content: {e}"

        data = response["data"]
        website_content.title = data["title"]
        website_content.content = clean_markdown(data["content"]) if "content" in data else ""
        website_content.domain = urlparse(url).netloc
        session.add(website_content)
        await session.commit()
        await session.refresh(website_content)

        logger.info(f"Processing Website Content: {website_content.id}")

        await index_website(website_content, session, is_parent=True)

        # Index the content of all links
        if not is_update and website_content.include_links:
            for link_dict in sub_links:
                title = link_dict.get("title")
                url = link_dict.get("url")
                query = select(WebsiteContent).where(
                    WebsiteContent.assistant_id == website_content.assistant_id, WebsiteContent.url == url
                )
                result = await session.execute(query)
                content = result.scalar_one_or_none()

                if content is not None:
                    logger.warning(f"Skipping Website: {url}. The URL already exists!")
                    continue
                try:
                    response = await website_hook.get_content(url)
                except HTTPStatusError as e:
                    logger.error(f"Error processing website content: {e}")
                    continue

                data = response["data"]
                domain = urlparse(url).netloc
                child_content = WebsiteContent(
                    assistant_id=website_content.assistant_id,
                    title=title,
                    content=clean_markdown(data["content"]) if "content" in data else "",
                    content_type=website_content.content_type,
                    categories=website_content.categories,
                    domain=domain,
                    url=url,
                    include_links=False,
                    parent_id=website_content.id,
                    meta=website_content.meta,
                    cron_expression=website_content.cron_expression,
                )
                session.add(child_content)
                await session.commit()
                await session.refresh(child_content)

                logger.info(f"Processing Child Website Content: {child_content.id}")
                await index_website(child_content, session, is_parent=False)

        website_content.is_indexed = True
        await session.commit()
        logger.info(f"Website Content {website_content.id} indexed")
        refresh_beat_schedule.delay()
        return website_content.id, website_content.is_indexed


@celery_app.task(bind=True, max_retries=3)
def update_and_reindex_websites(self, website_ids):
    try:
        loop = asyncio.get_event_loop()
        result = loop.run_until_complete(a_update_and_reindex_websites(website_ids))
        return result
    except Exception as e:
        logger.error(f"Error updating and reindexing websites {website_ids}: {e}")
        self.retry(countdown=10)


async def fetch_and_update_website(website_id):
    async with session_manager() as session:
        query = select(WebsiteContent).where(WebsiteContent.id == website_id)
        result = await session.execute(query)
        website = result.scalar_one_or_none()

        if website is None:
            logger.error(f"Website Content {website_id} not found")
            return

        url = website.url
        try:
            try:
                response = await website_hook.get_content(url)
            except HTTPStatusError as e:
                # handle the case where the website is temporarily unavailable
                if e.response.status_code == status.HTTP_503_SERVICE_UNAVAILABLE:
                    logger.warning(f"Website {url} is temporarily unavailable: {e}")
                    return
                # handle the case where the website is returning a bad gateway error
                elif e.response.status_code == status.HTTP_502_BAD_GATEWAY:
                    logger.warning(f"Website {url} is returning 502 Bad Gateway: {e}")
                    return
                # handle the case where the website is returning unprocessable entity error
                elif e.response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY:
                    logger.warning(f"Website {url} is returning 422 Unprocessable Entity: {e}")
                    return

                logger.warning(f"Failed to update website content: {e}")
                return

            data = response["data"]
            website.title = data["title"]
            website.content = clean_markdown(data["content"]) if "content" in data else ""
            website.updated_at = datetime.now()
            session.add(website)
            await session.commit()
            await session.refresh(website)

            generic_hook = await get_generic_hook(website.assistant_id)
            await generic_hook.delete_document(website.id, namespace=website.assistant_id)
            await index_website(website, session, is_parent=False)
            logger.info(f"Successfully updated and reindexed website: {url}")
        except Exception as e:
            logger.error(f"Error updating website {url}: {e}")


async def a_update_and_reindex_websites(website_ids):
    logger.info(f"Updating and reindexing websites {website_ids}...")
    async with session_manager() as session:
        # Fetch all websites that need to be updated
        websites = await session.execute(select(WebsiteContent).where(WebsiteContent.id.in_(website_ids)))
        websites = websites.scalars().all()
        logger.info(f"Found {len(websites)} websites to update and reindex")

        for website in websites:
            await fetch_and_update_website(website.id)

    logger.info("Finished updating and reindexing websites")


@celery_app.task(bind=True, max_retries=3)
def refresh_beat_schedule(self):
    try:
        loop = asyncio.get_event_loop()
        result = loop.run_until_complete(a_refresh_celery_beat_schedule())
        return result
    except Exception as e:
        logger.error(f"Error updating celery beat schedule: {e}")
        self.retry(countdown=10)


async def a_refresh_celery_beat_schedule():
    async with session_manager() as session:
        websites = await session.execute(select(WebsiteContent))
        websites = websites.scalars().all()

        cron_groups = defaultdict(list)
        for website in websites:
            if (
                website.cron_expression is None
                or website.cron_expression.strip() == ""
                or len(website.cron_expression.split()) != 5
            ):
                continue
            cron_groups[website.cron_expression].append(website.id)

        existing_entries = set()
        for entry in get_schedules().values():
            logger.info(f"task ---------------> {entry.task}")
            if entry.task == "tasks.website.update_and_reindex_websites":
                existing_entries.add(entry.name)

        new_entries = set()
        for cron_expression, website_ids in cron_groups.items():
            minute, hour, day_of_month, month_of_year, day_of_week = cron_expression.split()
            schedule = crontab(
                minute=minute,
                hour=hour,
                day_of_month=day_of_month,
                month_of_year=month_of_year,
                day_of_week=day_of_week,
            )
            name = f"update_and_reindex_website_{cron_expression.replace(' ', '_')}"
            new_entries.add(name)
            entry = RedBeatSchedulerEntry(
                name=name,
                task="tasks.website.update_and_reindex_websites",
                schedule=schedule,
                args=[website_ids],
                app=celery_app,
            )
            entry.save()

        outdated_entries = existing_entries - new_entries
        if outdated_entries:
            redbeat_scheduler = RedBeatScheduler(app=celery_app)
            key_prefix = get_redbeat_key_prefix()
            for entry_name in outdated_entries:
                entry = redbeat_scheduler.Entry.from_key(key=f"{key_prefix}:{entry_name}", app=celery_app)
                entry.delete()

        logger.info(f"Final beat schedule: {get_schedules()}")
        logger.info(f"Updated celery beat schedule for {len(websites)} websites")


@celery_app.task(bind=True, max_retries=3)
def update_website_categories(self, website_content_id):
    try:
        loop = asyncio.get_event_loop()
        result = loop.run_until_complete(a_update_website_categories(website_content_id))
        return result
    except Exception as e:
        logger.error(f"Error updating categories for website content {website_content_id}: {e}")
        self.retry(countdown=10)


async def a_update_website_categories(website_content_id):
    async with session_manager() as session:
        query = (
            select(WebsiteContent)
            .options(selectinload(WebsiteContent.assistant))
            .options(selectinload(WebsiteContent.children))
            .where(WebsiteContent.id == website_content_id)
        )
        result = await session.execute(query)
        website_content = result.scalar_one_or_none()

        if website_content is None:
            logger.error(f"Website Content {website_content_id} not found")
            return

        # Assuming you have a logic to update categories
        generic_hook = await get_generic_hook(website_content.assistant_id)
        for child in website_content.children:
            await generic_hook.update_metadata(
                id=str(child.id),
                categories=child.categories,
                namespace=str(child.assistant_id),
            )
        await generic_hook.update_metadata(
            id=str(website_content.id),
            categories=website_content.categories,
            namespace=str(website_content.assistant_id),
        )
        logger.info(f"Updated categories for website content {website_content_id}")
