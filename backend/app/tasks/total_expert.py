import asyncio

from celery_app import celery_app
from db.models import TotalExpertInsight
from db.session import session_manager
from loguru import logger
from sqlalchemy import delete, select
from tools.utils.total_expert import total_expert_utils


@celery_app.task(bind=True, max_retries=3)
def sync_insights(self):
    try:
        loop = asyncio.get_event_loop()
        result = loop.run_until_complete(a_sync_insights())
        return result
    except Exception as e:
        logger.error(f"Error syncing insights: {e}")
        self.retry(countdown=10)


async def fetch_and_sync(insight, session, token):
    # Fetch insight from total expert
    response = await total_expert_utils.fetch_insight(insight.id, token=token)

    # If insight is not found, delete it from db
    if not response:
        await session.execute(delete(TotalExpertInsight).where(TotalExpertInsight.id == insight.id))
        await session.commit()


async def a_sync_insights():
    logger.info("Syncing Totalexpert Insights!")
    async with session_manager() as session:
        try:
            result = await session.execute(select(TotalExpertInsight))
            insights = result.scalars().all()
            token = await total_expert_utils.get_token()
            # Create tasks for each insight to run fetch_and_sync
            tasks = [fetch_and_sync(insight, session, token) for insight in insights]

            # Run tasks concurrently
            await asyncio.gather(*tasks)
        except Exception as e:
            logger.error(f"Error syncing insights: {e}")
            await session.rollback()
            raise e
        finally:
            await session.close()


@celery_app.task(bind=True, max_retries=3)
def sync_journeys(self, user_id: str, assistant_id: str):
    try:
        loop = asyncio.get_event_loop()
        result = loop.run_until_complete(total_expert_utils.sync_journeys(user_id=user_id, assistant_id=assistant_id))
        return result
    except Exception as e:
        logger.error(f"Error syncing contact groups: {e}")
        self.retry(countdown=10)
