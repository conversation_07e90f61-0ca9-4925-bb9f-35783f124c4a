import asyncio

from celery_app import celery_app
from db.models import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, User
from db.session import session_manager
from loguru import logger
from schema.enums import RoleTypes
from sqlalchemy import delete
from tools.fields.encompass import fetch_encompass_assistants
from tools.utils.encompass.loan import loan_utils


@celery_app.task(bind=True, max_retries=3)
def sync_loan_officer_data(self):
    try:
        loop = asyncio.get_event_loop()
        result = loop.run_until_complete(a_sync_loan_officer_data())
        return result
    except Exception as e:
        logger.error(f"Error syncing loan officer data: {e}")
        self.retry(countdown=10)


async def fetch_and_store_loan_officer_data(assistant_id: str):
    logger.info(f"Fetching latest loan officer data for assistant_id: {assistant_id}")
    loan_officer_data_df = await loan_utils.get_latest_loan_officer_data(assistant_id)

    if not loan_officer_data_df.empty:
        logger.info(f"Adding {loan_officer_data_df.shape[0]} loan officer data to database...")
        async with session_manager() as session:
            try:
                # Clear existing data
                await session.execute(delete(LoanOfficer).where(LoanOfficer.assistant_id == assistant_id))

                # Populate with new data
                for _, row in loan_officer_data_df.iterrows():
                    loan_officer = LoanOfficer(
                        loan_id=row["id"],
                        last_name=row["lastName"],
                        first_name=row["firstName"],
                        email=row["email"],
                        assistant_id=assistant_id,
                    )
                    session.add(loan_officer)

                await session.commit()
            except Exception as e:
                logger.error(f"Error storing loan officer data: {e}")
                await session.rollback()
                raise e
            finally:
                await session.close()


async def a_sync_loan_officer_data(user: User | None = None):
    assistants = await fetch_encompass_assistants()
    # check for assistants now
    # if user is admin, we sync all assistants, if user is division_admin, sync only that division,
    # if user is assistant_admin, only sync their assistant
    if user:
        if user.role == RoleTypes.DIVISION_ADMIN and user.division_id:
            assistants = [assistant for assistant in assistants if assistant.division_id == user.division_id]
        elif user.role == RoleTypes.ASSISTANT_ADMIN and hasattr(user, "assistants"):
            user_assistant_ids = {assistant.id for assistant in user.assistants}
            assistants = [assistant for assistant in assistants if assistant.id in user_assistant_ids]

    tasks = [fetch_and_store_loan_officer_data(assistant.id) for assistant in assistants]
    await asyncio.gather(*tasks)
