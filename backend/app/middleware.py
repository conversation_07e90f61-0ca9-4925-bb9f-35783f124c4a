from fastapi import Request
from fastapi.middleware.gzip import GZipMiddleware
from rate_limit import handle_rate_limit
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import <PERSON><PERSON><PERSON><PERSON>, Receive, Scope, Send


class SelectiveGZipMiddleware(GZipMiddleware):
    async def __call__(self, scope: Scope, receive: Receive, send: Send) -> None:
        if "/stream" in scope.get("path", ""):
            await self.app(scope, receive, send)
        else:
            await super().__call__(scope, receive, send)


class RateLimitMiddleware(BaseHTTPMiddleware):
    """
    Middleware for selective, Redis-backed rate limiting.
    Delegates logic to rate_limit.py for path matching and limit handling.
    """

    def __init__(self, app: ASGIApp):
        super().__init__(app)

    async def dispatch(self, request: Request, call_next):
        response = await handle_rate_limit(request)
        if response is not None:
            return response
        return await call_next(request)
