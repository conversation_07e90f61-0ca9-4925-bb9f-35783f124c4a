import os
import secrets
import string
from datetime import datetime
from io import Bytes<PERSON>
from pathlib import Path

import magic
import pandas as pd
from db.models import Assistant
from fastapi import HTTPEx<PERSON>, status
from hooks.s3 import default_s3_hook
from loguru import logger
from pdfid import pdfid
from schema.enums import AssistantSubTypes
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession


def generate_and_upload_excel(folder: str, prefix: str, report_data: dict) -> str | None:
    try:
        df = pd.DataFrame(report_data)

        excel_buffer = BytesIO()
        df.to_excel(excel_buffer, index=False)
        excel_buffer.seek(0)

        # Upload to S3
        filename = f"{folder}{prefix}_{datetime.now().strftime('%Y%m%d%H%M%S')}.xlsx"
        response = default_s3_hook.put_object(object_name=filename, file=excel_buffer)

        if response["ResponseMetadata"]["HTTPStatusCode"] == 200:
            excel_url = default_s3_hook.get_presigned_url_for_download(filename)
            return excel_url
        else:
            return "Failed to upload Excel file."

    except Exception as e:
        logger.error(f"Failed to generate and upload Excel file: {e}")
        return "Failed to generate and upload Excel file."


def extract_text(message_content: list[dict]) -> str:
    return " ".join([message["text"] for message in message_content if message["type"] == "text"])


async def generate_msg_id(prefix: str, length: int = 22) -> str:
    random_string = "".join(secrets.choice(string.ascii_letters + string.digits) for _ in range(length))
    return f"{prefix}_{random_string}"


def get_extension_and_mime_type(filename: str, content: bytes) -> tuple[str, str]:
    ext = Path(filename).suffix.lower().lstrip(".")
    mime = magic.Magic(mime=True).from_buffer(content)
    return ext, mime


def is_malicious_pdf(buffer: bytes, filename: str) -> bytes:
    options = pdfid.get_fake_options()
    options.scan = True
    options.json = True

    # Analyze for threats
    analysis = pdfid.PDFiDMain([filename], options, [buffer])["reports"]

    suspicious_keys = [
        "/JS",
        "/JavaScript",
        "/AA",
        "/OpenAction",
        "/AcroForm",
        "/JBIG2Decode",
        "/RichMedia",
        "/Launch",
        "/EmbeddedFile",
    ]
    is_malicious = any(key in analysis[0] and int(analysis[0][key]) > 0 for key in suspicious_keys)
    return is_malicious


async def disarm_malicious_content(buffer: bytes, filename: str) -> bytes:
    try:
        logger.info(f"Malicious PDF detected: {filename}, disarming...")

        disarm_opts = pdfid.get_fake_options()
        disarm_opts.disarm = True
        disarm_opts.return_disarmed_buffer = True

        disarmed = pdfid.PDFiDMain([filename], disarm_opts, [buffer])
        content = disarmed["buffers"][0]
        disarmed_filename = f"{Path(filename).stem}.disarmed.pdf"
        if os.path.exists(disarmed_filename):
            os.remove(disarmed_filename)

        return content

    except Exception as e:
        logger.error(f"Error disarming PDF {filename}: {e}")
        return None


async def validate_assistant(assistant_id: str, session: AsyncSession) -> None:
    """
    Validate assistant.
    """
    # First check if assistant exists
    base_query = select(Assistant).where(Assistant.id == assistant_id)
    result = await session.execute(base_query)
    assistant = result.scalar_one_or_none()

    if not assistant:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail=f"Assistant with ID {assistant_id} not found"
        )

    # check assistant subtype
    if assistant.sub_type not in [
        AssistantSubTypes.ENCOMPASS_SALES,
        AssistantSubTypes.CALL_CAMPAIGN,
    ]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Assistant '{assistant.sub_type}' is not allowed to upload document.",
        )

    return
