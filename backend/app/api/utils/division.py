from auth.cognito import get_or_create_auth_user
from db.models import Division, User
from db.session import get_session
from fastapi import Depends, HTTPException, status
from sqlalchemy import func, select
from sqlalchemy.ext.asyncio import AsyncSession


async def get_division_or_404(
    division_id: str | None = None,
    user: User = Depends(get_or_create_auth_user),
    session: AsyncSession = Depends(get_session),
) -> Division | None:
    """
    Retrieve a division by its ID. If no divisions exist in the system, returns None.
    If divisions exist but no division_id is provided, uses the user's division.

    Args:
        division_id (str | None): The ID of the division to retrieve.
        user (User): The authenticated user.
        session (AsyncSession): The database session.

    Returns:
        Division | None: The division object or None if no divisions exist.

    Raises:
        HTTPException: If divisions exist but division_id is required but not provided.
    """
    query = select(func.count()).select_from(Division)
    result = await session.execute(query)
    total_divisions = result.scalar() or 0

    # Normalize empty string or None
    if not division_id:
        division_id = None

    if total_divisions > 0 and division_id is None:
        # If divisions exist but no division_id provided, use user's division
        division_id = user.division_id
        if not division_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Division ID is required when divisions exist in the system.",
            )
    elif total_divisions <= 0:
        # No divisions exist in the system
        return None

    # Get the specific division
    query = select(Division).where(Division.id == division_id)
    result = await session.execute(query)
    division = result.scalar_one_or_none()

    if division is None:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Division not found with given information.")

    return division
