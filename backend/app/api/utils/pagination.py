from datetime import datetime

from dateutil.parser import parse
from fastapi import HTTPException, Query


async def pagination(
    before: str = Query(
        default_factory=lambda: datetime.utcnow().isoformat(), description="Return results before this date"
    ),
    after: str = Query(None, description="Return results after this date (ISO 8601 format)"),
    limit: int = Query(10, ge=0, le=100, description="Number of results to return, capped at 100"),
) -> tuple[datetime, datetime, int]:
    """
    Returns a tuple containing the 'before' and 'after' datetimes, and the 'limit' for pagination.
    """
    before = parse(before).replace(tzinfo=None)
    after = parse(after).replace(tzinfo=None) if after else None
    if before and after and before <= after:
        raise ValueError("The 'before' date must be later than the 'after' date.")
    return (before, after, limit)


async def datetime_pagination(
    before: str = Query(
        default_factory=lambda: datetime.utcnow().isoformat(), description="Return results before this date"
    ),
    after: str = Query(None, description="Return results after this date (ISO 8601 format)"),
    page: int = Query(1, ge=1, description="Page number"),
    size: int = Query(20, ge=0, description="Number of results to return"),
) -> tuple[datetime, datetime, int]:
    """
    Returns a tuple containing the 'before' and 'after' datetimes, and the 'limit' for pagination.
    """
    try:
        before = parse(before).replace(tzinfo=None)
    except Exception:
        raise HTTPException(status_code=400, detail=f"Invalid 'before' date: {before}")
    try:
        after = parse(after).replace(tzinfo=None) if after else None
    except Exception:
        raise HTTPException(status_code=400, detail=f"Invalid 'after' date: {after}")

    if before and after and before < after:
        raise ValueError("The 'before' date must be later than the 'after' date.")
    return (before, after, page, size)


class PageNumberPagination:
    def __init__(self, maximum_limit: int = 100):
        self.maximum_limit = maximum_limit

    async def __call__(
        self,
        page: int = Query(1, ge=1),
        size: int = Query(20, ge=0),
    ) -> tuple[int, int]:
        capped_size = min(self.maximum_limit, size)
        return (page, capped_size)


page_number_pagination = PageNumberPagination()
