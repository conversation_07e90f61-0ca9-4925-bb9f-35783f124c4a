from db.models import Assistant
from sqlalchemy import and_, or_, select
from sqlalchemy.ext.asyncio import AsyncSession


async def get_assistants_id(session: AsyncSession, and_filters=None, or_filters=None):
    query_assistant = select(Assistant)
    conditions = []
    if and_filters:
        conditions.append(and_(*and_filters))
    if or_filters:
        conditions.append(or_(*or_filters))

    if conditions:
        query_assistant = query_assistant.where(and_(*conditions))

    result = await session.execute(query_assistant)
    assistant_ids = [assistant.id for assistant in result.scalars()]
    return assistant_ids
