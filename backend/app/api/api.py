from api.endpoints.analytics import analytics_router
from api.endpoints.assistant import router as assistant_router
from api.endpoints.chat import router as chat_router
from api.endpoints.connectors import connectors_router
from api.endpoints.conversation import router as conversation_router
from api.endpoints.custom_lo_mapping import router as custom_lo_mapping_router
from api.endpoints.datapulse import router as datapulse_router
from api.endpoints.division import router as division_router
from api.endpoints.encompass import router as encompass_router
from api.endpoints.feedback import router as feedback_router
from api.endpoints.persona import personas_router
from api.endpoints.resources import resources_router
from api.endpoints.resources.admin import admin_router
from api.endpoints.sales import router as sales_router
from api.endpoints.total_expert import router as total_expert_router
from api.endpoints.twilio_call import router as call_router
from api.endpoints.user import router as user_router
from config import settings
from fastapi import APIRouter
from tests.load_testing_api import router as load_testing_router

api_router = APIRouter()

api_router.include_router(user_router, prefix="/user", tags=["User"])
api_router.include_router(conversation_router, prefix="/conversation", tags=["Conversation"])
api_router.include_router(assistant_router, prefix="/assistant", tags=["Assistant"])
api_router.include_router(resources_router, prefix="/assistant", tags=["Resources"])
api_router.include_router(feedback_router, prefix="/feedback", tags=["Feedback"])
api_router.include_router(chat_router, prefix="/chat", tags=["Chat"])
api_router.include_router(analytics_router, prefix="/analytics", tags=["Analytics"])
api_router.include_router(custom_lo_mapping_router, prefix="/assistant", tags=["Assistant Impersonation"])
api_router.include_router(connectors_router, prefix="/connectors", tags=["Connectors"])
api_router.include_router(total_expert_router, prefix="/total-expert", tags=["Total Expert Integration"])
api_router.include_router(encompass_router, prefix="/encompass", tags=["Encompass Loan Assistant"])
api_router.include_router(load_testing_router, prefix="/load-testing", tags=["Load Testing"])
api_router.include_router(personas_router, tags=["Personas"])
api_router.include_router(sales_router, prefix="/sales", tags=["Sales"])
api_router.include_router(division_router, prefix="/division", tags=["Division"])
api_router.include_router(call_router, prefix="/twilio-call", tags=["Twilio Call"])
api_router.include_router(datapulse_router, prefix="/datapulse", tags=["DataPulse"])
api_router.include_router(admin_router, prefix="/admin", tags=["Admin Resource Endpoints"])


@api_router.get("/", tags=["Root"])
async def api_root():
    return {"app": settings.PROJECT_NAME}


@api_router.get("/health", tags=["Health"])
async def health():
    return {"status": "OK"}
