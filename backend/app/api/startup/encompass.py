import json

import redis.asyncio as aioredis
from config import settings
from db.models import Assistant, <PERSON>Field, DataPulseReport
from db.session import session_manager
from loguru import logger
from schema.enums import AssistantSubTypes
from sqlalchemy import select
from tasks.loid import a_sync_loan_officer_data
from tools.datapulse import client_datapulse_settings
from tools.fields.encompass import fetch_encompass_assistants
from tools.utils.encompass.loan import loan_utils


async def sync_loan_officer_data():
    """
    Syncs loan officer data from Encompass to the database.
    """
    try:
        await a_sync_loan_officer_data()
    except Exception as e:
        logger.error(f"Error syncing loan officer data: {e}")


async def sync_encompass_fields_options():
    """
    Fetch Encompass fields from the database, retrieve their valid values,
    and cache the results.
    """
    try:
        assistants = await fetch_encompass_assistants()
        async with session_manager() as session:
            result = await session.execute(
                select(DataField.field_id).where(
                    DataField.assistant_id.in_([assistant.id for assistant in assistants])
                )
            )
            canonical_names = result.scalars().all()
            canonical_names = list(set(canonical_names))
            if not canonical_names:
                logger.warning("No Encompass field IDs found in the database.")
                return

    except Exception as e:
        logger.error(f"Database query failed: {e}")
        return

    try:
        field_details = await loan_utils.get_encompass_field_details(canonical_names)
        fields_with_options = {}
        for field in field_details:
            if field.get("isOptionListFixed", False):
                canonical_name = field.get("canonicalName")
                option_values = [opt.get("reportingDatabaseValue") for opt in field.get("options", [])]
                fields_with_options[canonical_name] = option_values

        if fields_with_options:
            redis = await aioredis.from_url(settings.REDIS_URL)
            await redis.set("encompass_fields_with_options", json.dumps(fields_with_options))
    except Exception as e:
        logger.error(f"Error during field option retrieval or caching: {e}")


async def add_datapulse_reports():
    """
    Adds DataPulse reports to the database.
    """
    try:
        if not client_datapulse_settings.DATAPULSE_ENABLE:
            logger.info("DataPulse reports are disabled, skipping addition.")
            return
        # TODO: Upload reports in parallel
        async with session_manager() as session:
            assistant_query = select(Assistant).where(Assistant.sub_type == AssistantSubTypes.ENCOMPASS)
            assistant_result = await session.execute(assistant_query)
            assistants = assistant_result.scalars().all()

            for assistant in assistants:
                for report in client_datapulse_settings.DATAPULSE_REPORTS:
                    existing_report = await session.execute(
                        select(DataPulseReport).where(
                            DataPulseReport.id == report["id"], DataPulseReport.assistant_id == assistant.id
                        )
                    )
                    if not existing_report.scalars().first():
                        report_data = {**report, "assistant_id": assistant.id}
                        new_report = DataPulseReport(**report_data)
                        session.add(new_report)

            await session.commit()
            logger.info("DataPulse reports added successfully.")
    except Exception as e:
        logger.error(f"Error adding DataPulse reports: {e}")
