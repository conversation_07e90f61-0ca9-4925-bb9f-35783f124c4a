from config import settings

from .assistants import <PERSON><PERSON><PERSON><PERSON>
from .encompass import add_datapulse_reports, sync_encompass_fields_options, sync_loan_officer_data
from .totalexpert import sync_insight_types


async def run_startup_events(app):
    """
    Run all events.
    1. Auto setup assistants
    2. Sync insight types from TotalExpert to the database.
    3. Sync loan officer data from Encompass to the database. (only in production environment)
    4. Sync Encompass fields options to Redis cache. (only in production environment)
    5. Add DataPulse reports to the database.
    """
    # create assistants
    assistant_manager = AssistantManager(app)
    await assistant_manager.auto_setup_assistants()
    await sync_insight_types()
    await add_datapulse_reports()
    if settings.APP_ENV != "local":
        await sync_encompass_fields_options()
        await sync_loan_officer_data()
