import asyncio
import importlib
import json
import os
from datetime import datetime

from config import settings
from db.models import Assistant, DataField, Division, DivisionCredentials
from db.session import session_manager
from fastapi import FastAPI
from hooks.gemini_hook import gemini_hook
from hooks.openai_hook import oai_hook
from loguru import logger
from schema.assistant import Assistant<PERSON><PERSON>
from schema.enums import <PERSON><PERSON><PERSON><PERSON>, AssistantSubTypes, AssistantTypes, DataFieldFolder
from sqlalchemy import func, select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload
from utils.division import create_division_with_credentials


class AssistantManager:
    """
    Overview of Assistant Initialization Process:

    1. Assistant Verification:
       - Ensure the setup of essential assistants, including:
         - Encompass Assistant
         - TotalExpert Assistant
         - Sidekick Assistant
         - Sales Assistant
         - Various Personal Assistants

    2. Existing Assistant Handling:
       - If an assistant already exists, check for and integrate any newly available functions within
       the OpenAI ecosystem.

    3. New Assistant Creation:
       - For non-existing assistants, initiate creation inclusive of all relevant functions.

    4. Default Field Addition for Specific Assistants:
       - Upon the creation of TotalExpert and Encompass assistants, ensure the addition of default fields.

    5. Merge Field Management for Encompass:
       - Establish merge fields as necessary for the Encompass assistant.

    6. Field Integrity for Existing Assistants:
       - Verify and append default fields to existing assistants if they are absent, maintaining existing
       field data without removal.
    """

    ASSISTANTS_CONFIG = [
        {
            "type": AssistantTypes.CUSTOM,
            "sub_type": AssistantSubTypes.SIDEKICK,
            "provider": AssistantProvider.OPENAI,
            "assistant_name": f"{settings.CLIENT_NAME} Sidekick",
            "client_setting": True,
            "description": "A friendly AI assistant designed to help you with various tasks and questions.",
            "assistant_id": settings.SIDEKICK_ASSISTANT_ID if settings.APP_ENV == "local" else None,
        },
        {
            "type": AssistantTypes.CUSTOM,
            "sub_type": AssistantSubTypes.ENCOMPASS,
            "provider": AssistantProvider.OPENAI,
            "assistant_name": f"{settings.CLIENT_NAME} Encompass Assistant",
            "client_setting": settings.ENCOMPASS_ASSISTANT_ENABLED,
            "description": "A specialized AI assistant tailored to assist with Encompass-related tasks and questions.",
            "assistant_id": settings.LOAN_ASSISTANT_ID if settings.APP_ENV == "local" else None,
        },
        {
            "type": AssistantTypes.CUSTOM,
            "sub_type": AssistantSubTypes.TOTALEXPERT,
            "provider": AssistantProvider.OPENAI,
            "assistant_name": f"{settings.CLIENT_NAME} TotalExpert Assistant",
            "client_setting": settings.TOTALEXPERT_ASSISTANT_ENABLED,
            "description": (
                "A specialized AI assistant tailored to assist with TotalExpert-related tasks and questions."
            ),
            "assistant_id": settings.TOTAL_EXPERT_ASSISTANT_ID if settings.APP_ENV == "local" else None,
        },
        {
            "type": AssistantTypes.CUSTOM,
            "sub_type": AssistantSubTypes.ENCOMPASS_SALES,
            "provider": AssistantProvider.OPENAI,
            "assistant_name": f"{settings.CLIENT_NAME} Sales Assistant",
            "client_setting": settings.ENCOMPASS_SALES_ASSISTANT_ENABLED,
            "description": "A specialized AI assistant tailored to assist with sales-related tasks and questions.",
            "assistant_id": settings.SALES_ASSISTANT_ID if settings.APP_ENV == "local" else None,
        },
        {
            "type": AssistantTypes.PERSONAL,
            "sub_type": AssistantSubTypes.OUTLOOK,
            "provider": AssistantProvider.OPENAI,
            "client_setting": settings.PERSONAL_ASSISTANTS_ENABLED,
            "assistant_name": "Outlook",
            "description": (
                "A friendly AI assistant designed to manage emails and calendar events through API "
                "connections to Microsoft Outlook."
            ),
            "assistant_id": settings.OUTLOOK_ASSISTANT_ID if settings.APP_ENV == "local" else None,
        },
        {
            "type": AssistantTypes.PERSONAL,
            "sub_type": AssistantSubTypes.RETRIEVAL,
            "provider": AssistantProvider.OPENAI,
            "client_setting": settings.PERSONAL_ASSISTANTS_ENABLED,
            "assistant_name": "Search Docs",
            "description": "Search documents for information.",
            "assistant_id": settings.RETRIEVAL_ASSISTANT_ID if settings.APP_ENV == "local" else None,
        },
        {
            "type": AssistantTypes.PERSONAL,
            "sub_type": AssistantSubTypes.DOCUMENT_UNDERSTANDING,
            "provider": AssistantProvider.GEMINI,
            "client_setting": settings.PERSONAL_ASSISTANTS_ENABLED,
            "assistant_name": "Summarize/Compare Documents",
            "description": "Document analyzing and giving insights towards those documents",
            "assistant_id": settings.DOCUMENT_UNDERSTANDING_ID if settings.APP_ENV == "local" else None,
        },
        {
            "type": AssistantTypes.PERSONAL,
            "sub_type": AssistantSubTypes.IMAGE,
            "provider": AssistantProvider.OPENAI,
            "client_setting": settings.PERSONAL_ASSISTANTS_ENABLED,
            "assistant_name": "Analyze Image",
            "description": "Analyze an image from a file.",
            "assistant_id": settings.IMAGE_ASSISTANT_ID if settings.APP_ENV == "local" else None,
        },
        {
            "type": AssistantTypes.PERSONAL,
            "sub_type": AssistantSubTypes.ANALYSIS,
            "provider": AssistantProvider.OPENAI,
            "client_setting": settings.PERSONAL_ASSISTANTS_ENABLED,
            "assistant_name": "Analyze Data",
            "description": "Analyze data from a file or text.",
            "assistant_id": settings.ANALYSIS_ASSISTANT_ID if settings.APP_ENV == "local" else None,
        },
        {
            "type": AssistantTypes.CUSTOM,
            "sub_type": AssistantSubTypes.CALL_CAMPAIGN,
            "provider": AssistantProvider.OPENAI,
            "client_setting": settings.CALL_CAMPAIGN_ASSISTAN_ENABLED,
            "assistant_name": "Call Campaign Assistant",
            "description": "A specialized AI assistant designed to assist with call campaign-related tasks",
            "assistant_id": settings.CALL_CAMPAIGN_ASSISTANT_ID if settings.APP_ENV == "local" else None,
        },
    ]

    if settings.EXTRA_ENCOMPASS_ASSISTANTS_ENVIRONMENTS:
        for environment in settings.EXTRA_ENCOMPASS_ASSISTANTS_ENVIRONMENTS:
            ASSISTANTS_CONFIG.append(
                {
                    "type": AssistantTypes.CUSTOM,
                    "sub_type": AssistantSubTypes.ENCOMPASS,
                    "provider": AssistantProvider.OPENAI,
                    "client_setting": True,
                    "assistant_name": f"{settings.CLIENT_NAME} Encompass Assistant {environment}",
                    "description": (
                        "A specialized AI assistant tailored to assist with Encompass-related tasks and questions."
                    ),
                    "environment": environment,
                    "assistant_id": settings.DEV_LOAN_ASSISTANT_ID if settings.APP_ENV == "local" else None,
                }
            )

    if settings.EXTRA_ENCOMPASS_SALES_ASSISTANTS_ENVIRONMENTS:
        for environment in settings.EXTRA_ENCOMPASS_SALES_ASSISTANTS_ENVIRONMENTS:
            ASSISTANTS_CONFIG.append(
                {
                    "type": AssistantTypes.CUSTOM,
                    "sub_type": AssistantSubTypes.ENCOMPASS_SALES,
                    "provider": AssistantProvider.OPENAI,
                    "client_setting": True,
                    "assistant_name": f"{settings.CLIENT_NAME} Sales Assistant {environment}",
                    "description": (
                        "A specialized AI assistant tailored to assist with sales-related tasks and questions."
                    ),
                    "environment": environment,
                    "assistant_id": settings.DEV_SALES_ASSISTANT_ID if settings.APP_ENV == "local" else None,
                }
            )

    def __init__(self, app: FastAPI):
        self.app = app
        self.field_type_mapper = {datetime: "date", float: "decimalnumber", int: "integernumber", "string": "text"}
        self.code_interpreter_tool = {"type": "code_interpreter"}
        self.base_schema_path = "/app/prompts"
        self.code_interpreter_enabled_assistants = [
            AssistantSubTypes.ENCOMPASS,
            AssistantSubTypes.TOTALEXPERT,
            AssistantSubTypes.SIDEKICK,
            AssistantSubTypes.ANALYSIS,
            AssistantSubTypes.ENCOMPASS_SALES,
        ]
        self.personal_assistants = {}
        self.total_export_assistant = {}
        self.sales_assistant = {}
        self.sidekick_assistant = {}
        self.encompass_assistants = {}

    async def create_default_fields(
        self, assistant_id: int, assistant_sub_type: AssistantSubTypes, session: AsyncSession
    ) -> None:
        """Create default fields for an assistant."""
        if assistant_sub_type in [AssistantSubTypes.ENCOMPASS, AssistantSubTypes.ENCOMPASS_SALES]:
            try:
                await self._create_encompass_default_fields(assistant_id, session)
            except Exception as e:
                logger.error(f"Failed to create Encompass default fields: {e}")
                await session.rollback()

        elif assistant_sub_type in [AssistantSubTypes.TOTALEXPERT]:
            try:
                await self._create_totalexpert_default_fields(assistant_id, session)
            except Exception as e:
                logger.error(f"Failed to create TotalExpert default fields: {e}")
                await session.rollback()
        else:
            return

    async def auto_setup_assistants(self) -> None:
        """Automatically set up all necessary assistants."""
        logger.info("Starting the automatic setup of assistants.")
        try:
            await self._setup_assistants()
            logger.info("Assistant setup completed successfully.")
        except Exception as e:
            logger.error(f"Failed to set up assistants: {e}")

    async def _setup_local_division(self, session: AsyncSession) -> None:
        """Set up divisions and their credentials for local development."""
        if settings.ENABLE_DIVISION_LOCAL:
            division_data = []

            file_path = os.path.join(settings.BASE_DIR, "scripts", "division.json")
            if os.path.exists(file_path):
                logger.info(f"Loading division data from {file_path}.")
                with open(file_path) as file:
                    division_data = json.load(file)
            else:
                logger.warning(f"Division data file {file_path} not found. Skipping division setup.")
                raise FileNotFoundError(f"Division data file {file_path} not found.")

            if division_data:
                for division in division_data:
                    credentials_data = division.pop("credentials", {})
                    encompass_data_list = credentials_data.pop("encompass_credentials", [])
                    self.personal_assistants[division.get("id")] = division.pop("personal_assistants", {})
                    self.total_export_assistant[division.get("id")] = division.pop("total_export_assistant", None)
                    self.sales_assistant[division.get("id")] = division.pop("sales_assistant", None)
                    self.sidekick_assistant[division.get("id")] = division.pop("sidekick_assistant", None)

                    self.encompass_assistants[division.get("id")] = division.pop("encompass_assistant_id", {})

                    await create_division_with_credentials(
                        division_data=division,
                        credentials_data=credentials_data,
                        encompass_data_list=encompass_data_list,
                    )

    async def _setup_assistants(self):
        async with session_manager() as session:

            logger.info("Setting up divisions and assistants.")
            if settings.APP_ENV == "local":
                logger.info("Setting up local division in local environment.")
                await self._setup_local_division(session=session)
            logger.info("Fetching divisions from the database.")

            divisions = await session.execute(
                select(Division).options(
                    selectinload(Division.credentials).selectinload(DivisionCredentials.encompass_credentials)
                )
            )
            divisions = divisions.scalars().all()
            if divisions:
                await self._setup_division_assistant(
                    session=session,
                    divisions=divisions,
                )
            else:
                for assistant in self.ASSISTANTS_CONFIG:
                    try:
                        logger.info(f"Setting up {assistant['assistant_name']} Assistant.")
                        await self._setup_assistant(
                            session=session,
                            type=assistant.get("type"),
                            sub_type=assistant.get("sub_type"),
                            provider=assistant.get("provider"),
                            client_setting=assistant.get("client_setting"),
                            assistant_name=assistant.get("assistant_name"),
                            description=assistant.get("description"),
                            environment=assistant.get("environment"),
                            assistant_id=assistant.get("assistant_id"),
                        )
                        logger.info(f"Assistant {assistant.get('assistant_name')} setup completed.")

                    except Exception as e:
                        logger.error(f"Failed to set up {assistant['assistant_name']} Assistant: {e}")

    async def _setup_division_assistant(
        self,
        session: AsyncSession,
        divisions: list[Division],
    ) -> None:
        for division in divisions:
            division_id = division.id
            division_name = division.name
            logger.info(f"Setting up assistants for division: {division_name} (ID: {division_id}).")

            for assistant in self.ASSISTANTS_CONFIG:
                try:
                    if assistant.get("sub_type") in [
                        AssistantSubTypes.ENCOMPASS,
                        AssistantSubTypes.TOTALEXPERT,
                        AssistantSubTypes.ENCOMPASS_SALES,
                    ]:
                        continue

                    client_setting = assistant.get("client_setting")
                    assistant_id = assistant.get("assistant_id")
                    if (
                        assistant.get("type") == AssistantTypes.PERSONAL
                        and division.credentials
                        and division.credentials.enabled_personal_assistant
                    ):
                        client_setting = division.credentials.enabled_personal_assistant

                    if assistant.get("type") == AssistantTypes.PERSONAL and self.personal_assistants:
                        assistant_id = self.personal_assistants.get(division_id, {}).get(
                            assistant.get("sub_type").value.lower(), None
                        )
                    elif (
                        assistant.get("type") == AssistantTypes.CUSTOM
                        and assistant.get("sub_type") == AssistantSubTypes.SIDEKICK
                        and self.sidekick_assistant
                    ):
                        assistant_id = self.sidekick_assistant.get(division_id, "")

                    logger.info(f"Setting up {assistant['assistant_name']} Assistant.")
                    await self._setup_assistant(
                        session=session,
                        type=assistant.get("type"),
                        sub_type=assistant.get("sub_type"),
                        provider=assistant.get("provider"),
                        client_setting=client_setting,
                        assistant_name=f"{division_name} {assistant.get('assistant_name')}",
                        description=assistant.get("description"),
                        environment=assistant.get("environment"),
                        division_id=division_id,
                        assistant_id=assistant_id,
                    )
                    logger.info(f"Assistant {assistant.get('assistant_name')} setup completed.")

                except Exception as e:
                    logger.error(f"Failed to set up {assistant['assistant_name']} Assistant: {e}")

            encompass_credentials = division.credentials.encompass_credentials if division.credentials else []
            prod_encompass_for_sale_id = None
            for index, encompass_cred in enumerate(encompass_credentials):
                logger.info(f"Setting up Encompass Assistant. for {encompass_cred.id}")
                try:
                    assistant_id = None
                    if self.encompass_assistants:
                        assistant_id = self.encompass_assistants.get(division_id, {}).get(encompass_cred.id, None)

                    logger.info(f"Setting up {assistant['assistant_name']} Assistant.")
                    await self._setup_assistant(
                        session=session,
                        type=AssistantTypes.CUSTOM,
                        sub_type=AssistantSubTypes.ENCOMPASS,
                        provider=AssistantProvider.OPENAI,
                        client_setting=encompass_cred.enabled,
                        assistant_name=f"{encompass_cred.name} - {encompass_cred.encompass_type}",
                        description=(
                            "A specialized AI assistant tailored to assist with Encompass-related tasks and questions."
                        ),
                        division_id=division_id,
                        encompass_credential_id=encompass_cred.id,
                        assistant_id=assistant_id,
                    )
                    logger.info(f"Assistant {division_name} Encompass Assistant {index + 1} setup completed.")
                    if prod_encompass_for_sale_id is None:
                        prod_encompass_for_sale_id = encompass_cred.id
                except Exception as e:
                    logger.error(f"Failed to set up {assistant['assistant_name']} Assistant: {e}")

            if division.credentials and division.credentials.enabled_sales_assistant and prod_encompass_for_sale_id:
                try:
                    assistant_id = None
                    if self.sales_assistant:
                        assistant_id = self.sales_assistant.get(division_id, "")

                    logger.info(f"Setting up Sales {assistant['assistant_name']} Assistant. {assistant_id}")
                    await self._setup_assistant(
                        session=session,
                        type=AssistantTypes.CUSTOM,
                        sub_type=AssistantSubTypes.ENCOMPASS_SALES,
                        provider=AssistantProvider.OPENAI,
                        client_setting=division.credentials.enabled_sales_assistant,
                        assistant_name=f"{division_name} Sales Assistant",
                        description=(
                            "A specialized AI assistant tailored to assist with sales-related tasks and questions."
                        ),
                        division_id=division_id,
                        encompass_credential_id=prod_encompass_for_sale_id,
                        assistant_id=assistant_id,
                    )
                    logger.info(f"Assistant Sales {division_name} Encompass Assistant {index + 1} setup completed.")

                except Exception as e:
                    logger.error(f"Failed to set up Sales {assistant['assistant_name']} Assistant: {e}")

            if division.credentials and division.credentials.total_expert_client_id:
                logger.info("Setting up TotalExpert Assistant.")
                try:
                    assistant_id = None
                    if self.total_export_assistant:
                        assistant_id = self.total_export_assistant.get(division_id, "")

                    logger.info(f"Setting up {assistant['assistant_name']} Assistant. {assistant_id}")
                    await self._setup_assistant(
                        session=session,
                        type=AssistantTypes.CUSTOM,
                        sub_type=AssistantSubTypes.TOTALEXPERT,
                        provider=AssistantProvider.OPENAI,
                        client_setting=division.credentials.enabled_total_expert,
                        assistant_name=f"{division_name} TotalExpert Assistant",
                        description=(
                            "A specialized AI assistant tailored to assist with TotalExpert-related tasks"
                            " and questions."
                        ),
                        division_id=division_id,
                        assistant_id=assistant_id,
                    )
                    logger.info(f"Assistant {assistant.get('assistant_name')} setup completed.")

                except Exception as e:
                    logger.error(f"Failed to set up {assistant['assistant_name']} Assistant: {e}")

    async def _setup_assistant(
        self,
        session: AsyncSession,
        type: AssistantTypes,
        sub_type: AssistantSubTypes,
        provider: AssistantProvider,
        client_setting: str,
        assistant_name: str,
        description: str = "",
        assistant_id: str = None,
        division_id: str = None,
        encompass_credential_id: str = None,
        **kwargs,
    ) -> None:
        """Generic assistant setup function."""

        async def create_assistant_and_add_fields(assistant_id):
            assistant_create = AssistantCreate(
                name=assistant_name,
                display_name=assistant_name,
                description=description if description else assistant_name,
                instructions="Local environment assistant",
                type=type,
                sub_type=sub_type,
                provider=provider,
                is_published=(
                    True if sub_type == AssistantSubTypes.SIDEKICK else False
                ),  # publish sidekick assistant by default
                division_id=division_id,
                encompass_credential_id=encompass_credential_id,
            )
            await self._create_assistant(assistant_id, assistant_create, session, **kwargs)
            await self.create_default_fields(assistant_id, sub_type, session)

        logger.info(f"Setting up assistant: {assistant_name} (type: {sub_type}).")

        if assistant_id:
            # Check if assistant ID is valid
            query = select(Assistant).where(Assistant.id == assistant_id)
            result = await session.execute(query)
            assistant = result.scalar_one_or_none()
            if not assistant:
                await create_assistant_and_add_fields(assistant_id)
                return
            else:
                await self.create_default_fields(assistant_id, sub_type, session)
                return
        else:
            logger.info("No assistant ID provided. Proceeding to check for existing assistants.")
            # Check if assistant already exists in DB.
            query = select(Assistant).where(
                Assistant.type == type, Assistant.sub_type == sub_type, Assistant.provider == provider
            )
            if division_id:
                query = query.where(Assistant.division_id == division_id)
            if encompass_credential_id:
                query = query.where(Assistant.encompass_credential_id == encompass_credential_id)
            result = await session.execute(query)
            all_existing_assistants = result.scalars().all()

        existing_assistants = []
        # now check based on environment to further reduce assistants.(This must always be one in theory)
        for assistant in all_existing_assistants:
            meta = assistant.meta or {}
            environment = meta.get("environment")
            if environment == kwargs.get("environment"):
                existing_assistants.append(assistant)

        # Schema path
        schema_path = f"{self.base_schema_path}/{sub_type.value.lower()}"

        # Read instructions from markdown file
        instructions = self._read_instructions(schema_path, sub_type)

        # Get all functions from the folder
        tools = self._gather_tools(schema_path)

        if sub_type in self.code_interpreter_enabled_assistants:
            tools.append(self.code_interpreter_tool)

        if existing_assistants:
            for existing_assistant in existing_assistants:
                logger.info(f"Assistant {existing_assistant.name} already exists. Updating tools and instructions.")
                if provider not in [AssistantProvider.GEMINI]:
                    # Update tools and instructions
                    await oai_hook.client.beta.assistants.update(
                        existing_assistant.id,
                        instructions=instructions,
                        tools=tools,
                        model=settings.OPENAI_DEFAULT_MODEL,
                    )
                else:
                    # update db
                    existing_assistant.instructions = instructions

                await session.commit()

                await self.create_default_fields(existing_assistant.id, sub_type, session)
            return

        if not client_setting:
            logger.warning(f"Skipping creation of Assistant {assistant_name} due to missing client setting.")
            return None

        for attempt in range(3):  # Retry up to 3 times
            try:
                logger.info(f"Creating new assistant: {assistant_name}. Attempt {attempt + 1}.")
                # Create new assistant
                if provider in [AssistantProvider.GEMINI]:
                    new_assistant_id = await gemini_hook.create_custom_assistant()
                else:
                    new_assistant_id = await oai_hook.create_custom_assistant(
                        name=assistant_name,
                        instructions=instructions,
                        grounding_instructions="",
                        tools=tools,
                    )

                assistant_create = AssistantCreate(
                    name=assistant_name,
                    display_name=assistant_name,
                    description=description if description else assistant_name,
                    instructions=instructions,
                    type=type,
                    sub_type=sub_type,
                    provider=provider,
                    is_published=(
                        True if sub_type == AssistantSubTypes.SIDEKICK else False
                    ),  # publish sidekick assistant by default
                    division_id=division_id,
                    encompass_credential_id=encompass_credential_id,
                )
                await self._create_assistant(new_assistant_id, assistant_create, session, **kwargs)
                logger.info(f"Successfully created assistant: {assistant_name}.")
                await self.create_default_fields(new_assistant_id, sub_type, session)
                return
            except Exception as e:
                logger.error(f"Attempt {attempt + 1} - Failed to create assistant: {str(e)}")
                await asyncio.sleep(attempt + 1)
                if attempt == 2:
                    logger.error("Exceeded maximum retry attempts to create assistant.")
                    return None

    def _read_instructions(self, schema_path: str, sub_type: AssistantSubTypes) -> str:
        """Read and return instructions from a markdown file."""
        instructions_path = os.path.join(schema_path, f"{sub_type.value.lower()}_instruction.md")
        logger.debug(f"Reading instructions from {instructions_path}.")
        with open(instructions_path) as f:
            return f.read()

    def _gather_tools(self, schema_path: str) -> list:
        """Gather tools from the schema folder."""
        logger.debug(f"Gathering tools from {schema_path}.")
        tools = []
        for file in os.listdir(schema_path):
            if file.endswith(".json"):
                with open(os.path.join(schema_path, file)) as f:
                    tool = json.load(f)
                    tools.append({"type": "function", "function": tool})
        return tools

    async def _create_assistant(self, assistant_id, assistant_create, session, **kwargs):
        """Create an assistant and add it to the database."""
        logger.debug(f"Creating assistant entry in database with ID: {assistant_id}.")
        # Get the current maximum display_order value
        result = await session.execute(select(func.max(Assistant.display_order)))
        max_display_order = result.scalar() or 0

        assistant = Assistant(
            id=assistant_id,
            display_order=max_display_order + 1,
            grounding_instructions="",
            **assistant_create.model_dump(exclude={"grounding_instructions"}),
            meta={
                "environment": kwargs.get("environment"),
            },
        )
        session.add(assistant)
        await session.commit()
        logger.info(f"Assistant {assistant_id} added to the database.")

        return assistant

    async def _fetch_assistant_data_fields(
        self,
        assistant_id: str,
        session: AsyncSession,
        folder: DataFieldFolder | None = None,
    ):
        """Fetch a specific data field for an assistant."""
        logger.debug(f"Fetching data fields for assistant ID: {assistant_id}.")
        query = select(DataField).where(DataField.assistant_id == assistant_id)
        if folder:
            query = query.where(DataField.folder == folder)
        result = await session.execute(query)
        data_fields = result.scalars().all()
        return data_fields

    async def _add_field_to_db(self, session: AsyncSession, **kwargs) -> None:
        """Add a specific field to the database."""

        logger.info(
            f"Adding field: {kwargs.get('field_id')} to the database for assistant ID {kwargs.get('assistant_id')}"
        )
        assistant_id = kwargs.get("assistant_id")
        MERGE_FIELDS = {"Fields.1822": "Referral Source", "Fields.VEND.X133": "Buyer's Agent"}
        if not assistant_id:
            raise ValueError("Assistant ID is required")
        try:
            if kwargs.get("field_id") in MERGE_FIELDS:
                kwargs["is_merged"] = True
                kwargs["merged_name"] = kwargs.get("name")
                kwargs["name"] = MERGE_FIELDS.get(kwargs.get("field_id"))
            data_field = DataField(**kwargs)
            session.add(data_field)
            logger.info(f"Field {kwargs.get('field_id')} added to the database for assistant ID {assistant_id}.")
        except Exception as e:
            logger.warning(f"Could not add {kwargs.get('field_id')}:: {e}")
            raise e

    async def _create_encompass_default_fields(
        self, assistant_id, session, assistant_type: str = AssistantSubTypes.ENCOMPASS
    ):
        """Create default fields specific to the Encompass assistant."""

        logger.info(
            "Creating default fields for Encompass"
            f" {'Sales' if assistant_type in [AssistantSubTypes.ENCOMPASS_SALES] else ''} assistant"
        )
        from tools.fields.encompass._default import DISPLAY_FIELDS, FIELD_MAPPING, FIELD_TYPE_MAPPING, SEARCH_FIELDS

        client_module = settings.CLIENT_NAME.lower().replace(" ", "_")
        try:
            client_fields = importlib.import_module(f"tools.fields.encompass.{client_module}")
        except ImportError:
            logger.warning(f"Fields for {client_module} not found! Using default fields")
            client_fields = importlib.import_module("tools.fields.encompass._default")

        FIELD_MAPPING.update(getattr(client_fields, "FIELD_MAPPING", {}))
        FIELD_TYPE_MAPPING.update(getattr(client_fields, "FIELD_TYPE_MAPPING", {}))
        SEARCH_FIELDS.extend(getattr(client_fields, "SEARCH_FIELDS", []))
        DISPLAY_FIELDS.extend(getattr(client_fields, "DISPLAY_FIELDS")) or list(FIELD_MAPPING.values())
        data_fields = await self._fetch_assistant_data_fields(assistant_id, session)
        db_field_ids = [data_field.field_id for data_field in data_fields]
        result = await session.execute(
            select(func.max(DataField.display_order)).where(DataField.assistant_id == assistant_id)
        )
        max_display_order = result.scalar() or 0
        field_tasks = [
            self._add_field_to_db(
                session,
                assistant_id=assistant_id,
                name=name,
                field_id=field_id,
                type=self.field_type_mapper.get(FIELD_TYPE_MAPPING.get(field_id, "string"), "text"),
                is_searchable=name in SEARCH_FIELDS,
                display_in_ui=name in DISPLAY_FIELDS,
                display_order=max_display_order + idx + 1,
            )
            for idx, (field_id, name) in enumerate(FIELD_MAPPING.items())
            if field_id not in db_field_ids
        ]

        await asyncio.gather(*field_tasks)
        await session.commit()

    async def _add_totalexpert_field(
        self, session, assistant_id, field_mapping, display_fields=None, search_fields=None, folder=""
    ):
        """Add default fields for a given assistant."""
        display_fields = display_fields or []
        search_fields = search_fields or []

        from tools.fields.totalexpert._default import CONTACT_FIELD_TYPE_MAPPING, LOAN_FIELD_TYPE_MAPPING

        data_fields = await self._fetch_assistant_data_fields(assistant_id, session, folder=folder)
        db_field_ids = [data_field.field_id for data_field in data_fields]
        result = await session.execute(
            select(func.max(DataField.display_order)).where(DataField.assistant_id == assistant_id)
        )
        max_display_order = result.scalar() or 0
        field_tasks = [
            self._add_field_to_db(
                session,
                assistant_id=assistant_id,
                field_id=field_id,
                name=field_name,
                type=self.field_type_mapper.get(
                    (
                        LOAN_FIELD_TYPE_MAPPING.get(field_id, "string")
                        if folder == DataFieldFolder.LOAN
                        else CONTACT_FIELD_TYPE_MAPPING.get(field_id, "string")
                    ),
                    "text",
                ),
                is_searchable=field_name in search_fields,
                display_in_ui=field_name in display_fields,
                folder=folder,
                display_order=max_display_order + idx + 1,
            )
            for idx, (field_id, field_name) in enumerate(field_mapping.items())
            if field_id not in db_field_ids
        ]
        await asyncio.gather(*field_tasks)
        await session.commit()

    async def _create_totalexpert_default_fields(self, assistant_id, session):
        """Setup default fields for the TotalExpert assistant."""

        logger.info("Creating default fields for Encompass assistant")
        from tools.fields.totalexpert._default import (
            ACTIVITY_DISPLAY_FIELDS,
            ACTIVITY_FIELD_MAPPINGS,
            ACTIVITY_SEARCH_FIELDS,
            TE_CONTACT_DISPLAY_FIELDS,
            TE_CONTACT_FIELD_MAPPING,
            TE_CONTACT_SEARCH_FIELDS,
            TE_LOAN_DISPLAY_FIELDS,
            TE_LOAN_FIELD_MAPPING,
            TE_LOAN_SEARCH_FIELDS,
        )

        await self._add_totalexpert_field(
            session,
            assistant_id,
            TE_LOAN_FIELD_MAPPING,
            TE_LOAN_DISPLAY_FIELDS,
            TE_LOAN_SEARCH_FIELDS,
            folder=DataFieldFolder.LOAN,
        )
        await self._add_totalexpert_field(
            session,
            assistant_id,
            TE_CONTACT_FIELD_MAPPING,
            TE_CONTACT_DISPLAY_FIELDS,
            TE_CONTACT_SEARCH_FIELDS,
            folder=DataFieldFolder.CONTACT,
        )
        await self._add_totalexpert_field(
            session,
            assistant_id,
            ACTIVITY_FIELD_MAPPINGS,
            ACTIVITY_DISPLAY_FIELDS,
            ACTIVITY_SEARCH_FIELDS,
            folder=DataFieldFolder.ACTIVITY,
        )
