from auth.cognito import get_or_create_auth_user
from db.models import Conversation, Feedback, Message, User
from db.session import get_session
from fastapi import APIRouter, Depends, HTTPException, status
from schema.enums import RoleTypes
from schema.feedback import <PERSON><PERSON><PERSON><PERSON><PERSON>, FeedbackRead, FeedbackUpdate
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

router = APIRouter()


async def get_feedback_or_404(
    id: str, session: AsyncSession = Depends(get_session), user: User = Depends(get_or_create_auth_user)
) -> FeedbackRead:
    """
    Retrieve a feedback by its ID.

    Args:
        id (str): The ID of the feedback to retrieve.
        session (AsyncSession): The database session, automatically provided by the `get_session` dependency.

    Returns:
        FeedbackRead: A pydantic model containing the feedback details.

    Raises:
        HTTPException: 404 Not Found if the feedback does not exist.
    """
    query = (
        select(Feedback)
        .options(selectinload(Feedback.message).selectinload(Message.conversation))
        .where(Feedback.id == id)
    )

    result = await session.execute(query)
    feedback = result.scalar_one_or_none()

    if feedback is None:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND)

    if user.role == RoleTypes.GENERAL and feedback.message.conversation.user_id != user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, detail="You do not have permission to access this feedback."
        )

    return feedback


@router.post(
    "/",
    response_model=FeedbackRead,
    status_code=status.HTTP_201_CREATED,
)
async def create_feedback(
    feedback: FeedbackCreate,
    session: AsyncSession = Depends(get_session),
    user: User = Depends(get_or_create_auth_user),
) -> Feedback:
    """
    Create a new feedback entry.

    Args:
        feedback (FeedbackCreate): The feedback data to create.
        session (AsyncSession): The database session, automatically provided by the `get_session` dependency.

    Returns:
        FeedbackRead: A pydantic model containing the created feedback details.
    """
    query = (
        select(Message)
        .join(Message.conversation)
        .where(Message.id == feedback.message_id)
        .where(Conversation.user_id == user.id)
    )
    result = await session.execute(query)
    message = result.scalar_one_or_none()
    if message is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Message not found or you do not have permission."
        )

    feedback = Feedback(
        message_id=feedback.message_id,
        user_rating=feedback.user_rating,
        feedback_text=feedback.feedback_text,
    )
    session.add(feedback)
    await session.commit()

    return feedback


@router.get("/{id}/", response_model=FeedbackRead)
async def get_feedback(id: str, feedback=Depends(get_feedback_or_404)) -> Feedback:
    """
    Retrieve a feedback by its ID.

    Args:
        id (str): The ID of the feedback to retrieve.
        feedback (Feedback): The feedback object, provided by the `get_feedback_or_404` dependency.

    Returns:
        FeedbackRead: A pydantic model containing the feedback details.
    """
    return feedback


@router.patch("/{id}/", response_model=FeedbackRead)
async def update_feedback(
    feedback_update: FeedbackUpdate,
    feedback: Feedback = Depends(get_feedback_or_404),
    user: User = Depends(get_or_create_auth_user),
    session: AsyncSession = Depends(get_session),
) -> Feedback:
    """
    Update an existing feedback entry.

    Args:
        feedback_update (FeedbackUpdate): The updated feedback data.
        feedback (Feedback): The existing feedback object, provided by the `get_feedback_or_404` dependency.
        session (AsyncSession): The database session, automatically provided by the `get_session` dependency.

    Returns:
        FeedbackRead: A pydantic model containing the updated feedback details.
    """
    if feedback.message.conversation.user_id != user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, detail="You do not have permission to update this feedback."
        )

    feedback_update_dict = feedback_update.model_dump(exclude_unset=True)
    for k, v in feedback_update_dict.items():
        setattr(feedback, k, v)

    session.add(feedback)
    await session.commit()
    await session.refresh(feedback)

    return feedback


@router.delete(
    "/{id}/",
    status_code=status.HTTP_204_NO_CONTENT,
)
async def delete_feedback(
    feedback: Feedback = Depends(get_feedback_or_404),
    session: AsyncSession = Depends(get_session),
    user: User = Depends(get_or_create_auth_user),
):
    """
    Delete a feedback entry by its ID.

    Args:
        feedback (Feedback): The feedback object, provided by the `get_feedback_or_404` dependency.
        session (AsyncSession): The database session, automatically provided by the `get_session` dependency.

    Returns:
        None: A 204 No Content status code if the deletion is successful.
    """
    if feedback.message.conversation.user_id != user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, detail="You do not have permission to delete this feedback."
        )

    await session.delete(feedback)
    await session.commit()
