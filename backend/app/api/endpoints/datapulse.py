from api.endpoints.assistant import get_assistant_or_404
from api.utils.pagination import page_number_pagination
from auth.cognito import check_user_permissions, get_or_create_auth_user
from db.models import Assistant, User
from fastapi import APIRouter, Body, Depends, HTTPException, Query, status
from loguru import logger
from schema.encompass import DataPulseReport, DataPulseSummaryRequest, DataPulseUpdateResponse, UpdateDataPulseReport
from tools.datapulse import client_datapulse_settings
from tools.functions.encompass.datapulse import datapulse_function
from tools.utils.encompass.datapulse import datapulse_utils

router = APIRouter()


@router.get("/status/", dependencies=[Depends(get_or_create_auth_user)])
async def get_datapulse_status():
    try:
        return {
            "status": client_datapulse_settings.DATAPULSE_ENABLE,
        }
    except Exception as e:
        logger.error(f"Error getting datapulse status: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to get datapulse status")


@router.get("/reports/", dependencies=[Depends(get_or_create_auth_user)])
async def get_datapulse_reports(
    assistant_id: str = Query(..., description="ID of the assistant to fetch reports for"),
):
    try:
        data = await datapulse_function.get_reports(assistant_id=assistant_id)
        return data
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting datapulse reports: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to get datapulse reports"
        )


@router.get("/generate-report/")
async def generate_datapulse_report(
    assistant_id: str = Query(..., description="ID of the assistant to fetch reports for"),
    user: User = Depends(get_or_create_auth_user),
    report_id: str = Query(..., description="ID of the report to generate"),
    regenerate: bool = Query(False, description="Regenerate the report with updated data"),
    start_date: str = Query(None, description="Start date for the report in YYYY-MM-DD format"),
    end_date: str = Query(None, description="End date for the report in YYYY-MM-DD format"),
):
    try:
        if regenerate:
            data = await datapulse_function.regenerate_report(
                assistant_id=assistant_id,
                user=user,
                report_id=report_id,
                start_date=start_date,
                end_date=end_date,
            )
        else:
            data = await datapulse_function.generate_report(
                assistant_id=assistant_id,
                user=user,
                report_id=report_id,
                start_date=start_date,
                end_date=end_date,
            )
        return data
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error generating datapulse report: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to generate datapulse report"
        )


@router.post("/summary/")
async def get_datapulse_summary(
    user: User = Depends(get_or_create_auth_user),
    report_key: str = Query(..., description="Report key to fetch summary for"),
    assistant_id: str = Query(..., description="ID of the assistant to fetch reports for"),
    body: DataPulseSummaryRequest = Body(...),
    pagination: tuple[int, int] = Depends(page_number_pagination),
):
    try:
        page, size = pagination
        data = await datapulse_function.get_summary(
            user=user,
            report_key=report_key,
            loid=body.loid,
            assistant_id=assistant_id,
            page=page,
            size=size,
        )
        return data
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting datapulse summary: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to get datapulse summary"
        )


@router.get("/full-report/", dependencies=[Depends(get_or_create_auth_user)])
async def get_complete_report(
    report_key: str = Query(..., description="Report key to fetch complete report"),
):
    try:
        data = await datapulse_function.get_complete_report(
            report_key=report_key,
        )
        return data
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting complete report: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to get complete report")


@router.patch(
    "/admin/report/{report_id}",
    dependencies=[Depends(check_user_permissions)],
    response_model=DataPulseUpdateResponse,
)
async def update_datapulse_report(
    report_id: str,
    update_data: UpdateDataPulseReport = Body(..., description="Data to update the report with"),
    assistant: Assistant = Depends(get_assistant_or_404),
) -> DataPulseUpdateResponse:
    try:
        data = await datapulse_utils.update_report(
            report_id=report_id, update_data=update_data, assistant_id=assistant.id
        )
        return data
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating datapulse report: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to update datapulse report"
        )


@router.get("/admin/reports/", dependencies=[Depends(check_user_permissions)], response_model=list[DataPulseReport])
async def get_all_datapulse_reports(
    assistant: Assistant = Depends(get_assistant_or_404),
) -> list[DataPulseReport]:
    try:
        data = await datapulse_utils.get_reports_from_db(assistant_id=assistant.id)
        return data
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting datapulse reports: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to get datapulse reports"
        )
