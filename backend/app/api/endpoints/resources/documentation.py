from collections.abc import Sequence

import pytz
from api.endpoints.assistant import get_assistant_or_404
from auth.cognito import check_user_permissions
from db.models import Assistant, Documentation
from db.session import get_session
from fastapi import APIRouter, Depends, HTTPException, status
from hooks.pinecone_hook import get_generic_hook
from schema.resources.documentation import DocumentationCreate, DocumentationRead, DocumentationUpdate
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload
from tasks.indexing import index_documentation

router = APIRouter()


async def get_assistant_documentation_or_404(
    id: str, doc_id: str, session: AsyncSession = Depends(get_session)
) -> Documentation:
    """
    Retrieve a specific documentation record by its ID for a given assistant.

    Fetches a documentation record from the database based on its ID and the assistant ID.

    Args:
        id (str): The ID of the assistant.
        doc_id (str): The ID of the documentation record.
        session (AsyncSession): The SQLAlchemy asynchronous session, provided by the `get_session` dependency.

    Returns:
        Documentation: The documentation record object.

    Raises:
        HTTPException: If the documentation record is not found, an HTTP 404 error is raised.
    """
    query = (
        select(Documentation)
        .options(selectinload(Documentation.assistant))
        .where(Documentation.id == doc_id, Documentation.assistant_id == id)
    )
    result = await session.execute(query)
    documentation = result.scalar_one_or_none()

    if documentation is None:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Documentation not found")

    return documentation


@router.get(
    "/v2/{id}/documentation/{doc_id}/",
    response_model=DocumentationRead,
    dependencies=[Depends(check_user_permissions)],
)
async def get_assistant_documentation(
    documentation: Documentation = Depends(get_assistant_documentation_or_404),
) -> Documentation:
    """
    Retrieve a specific documentation record by its ID.

    Fetches the details of a documentation record based on its ID.

    Args:
        documentation (Documentation): The documentation record object, provided by the
        `get_assistant_documentation_or_404` dependency.

    Returns:
        DocumentationRead: A pydantic model with the details of the documentation record.

    Raises:
        HTTPException: If the user does not have permissions, an HTTP 403 error is raised.
    """
    return documentation


@router.get(
    "/v2/{id}/documentation/", response_model=list[DocumentationRead], dependencies=[Depends(check_user_permissions)]
)
async def get_assistant_documentations(
    assistant: Assistant = Depends(get_assistant_or_404),
    session: AsyncSession = Depends(get_session),
) -> Sequence[Documentation]:
    """
    List all documentation records for a specific assistant.

    Retrieves a list of documentation records for an assistant.

    Args:
        assistant (Assistant): The assistant object, provided by the `get_assistant_or_404` dependency.
        session (AsyncSession): The SQLAlchemy asynchronous session, provided by the `get_session` dependency.

    Returns:
        list[DocumentationRead]: A list of pydantic models with details of the documentation records.

    Raises:
        HTTPException: If the user does not have permissions, an HTTP 403 error is raised.
    """
    query = select(Documentation).where(Documentation.assistant_id == assistant.id)
    result = await session.execute(query)
    return result.scalars().all()


@router.post("/v2/{id}/documentation/", status_code=status.HTTP_201_CREATED, response_model=DocumentationRead)
async def create_assistant_documentation(
    documentation: DocumentationCreate,
    assistant=Depends(get_assistant_or_404),
    session: AsyncSession = Depends(get_session),
    user=Depends(check_user_permissions),
) -> Documentation:
    """
    Create a new documentation record for a specific assistant.

    Adds a new documentation record to the database and triggers any associated indexing tasks.

    Args:
        documentation (DocumentationCreate): A pydantic model with the details of the new documentation record.
        assistant (Assistant): The assistant object, provided by the `get_assistant_or_404` dependency.
        session (AsyncSession): The SQLAlchemy asynchronous session, provided by the `get_session` dependency.
        user (User): The user making the request, provided by the `check_user_permissions` dependency.

    Returns:
        DocumentationRead: A pydantic model with the details of the created documentation record.

    Raises:
        HTTPException: If the user does not have permissions, an HTTP 403 error is raised.
    """
    user_info = {
        "user_id": str(user.id),
        "user_name": user.username,
        "email": user.email,
        "name": user.name,
    }

    if not documentation.categories:
        documentation.categories = assistant.categories

    documentation = Documentation(
        assistant_id=assistant.id,
        expiry_date=(
            documentation.expiry_date.astimezone(pytz.UTC).replace(tzinfo=None) if documentation.expiry_date else None
        ),
        meta={
            "user_info": {
                "created_by": user_info,
                "updated_by": user_info,
            }
        },
        **documentation.model_dump(exclude={"expiry_date"}),
    )
    session.add(documentation)
    await session.commit()
    index_documentation.delay(documentation.id)
    return documentation


@router.delete(
    "/v2/{id}/documentation/{doc_id}/",
    status_code=status.HTTP_204_NO_CONTENT,
    dependencies=[Depends(check_user_permissions)],
)
async def delete_assistant_documentation(
    documentation: Documentation = Depends(get_assistant_documentation_or_404),
    session: AsyncSession = Depends(get_session),
):
    """
    Delete a specific documentation record for an assistant.

    Removes a documentation record from the database and triggers any associated cleanup tasks.

    Args:
        documentation (Documentation): The documentation record object to be deleted, provided by the
        `get_assistant_documentation_or_404` dependency.
        session (AsyncSession): The SQLAlchemy asynchronous session, provided by the `get_session` dependency.

    Returns:
        None: No content is returned upon successful deletion.

    Raises:
        HTTPException: If the user does not have permissions, an HTTP 403 error is raised.
    """
    await session.delete(documentation)
    await session.commit()

    generic_hook = await get_generic_hook(documentation.assistant_id)
    await generic_hook.delete_document(documentation.id, namespace=documentation.assistant_id)


@router.patch("/v2/{id}/documentation/{doc_id}/", response_model=DocumentationRead, status_code=status.HTTP_200_OK)
async def update_assistant_documentation(
    documentation_update: DocumentationUpdate,
    documentation: Documentation = Depends(get_assistant_documentation_or_404),
    session: AsyncSession = Depends(get_session),
    user=Depends(check_user_permissions),
) -> Documentation:
    """
    Update a specific documentation record for an assistant.

    Updates the details of a documentation record based on the provided data.

    Args:
        documentation_update (DocumentationUpdate): A pydantic model with updated documentation details.
        documentation (Documentation): The documentation record object to be updated, provided by
        the `get_assistant_documentation_or_404` dependency.
        session (AsyncSession): The SQLAlchemy asynchronous session, provided by the `get_session` dependency.
        user (User): The user making the request, provided by the `check_user_permissions` dependency.

    Returns:
        DocumentationRead: A pydantic model with the updated details of the documentation record.

    Raises:
        HTTPException: If the user does not have permissions, an HTTP 403 error is raised.
    """
    user_info = {
        "user_id": str(user.id),
        "user_name": user.username,
        "email": user.email,
        "name": user.name,
    }

    if not documentation_update.categories:
        documentation_update.categories = documentation.assistant.categories

    documentation_update_dict = documentation_update.model_dump(exclude_unset=True)
    expiry_date = documentation_update_dict.pop("expiry_date", None)
    if expiry_date:
        documentation.expiry_date = expiry_date.astimezone(pytz.UTC).replace(tzinfo=None)

    # Update fields from DocumentUpdate
    for var, value in documentation_update_dict.items():
        setattr(documentation, var, value)

    # Update metadata with user information
    if documentation.meta:
        documentation.meta = {
            **documentation.meta,
            "user_info": {**documentation.meta["user_info"], "updated_by": user_info},
        }
    else:
        documentation.meta = {"user_info": {"updated_by": user_info}}
    session.add(documentation)
    await session.commit()
    await session.refresh(documentation)

    generic_hook = await get_generic_hook(documentation.assistant_id)
    await generic_hook.delete_document(documentation.id, namespace=documentation.assistant_id)
    index_documentation.delay(documentation.id)

    return documentation
