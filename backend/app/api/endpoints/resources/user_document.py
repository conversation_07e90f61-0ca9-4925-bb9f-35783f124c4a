from collections.abc import Sequence

from api.endpoints.assistant import get_personal_assistant_or_404
from api.utils.pagination import page_number_pagination
from api.utils.utils import disarm_malicious_content, get_extension_and_mime_type, is_malicious_pdf
from auth.cognito import get_or_create_auth_user
from config import settings
from db.models import Assistant, Document, User, UserDocument
from db.session import get_session
from fastapi import APIRouter, Depends, File, HTTPException, Query, UploadFile, status
from hooks.pinecone_hook import get_generic_hook
from hooks.s3 import get_s3_hook
from schema.enums import AssistantSubTypes, AssistantTypes, DocumentTypes
from schema.resources.document import DocumentCreate, DocumentRead, DocumentReadList, DocumentUpdate
from sqlalchemy import String, asc, cast, desc, func, select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload
from tasks.indexing import process_user_document

router = APIRouter()


@router.post("/v2/{id}/user/document/upload/", status_code=status.HTTP_200_OK)
async def upload_user_document(
    file: UploadFile = File(None, description="File to upload"),
    assistant=Depends(get_personal_assistant_or_404),
    user=Depends(get_or_create_auth_user),
):
    # get extension and mime type
    file_content = await file.read()
    ext, mime = get_extension_and_mime_type(file.filename, file_content)

    # Check file extension
    if ext not in settings.ALLOWED_EXTENSIONS:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=f"File type '{ext}' is not allowed.")

    # Check MIME type
    if mime not in settings.ALLOWED_MIME_TYPES:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Could not verify a safe MIME type for this file."
        )

    # Validate pdf file
    s3_hook = await get_s3_hook(assistant.id)
    if ext == "pdf" or mime == "application/pdf":
        object_name = f"{assistant.id}/{user.id}/{file.filename}"

        if is_malicious_pdf(file_content, file.filename):
            file_content = await disarm_malicious_content(file_content, file.filename)
            if not file_content:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="This PDF file is potentially malicious. Disarming failed, so the upload was aborted.",
                )

        return s3_hook.upload_file(object_name=object_name, content=file_content, content_type=mime)

    # Check assistant subtype
    if assistant.sub_type not in [
        AssistantSubTypes.IMAGE,
        AssistantSubTypes.ANALYSIS,
        AssistantSubTypes.RETRIEVAL,
        AssistantSubTypes.DOCUMENT_UNDERSTANDING,
    ]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Assistant '{assistant.sub_type}' is not allowed to upload document.",
        )

    # Construct S3 object key
    object_name = f"{assistant.id}/{user.id}/{file.filename}"
    return s3_hook.upload_file(object_name=object_name, content=file_content, content_type=mime)


@router.get("/v2/{id}/user/document/presigned/", status_code=status.HTTP_200_OK)
async def get_user_document_presigned_url_download(
    filename: str, assistant=Depends(get_personal_assistant_or_404), user=Depends(get_or_create_auth_user)
):
    object_name = f"{assistant.id}/{user.id}/{filename}"
    s3_hook = await get_s3_hook(assistant.id)
    return s3_hook.get_presigned_url_for_download(object_name=object_name)


@router.get(
    "/v2/{id}/presigned/renew/", status_code=status.HTTP_200_OK, dependencies=[Depends(get_or_create_auth_user)]
)
async def renew_user_document_presigned_url(id: str, expired_url: str):
    s3_hook = await get_s3_hook(id)
    return s3_hook.get_presigned_url_from_link(expired_url)


async def get_user_document_or_404(
    id: str,
    doc_id: str,
    user: User = Depends(get_or_create_auth_user),
    session: AsyncSession = Depends(get_session),
) -> Document:
    query = (
        select(Document)
        .join(UserDocument)
        .options(selectinload(Document.assistant))
        .where(
            Document.id == doc_id,
            Document.assistant_id == id,
            UserDocument.user_id == user.id,
        )
    )
    result = await session.execute(query)
    document = result.scalar_one_or_none()

    if document is None:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="User document not found")

    return document


@router.get("/v2/{id}/user/document/{doc_id}/", response_model=DocumentRead, status_code=status.HTTP_200_OK)
async def get_user_document(document: Document = Depends(get_user_document_or_404)) -> Document:
    return document


@router.get("/v2/{id}/user/document/", response_model=DocumentReadList, status_code=status.HTTP_200_OK)
async def get_user_documents(
    assistant: Assistant = Depends(get_personal_assistant_or_404),
    user: User = Depends(get_or_create_auth_user),
    pagination: tuple[int, int] = Depends(page_number_pagination),
    session: AsyncSession = Depends(get_session),
    title: str = Query(None, description="Filter by title"),
    categories: Sequence[str] = Query(None, description="Filter by categories"),
    sort_order: str = Query("desc", description="Sort order by created date ('asc' or 'desc')"),
    type: DocumentTypes = Query(None, description="Filter by document type"),
) -> DocumentReadList:
    page, size = pagination
    query = (
        select(Document)
        .join(UserDocument)
        .where(UserDocument.user_id == user.id, Document.assistant_id == assistant.id)
    )
    if title:
        query = query.where(Document.title.ilike(f"%{title}%"))
    if categories:
        for category in categories:
            query = query.where(cast(Document.categories, String).ilike(f"%{category}%"))
    if type:
        query = query.where(Document.type == type)

    total_documents = await session.execute(select(func.count()).select_from(query))
    count = total_documents.scalar()

    # Validate the sort order
    if sort_order.lower() not in ["asc", "desc"]:
        sort_order = "desc"  # Default to descending if invalid

    # Assign the sorting function based on the validated sort_order
    sort_function = desc if sort_order == "desc" else asc
    query = query.order_by(sort_function(Document.created_at))

    query = query.offset((page - 1) * size).limit(size)
    result = await session.execute(query)
    return {
        "count": count,
        "data": result.scalars().all(),
    }


@router.post("/v2/{id}/user/document/", status_code=status.HTTP_201_CREATED, response_model=DocumentRead)
async def create_user_document(
    document: DocumentCreate,
    assistant=Depends(get_personal_assistant_or_404),
    session: AsyncSession = Depends(get_session),
    user=Depends(get_or_create_auth_user),
) -> Document:
    user_info = {
        "user_id": str(user.id),
        "user_name": user.username,
        "email": user.email,
        "name": user.name,
    }

    if not document.categories:
        document.categories = assistant.categories

    if document.meta:
        document.meta = {
            **document.meta,
            "user_info": {"created_by": user_info, "updated_by": user_info},
        }
    else:
        document.meta = {"user_info": {"created_by": user_info, "updated_by": user_info}}

    # Create the document associated with the assistant and user
    new_document = Document(
        assistant_id=assistant.id,
        **document.model_dump(),
    )

    session.add(new_document)
    await session.commit()

    # Associate the document with the user
    user_document = UserDocument(user_id=user.id, document_id=new_document.id)
    session.add(user_document)
    await session.commit()

    if assistant.type == AssistantTypes.PERSONAL and assistant.sub_type == AssistantSubTypes.RETRIEVAL:
        process_user_document.delay(new_document.id, str(user.id))
    return new_document


@router.delete(
    "/v2/{id}/user/document/{doc_id}/",
    status_code=status.HTTP_204_NO_CONTENT,
)
async def delete_user_document(
    session: AsyncSession = Depends(get_session),
    document: Document = Depends(get_user_document_or_404),
    user=Depends(get_or_create_auth_user),
):
    await session.delete(document)
    await session.commit()

    # Delete from S3
    s3_hook = await get_s3_hook(document.assistant_id)
    object_name = s3_hook.get_key_from_url(document.link)
    s3_hook.delete_object(object_name=object_name)

    # Delete from pinecone
    if (
        document.assistant.type == AssistantTypes.PERSONAL
        and document.assistant.sub_type == AssistantSubTypes.RETRIEVAL
    ):
        generic_hook = await get_generic_hook(document.assistant_id)
        await generic_hook.delete_document(document.id, namespace=f"{document.assistant_id}_{str(user.id)}")


@router.patch("/v2/{id}/user/document/{doc_id}/", response_model=DocumentRead, status_code=status.HTTP_200_OK)
async def update_user_document(
    document_update: DocumentUpdate,
    session: AsyncSession = Depends(get_session),
    user: User = Depends(get_or_create_auth_user),
    document: Document = Depends(get_user_document_or_404),
) -> Document:
    user_info = {
        "user_id": str(user.id),
        "user_name": user.username,
        "email": user.email,
        "name": user.name,
    }

    if document_update.categories is not None:
        if document_update.categories:
            categories = document_update.categories
            generic_hook = await get_generic_hook(document.assistant_id)
            await generic_hook.update_metadata(
                id=document.id, categories=categories, namespace=f"{document.assistant_id}_{str(user.id)}"
            )
        else:
            categories = document.assistant.categories
            document_update.categories = categories
            generic_hook = await get_generic_hook(document.assistant_id)
            await generic_hook.update_metadata(
                id=document.id, categories=categories, namespace=f"{document.assistant_id}_{str(user.id)}"
            )

    # Update fields from DocumentUpdate
    for var, value in document_update.model_dump(exclude_unset=True, exclude={"meta"}).items():
        setattr(document, var, value)

    # Update metadata with user information
    if document.meta:
        document.meta = {**document.meta, "user_info": {**document.meta["user_info"], "updated_by": user_info}}
    else:
        document.meta = {"user_info": {"updated_by": user_info}}

    if document_update.meta:
        document.meta.update(document_update.meta)

    session.add(document)
    await session.commit()
    await session.refresh(document)

    return document
