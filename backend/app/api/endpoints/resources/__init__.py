from fastapi import APIRouter

from .data_fields import router as data_fields_router
from .document import router as document_router
from .documentation import router as documentation_router
from .question_answer import router as question_answer_router
from .user_document import router as user_document_router
from .website import router as website_router

resources_router = APIRouter()

resources_router.include_router(document_router)
resources_router.include_router(question_answer_router)
resources_router.include_router(documentation_router)
resources_router.include_router(website_router)
resources_router.include_router(data_fields_router)
resources_router.include_router(user_document_router)
