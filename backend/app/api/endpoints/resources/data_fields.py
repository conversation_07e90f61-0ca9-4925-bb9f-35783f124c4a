import importlib
import json
from datetime import datetime
from typing import Literal

import redis.asyncio as aioredis
from api.endpoints.assistant import get_assistant_or_404
from api.utils.pagination import page_number_pagination
from auth.cognito import check_user_permissions
from config import settings
from db.models import Assistant, <PERSON>Field
from db.session import get_session
from fastapi import APIRouter, Depends, HTTPException, Query, Response, status
from loguru import logger
from schema.enums import AssistantSubTypes, DataFieldFolder, DataFieldTypes
from schema.resources.data_field import (
    DataFieldCreate,
    DataFieldRead,
    DataFieldReadList,
    DataFieldUpdate,
    ListMergedDateField,
    MergedDataField,
    MergedDataFieldDelete,
    MergedDataFieldInput,
)
from sqlalchemy import func, select, update
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload
from tools.utils.encompass.loan import loan_utils

router = APIRouter()


async def get_assistant_data_field_by_id_or_404(
    id: str, field_id: str, folder: DataFieldFolder | None = None, session: AsyncSession = Depends(get_session)
):
    query = (
        select(DataField)
        .options(selectinload(DataField.assistant))
        .where(DataField.field_id == field_id, DataField.assistant_id == id)
    )
    if folder:
        query = query.where(DataField.folder == folder)
    result = await session.execute(query)
    data_field = result.scalar_one_or_none()

    if data_field is None:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Field not found!")

    return data_field


async def get_assistant_data_fields_or_404(id: str, f_id: str, session: AsyncSession = Depends(get_session)):
    query = (
        select(DataField)
        .options(selectinload(DataField.assistant))
        .where(DataField.id == f_id, DataField.assistant_id == id)
    )
    result = await session.execute(query)
    data_field = result.scalar_one_or_none()

    if data_field is None:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Field not found!")

    return data_field


@router.get(
    "/v2/{id}/data-field/{f_id}/",
    response_model=DataFieldRead,
    dependencies=[Depends(check_user_permissions)],
)
async def get_assistant_data_field(
    data_field: DataField = Depends(get_assistant_data_fields_or_404),
) -> DataField:
    return data_field


@router.get(
    "/v2/{id}/data-field/",
    response_model=DataFieldReadList,
    dependencies=[Depends(check_user_permissions)],
)
async def get_assistant_data_fields(
    assistant: Assistant = Depends(get_assistant_or_404),
    pagination: tuple[int, int] = Depends(page_number_pagination),
    session: AsyncSession = Depends(get_session),
    sort_order: Literal["asc", "desc"] = Query("asc", description="Sort by display order ('asc' or 'desc')"),
    type: DataFieldTypes = Query(None, description="Filter by field type"),
    folder: DataFieldFolder = Query(
        None, description="Field Folder/ Only applicable for totalexpert assistant (eg. Loan, contact, activity etc.)"
    ),
    q: str = Query(None, description="Search query for Field ID and Descriptive Name"),
) -> DataFieldReadList:
    page, size = pagination
    query = select(DataField).where(DataField.assistant_id == assistant.id)

    if type:
        query = query.where(DataField.type == type)

    if folder:
        query = query.where(DataField.folder == folder)

    if q:
        query = query.where(DataField.field_id.ilike(f"%{q}%") | DataField.name.ilike(f"%{q}%"))

    total_data_fields = await session.execute(select(func.count()).select_from(query))
    count = total_data_fields.scalar()

    if sort_order == "asc":
        query = query.order_by(DataField.display_order.asc())
    else:
        query = query.order_by(DataField.display_order.desc())

    query = query.offset((page - 1) * size).limit(size)
    result = await session.execute(query)
    data_fields = result.all()
    return {"count": count, "data": [DataFieldRead(**field.DataField.__dict__) for field in data_fields]}


@router.post("/v2/{id}/data-field/", status_code=status.HTTP_201_CREATED, response_model=DataFieldRead)
async def create_assistant_data_field(
    data_field: DataFieldCreate,
    assistant=Depends(get_assistant_or_404),
    session: AsyncSession = Depends(get_session),
    user=Depends(check_user_permissions),
) -> DataFieldRead:

    user_info = {
        "user_id": str(user.id),
        "user_name": user.username,
        "email": user.email,
        "name": user.name,
    }

    data_field.field_id = data_field.field_id.strip()
    # if assistant_id is encompass or encompass_sales
    if assistant.sub_type in [AssistantSubTypes.ENCOMPASS, AssistantSubTypes.ENCOMPASS_SALES]:
        # check if the field_id starts with 'Fields.' or not
        if not data_field.field_id.startswith(("Fields.", "Loan.")):
            # append 'Fields.' to the field_id
            data_field.field_id = f"Fields.{data_field.field_id}"

    # strip any spaces in field id
    data_field.field_id = data_field.field_id.strip()
    data_field.name = data_field.name.strip()

    # check if field_id already exists
    try:
        if data_field.folder:
            folder = data_field.folder
        else:
            folder = None
        _ = await get_assistant_data_field_by_id_or_404(
            id=assistant.id, field_id=data_field.field_id, folder=folder, session=session
        )
    except HTTPException:
        pass
    else:
        raise HTTPException(status_code=status.HTTP_409_CONFLICT, detail="Field ID already exists")

    # TODO: Check if field is valid or not from totalexpert fields

    # Get the current maximum display_order value
    result = await session.execute(
        select(func.max(DataField.display_order)).where(DataField.assistant_id == assistant.id)
    )
    max_display_order = result.scalar() or 0

    if data_field.meta:
        data_field.meta = {
            **data_field.meta,
            "user_info": {"created_by": user_info, "updated_by": user_info},
        }
    else:
        data_field.meta = {"user_info": {"created_by": user_info, "updated_by": user_info}}

    if assistant.sub_type in [
        AssistantSubTypes.ENCOMPASS,
        AssistantSubTypes.ENCOMPASS_SALES,
    ]:
        field_details = await loan_utils.get_encompass_field_details([data_field.field_id])

        if not field_details:
            logger.error(f"Field {data_field.field_id} not found in Encompass!")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Field {data_field.field_id} not found in Encompass!",
            )

        field_details = field_details[0]

        # TODO: Map User entered value and value received from Encompass

        # data_field.field_id = field_details.get("canonicalName")
        # data_field.type = DB_FIELD_TYPE_MAP.get(field_details.get("fieldFormat"))
        data_field = DataField(
            assistant_id=assistant.id,
            display_order=max_display_order + 1,
            **data_field.model_dump(),
        )
        session.add(data_field)
        await session.commit()

        if field_details.get("isOptionListFixed", False):
            option_values = [opt.get("reportingDatabaseValue") for opt in field_details.get("options", [])]
            try:
                redis = await aioredis.from_url(settings.REDIS_URL)
                current_fields = await redis.get("encompass_fields_with_options")
                current_fields = json.loads(current_fields) if current_fields else {}
                current_fields[data_field.field_id] = option_values
                await redis.set("encompass_fields_with_options", json.dumps(current_fields))
            except Exception as e:
                logger.error(f"Error updating redis cache for field options: {e}")

        return data_field
    else:
        data_field = DataField(
            assistant_id=assistant.id,
            display_order=max_display_order + 1,
            **data_field.model_dump(),
        )
        session.add(data_field)
        await session.commit()
        return data_field


@router.delete(
    "/v2/{id}/data-field/{f_id}/",
    status_code=status.HTTP_204_NO_CONTENT,
    dependencies=[Depends(check_user_permissions)],
)
async def delete_data_field(
    data_field: DataField = Depends(get_assistant_data_fields_or_404),
    session: AsyncSession = Depends(get_session),
):
    await session.delete(data_field)
    await session.commit()
    return Response(status_code=status.HTTP_204_NO_CONTENT)


async def adjust_display_order(data_field: DataField, new_display_order: int, session: AsyncSession) -> None:
    if new_display_order < data_field.display_order:
        # move down
        await session.execute(
            update(DataField)
            .where(DataField.assistant_id == data_field.assistant_id)
            .where(DataField.display_order >= new_display_order)
            .where(DataField.display_order < data_field.display_order)
            .values(display_order=DataField.display_order + 1)
        )
    elif new_display_order > data_field.display_order:
        # move up
        await session.execute(
            update(DataField)
            .where(DataField.assistant_id == data_field.assistant_id)
            .where(DataField.display_order <= new_display_order)
            .where(DataField.display_order > data_field.display_order)
            .values(display_order=DataField.display_order - 1)
        )
    return None


@router.patch("/v2/{id}/data-field/{f_id}/", response_model=DataFieldRead, status_code=status.HTTP_200_OK)
async def update_assistant_date_field(
    data_field_update: DataFieldUpdate,
    id: str,
    data_field: DataField = Depends(get_assistant_data_fields_or_404),
    session: AsyncSession = Depends(get_session),
    user=Depends(check_user_permissions),
) -> DataField:
    user_info = {
        "user_id": str(user.id),
        "user_name": user.username,
        "email": user.email,
        "name": user.name,
    }

    # Check if new field_id already exists
    if data_field_update.field_id:
        try:
            existing_field = await get_assistant_data_field_by_id_or_404(
                id=id, field_id=data_field_update.field_id, folder=data_field.folder, session=session
            )
        except HTTPException:
            pass
        else:
            if existing_field.id != data_field.id:
                raise HTTPException(status_code=409, detail="Field ID already exists")

    data_field_update_dict = data_field_update.model_dump(exclude_unset=True, exclude={"meta"})
    new_display_order = data_field_update_dict.get("display_order", data_field.display_order)

    if new_display_order != data_field.display_order:
        await adjust_display_order(data_field, new_display_order, session)

    # Update fields with new values
    for var, value in data_field_update_dict.items():
        setattr(data_field, var, value)

    # Update metadata with user information
    if data_field.meta:
        data_field.meta = {
            **data_field.meta,
            "user_info": {**data_field.meta["user_info"], "updated_by": user_info},
        }
    else:
        data_field.meta = {"user_info": {"updated_by": user_info}}

    if data_field_update.meta:
        data_field.meta.update(data_field_update.meta)

    session.add(data_field)
    await session.commit()
    await session.refresh(data_field)

    return data_field


@router.post("/v2/{id}/data-field/default/", status_code=status.HTTP_201_CREATED, response_model=DataFieldRead)
async def create_default_fields_for_assistant(
    assistant=Depends(get_assistant_or_404),
    session: AsyncSession = Depends(get_session),
    user=Depends(check_user_permissions),
) -> DataFieldRead:

    from tools.fields.encompass._default import FIELD_MAPPING, FIELD_TYPE_MAPPING, SEARCH_FIELDS

    client_module = settings.CLIENT_NAME.lower().replace(" ", "_")

    try:
        client_fields = importlib.import_module(f"tools.fields.encompass.{client_module}")
    except ImportError:
        logger.warning(f"Fields for {client_module} not found! Using default fields")
        client_fields = importlib.import_module("tools.fields.encompass._default")

    FIELD_MAPPING.update(getattr(client_fields, "FIELD_MAPPING", {}))
    FIELD_TYPE_MAPPING.update(getattr(client_fields, "FIELD_TYPE_MAPPING", {}))
    SEARCH_FIELDS.extend(getattr(client_fields, "SEARCH_FIELDS", []))
    DISPLAY_FIELDS = getattr(client_fields, "DISPLAY_FIELDS") or list(FIELD_MAPPING.values())

    field_type_mapper = {datetime: "date", float: "decimalnumber", int: "integernumber", "string": "text"}

    user_info = {
        "user_id": str(user.id),
        "user_name": user.username,
        "email": user.email,
        "name": user.name,
    }

    for idx in range(len(list(FIELD_MAPPING.values()))):
        # check if field_id already exists
        field_id = list(FIELD_MAPPING.keys())[idx]
        name = FIELD_MAPPING.get(field_id)
        try:
            _ = await get_assistant_data_field_by_id_or_404(id=assistant.id, field_id=field_id, session=session)
        except HTTPException:
            pass
        else:
            continue

        try:
            # Get the current maximum display_order value for a specific assistant
            result = await session.execute(
                select(func.max(DataField.display_order)).where(DataField.assistant_id == assistant.id)
            )
            max_display_order = result.scalar() or 0
            data_field = DataField(
                assistant_id=assistant.id,
                name=FIELD_MAPPING.get(field_id),
                field_id=field_id,
                type=field_type_mapper.get(FIELD_TYPE_MAPPING.get(field_id, "string"), "text"),
                is_searchable=True if name in SEARCH_FIELDS else False,
                display_in_ui=True if name in DISPLAY_FIELDS else False,
                display_order=max_display_order + 1,
            )
            data_field.meta = {"user_info": {"created_by": user_info, "updated_by": user_info}}
            session.add(data_field)
            await session.commit()

        except Exception as e:
            logger.warning(f"Could not add {list(FIELD_MAPPING.keys())[idx]}:: {e}")
            continue

    return Response(status_code=status.HTTP_201_CREATED)


@router.post("/v2/{id}/data-field/merge/field/")
async def create_merge_fields(
    merged_data_field: MergedDataFieldInput,
    assistant=Depends(get_assistant_or_404),
    session: AsyncSession = Depends(get_session),
    user=Depends(check_user_permissions),
) -> list[DataFieldRead]:
    # check if fields exist
    fields = merged_data_field.field_ids
    for field in fields:
        try:
            _ = await get_assistant_data_field_by_id_or_404(id=assistant.id, field_id=field, session=session)
        except Exception:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=f"Field {field} not found!")

    # merge fields
    user_info = {
        "user_id": str(user.id),
        "user_name": user.username,
        "email": user.email,
        "name": user.name,
    }
    data_fields = []

    for field_id in fields:
        data_field = await get_assistant_data_field_by_id_or_404(id=assistant.id, field_id=field_id, session=session)
        data_field.is_merged = True
        data_field.merged_name = merged_data_field.name

        data_field.meta = {
            **data_field.meta,
            "user_info": {**data_field.meta["user_info"], "updated_by": user_info},
        }
        data_fields.append(data_field)
        session.add(data_field)
        await session.commit()
        await session.refresh(data_field)

    return data_fields


@router.get("/v2/{id}/data-field/merge/field/")
async def list_merged_fields(
    assistant: Assistant = Depends(get_assistant_or_404),
    pagination: tuple[int, int] = Depends(page_number_pagination),
    session: AsyncSession = Depends(get_session),
) -> ListMergedDateField:
    page, size = pagination
    query = select(DataField).where(DataField.assistant_id == assistant.id, DataField.is_merged)
    query = query.offset((page - 1) * size).limit(size)
    result = await session.execute(query)
    merged_data_fields = result.all()
    merged_names = {}
    for df in merged_data_fields:
        df_dict = df.DataField.__dict__
        merged_name = df_dict.get("merged_name")
        if merged_name not in merged_names:
            merged_names[merged_name] = [df_dict]
        else:
            merged_names[merged_name].append(df_dict)
    data = [MergedDataField(fields=fields, name=name) for name, fields in merged_names.items()]
    count = len(data)

    return {"count": count, "data": data}


# @router.patch("/v2/{id}/data-field/merge/field/")
# async def update_merged_field(
#     merged_data_field_input: MergedDataFieldInput,
#     assistant: Assistant = Depends(get_assistant_or_404),
#     session: AsyncSession = Depends(get_session),
# ) -> MergedDataFieldRead:
#     data_fields = await session.execute(select(DataField).where(DataField.id.in_(merged_data_field_input.field_ids)))
#     data_fields = data_fields.scalars().all()
#     for df in data_fields:
#         df.is_merged = True
#         df.merged_name = merged_data_field_input.name
#     await session.commit()
#     return MergedDataFieldRead(fields=data_fields, name=merged_data_field_input.name)


@router.delete(
    "/v2/{id}/data-field/merge/field/",
    status_code=status.HTTP_204_NO_CONTENT,
)
async def delete_merged_field(
    merged_data_field: MergedDataFieldDelete,
    assistant: Assistant = Depends(get_assistant_or_404),
    session: AsyncSession = Depends(get_session),
):
    data_fields = await session.execute(
        select(DataField)
        .where(DataField.field_id.in_(merged_data_field.field_ids))
        .where(DataField.assistant_id == assistant.id)
    )
    data_fields = data_fields.scalars().all()
    for df in data_fields:
        df.is_merged = False
        df.merged_name = None
        session.add(df)
        await session.commit()
        await session.refresh(df)


@router.post(
    "/v2/{id}/data-field/totalexpert/default/", status_code=status.HTTP_201_CREATED, response_model=DataFieldRead
)
async def create_totalexpert_fields(
    assistant=Depends(get_assistant_or_404),
    session: AsyncSession = Depends(get_session),
    user=Depends(check_user_permissions),
) -> DataFieldRead:
    from tools.fields.totalexpert._default import (
        ACTIVITY_FIELD_MAPPINGS,
        LOAN_FIELD_TYPE_MAPPING,
        TE_CONTACT_DISPLAY_FIELDS,
        TE_CONTACT_FIELD_MAPPING,
        TE_CONTACT_SEARCH_FIELDS,
        TE_LOAN_DISPLAY_FIELDS,
        TE_LOAN_FIELD_MAPPING,
        TE_LOAN_SEARCH_FIELDS,
    )

    field_type_mapper = {datetime: "date", float: "decimalnumber", int: "integernumber", "string": "text"}

    user_info = {
        "user_id": str(user.id),
        "user_name": user.username,
        "email": user.email,
        "name": user.name,
    }

    async def add_to_db(FIELD_MAPPING, DISPLAY_FIELDS=[], SERACH_FIELDS=[], folder=""):
        for idx in range(len(list(FIELD_MAPPING.values()))):
            # check if field_id already exists
            field_id = list(FIELD_MAPPING.keys())[idx]
            try:
                _ = await get_assistant_data_field_by_id_or_404(
                    id=assistant.id, field_id=field_id, folder=folder, session=session
                )
            except HTTPException:
                logger.warning(f"Field {field_id} already exists!")
            else:
                continue

            try:
                # Get the current maximum display_order value for a specific assistant
                result = await session.execute(
                    select(func.max(DataField.display_order)).where(DataField.assistant_id == assistant.id)
                )
                max_display_order = result.scalar() or 0
                data_field = DataField(
                    assistant_id=assistant.id,
                    name=FIELD_MAPPING.get(field_id),
                    field_id=field_id,
                    type=field_type_mapper.get(LOAN_FIELD_TYPE_MAPPING.get(field_id, "string"), "text"),
                    is_searchable=FIELD_MAPPING.get(field_id) in SERACH_FIELDS if SERACH_FIELDS else True,
                    display_in_ui=FIELD_MAPPING.get(field_id) in DISPLAY_FIELDS if DISPLAY_FIELDS else True,
                    folder=folder,
                    display_order=max_display_order + 1,
                )
                data_field.meta = {"user_info": {"created_by": user_info, "updated_by": user_info}}
                session.add(data_field)
                await session.commit()

            except Exception as e:
                logger.warning(f"Could not add {list(FIELD_MAPPING.keys())[idx]}:: {e}")
                continue

    await add_to_db(TE_LOAN_FIELD_MAPPING, TE_LOAN_DISPLAY_FIELDS, TE_LOAN_SEARCH_FIELDS, folder=DataFieldFolder.LOAN)
    await add_to_db(
        TE_CONTACT_FIELD_MAPPING, TE_CONTACT_DISPLAY_FIELDS, TE_CONTACT_SEARCH_FIELDS, folder=DataFieldFolder.CONTACT
    )
    await add_to_db(ACTIVITY_FIELD_MAPPINGS, folder=DataFieldFolder.ACTIVITY)

    return Response(status_code=status.HTTP_201_CREATED)
