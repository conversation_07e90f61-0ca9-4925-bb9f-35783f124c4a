from collections.abc import Sequence
from datetime import datetime, timed<PERSON>ta
from typing import Literal

from api.endpoints.assistant import get_assistant_or_404
from api.utils.pagination import page_number_pagination
from auth.cognito import check_user_permissions
from dateutil.relativedelta import relativedelta
from db.models import Assistant, <PERSON><PERSON><PERSON>, Question<PERSON><PERSON>wer, User
from db.session import get_session
from fastapi import APIRouter, Depends, HTTPException, Query, status
from hooks.pinecone_hook import get_generic_hook
from schema.enums import QuestionAnswerTypes
from schema.resources.question_answer import (
    QuestionAnswerCreate,
    QuestionAnswerRead,
    QuestionAnswerReadList,
    QuestionAnswerUpdate,
)
from sqlalchemy import String, cast, func, or_, select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload
from tasks.indexing import index_question_answer

router = APIRouter()


async def get_assistant_question_answer_or_404(
    id: str, qa_id: str, session: AsyncSession = Depends(get_session)
) -> QuestionAnswer:
    """
    Retrieve a specific question-answer pair by its ID for a given assistant.

    Fetches a question-answer pair from the database based on its ID and the assistant ID.

    Args:
        id (str): The ID of the assistant.
        qa_id (str): The ID of the question-answer pair.
        session (AsyncSession): The SQLAlchemy asynchronous session, provided by the `get_session` dependency.

    Returns:
        QuestionAnswer: The question-answer pair object.

    Raises:
        HTTPException: If the question-answer pair is not found, an HTTP 404 error is raised.
    """
    query = (
        select(QuestionAnswer)
        .options(selectinload(QuestionAnswer.assistant))
        .where(QuestionAnswer.id == qa_id, QuestionAnswer.assistant_id == id)
    )
    result = await session.execute(query)
    question_answer = result.scalar_one_or_none()

    if question_answer is None:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Question Answer not found")

    return question_answer


@router.get(
    "/v2/{id}/question-answer/{qa_id}/",
    response_model=QuestionAnswerRead,
    dependencies=[Depends(check_user_permissions)],
)
async def get_assistant_question_answer(
    question_answer: QuestionAnswer = Depends(get_assistant_question_answer_or_404),
) -> QuestionAnswer:
    """
    Retrieve a specific question-answer pair by its ID.

    Fetches the details of a question-answer pair based on its ID.

    Args:
        question_answer (QuestionAnswer): The question-answer pair object, provided by the
        `get_assistant_question_answer_or_404` dependency.

    Returns:
        QuestionAnswerRead: A pydantic model with the details of the question-answer pair.

    Raises:
        HTTPException: If the user does not have permissions, an HTTP 403 error is raised.
    """
    return question_answer


@router.get(
    "/v2/{id}/question-answer/",
    response_model=QuestionAnswerReadList,
    dependencies=[Depends(check_user_permissions)],
)
async def get_assistant_question_answers(
    assistant: Assistant = Depends(get_assistant_or_404),
    pagination: tuple[int, int] = Depends(page_number_pagination),
    session: AsyncSession = Depends(get_session),
    categories: Sequence[str] = Query(None, description="Filter by categories"),
    search: str = Query(None, description="Filter by name or email"),
    question: str = Query(None, description="Filter by question"),
    sort_order: Literal["asc", "desc"] = Query("desc", description="Sort order by updated date ('asc' or 'desc')"),
    start_date: datetime | None = Query(None, description="Start date for message filter"),
    end_date: datetime | None = Query(None, description="End date for message filter"),
    filter_type: str = Query(None, description="Preset filter for date range: 'last_month', 'last_3_months', etc."),
    type: QuestionAnswerTypes = Query(None, description="Filter by question answer type"),
) -> QuestionAnswerReadList:
    """
    List all question-answer pairs for a specific assistant with optional filtering.

    Retrieves a list of question-answer pairs for an assistant, with support for filtering by type.

    Args:
        assistant (Assistant): The assistant object, provided by the `get_assistant_or_404` dependency.
        session (AsyncSession): The SQLAlchemy asynchronous session, provided by the `get_session` dependency.
        type (QuestionAnswerTypes): Optional filter by question-answer type.

    Returns:
        list[QuestionAnswerRead]: A list of pydantic models with details of the question-answer pairs.

    Raises:
        HTTPException: If the user does not have permissions, an HTTP 403 error is raised.
    """
    page, size = pagination

    # Apply date range filters based on filter_type
    if filter_type:
        now = datetime.now()
        if filter_type == "today":
            start_date = now.replace(hour=0, minute=0, second=0, microsecond=0)
            end_date = now
        elif filter_type == "last_week":
            start_date = now - timedelta(days=7)
        elif filter_type == "last_month":
            start_date = now - relativedelta(months=1)
            end_date = now
        elif filter_type == "last_3_months":
            start_date = now - relativedelta(months=3)
            end_date = now
        elif filter_type == "last_6_months":
            start_date = now - relativedelta(months=6)
            end_date = now
        elif filter_type == "last_year":
            start_date = now - relativedelta(years=1)
            end_date = now
    query = (
        select(QuestionAnswer, Feedback.feedback_text.label("feedback_text"))
        .outerjoin(Feedback, cast(QuestionAnswer.meta["feedback_id"].astext, String) == cast(Feedback.id, String))
        .outerjoin(
            User,
            cast(QuestionAnswer.meta["user_info"]["created_by"]["user_id"].astext, String) == cast(User.id, String),
        )
        .where(QuestionAnswer.assistant_id == assistant.id)
    )

    if categories:
        for category in categories:
            query = query.where(cast(QuestionAnswer.categories, String).ilike(f"%{category}%"))

    if type:
        query = query.where(QuestionAnswer.type == type)

    if search:
        query = query.where(
            or_(
                User.search_tsvector.op("@@")(func.plainto_tsquery("english", f"{search}:*")),
                User.name.ilike(f"%{search}%"),
                User.email.ilike(f"%{search}%"),
            )
        )
    if question:
        query = query.where(QuestionAnswer.question.ilike(f"%{question}%"))

    # Apply date range filters
    if start_date:
        query = query.where(func.date(QuestionAnswer.updated_at) >= start_date)
    if end_date:
        query = query.where(func.date(QuestionAnswer.updated_at) <= end_date)

    total_question_answers = await session.execute(select(func.count()).select_from(query))
    count = total_question_answers.scalar()

    if sort_order == "asc":
        query = query.order_by(QuestionAnswer.updated_at.asc())
    else:
        query = query.order_by(QuestionAnswer.updated_at.desc())

    query = query.offset((page - 1) * size).limit(size)
    result = await session.execute(query)
    question_answers = result.all()
    return {
        "count": count,
        "data": [
            QuestionAnswerRead(**qa.QuestionAnswer.__dict__, feedback_text=qa.feedback_text) for qa in question_answers
        ],
    }


@router.post("/v2/{id}/question-answer/", status_code=status.HTTP_201_CREATED, response_model=QuestionAnswerRead)
async def create_assistant_question_answer(
    question_answer: QuestionAnswerCreate,
    assistant=Depends(get_assistant_or_404),
    session: AsyncSession = Depends(get_session),
    user=Depends(check_user_permissions),
) -> QuestionAnswer:
    """
    Create a new question-answer pair for a specific assistant.

    Adds a new question-answer pair record to the database and triggers any associated indexing tasks.

    Args:
        question_answer (QuestionAnswerCreate): A pydantic model with the details of the new question-answer pair.
        assistant (Assistant): The assistant object, provided by the `get_assistant_or_404` dependency.
        session (AsyncSession): The SQLAlchemy asynchronous session, provided by the `get_session` dependency.
        user (User): The user making the request, provided by the `check_user_permissions` dependency.

    Returns:
        QuestionAnswerRead: A pydantic model with the details of the created question-answer pair.

    Raises:
        HTTPException: If the user does not have permissions, an HTTP 403 error is raised.
    """
    user_info = {
        "user_id": str(user.id),
        "user_name": user.username,
        "email": user.email,
        "name": user.name,
    }

    if not question_answer.categories:
        question_answer.categories = assistant.categories

    if question_answer.meta:
        question_answer.meta = {
            **question_answer.meta,
            "user_info": {"created_by": user_info, "updated_by": user_info},
        }
    else:
        question_answer.meta = {"user_info": {"created_by": user_info, "updated_by": user_info}}

    if question_answer.type == QuestionAnswerTypes.FEEDBACK:
        feedback_id = question_answer.meta.get("feedback_id")
        if feedback_id:
            query = select(QuestionAnswer).where(QuestionAnswer.meta["feedback_id"].astext == feedback_id)
            result = await session.execute(query)
            feedback = result.scalar_one_or_none()
            if feedback:
                raise HTTPException(
                    status_code=status.HTTP_409_CONFLICT, detail="Feedback already added to the Knowledge Base"
                )

    question_answer = QuestionAnswer(
        assistant_id=assistant.id,
        **question_answer.model_dump(),
    )
    session.add(question_answer)
    await session.commit()
    index_question_answer.delay(question_answer.id)
    return question_answer


@router.delete(
    "/v2/{id}/question-answer/{qa_id}/",
    status_code=status.HTTP_204_NO_CONTENT,
    dependencies=[Depends(check_user_permissions)],
)
async def delete_assistant_question_answer(
    question_answer: QuestionAnswer = Depends(get_assistant_question_answer_or_404),
    session: AsyncSession = Depends(get_session),
):
    """
    Delete a specific question-answer pair for an assistant.

    Removes a question-answer pair record from the database and triggers any associated cleanup tasks.

    Args:
        question_answer (QuestionAnswer): The question-answer pair object to be deleted,
        provided by the `get_assistant_question_answer_or_404` dependency.
        session (AsyncSession): The SQLAlchemy asynchronous session, provided by the `get_session` dependency.

    Returns:
        None: No content is returned upon successful deletion.

    Raises:
        HTTPException: If the user does not have permissions, an HTTP 403 error is raised.
    """
    await session.delete(question_answer)
    await session.commit()
    generic_hook = await get_generic_hook(question_answer.assistant_id)
    await generic_hook.delete_document(question_answer.id, namespace=question_answer.assistant_id)


@router.patch("/v2/{id}/question-answer/{qa_id}/", response_model=QuestionAnswerRead, status_code=status.HTTP_200_OK)
async def update_assistant_question_answer(
    question_answer_update: QuestionAnswerUpdate,
    question_answer: QuestionAnswer = Depends(get_assistant_question_answer_or_404),
    session: AsyncSession = Depends(get_session),
    user=Depends(check_user_permissions),
) -> QuestionAnswer:
    """
    Update a specific question-answer pair for an assistant.

    Updates the details of a question-answer pair based on the provided data.

    Args:
        question_answer_update (QuestionAnswerUpdate): A pydantic model with updated question-answer details.
        question_answer (QuestionAnswer): The question-answer pair object to be updated, provided by
        the `get_assistant_question_answer_or_404` dependency.
        session (AsyncSession): The SQLAlchemy asynchronous session, provided by the `get_session` dependency.
        user (User): The user making the request, provided by the `check_user_permissions` dependency.

    Returns:
        QuestionAnswerRead: A pydantic model with the updated details of the question-answer pair.

    Raises:
        HTTPException: If the user does not have permissions, an HTTP 403 error is raised.
    """
    user_info = {
        "user_id": str(user.id),
        "user_name": user.username,
        "email": user.email,
        "name": user.name,
    }

    if not question_answer_update.categories:
        question_answer_update.categories = question_answer.assistant.categories

    # Update fields from DocumentUpdate
    for var, value in question_answer_update.model_dump(exclude_unset=True, exclude={"meta"}).items():
        setattr(question_answer, var, value)

    # Update metadata with user information
    if question_answer.meta:
        question_answer.meta = {
            **question_answer.meta,
            "user_info": {**question_answer.meta["user_info"], "updated_by": user_info},
        }
    else:
        question_answer.meta = {"user_info": {"updated_by": user_info}}

    if question_answer_update.meta:
        question_answer.meta.update(question_answer_update.meta)

    session.add(question_answer)
    await session.commit()
    await session.refresh(question_answer)

    generic_hook = await get_generic_hook(question_answer.assistant_id)
    await generic_hook.delete_document(question_answer.id, namespace=question_answer.assistant_id)
    index_question_answer.delay(question_answer.id)

    return question_answer
