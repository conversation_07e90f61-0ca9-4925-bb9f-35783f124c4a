import json
from typing import Literal

import redis.asyncio as aioredis
from api.endpoints.resources.data_fields import adjust_display_order
from api.utils.pagination import page_number_pagination
from auth.cognito import is_admin_user
from config import settings
from db.models import Assistant, DataField, Division, User
from db.session import get_session
from fastapi import APIRouter, Depends, HTTPException, Query, Response, status
from fastapi.responses import JSONResponse
from loguru import logger
from schema.enums import AssistantSubTypes, DataFieldFolder, DataFieldTypes
from schema.resources.admin.data_field import (
    AdminDataFieldCreate,
    AdminDataFieldRead,
    AdminDataFieldReadList,
    AdminDataFieldUpdate,
    DataFieldRead,
    DivisionDataFieldStatus,
)
from sqlalchemy import func, select
from sqlalchemy.ext.asyncio import AsyncSession
from tools.utils.encompass.loan import loan_utils

router = APIRouter()


@router.post("/", status_code=status.HTTP_201_CREATED, response_model=<PERSON>FieldRead)
async def admin_create_data_field(
    data_field: AdminDataFieldCreate,
    session: AsyncSession = Depends(get_session),
    user: User = Depends(is_admin_user),
) -> DataFieldRead:
    """Create a data field for multiple divisions"""
    user_info = {
        "user_id": str(user.id),
        "user_name": user.username,
        "email": user.email,
        "name": user.name,
    }

    if not data_field.folder and data_field.assistant_subtype == AssistantSubTypes.TOTALEXPERT:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Folder is required for Total Expert assistant",
        )

    # Verify if all divisions exist
    division_query = select(Division).where(Division.id.in_(data_field.division_ids))
    result = await session.execute(division_query)
    divisions = result.scalars().all()

    if len(divisions) != len(data_field.division_ids):
        raise HTTPException(status_code=404, detail="One or more divisions not found")

    # Find all assistants of specified types for the given divisions
    assistants_query = select(Assistant).where(
        Assistant.division_id.in_(data_field.division_ids),
        Assistant.sub_type == data_field.assistant_subtype,
    )
    result = await session.execute(assistants_query)
    assistants = result.scalars().all()

    if not assistants:
        raise HTTPException(status_code=404, detail="No assistants found for the specified divisions and types")

    created_data_fields = []
    # Process each assistant
    for assistant in assistants:
        try:
            # Check if field already exists for this assistant
            field_id = data_field.field_id.strip()

            # Handle Encompass field ID format
            if assistant.sub_type in [AssistantSubTypes.ENCOMPASS, AssistantSubTypes.ENCOMPASS_SALES]:
                if not field_id.startswith(("Fields.", "Loan.")):
                    field_id = f"Fields.{field_id}"

            existing_query = select(DataField).where(
                DataField.assistant_id == assistant.id, DataField.field_id == field_id
            )
            if data_field.folder:
                existing_query = existing_query.where(DataField.folder == data_field.folder)

            result = await session.execute(existing_query)
            existing_field = result.scalar_one_or_none()

            if existing_field:
                logger.info(f"Field {field_id} already exists for assistant {assistant.id}")
                continue

            # Get max display order
            result = await session.execute(
                select(func.max(DataField.display_order)).where(DataField.assistant_id == assistant.id)
            )
            max_display_order = result.scalar() or 0

            # Prepare data field
            field_data = data_field.model_dump(exclude={"division_ids", "assistant_subtype"})
            field_data["field_id"] = field_id

            # Handle Encompass specific field validation
            if assistant.sub_type in [AssistantSubTypes.ENCOMPASS, AssistantSubTypes.ENCOMPASS_SALES]:
                field_details = await loan_utils.get_encompass_field_details([field_id])
                if not field_details:
                    logger.warning(f"Field {field_id} not found in Encompass for assistant {assistant.id}")
                    continue

                field_details = field_details[0]

                # TODO: Map User entered value and value received from Encompass
                # field_data["field_id"] = field_details.get("canonicalName")

                if field_details.get("isOptionListFixed", False):
                    option_values = [opt.get("reportingDatabaseValue") for opt in field_details.get("options", [])]
                    try:
                        redis = await aioredis.from_url(settings.REDIS_URL)
                        current_fields = await redis.get("encompass_fields_with_options")
                        current_fields = json.loads(current_fields) if current_fields else {}
                        current_fields[field_data["field_id"]] = option_values
                        await redis.set("encompass_fields_with_options", json.dumps(current_fields))
                    except Exception as e:
                        logger.error(f"Error updating redis cache for field options: {e}")

            # Create metadata
            meta = field_data.get("meta", {}) or {}
            meta = {
                **meta,
                "user_info": {"created_by": user_info, "updated_by": user_info},
            }
            field_data["meta"] = meta

            # Create the data field
            new_data_field = DataField(assistant_id=assistant.id, display_order=max_display_order + 1, **field_data)

            session.add(new_data_field)
            created_data_fields.append(new_data_field)

        except Exception as e:
            logger.error(f"Error creating data field for assistant {assistant.id}: {e}")

    if not created_data_fields:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Could not create data field for any of the specified divisions",
        )

    # Return the first created data field
    await session.commit()
    await session.refresh(created_data_fields[0])

    result = DataFieldRead(**created_data_fields[0].__dict__)
    return result


@router.get(
    "/",
    response_model=AdminDataFieldReadList,
    dependencies=[Depends(is_admin_user)],
)
async def admin_list_data_fields(
    pagination: tuple[int, int] = Depends(page_number_pagination),
    session: AsyncSession = Depends(get_session),
    sort_order: Literal["asc", "desc"] = Query("asc", description="Sort by display order ('asc' or 'desc')"),
    type: DataFieldTypes = Query(None, description="Filter by field type"),
    q: str = Query(None, description="Search query for Field ID and Descriptive Name"),
    folder: DataFieldFolder = Query(
        None, description="Field Folder/ Only applicable for totalexpert assistant (eg. Loan, contact, activity etc.)"
    ),
    assistant_subtype: AssistantSubTypes = Query(..., description="Filter by assistant sub-type (required)"),
) -> AdminDataFieldReadList:
    """List all data fields with division information"""
    page, size = pagination

    # Get all divisions
    result = await session.execute(select(Division))
    all_divisions = result.scalars().all()

    base_query = (
        select(DataField.field_id, DataField.folder)
        .join(Assistant, DataField.assistant_id == Assistant.id)
        .where(Assistant.sub_type == assistant_subtype)
        .group_by(DataField.field_id, DataField.folder)
    )

    # Apply filters
    if type:
        base_query = base_query.where(DataField.type == type)

    if folder:
        base_query = base_query.where(DataField.folder == folder)

    if q:
        base_query = base_query.where(DataField.field_id.ilike(f"%{q}%") | DataField.name.ilike(f"%{q}%"))

    # Count total unique field IDs
    count_query = select(func.count()).select_from(base_query.subquery())
    total = await session.execute(count_query)
    count = total.scalar()

    result = await session.execute(base_query)
    field_ids_folders = [(row[0], row[1]) for row in result]

    # Get detailed info for each field ID
    admin_data_fields = []
    for field_id, folder in field_ids_folders:
        # Get a sample data field record for this field ID
        sample_query = (
            select(DataField)
            .join(Assistant, DataField.assistant_id == Assistant.id)
            .where(DataField.field_id == field_id, DataField.folder == folder, Assistant.sub_type == assistant_subtype)
        ).limit(1)
        sample_result = await session.execute(sample_query)
        sample_field = sample_result.scalar_one_or_none()

        if not sample_field:
            continue

        # Get all divisions where this field exists
        division_query = (
            select(Assistant.division_id, Division.name)
            .join(DataField, DataField.assistant_id == Assistant.id)
            .join(Division, Division.id == Assistant.division_id)
            .where(DataField.field_id == field_id, DataField.folder == folder, Assistant.sub_type == assistant_subtype)
        )

        division_result = await session.execute(division_query)
        field_divisions = {row[0]: row[1] for row in division_result}

        # Create division statuses
        division_statuses = []
        for division in all_divisions:
            division_statuses.append(
                DivisionDataFieldStatus(
                    division_id=division.id, division_name=division.name, is_enabled=division.id in field_divisions
                )
            )

        # Create admin data field record
        admin_field = AdminDataFieldRead(**sample_field.__dict__)
        admin_field.divisions = division_statuses
        admin_data_fields.append(admin_field)

    # sort admin data fields by display order
    if sort_order == "asc":
        admin_data_fields.sort(key=lambda x: x.display_order)
    else:
        admin_data_fields.sort(key=lambda x: x.display_order, reverse=True)

    # Apply pagination to the final list
    start_index = (page - 1) * size
    end_index = start_index + size
    admin_data_fields = admin_data_fields[start_index:end_index]

    return AdminDataFieldReadList(count=count, data=admin_data_fields)


@router.patch("/{field_id}/", response_model=DataFieldRead | dict)
async def admin_update_data_field(
    field_id: str,
    update_data: AdminDataFieldUpdate,
    folder: DataFieldFolder = Query(
        None, description="Field Folder/ Only applicable for totalexpert assistant (eg. Loan, contact, activity etc.)"
    ),
    session: AsyncSession = Depends(get_session),
    user: User = Depends(is_admin_user),
) -> DataFieldRead | dict:
    """Update a data field and its division associations"""
    user_info = {
        "user_id": str(user.id),
        "user_name": user.username,
        "email": user.email,
        "name": user.name,
    }

    if not folder and update_data.assistant_subtype == AssistantSubTypes.TOTALEXPERT:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Folder is required for Total Expert assistant",
        )

    # Get all fields with this field_id for the specified assistant_subtype
    fields_query = (
        select(DataField)
        .join(Assistant, DataField.assistant_id == Assistant.id)
        .where(DataField.field_id == field_id, Assistant.sub_type == update_data.assistant_subtype)
    )
    if folder:
        fields_query = fields_query.where(DataField.folder == folder)

    result = await session.execute(fields_query)
    fields = result.scalars().all()

    if not fields:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Data field {field_id} not found for {update_data.assistant_subtype} assistant",
        )

    # Get current division IDs where this field exists for the specified assistant subtype
    assistants_query = (
        select(Assistant.division_id, Assistant.id)
        .join(DataField, DataField.assistant_id == Assistant.id)
        .where(DataField.field_id == field_id, Assistant.sub_type == update_data.assistant_subtype)
    )

    if folder:
        assistants_query = assistants_query.where(DataField.folder == folder)

    result = await session.execute(assistants_query)
    current_divisions = {row[0]: row[1] for row in result}

    # Process division changes if provided
    if update_data.division_ids is not None:
        # Identify divisions to add and remove
        divisions_to_add = [d for d in update_data.division_ids if d and d not in current_divisions]
        divisions_to_remove = [d for d in current_divisions.keys() if d and d not in update_data.division_ids]

        # Find assistants for divisions to add
        if divisions_to_add:
            new_assistants_query = select(Assistant).where(
                Assistant.division_id.in_(divisions_to_add),
                Assistant.sub_type == update_data.assistant_subtype,
            )
            result = await session.execute(new_assistants_query)
            new_assistants = result.scalars().all()

            # Create data field for each new assistant
            for assistant in new_assistants:
                # Get a sample data field to copy properties
                sample_field = fields[0]

                # Get max display order
                result = await session.execute(
                    select(func.max(DataField.display_order)).where(DataField.assistant_id == assistant.id)
                )
                max_display_order = result.scalar() or 0

                # Create new data field
                new_field = DataField(
                    assistant_id=assistant.id,
                    field_id=sample_field.field_id,
                    name=sample_field.name,
                    type=sample_field.type,
                    is_searchable=sample_field.is_searchable,
                    display_in_ui=sample_field.display_in_ui,
                    folder=sample_field.folder,
                    display_order=max_display_order + 1,
                    meta={"user_info": {"created_by": user_info, "updated_by": user_info}},
                )
                session.add(new_field)

        # Delete fields for divisions to remove
        if divisions_to_remove:
            for div_id in divisions_to_remove:
                # Get the assistant ID for this division
                assistant_id = current_divisions[div_id]
                if assistant_id:
                    # Delete the data field for this assistant
                    delete_query = (
                        select(DataField)
                        .join(Assistant, DataField.assistant_id == Assistant.id)
                        .where(
                            DataField.assistant_id == assistant_id,
                            DataField.field_id == field_id,
                            Assistant.sub_type == update_data.assistant_subtype,
                        )
                    )
                    if folder:
                        delete_query = delete_query.where(DataField.folder == folder)

                    result = await session.execute(delete_query)
                    to_delete = result.scalar_one_or_none()
                    if to_delete:
                        await session.delete(to_delete)
    await session.commit()

    # Refresh the fields to get the latest state
    result = await session.execute(fields_query)
    fields = result.scalars().all()
    if fields:
        # Update field properties if any are provided (for all instances)
        update_dict = update_data.model_dump(exclude={"division_ids", "assistant_subtype"}, exclude_unset=True)
        if update_dict:
            new_display_order = update_dict.get("display_order")

            for field in fields:
                # Handle display order update if needed
                if new_display_order is not None and new_display_order != field.display_order:
                    await adjust_display_order(field, new_display_order, session)

                for key, value in update_dict.items():
                    setattr(field, key, value)

                # Update metadata
                if field.meta:
                    field.meta = {
                        **field.meta,
                        "user_info": {**field.meta.get("user_info", {}), "updated_by": user_info},
                    }
                else:
                    field.meta = {"user_info": {"updated_by": user_info}}

                session.add(field)

        await session.commit()
        await session.refresh(fields[0])
        return fields[0]
    return JSONResponse(
        content={"detail": "Fields removed from all divsions successfully"}, status_code=status.HTTP_200_OK
    )


@router.delete(
    "/{field_id}/",
    status_code=status.HTTP_204_NO_CONTENT,
    dependencies=[Depends(is_admin_user)],
)
async def admin_delete_data_field(
    field_id: str,
    folder: DataFieldFolder = Query(
        None, description="Field Folder/ Only applicable for totalexpert assistant (eg. Loan, contact, activity etc.)"
    ),
    assistant_subtype: AssistantSubTypes = Query(..., description="Assistant sub-type (required)"),
    session: AsyncSession = Depends(get_session),
):
    """Delete a data field from all assistants"""
    if not folder and assistant_subtype == AssistantSubTypes.TOTALEXPERT:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Folder is required for Total Expert assistant",
        )

    # Get all fields with this field_id
    fields_query = (
        select(DataField)
        .join(Assistant, DataField.assistant_id == Assistant.id)
        .where(DataField.field_id == field_id, Assistant.sub_type == assistant_subtype)
    )
    if folder:
        fields_query = fields_query.where(DataField.folder == folder)

    result = await session.execute(fields_query)
    fields = result.scalars().all()

    if not fields:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Data field {field_id} not found for {assistant_subtype} assistant",
        )

    # Delete all instances
    for field in fields:
        await session.delete(field)

    await session.commit()
    return Response(status_code=status.HTTP_204_NO_CONTENT)
