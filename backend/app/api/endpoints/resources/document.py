from collections.abc import Sequence

from api.endpoints.assistant import get_assistant_or_404
from api.utils.pagination import page_number_pagination
from api.utils.utils import disarm_malicious_content, get_extension_and_mime_type, is_malicious_pdf
from auth.cognito import check_user_permissions, get_or_create_auth_user
from config import settings
from db.models import Assistant, Document
from db.session import get_session
from fastapi import APIRouter, Depends, File, HTTPException, Query, UploadFile, status
from hooks.pinecone_hook import get_generic_hook
from hooks.s3 import get_s3_hook
from schema.enums import AssistantTypes, DocumentIndexStatus, DocumentTypes
from schema.resources.document import DocumentCreate, DocumentRead, DocumentReadList, DocumentUpdate
from sqlalchemy import String, asc, cast, desc, func, select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload
from tasks.indexing import delete_documents, process_document

router = APIRouter()


@router.post(
    "/v2/{id}/document/upload/", status_code=status.HTTP_200_OK, dependencies=[Depends(check_user_permissions)]
)
async def upload_assistant_document(
    file: UploadFile = File(None, description="File to upload"),
    assistant=Depends(get_assistant_or_404),
):
    # get extension and mime type
    file_content = await file.read()
    ext, mime = get_extension_and_mime_type(file.filename, file_content)

    # Check file extension
    if ext not in settings.ALLOWED_EXTENSIONS:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=f"File type '{ext}' is not allowed.")

    # Check MIME type
    if mime not in settings.ALLOWED_MIME_TYPES:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Could not verify a safe MIME type for this file."
        )

    # Validate pdf file
    if ext == "pdf" or mime == "application/pdf":
        object_name = f"{assistant.id}/{file.filename}"

        if is_malicious_pdf(file_content, file.filename):
            file_content = await disarm_malicious_content(file_content, file.filename)
            if not file_content:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="This PDF file is potentially malicious. Disarming failed, so the upload was aborted.",
                )
        s3_hook = await get_s3_hook(assistant.id)
        return s3_hook.upload_file(object_name=object_name, content=file_content, content_type=mime)


@router.get(
    "/v2/{id}/document/presigned/",
    status_code=status.HTTP_200_OK,
    dependencies=[Depends(get_or_create_auth_user)],
)
async def get_assistant_document_presigned_url_download(id: str, filename: str):
    """
    Get a presigned URL for downloading a document from S3.

    Provides a presigned URL that allows the user to download a document from S3.

    Args:
        filename (str): The name of the document to download.
        assistant (Assistant): The assistant object, provided by the `get_assistant_or_404` dependency.

    Returns:
        dict: A dictionary containing the presigned URL for the download.

    Raises:
        HTTPException: If the user does not have permissions, an HTTP 403 error is raised.
    """
    object_name = f"{id}/{filename}"
    s3_hook = await get_s3_hook(id)
    return s3_hook.get_presigned_url_for_download(object_name=object_name)


async def get_assistant_document_or_404(
    id: str, doc_id: str, session: AsyncSession = Depends(get_session)
) -> Document:
    """
    Retrieve a document by ID for a specific assistant.

    Fetches a document from the database based on its ID and the assistant ID.

    Args:
        id (str): The ID of the assistant.
        doc_id (str): The ID of the document.
        session (AsyncSession): The SQLAlchemy asynchronous session, provided by the `get_session` dependency.

    Returns:
        Document: The document object.

    Raises:
        HTTPException: If the document is not found, an HTTP 404 error is raised.
    """
    query = (
        select(Document)
        .options(selectinload(Document.assistant))
        .where(Document.id == doc_id, Document.assistant_id == id)
    )
    result = await session.execute(query)
    document = result.scalar_one_or_none()

    if document is None:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Document not found")

    return document


@router.get("/v2/{id}/document/{doc_id}/", response_model=DocumentRead, dependencies=[Depends(check_user_permissions)])
async def get_assistant_document(document: Document = Depends(get_assistant_document_or_404)) -> Document:
    """
    Retrieve a specific document by ID for an assistant.

    Fetches the details of a document based on its ID.

    Args:
        document (Document): The document object, provided by the `get_assistant_document_or_404` dependency.

    Returns:
        DocumentRead: A pydantic model with the details of the document.

    Raises:
        HTTPException: If the user does not have permissions, an HTTP 403 error is raised.
    """
    return document


@router.get("/v2/{id}/document/", response_model=DocumentReadList, dependencies=[Depends(check_user_permissions)])
async def get_assistant_documents(
    assistant: Assistant = Depends(get_assistant_or_404),
    pagination: tuple[int, int] = Depends(page_number_pagination),
    session: AsyncSession = Depends(get_session),
    title: str = Query(None, description="Filter by title"),
    categories: Sequence[str] = Query(None, description="Filter by categories"),
    sort_order: str = Query("desc", description="Sort order by created date ('asc' or 'desc')"),
    type: DocumentTypes = Query(None, description="Filter by document type"),
) -> DocumentReadList:
    """
    List documents for a specific assistant with optional filtering and pagination.

    Retrieves a list of documents for an assistant, with support for filtering by title and categories,
    and pagination.

    Args:
        assistant (Assistant): The assistant object, provided by the `get_assistant_or_404` dependency.
        pagination (tuple[int, int]): Tuple containing the page number and page size for pagination.
        session (AsyncSession): The SQLAlchemy asynchronous session, provided by the `get_session` dependency.
        title (str): Optional filter by document title.
        categories (Sequence[str]): Optional filter by document categories.

    Returns:
        DocumentReadList: A dictionary containing the count and list of documents.

    Raises:
        HTTPException: If the user does not have permissions, an HTTP 403 error is raised.
    """
    page, size = pagination
    query = select(Document).where(Document.assistant_id == assistant.id)
    if title:
        query = query.where(Document.title.ilike(f"%{title}%"))
    if categories:
        for category in categories:
            query = query.where(cast(Document.categories, String).ilike(f"%{category}%"))
    if type:
        query = query.where(Document.type == type)

    total_documents = await session.execute(select(func.count()).select_from(query))
    count = total_documents.scalar()

    # Validate the sort order
    if sort_order.lower() not in ["asc", "desc"]:
        sort_order = "desc"  # Default to descending if invalid

    # Assign the sorting function based on the validated sort_order
    sort_function = desc if sort_order == "desc" else asc
    query = query.order_by(sort_function(Document.created_at))

    query = query.offset((page - 1) * size).limit(size)
    result = await session.execute(query)
    return {
        "count": count,
        "data": result.scalars().all(),
    }


@router.post("/v2/{id}/document/", status_code=status.HTTP_201_CREATED, response_model=DocumentRead)
async def create_assistant_document(
    document: DocumentCreate,
    assistant=Depends(get_assistant_or_404),
    session: AsyncSession = Depends(get_session),
    user=Depends(check_user_permissions),
) -> Document:
    """
    Create a new document for a specific assistant.

    Adds a new document record to the database and triggers any associated processing tasks.

    Args:
        document (DocumentCreate): A pydantic model with the details of the new document.
        assistant (Assistant): The assistant object, provided by the `get_assistant_or_404` dependency.
        session (AsyncSession): The SQLAlchemy asynchronous session, provided by the `get_session` dependency.
        user (User): The user making the request, provided by the `check_user_permissions` dependency.

    Returns:
        DocumentRead: A pydantic model with the details of the created document.

    Raises:
        HTTPException: If the user does not have permissions, an HTTP 403 error is raised.
    """
    user_info = {
        "user_id": str(user.id),
        "user_name": user.username,
        "email": user.email,
        "name": user.name,
    }

    if not document.categories:
        document.categories = assistant.categories

    if document.meta:
        document.meta = {
            **document.meta,
            "user_info": {"created_by": user_info, "updated_by": user_info},
        }
    else:
        document.meta = {"user_info": {"created_by": user_info, "updated_by": user_info}}

    document = Document(
        assistant_id=assistant.id,
        **document.model_dump(),
    )

    if document.type in [DocumentTypes.IMAGE, DocumentTypes.SHAREPOINT_IMAGE]:
        document.is_indexed = True
        document.index_status = DocumentIndexStatus.INDEXED
        session.add(document)
        await session.commit()
        await session.refresh(document)
        return document

    session.add(document)
    await session.commit()
    if not assistant.type == AssistantTypes.CUSTOM:
        process_document.delay(document.id)
    return document


@router.delete(
    "/v2/{id}/document/{doc_id}/",
    status_code=status.HTTP_204_NO_CONTENT,
    dependencies=[Depends(check_user_permissions)],
)
async def delete_assistant_document(
    document: Document = Depends(get_assistant_document_or_404), session: AsyncSession = Depends(get_session)
):
    """
    Delete a specific document for an assistant.

    Removes a document record from the database and triggers any associated cleanup tasks.

    Args:
        document (Document): The document object to be deleted, provided by the
        `get_assistant_document_or_404` dependency.
        session (AsyncSession): The SQLAlchemy asynchronous session, provided by the `get_session` dependency.

    Returns:
        None: No content is returned upon successful deletion.

    Raises:
        HTTPException: If the user does not have permissions, an HTTP 403 error is raised.
    """
    await session.delete(document)
    await session.commit()

    # Delete from S3
    s3_hook = await get_s3_hook(document.assistant_id)
    object_name = s3_hook.get_key_from_url(document.link)
    s3_hook.delete_object(object_name=object_name)

    if document.type in [DocumentTypes.IMAGE, DocumentTypes.SHAREPOINT_IMAGE]:
        return

    # Delete from pinecone
    generic_hook = await get_generic_hook(document.assistant_id)
    await generic_hook.delete_document(document.id, namespace=document.assistant_id)


@router.delete(
    "/v2/{id}/batch-delete/document/",
    status_code=status.HTTP_204_NO_CONTENT,
    dependencies=[Depends(check_user_permissions)],
)
async def batch_delete_documents(doc_ids: list[str], assistant=Depends(get_assistant_or_404)):
    delete_documents.delay(assistant.id, doc_ids)
    return {"message": "Document deletion started successfully"}


@router.patch("/v2/{id}/document/{doc_id}/", response_model=DocumentRead, status_code=status.HTTP_200_OK)
async def update_assistant_document(
    document_update: DocumentUpdate,
    document: Document = Depends(get_assistant_document_or_404),
    session: AsyncSession = Depends(get_session),
    user=Depends(check_user_permissions),
) -> Document:
    """
    Update a specific document for an assistant.

    Updates the details of a document based on the provided data.

    Args:
        document_update (DocumentUpdate): A pydantic model with updated document details.
        document (Document): The document object to be updated, provided by the
        `get_assistant_document_or_404` dependency.
        session (AsyncSession): The SQLAlchemy asynchronous session, provided by the `get_session` dependency.
        user (User): The user making the request, provided by the `check_user_permissions` dependency.

    Returns:
        DocumentRead: A pydantic model with the updated details of the document.

    Raises:
        HTTPException: If the user does not have permissions, an HTTP 403 error is raised.
    """
    user_info = {
        "user_id": str(user.id),
        "user_name": user.username,
        "email": user.email,
        "name": user.name,
    }

    if document_update.categories is not None:
        if document_update.categories:
            categories = document_update.categories
        else:
            categories = document.assistant.categories
            document_update.categories = categories
        if document.type not in [DocumentTypes.IMAGE, DocumentTypes.SHAREPOINT_IMAGE]:
            generic_hook = await get_generic_hook(document.assistant_id)
            await generic_hook.update_metadata(id=document.id, categories=categories, namespace=document.assistant_id)

    # Update fields from DocumentUpdate
    for var, value in document_update.model_dump(exclude_unset=True, exclude={"meta"}).items():
        setattr(document, var, value)

    # Update metadata with user information
    if document.meta:
        document.meta = {**document.meta, "user_info": {**document.meta["user_info"], "updated_by": user_info}}
    else:
        document.meta = {"user_info": {"updated_by": user_info}}

    if document_update.meta:
        document.meta.update(document_update.meta)

    session.add(document)
    await session.commit()
    await session.refresh(document)

    return document
