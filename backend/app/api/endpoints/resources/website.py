from collections.abc import Sequence
from typing import Literal

from api.endpoints.assistant import get_assistant_or_404
from api.utils.pagination import page_number_pagination
from auth.cognito import check_user_permissions, is_not_general_user
from db.models import Assistant, User, WebsiteContent
from db.session import get_session
from fastapi import APIRouter, Depends, HTTPException, Query, status
from fastapi.responses import JSONResponse
from hooks.pinecone_hook import get_generic_hook
from hooks.website_hook import website_hook
from pydantic import AnyUrl
from schema.resources.website import (
    WebsiteContentCreate,
    WebsiteContentRead,
    WebsiteContentReadList,
    WebsiteContentUpdate,
    WebsiteLinksList,
)
from sqlalchemy import String, asc, cast, desc, func, or_, select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload
from tasks.website import index_website_content, refresh_beat_schedule, update_website_categories
from utils.website import normalize_url

router = APIRouter()


async def get_assistant_website_content_or_404(
    id: str, website_id: str, session: AsyncSession = Depends(get_session)
) -> WebsiteContent:
    """
    Retrieve a specific website content record by its ID for a given assistant.

    Fetches a website content record from the database based on its ID and the assistant ID.
    This includes any child content associated with the website content.

    Args:
        id (str): The ID of the assistant.
        website_id (str): The ID of the website content record.
        session (AsyncSession): The SQLAlchemy asynchronous session,
        provided by the `get_session` dependency.

    Returns:
        WebsiteContent: The website content record object.

    Raises:
        HTTPException: If the website content record is not found, an HTTP 404 error is raised.
    """
    query = (
        select(WebsiteContent)
        .options(selectinload(WebsiteContent.assistant))
        .options(selectinload(WebsiteContent.children))
        .where(WebsiteContent.id == website_id, WebsiteContent.assistant_id == id)
    )
    result = await session.execute(query)
    website_content = result.scalar_one_or_none()

    if website_content is None:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Website Content not found")

    return website_content


@router.get(
    "/v2/{id}/website/{website_id}/",
    response_model=WebsiteContentRead,
    dependencies=[Depends(check_user_permissions)],
)
async def get_assistant_website_content(
    website_content: WebsiteContent = Depends(get_assistant_website_content_or_404),
) -> WebsiteContentRead:
    """
    Retrieve a specific website content record by its ID.

    Fetches the details of a website content record based on its ID. This endpoint ensures that
    the user has appropriate permissions to access the resource.

    Args:
        website_content (WebsiteContent): The website content record object, provided by the
        `get_assistant_website_content_or_404` dependency.

    Returns:
        WebsiteContentRead: A pydantic model with the details of the website content record.

    Raises:
        HTTPException: If the user does not have permissions, an HTTP 403 error is raised.
    """
    return website_content


@router.get(
    "/v2/website/links/",
    response_model=WebsiteLinksList,
    dependencies=[Depends(is_not_general_user)],
)
async def get_website_links(
    url: AnyUrl = Query(..., description="The URL to fetch links from"),
) -> WebsiteLinksList:
    links = await website_hook.get_links(str(url))
    seen_urls = set()
    unique_links = []

    for key, url in links.items():
        url = normalize_url(url)
        if url not in seen_urls:
            unique_links.append({"title": key, "url": url})
            seen_urls.add(url)

    return {"count": len(unique_links), "data": unique_links}


@router.get("/v2/{id}/website/", response_model=WebsiteContentReadList, dependencies=[Depends(check_user_permissions)])
async def get_assistant_website_contents(
    assistant: Assistant = Depends(get_assistant_or_404),
    pagination: tuple[int, int] = Depends(page_number_pagination),
    title: str = Query(None, description="Filter by title"),
    categories: Sequence[str] = Query(None, description="Filter by categories"),
    search: str = Query(None, description="Search Filter for username or email"),
    session: AsyncSession = Depends(get_session),
    sort_order: Literal["asc", "desc"] = Query("desc", description="Sort order by created date ('asc' or 'desc')"),
) -> WebsiteContentReadList:
    """
    List all website content records for a specific assistant.

    Retrieves a list of website content records for an assistant. Only top-level content (i.e., content with no parent)
    is included in the results.

    Args:
        assistant (Assistant): The assistant object, provided by the `get_assistant_or_404` dependency.
        pagination (tuple[int, int]): A tuple containing the page number and page size for pagination.
        session (AsyncSession): The SQLAlchemy asynchronous session, provided by the `get_session` dependency.
        title (str): Optional filter by website content title.
        categories (Sequence[str]): Optional filter by website content categories.
        search (str): Optional search filter for username or email.
        sort_order (Literal["asc", "desc"]): The order in which to sort the results by created date ('asc' or 'desc').

    Returns:
        dict: Dictionary with the count of website content records and a list of details of the website content records

    Raises:
        HTTPException: If the user does not have permissions, an HTTP 403 error is raised.
    """
    page, size = pagination
    query = (
        select(WebsiteContent)
        .options(selectinload(WebsiteContent.children))
        .where(WebsiteContent.assistant_id == assistant.id)
        .where(WebsiteContent.parent_id == None)  # noqa
        .outerjoin(
            User,
            cast(WebsiteContent.meta["user_info"]["created_by"]["user_id"].astext, String) == cast(User.id, String),
        )
    )

    if title:
        query = query.where(WebsiteContent.title.ilike(f"%{title}%"))
    if categories:
        for category in categories:
            query = query.where(cast(WebsiteContent.categories, String).ilike(f"%{category}%"))
    if search:
        query = query.where(
            or_(
                User.search_tsvector.op("@@")(func.plainto_tsquery("english", f"{search}:*")),
                User.name.ilike(f"%{search}%"),
                User.email.ilike(f"%{search}%"),
            )
        )

    count_result = await session.execute(select(func.count()).select_from(query))
    count = count_result.scalar()

    sort_function = desc if sort_order == "desc" else asc
    query = query.order_by(sort_function(WebsiteContent.created_at))

    query = query.offset((page - 1) * size).limit(size)
    result = await session.execute(query)
    website_contents = result.scalars().all()

    for website in website_contents:
        website.count = len(website.children) + 1
    return {
        "count": count,
        "data": website_contents,
        "page": page,
    }


@router.post("/v2/{id}/website/", status_code=status.HTTP_201_CREATED, response_model=WebsiteContentRead)
async def create_assistant_website_content(
    website_content_create: WebsiteContentCreate,
    assistant=Depends(get_assistant_or_404),
    session: AsyncSession = Depends(get_session),
    user=Depends(check_user_permissions),
) -> WebsiteContentRead:
    """
    Create a new website content record for a specific assistant.

    Adds a new website content record to the database and triggers any associated indexing tasks.
    Before creating the record, it checks if a website content record with the same URL already
    exists for the assistant to avoid duplication.

    Args:
        website_content_create (WebsiteContentCreate): A pydantic model with the details of the
        new website content record. assistant (Assistant): The assistant object, provided by
        the `get_assistant_or_404` dependency.
        session (AsyncSession): The SQLAlchemy asynchronous session, provided by the `get_session` dependency.
        user (User): The user making the request, provided by the `check_user_permissions` dependency.

    Returns:
        WebsiteContentRead: A pydantic model with the details of the created website content record.

    Raises:
        HTTPException: If a website content record with the same URL already exists
        for this assistant, an HTTP 409 error is raised.
        HTTPException: If the user does not have permissions, an HTTP 403 error is raised.
    """
    user_info = {
        "user_id": str(user.id),
        "user_name": user.username,
        "email": user.email,
        "name": user.name,
    }
    url = normalize_url(website_content_create.url)
    query = select(WebsiteContent).where(WebsiteContent.assistant_id == assistant.id, WebsiteContent.url == url)
    result = await session.execute(query)
    website_content = result.scalar_one_or_none()
    if website_content is not None:
        return JSONResponse(
            status_code=status.HTTP_409_CONFLICT,
            content={"detail": "Website content with the same URL already exists for this assistant."},
        )
    links = website_content_create.links or []
    links_serializable = [link.model_dump() for link in links]
    if not website_content_create.categories:
        website_content_create.categories = assistant.categories
    website_content = WebsiteContent(
        assistant_id=assistant.id,
        url=url,
        meta={
            "user_info": {
                "created_by": user_info,
                "updated_by": user_info,
            }
        },
        **website_content_create.model_dump(exclude={"url", "links"}),
    )
    session.add(website_content)
    await session.commit()
    index_website_content.delay(website_content.id, links_serializable)
    return await get_assistant_website_content_or_404(website_content.assistant_id, website_content.id, session)


@router.delete(
    "/v2/{id}/website/{website_id}/",
    status_code=status.HTTP_204_NO_CONTENT,
    dependencies=[Depends(check_user_permissions)],
)
async def delete_assistant_website_content(
    website_content: WebsiteContent = Depends(get_assistant_website_content_or_404),
    session: AsyncSession = Depends(get_session),
):
    """
    Delete a specific website content record for an assistant.

    Removes a website content record from the database and triggers any associated cleanup tasks,
    including removing child content.

    Args:
        website_content (WebsiteContent): The website content record object to be deleted,
        provided by the `get_assistant_website_content_or_404` dependency.
        session (AsyncSession): The SQLAlchemy asynchronous session, provided by the `get_session` dependency.

    Returns:
        None: No content is returned upon successful deletion.

    Raises:
        HTTPException: If the user does not have permissions, an HTTP 403 error is raised.
    """
    await session.delete(website_content)
    await session.commit()
    generic_hook = await get_generic_hook(website_content.assistant_id)
    for child in website_content.children:
        await generic_hook.delete_document(child.id, namespace=child.assistant_id)
    await generic_hook.delete_document(website_content.id, namespace=website_content.assistant_id)

    # Trigger a refresh of the beat schedule
    refresh_beat_schedule.delay()


@router.patch(
    "/v2/{id}/website/{website_id}/",
    response_model=WebsiteContentRead,
    status_code=status.HTTP_200_OK,
)
async def update_assistant_website_content(
    website_content_update: WebsiteContentUpdate,
    website_content: WebsiteContent = Depends(get_assistant_website_content_or_404),
    session: AsyncSession = Depends(get_session),
    user=Depends(check_user_permissions),
) -> WebsiteContentRead:
    user_info = {
        "user_id": str(user.id),
        "user_name": user.username,
        "email": user.email,
        "name": user.name,
    }

    website_content_update_dict = website_content_update.model_dump(exclude_unset=True)
    for k, v in website_content_update_dict.items():
        for child in website_content.children:
            setattr(child, k, v)
        setattr(website_content, k, v)

    # Update metadata with user information
    if website_content.meta:
        website_content.meta = {
            **website_content.meta,
            "user_info": {**website_content.meta["user_info"], "updated_by": user_info},
        }
    else:
        website_content.meta = {"user_info": {"updated_by": user_info}}
    session.add(website_content)
    await session.commit()
    await session.refresh(website_content)

    if website_content_update.categories is not None:
        update_website_categories.delay(website_content.id)
    refresh_beat_schedule.delay()
    return await get_assistant_website_content_or_404(website_content.assistant_id, website_content.id, session)
