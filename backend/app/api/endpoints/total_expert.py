import asyncio

from api.utils.pagination import page_number_pagination
from auth.cognito import check_user_permissions, get_or_create_auth_user, is_admin_user
from auth.cognito_sso import role_checker
from config import settings
from db.models import DataField, TotalExpertInsight, TotalExpertInsightType, TotalExpertJourney, TotalExpertToken, User
from db.session import get_session
from fastapi import APIRouter, Depends, HTTPException, Query, status
from fastapi.responses import JSONResponse
from loguru import logger
from schema.enums import DataFieldFolder, ReadStatus, RoleTypes, TEJourneyTypes, TotalExpertActivityType
from schema.total_expert import (
    ContactAdd,
    ContactGroupCreate,
    ContactGroupCreateResponse,
    ContactGroupRead,
    ContactNoteAdd,
    ContactNoteUpdate,
    ContactUpdate,
    CustomFieldRead,
    ImpersonateUserResponse,
    InsightTypeRead,
    InsightTypeUpdate,
    JourneyRead,
    JourneyUpdate,
    ListContactGroup,
    ListContacts,
    ListInsights,
    ListJourneys,
    ListUserForImpersonation,
    OauthCallback,
)
from sqlalchemy import delete, func, select
from sqlalchemy.ext.asyncio import AsyncSession
from tasks.total_expert import sync_journeys as sync_journeys_task
from tools.fields.totalexpert import CUSTOM_FIELD_NAME, fetch_all_fields
from tools.fields.totalexpert._default import (
    CASHOUT_REFI_CUSTOM_FIELD_MAPPING,
    CASHOUT_REFI_VALID_FIELDS,
    DEFAULT_JOURNEY_LOAN_FILTER_FIELDS,
    NOTE_TYPE_IDS_MAPPING,
    RATE_TERM_REFI_CUSTOM_FIELD_MAPPING,
    RATE_TERM_REFI_VALID_FIELDS,
)
from tools.utils.total_expert import total_expert_utils
from tools.utils.total_expert_token import TotalExpertTokenManager
from utils.type_conversion import type_converter

router = APIRouter()


@router.get("/contact-group/", response_model=ListContactGroup)
async def fetch_contact_groups(assistant_id: str, user: User = Depends(get_or_create_auth_user)):
    """
    Fetch contact groups for the authenticated user.

    This endpoint retrieves contact groups associated with the authenticated user by calling an external function.
    It returns a list of contact groups, including their IDs and names.

    Args:
        user (User): The authenticated user object, automatically provided by the `get_or_create_auth_user` dependency.

    Returns:
        ListContactGroup: A list of contact groups.

    Raises:
        HTTPException: If there is an error fetching contact groups from the external service or
        if the service returns an unexpected result.
    """
    try:
        response = await total_expert_utils.fetch_contact_groups(user=user, assistant_id=assistant_id)
        contact_groups = [
            ContactGroupRead(id=contact_group.get("id", ""), group_name=contact_group.get("group_name", ""))
            for contact_group in response
        ]
        return ListContactGroup(data=contact_groups)
    except Exception as e:
        logger.error(f"Error while fetching contact groups from total expert.{e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Some unexpected error occured while fetching contact groups from total expert.",
        )


@router.post("/contact-group/", response_model=ContactGroupCreateResponse)
async def create_contact_group(
    assistant_id: str,
    request: ContactGroupCreate,
    user: User = Depends(get_or_create_auth_user),
):
    """
    Create a new contact group in the Total Expert system.
    """

    try:
        response = await total_expert_utils.create_contact_group(
            user=user, group_name=request.group_name, assistant_id=assistant_id
        )
        return response
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"Error creating contact group: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An Error occured while creating contact group.",
        )


@router.patch("/contact/")
async def update_contact(
    assistant_id: str,
    request: ContactUpdate,
    user: User = Depends(get_or_create_auth_user),
    session: AsyncSession = Depends(get_session),
    operation: str = Query(None, description="Add or Remove contact from contact group"),
):
    """
    Update a contact in the Total Expert system.

    This endpoint allows updating a contact by providing the contact ID and the necessary details.
    It supports both single and bulk updates.

    Args:
        request (ContactUpdate): The request body containing the contact ID and the details to update.
        user (User): The authenticated user object, automatically provided by the `get_or_create_auth_user` dependency.

    Returns:
        JSONResponse: A JSON response indicating the success or failure of the update operation.

    Raises:
        HTTPException: If there is an error updating the contact in the Total Expert system or
        if the service returns an unexpected result.
    """
    contact_ids = request.contact_id
    if not contact_ids:
        logger.error("Contact id is required to update contact")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Contact id is required to update contact",
        )
    contact_id = None

    payload = request.model_dump(exclude={"contact_id"}, exclude_unset=True)

    # define success message
    message = "The contact has been updated successfully with the following details:\n\n" + "\n".join(
        f"- **{key}**: {value}" for key, value in request.model_dump(exclude_unset=True).items()
    )
    if request.contact_groups:
        custom_fields = payload.get("custom_fields", [])
        contact_groups_to_trigger = request.contact_groups or []

        for custom_field in custom_fields:
            if custom_field.get("field_name") == CUSTOM_FIELD_NAME:
                field_value = custom_field.get("value", "").lower()

                for contact_group in contact_groups_to_trigger:
                    journey_name = contact_group.group_name
                    journey_details = await total_expert_utils.fetch_journey_details(assistant_id, journey_name)
                    if not journey_details or not journey_details.get("is_custom"):
                        continue

                    blacklisted_words = journey_details.get("blacklisted_words", [])
                    for word in blacklisted_words:
                        if word.lower() in field_value:
                            return JSONResponse(
                                status_code=status.HTTP_400_BAD_REQUEST,
                                content={
                                    "message": (
                                        f"The word '{word}' is restricted in the '{CUSTOM_FIELD_NAME}' field for the "
                                        f"'{journey_name}' journey. Please remove this word to proceed."
                                    ),
                                },
                            )

        # message = (
        #     f"The journey **{', '.join([contact_group.group_name for contact_group in request.contact_groups])}**"
        #     " has been successfully triggered."
        #     # " has been successfully triggered for the following contacts:"
        #     # f" **{', '.join(str(contact_id) for contact_id in contact_ids) if isinstance(contact_ids, list) else str(contact_ids)}**."  # noqa
        # )
        message = (
            f"The campaign (**{', '.join([contact_group.group_name for contact_group in request.contact_groups])}**)"
            " message was successfully triggered to"
            f" {len(contact_ids) if isinstance(contact_ids, list) else 1} contact(s) that you selected above. All"
            " emails go out from your company email and responses will go back to you. If you want to see details on"
            " the deployment, you can find them in your Total Expert account or you can ask to see them here."
        )
    else:
        payload["contact_groups"] = []

    if isinstance(contact_ids, list):
        if len(contact_ids) > 1:
            bulk_update = await total_expert_utils.bulk_contact_update(
                user=user, contact_ids=contact_ids, payload=payload, assistant_id=assistant_id
            )
            if isinstance(bulk_update, list):
                return JSONResponse(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    content={"message": "Some error occured while updating following contacts: " + str(bulk_update)},
                )
            return JSONResponse(
                status_code=status.HTTP_200_OK,
                content={"message": message},
            )
        else:
            contact_id = contact_ids[0]

    if isinstance(contact_ids, str) or isinstance(contact_ids, int):
        contact_id = contact_ids

    response = await total_expert_utils.update_contact(
        user=user, contact_id=contact_id, payload=payload, assistant_id=assistant_id
    )

    if operation == "add_to_contact_group":
        message = "The contact has been added to the contact group successfully."
    if operation == "remove_from_contact_group":
        message = "The contact has been removed from the contact group successfully."

    response["message"] = message
    return JSONResponse(
        status_code=status.HTTP_200_OK,
        content=response,
    )


@router.post("/contact/", status_code=status.HTTP_201_CREATED)
async def add_contact(assistant_id: str, request: ContactAdd, user: User = Depends(get_or_create_auth_user)):
    """
    Add a new contact to the Total Expert system.

    This endpoint allows adding a new contact by providing the necessary details.
    Following fields are required: source, first_name, last_name, email or phone_cell.

    Args:
        request (ContactAdd): The request body containing the contact details.
        user (User): The authenticated user object, automatically provided by the `get_or_create_auth_user` dependency.

    Returns:
        JSONResponse: A JSON response indicating the success or failure of the add operation.

    Raises:
        HTTPException: If there is an error adding the contact to the Total Expert system or
        if the service returns an unexpected result.
    """
    if not request.contact_groups:
        request.contact_groups = []

    response = await total_expert_utils.add_contact(
        user=user,
        source=request.source,
        first_name=request.first_name,
        last_name=request.last_name,
        email=request.email,
        phone_cell=request.phone_cell,
        extra_fields=request.model_dump(exclude=["source", "first_name", "last_name", "email", "phone_cell"]),
        assistant_id=assistant_id,
    )
    message = (
        f"I have successfully added a contact with ID: {response.get('id')} and the following details:\n"
        + "\n".join(
            f"- **{key.replace('_', ' ').title()}**: {value}"
            for key, value in request.model_dump(exclude_unset=True).items()
        )
    )
    response["message"] = message
    return response


@router.get("/contact/")
async def fetch_contacts(assistant_id: str, user: User = Depends(get_or_create_auth_user), filter: str = ""):
    """
    Fetch a contact from the Total Expert system.
    """
    filter_criteria = []
    if len(filter) < 3:
        filter_criteria.extend([f"first_name={filter}", f"last_name={filter}", f"email={filter}"])
    else:
        try:
            first_name, last_name = filter.strip().split(" ")
            filter_criteria.append(f"first_name_contains={first_name},last_name_contains={last_name}")
        except ValueError:
            filter_criteria.extend(
                [f"first_name_contains={filter}", f"last_name_contains={filter}", f"email={filter}"]
            )
        except Exception as e:
            logger.warning(f"Error parsing filter criteria: {e}")
            filter_criteria = [""]
    tasks = [
        total_expert_utils.fetch_contacts(user=user, filter=filter_criterion, assistant_id=assistant_id)
        for filter_criterion in filter_criteria
    ]
    contacts = await asyncio.gather(*tasks)
    contacts = [contact for sublist in contacts for contact in sublist]
    count = len(contacts)
    return ListContacts(count=count, data=contacts)


@router.get("/contact/{id}/")
async def fetch_contact(
    assistant_id: str,
    id: str,
    format_reponse: bool = Query(False, description="Format the Response Or get Raw response from Total Expert"),
    user: User = Depends(get_or_create_auth_user),
):
    """
    Fetch a contact from the Total Expert system.
    """

    contact_details = await total_expert_utils.fetch_contact(user=user, contact_id=id, assistant_id=assistant_id)
    if not format_reponse:
        return contact_details
    FIELD_MAPPING, FIELD_TYPE_MAPPING, DISPLAY_FIELDS, SEARCH_FIELDS = await fetch_all_fields(
        assistant_id, folder=DataFieldFolder.CONTACT
    )

    contact_details = {
        field_name: type_converter.convert_string_data(
            FIELD_TYPE_MAPPING, field_key, contact_details.get(field_key, "")
        )
        for field_key, field_name in FIELD_MAPPING.items()
        if field_name in DISPLAY_FIELDS
    }
    return contact_details


@router.post("/contact-note/", status_code=status.HTTP_201_CREATED)
async def add_note(
    assistant_id: str, contact_id: str, request: ContactNoteAdd, user: User = Depends(get_or_create_auth_user)
):
    """
    Add a note to a contact in the Total Expert system.

    This endpoint allows adding a note to a contact by providing the contact ID, the note details, and the note type.
    The note type must be one of the predefined types.

    Args:
        contact_id (str): The ID of the contact to add the note to.
        request (ContactNoteAdd): The request body containing the note details.
        user (User): The authenticated user object, automatically provided by the `get_or_create_auth_user` dependency.

    Returns:
        JSONResponse: A JSON response indicating the success or failure of the add operation.

    Raises:
        HTTPException: If there is an error adding the note to the Total Expert system or
        if the service returns an unexpected result.
    """
    type_id = NOTE_TYPE_IDS_MAPPING.get(request.note_type)
    if not type_id:
        logger.error(f"Invalid note type: {request.note_type}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid Note type. It must be one of the following: " + ", ".join(NOTE_TYPE_IDS_MAPPING.keys()),
        )
    response = await total_expert_utils.add_note(
        user=user,
        note_title=request.title,
        note=request.note,
        type_id=type_id,
        contact_id=contact_id,
        assistant_id=assistant_id,
    )
    if response:
        return JSONResponse(
            status_code=status.HTTP_201_CREATED,
            content={"message": "Note added successfully"},
        )


@router.get("/insights/", response_model=ListInsights)
async def fetch_insights(
    assistant_id: str,
    status: ReadStatus | None = Query(None, description="Filter insights by status"),
    type: str | None = Query(None, description="Filter insights by type"),
    impersonated_user_id: str | None = Query(None, description="Impersonated user id"),
    session=Depends(get_session),
    user: User = Depends(get_or_create_auth_user),
    pagination: tuple[int, int] = Depends(page_number_pagination),
):
    """
    Fetch insights for the authenticated user.
    """
    if impersonated_user_id:
        await total_expert_utils.handle_multi_impersonation(user, impersonated_user_id, assistant_id)

    insights, count = await total_expert_utils.fetch_all_insights(
        user=user, pagination=pagination, assistant_id=assistant_id, session=session, status=status, type=type
    )
    return ListInsights(count=count, data=insights)


@router.patch(
    "/insights/{id}/read/",
    status_code=status.HTTP_204_NO_CONTENT,
    dependencies=[],
)
async def mark_insight_read(
    id: str,
    session=Depends(get_session),
    user: User = Depends(get_or_create_auth_user),
) -> None:
    # TODO: only give access if this insight belongs to the user
    query = select(TotalExpertInsight).where(TotalExpertInsight.id == id, TotalExpertInsight.user_id == user.id)
    db_insight = await session.execute(query)
    db_insight = db_insight.scalar_one_or_none()

    if not db_insight:
        logger.error(f"Insight {id} not found")
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Insight not found")

    db_insight.is_read = True
    await session.commit()
    return


@router.get("/custom-fields/", response_model=CustomFieldRead, dependencies=[Depends(get_or_create_auth_user)])
async def get_custom_fields():
    if isinstance(CUSTOM_FIELD_NAME, str):
        custom_fields = [CUSTOM_FIELD_NAME]
    elif isinstance(CUSTOM_FIELD_NAME, list):
        custom_fields = CUSTOM_FIELD_NAME
    else:
        custom_fields = []
    return {"fields": custom_fields}


@router.patch("/contact-note/{id}")
async def update_note(
    id: str, assistant_id: str, request: ContactNoteUpdate, user: User = Depends(get_or_create_auth_user)
):
    payload = request.model_dump(exclude_unset=True)
    if payload.get("note_type"):
        payload["type_id"] = NOTE_TYPE_IDS_MAPPING.get(payload.get("note_type", 1))
    response = await total_expert_utils.update_contact_note(user, id, payload, assistant_id=assistant_id)
    response["message"] = "Note updated Successfully!"
    return response


@router.delete("/contact-note/{id}/")
async def delete_note(assistant_id: str, id: str, user: User = Depends(get_or_create_auth_user)):
    _ = await total_expert_utils.delete_note(user, id, assistant_id=assistant_id)
    return {"message": "Note deleted successfully!"}


@router.get("/activity/")
async def fetch_activities(
    assistant_id: str,
    contact_id: str = Query(None, description="Contact ID to fetch activities for"),
    activity_type: TotalExpertActivityType = Query(None, description="Type of activity to fetch"),
    limit: int | None = Query(None, description="Number of activities to fetch"),
    user: User = Depends(get_or_create_auth_user),
):
    filter_criteria = []
    filter_criteria.append(f"contact_id={contact_id}" if contact_id else "")
    filter_criteria.append(f"type={activity_type}" if activity_type else "")
    filter_criteria = ",".join(filter(None, filter_criteria)).strip(",")
    return await total_expert_utils.fetch_activities(
        user, assistant_id=assistant_id, filter=filter_criteria, limit=limit
    )


# Journeys
@router.get("/journey/sync/")
async def sync_journeys(assistant_id: str, user: User = Depends(is_admin_user)):
    try:
        _ = sync_journeys_task.delay(user_id=user.id, assistant_id=assistant_id)
        return {"message": "Journeys are syncing! They will be updated shortly!"}
    except Exception as e:
        logger.error(f"Error syncing journeys: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Some Error occured while syncing journeys. Please try again after some time.",
        )


@router.get("/journeys/", dependencies=[Depends(get_or_create_auth_user)], response_model=ListJourneys)
async def list_journeys(
    assistant_id: str,
    type: list[TEJourneyTypes] = Query(None, description="Filter by journey type"),
    custom: bool = Query(None, description="Filter by custom journey"),
    search: str = Query(None, description="Search by name"),
    published: bool = Query(None, description="Filter by published status"),
    session: AsyncSession = Depends(get_session),
    pagination: tuple[int, int] = Depends(page_number_pagination),
):
    try:
        # fetch data from db and handle pagination
        page, size = pagination
        query = select(TotalExpertJourney).where(TotalExpertJourney.assistant_id == assistant_id)
        if type:
            query = query.where(TotalExpertJourney.type.in_(type))
        if custom is not None:
            if custom:
                query = query.where(TotalExpertJourney.is_custom)
            else:
                query = query.where(~TotalExpertJourney.is_custom)
        if search:
            query = query.where(TotalExpertJourney.name.ilike(f"%{search}%"))

        if published is not None:
            if published:
                query = query.where(TotalExpertJourney.is_published)
            else:
                query = query.where(~TotalExpertJourney.is_published)

        journey_count = await session.execute(select(func.count()).select_from(query))
        query = query.order_by(TotalExpertJourney.created_at.desc()).offset((page - 1) * size).limit(size)
        journeys = await session.execute(query)

        journeys = [JourneyRead(**journey.__dict__) for journey in journeys.scalars().all()]
        return ListJourneys(data=journeys, count=journey_count.scalar())

    except Exception as e:
        logger.error(f"Error fetching journeys: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Some Error occured while fetching journeys. Please try again after some time.",
        )


@router.patch("/journey/{id}/", response_model=JourneyRead)
async def update_journey(
    id: str,
    assistant_id: str,
    journey: JourneyUpdate,
    session: AsyncSession = Depends(get_session),
    user=Depends(get_or_create_auth_user),
):
    try:
        journey_update = journey.model_dump(exclude_unset=True)
        return await total_expert_utils.update_journey(
            id,
            assistant_id,
            journey_update,
            session,
            user=user,
        )
    except Exception as e:
        logger.error(f"Error updating journey: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Some Error occured while updating journey. Please try again after some time.",
        )


@router.get("/journey/{type}/available-fields/", dependencies=[Depends(get_or_create_auth_user)])
async def get_journey_fields(type: TEJourneyTypes):
    try:
        if type == TEJourneyTypes.RATE_TERM_REFI:
            return {k: v.get("name") for k, v in RATE_TERM_REFI_VALID_FIELDS.items()}
        elif type in [TEJourneyTypes.CASHOUT_REFI]:
            return {k: v.get("name") for k, v in CASHOUT_REFI_VALID_FIELDS.items()}
        else:
            # return all searchable loan fields
            return {k: v.get("name") for k, v in DEFAULT_JOURNEY_LOAN_FILTER_FIELDS.items()}

    except Exception as e:
        logger.error(f"Error fetching journey fields: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Some Error occured while fetching journey fields. Please try again after some time.",
        )


# TODO: This is bad trick. Take time and pass journey id as body in create function and ask FE to change
async def check_user_permission_with_assistant_id(assistant_id: str, user: User = Depends(get_or_create_auth_user)):
    return await check_user_permissions(id=assistant_id, user=user)


@router.post(
    "/journey/",
    response_model=JourneyRead,
)
async def create_journey(
    id: str,
    assistant_id: str,
    user: User = Depends(check_user_permission_with_assistant_id),
    session: AsyncSession = Depends(get_session),
):
    try:
        return await total_expert_utils.create_journey(assistant_id, journey_id=id, session=session, user=user)
    except HTTPException as e:
        # if http exception, raise again
        raise e

    except Exception as e:
        logger.error(f"Error creating contact Group: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Some Error occured while creating contact group. Please try again after some time.",
        )


@router.delete(
    "/journey/{id}/",
    dependencies=[Depends(check_user_permission_with_assistant_id)],
    status_code=status.HTTP_204_NO_CONTENT,
)
async def delete_journey(id: str, assistant_id: str):
    try:
        await total_expert_utils.delete_journey(assistant_id, journey_id=id)

    except HTTPException as e:
        raise e

    except Exception as e:
        logger.error(f"Error deleting journey: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Some Error occured while deleting journey. Please try again after some time.",
        )


@router.get("/journey/{id}/", response_model=JourneyRead, dependencies=[Depends(get_or_create_auth_user)])
async def fetch_journey(
    id: str,
    assistant_id: str,
    session: AsyncSession = Depends(get_session),
):
    try:
        query = select(TotalExpertJourney).where(
            TotalExpertJourney.id == id, TotalExpertJourney.assistant_id == assistant_id
        )
        result = await session.execute(query)
        journey = result.scalar_one_or_none()
        if not journey:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Journey not found")
        return journey
    except Exception as e:
        logger.error(f"Error fetching journey: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Some Error occured while fetching journey. Please try again after some time.",
        )


@router.get("/journey-types/", dependencies=[Depends(get_or_create_auth_user)])
async def fetch_journey_types():
    try:
        journey_types = [journey_type.value for journey_type in TEJourneyTypes]
        return {"data": journey_types}
    except Exception as e:
        logger.error(f"Error fetching journey types: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Some Error occured while fetching journey types. Please try again after some time.",
        )


@router.get("/insight-types/sync/", dependencies=[Depends(get_or_create_auth_user)])
async def sync_insight_types(assistant_id: str, session: AsyncSession = Depends(get_session)):
    try:
        await total_expert_utils.sync_insight_types(assistant_id, session)
        return {"message": "Insight types synced successfully"}
    except Exception as e:
        logger.error(f"Error syncing insight types: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Some Error occured while syncing insight types. Please try again after some time.",
        )


@router.get("/insight-types/", response_model=InsightTypeRead, dependencies=[Depends(get_or_create_auth_user)])
async def fetch_insight_types(
    assistant_id: str,
    is_published: bool = Query(None, description="Filter by published status"),
    session: AsyncSession = Depends(get_session),
):
    """
    Fetch insight types for the authenticated user.
    """
    try:
        query = select(TotalExpertInsightType).where(TotalExpertInsightType.assistant_id == assistant_id)
        if is_published is not None:
            if is_published:
                query = query.where(TotalExpertInsightType.is_published)
            else:
                query = query.where(~TotalExpertInsightType.is_published)
        query = query.order_by(TotalExpertInsightType.created_at.asc())
        result = await session.execute(query)
        insight_types = result.scalars().all()
        return {"data": insight_types}
    except Exception as e:
        logger.error(f"Error fetching insight types: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Could not load insight types. Please try again!",
        )


@router.patch("/insight-types/{id}/", dependencies=[Depends(get_or_create_auth_user)])
async def update_insight_type(
    id: str,
    assistant_id: str,
    insight_type_update: InsightTypeUpdate,
    user: User = Depends(get_or_create_auth_user),
    session: AsyncSession = Depends(get_session),
):
    user_info = {
        "user_id": str(user.id),
        "user_name": user.username,
        "email": user.email,
        "name": user.name,
    }
    try:
        query = select(TotalExpertInsightType).where(
            TotalExpertInsightType.id == id, TotalExpertInsightType.assistant_id == assistant_id
        )
        result = await session.execute(query)
        insight_type = result.scalar_one_or_none()
        if not insight_type:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Insight type not found")
        # extract values
        insight_type_update_dict = insight_type_update.model_dump(exclude_unset=True, exclude={"meta"})

        for k, v in insight_type_update_dict.items():
            setattr(insight_type, k, v)

        if insight_type.meta:
            insight_type.meta = {
                **insight_type.meta,
                "user_info": {**insight_type.meta["user_info"], "updated_by": user_info},
            }
        else:
            insight_type.meta = {"user_info": {"updated_by": user_info}}

        await session.commit()
        return insight_type.__dict__
    except Exception as e:
        logger.error(f"Error updating insight type: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Some Error occured while updating insight type. Please try again after some time.",
        )


# TODO: FOR INTERNAL PURPOSES ONLY
@router.delete("/journeys/", dependencies=[Depends(get_or_create_auth_user)])
async def delete_all_journeys(assistant_id: str, session: AsyncSession = Depends(get_session)):
    query = delete(TotalExpertJourney).where(TotalExpertJourney.assistant_id == assistant_id)
    await session.execute(query)
    await session.commit()
    return {"message": "All journeys deleted"}


@router.get("/journey/{type}/custom-mapping/", dependencies=[Depends(get_or_create_auth_user)])
async def get_journey_custom_field_mappings(type: TEJourneyTypes):
    if type == TEJourneyTypes.STANDARD:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Standard Journeys don't have any custom fields."
        )
    field_type_mappings = {
        TEJourneyTypes.RATE_TERM_REFI: RATE_TERM_REFI_CUSTOM_FIELD_MAPPING,
        TEJourneyTypes.CASHOUT_REFI: CASHOUT_REFI_CUSTOM_FIELD_MAPPING,
    }
    return field_type_mappings.get(type, [])


@router.post("/total-expert/callback/", status_code=status.HTTP_201_CREATED)
async def total_expert_callback(
    assistant_id: str,
    request: OauthCallback,
    user: User = Depends(get_or_create_auth_user),
    session: AsyncSession = Depends(get_session),
):
    """
    Handle Total Expert OAuth callback and exchange code for tokens
    """
    try:
        code = request.code

        token_manager = TotalExpertTokenManager(session)
        await token_manager.initialize()
        token_data = await token_manager.get_access_token(code, assistant_id)

        if not token_data:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Failed to get access token")

        await token_manager.save_token(
            user_id=user.id,
            assistant_id=assistant_id,
            access_token=token_data["access_token"],
            refresh_token=token_data["refresh_token"],
            expires_in=token_data["expires_in"],
            token_type=token_data.get("token_type", "Bearer"),
        )

        return {"message": "Total Expert access saved successfully"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in Total Expert callback: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to complete Total Expert authorization"
        )


@router.post("/total-expert/revoke")
async def total_expert_revoke(
    assistant_id: str, user: User = Depends(get_or_create_auth_user), session: AsyncSession = Depends(get_session)
):
    """
    Revoke Total Expert access by deleting tokens
    """
    try:
        token_manager = TotalExpertTokenManager(session)
        await token_manager.initialize()
        await token_manager.delete_token(user.id, assistant_id)

        return {"message": "Total Expert access revoked successfully"}

    except Exception as e:
        logger.error(f"Error revoking Total Expert access: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to revoke Total Expert access"
        )


@router.get("/total-expert/auth-url/")
async def get_total_expert_auth_url(
    assistant_id: str,
    user: User = Depends(get_or_create_auth_user),
    session: AsyncSession = Depends(get_session),
):
    """
    Get Total Expert SSO URL or check if user is already authenticated.

    This endpoint checks if the user has a valid Total Expert token. If not, it returns
    the SSO URL for authentication. If a valid token exists, it returns a success status.

    Args:
        assistant_id (str): The ID of the assistant
        user (User): The authenticated user object
        session (AsyncSession): Database session

    Returns:
        dict: Either the SSO URL or a success status
    """
    try:
        token_manager = TotalExpertTokenManager(session)
        await token_manager.initialize()
        return await token_manager.get_auth_url(user, assistant_id, session)
    except Exception as e:
        logger.error(f"Error in Total Expert auth URL endpoint: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to generate Total Expert authentication URL",
        )


@router.get("/loan-officers/", response_model=ListUserForImpersonation, dependencies=[Depends(check_user_permissions)])
async def fetch_all_loan_officers(
    id: str,
    q: str = Query(None, description="Filter by name, email or user_id"),
    session=Depends(get_session),
    pagination: tuple[int, int] = Depends(page_number_pagination),
):
    """id: assistant id"""
    page, size = pagination
    query = select(User).join(
        TotalExpertToken, User.id == TotalExpertToken.user_id and TotalExpertToken.assistant_id == id
    )
    if q:
        query = query.where(User.email.ilike(f"%{q}%") | User.name.ilike(f"%{q}%") | User.username.ilike(f"%{q}%"))
    total_data_fields = await session.execute(select(func.count()).select_from(query))
    count = total_data_fields.scalar()
    query = query.offset((page - 1) * size).limit(size)
    result = await session.execute(query)
    return {"count": count, "data": result.scalars().all()}


@router.get("/impersonation/current", response_model=ImpersonateUserResponse)
async def get_current_impersonation(
    assistant_id: str,
    user: User = Depends(get_or_create_auth_user),
    session: AsyncSession = Depends(get_session),
):
    lo_email = await total_expert_utils.map_user(user, assistant_id)
    if lo_email == user.email:
        return user
    result = await session.execute(select(User).where(User.email == lo_email))
    active_impersonation = result.scalar_one_or_none()
    if not active_impersonation:
        logger.error("Impersonated loan officer doesnot exist or multiple active impersonation")
        # Defaulting to self
        return user
    return active_impersonation


@router.get("/oauth_status/", dependencies=[Depends(get_or_create_auth_user)])
async def get_oauh_status():
    return {"status": settings.ENABLE_TOTAL_EXPERT_OAUTH}


@router.delete("/fields", dependencies=[Depends(role_checker([RoleTypes.ADMIN]))])
async def delete_all_te_fields(assistant_id: str, session: AsyncSession = Depends(get_session)):
    try:
        query = delete(DataField).where(DataField.assistant_id == assistant_id)
        await session.execute(query)
        await session.commit()
        return {"status": "Deleted All the Fields"}
    except Exception as e:
        logger.info(e)
        await session.rollback()
        raise HTTPException(status_code=500, detail="Something went wrong.")
