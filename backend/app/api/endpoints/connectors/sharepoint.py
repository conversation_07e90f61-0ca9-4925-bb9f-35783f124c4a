from collections import defaultdict

from api.endpoints.assistant import get_assistant_or_404
from api.utils.pagination import page_number_pagination
from auth.cognito import check_user_permissions, is_not_general_user
from celery import chain, chord, group
from config import settings
from db.models import Assistant, Document, User
from db.session import get_session
from fastapi import APIRouter, Depends, Header, HTTPException, Query, status
from fastapi.responses import JSONResponse
from hooks.connectors.sharepoint_hook import sharepoint_hook
from loguru import logger
from schema.connectors.sharepoint import (
    BatchDeleteRequest,
    BatchPageDeleteRequest,
    BatchPageUploadRequest,
    BatchUploadRequest,
    DriveItemList,
    FileItem,
    FolderItem,
    PageContentItem,
    PageItemList,
    SiteItemList,
)
from schema.enums import DocumentTypes
from sqlalchemy import and_, select
from sqlalchemy.ext.asyncio import AsyncSession
from tasks.indexing import delete_documents, process_document
from tasks.sharepoint import mark_folder_indexed, upload_and_index_documents, upload_and_index_page
from utils.sharepoint import (
    fetch_all_files_from_folder,
    fetch_all_indexed_doc_ids,
    handle_modified_file,
    upload_new_file,
    valid_file_mime_types,
    valid_image_mime_types,
)

router = APIRouter()


@router.get(
    "/sites/",
    status_code=status.HTTP_200_OK,
    response_model=SiteItemList,
    dependencies=[Depends(is_not_general_user)],
)
async def list_sharepoint_sites(
    pagination: tuple[int, int] = Depends(page_number_pagination),
    sharepoint_token: str = Header(..., alias="X-Sharepoint-Token"),
) -> SiteItemList:
    page, size = pagination
    await sharepoint_hook.set_access_token(sharepoint_token)
    sites = await sharepoint_hook.list_sites()
    start = (page - 1) * size
    end = start + size
    return {"count": len(sites), "data": sites[start:end]}


@router.get(
    "/sites/{site_id}/drives/",
    status_code=status.HTTP_200_OK,
    response_model=DriveItemList,
    dependencies=[Depends(is_not_general_user)],
)
async def list_sharepoint_drives(
    site_id: str,
    pagination: tuple[int, int] = Depends(page_number_pagination),
    sharepoint_token: str = Header(..., alias="X-Sharepoint-Token"),
) -> DriveItemList:
    page, size = pagination
    await sharepoint_hook.set_access_token(sharepoint_token)
    drives = await sharepoint_hook.get_drive_id(site_id)
    start = (page - 1) * size
    end = start + size
    return {"count": len(drives), "data": drives[start:end]}


@router.get(
    "/{id}/sites/{site_id}/drives/{drive_id}/list/", status_code=status.HTTP_200_OK, response_model=list[FolderItem]
)
async def list_sharepoint_content(
    site_id: str,
    drive_id: str,
    folder_id: str = Query("root", description="Folder ID"),
    is_indexed: bool = Query(None, description="Filter by indexed status"),
    type: DocumentTypes = Query(DocumentTypes.SHAREPOINT, description="Filter by type"),
    hard_refresh: bool = Query(False, description="Hard refresh cache"),
    user=Depends(is_not_general_user),
    assistant: Assistant = Depends(get_assistant_or_404),
    title: str = Query(None, description="Filter by title"),
    categories: list[str] = Query(None, description="Filter by categories"),
    sharepoint_token: str = Header(..., alias="X-Sharepoint-Token"),
) -> list[FolderItem]:
    await sharepoint_hook.set_access_token(sharepoint_token)
    await sharepoint_hook.initialize_redis()
    logger.info(f"Listing SharePoint content for {site_id}, {drive_id}, {folder_id}")
    content = await sharepoint_hook.list_folder_contents(
        user.id, assistant.id, title, categories, site_id, drive_id, folder_id, type, hard_refresh, is_indexed
    )
    return content


@router.post(
    "/{id}/upload-file-to-s3/",
    status_code=status.HTTP_200_OK,
)
async def upload_file_to_s3(
    file: FileItem,
    assistant: Assistant = Depends(get_assistant_or_404),
    session: AsyncSession = Depends(get_session),
    sharepoint_token: str = Header(..., alias="X-Sharepoint-Token"),
    user=Depends(check_user_permissions),
) -> str:
    file_dict = file.model_dump()
    try:
        await sharepoint_hook.set_access_token(sharepoint_token)
        if file_dict["mimeType"] in valid_image_mime_types:
            type = DocumentTypes.SHAREPOINT_IMAGE
        elif file_dict["mimeType"] in valid_file_mime_types:
            type = DocumentTypes.SHAREPOINT
        else:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid file type")

        is_indexed, is_modified, document = await sharepoint_hook.check_is_indexed(
            assistant.id, file.id, file.lastModifiedDateTime, type
        )

        if is_indexed:
            if not is_modified:
                logger.info(f"File is already indexed and not modified: {file.fullpath}")
                return JSONResponse(
                    status_code=status.HTTP_409_CONFLICT, content={"message": "File is already indexed"}
                )
            user_info = {
                "user_id": str(user.id),
                "user_name": user.username,
                "email": user.email,
                "name": user.name,
            }
            document_id = await handle_modified_file(document, file_dict, assistant.id, session, user_info)
            if document_id:
                process_document.delay(document_id)
            return JSONResponse(status_code=status.HTTP_200_OK, content={"message": "Modified file indexed"})
        else:
            file_url = await upload_new_file(file_dict, assistant.id, file.site_id, file.drive_id)
            return file_url
    except Exception as e:
        logger.error(f"Error uploading file to S3: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to upload file to S3")


@router.post(
    "/{id}/batch-upload-files-to-s3/",
    status_code=status.HTTP_200_OK,
)
async def batch_upload_files_to_s3(
    request: BatchUploadRequest,
    assistant: Assistant = Depends(get_assistant_or_404),
    sharepoint_token: str = Header(..., alias="X-Sharepoint-Token"),
    session: AsyncSession = Depends(get_session),
    user=Depends(check_user_permissions),
) -> list[dict]:
    responses = []
    user_info = {
        "user_id": str(user.id),
        "user_name": user.username,
        "email": user.email,
        "name": user.name,
    }
    valid_mime_types = (
        valid_image_mime_types if request.type == DocumentTypes.SHAREPOINT_IMAGE else valid_file_mime_types
    )
    categories = request.categories if request.categories else assistant.categories

    files = []
    if request.files:
        files.extend([file for file in request.files if file.mimeType in valid_mime_types])

    if request.folders:
        await sharepoint_hook.set_access_token(sharepoint_token)
        await sharepoint_hook.initialize_redis()
        for folder in request.folders:
            folder_size = folder.size / (1024 * 1024)  # Convert bytes to MB
            if folder_size > settings.MAX_SHAREPOINT_FOLDER_SIZE_MB:
                logger.warning(
                    f"Folder size({folder_size} MB) exceeds the limit of {settings.MAX_SHAREPOINT_FOLDER_SIZE_MB} MB"
                )
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Folder size exceeds the limit of {settings.MAX_SHAREPOINT_FOLDER_SIZE_MB} MB",
                )
            files.extend(
                await fetch_all_files_from_folder(
                    folder,
                    user.id,
                    assistant.id,
                    folder.site_name,
                    session,
                    categories,
                    user_info,
                    request.type,
                    valid_mime_types,
                )
            )

    grouped_files: dict[str | None, list[FileItem]] = defaultdict(list)
    for file in files:
        grouped_files[file.folder_id].append(file)
    if request.folders and not grouped_files:
        for folder in request.folders:
            mark_folder_indexed.delay(None, folder.id, assistant.id, request.type)
            responses.append({"folder_id": folder.id, "message": "Folder indexed successfully"})

    for folder_id, files in grouped_files.items():
        tasks = []
        for file in files:
            file_dict = file.model_dump()
            meta = {**file_dict, "user_info": user_info}
            task_chain = chain(
                upload_and_index_documents.s(
                    file_dict, assistant.id, sharepoint_token, user_info, categories, request.type, meta
                ),
                process_document.s(),
            )
            tasks.append(task_chain)
            responses.append({"file_id": file.id, "file_name": file.name, "message": "File queued for indexing"})
        if tasks:
            if folder_id:
                chord(tasks)(mark_folder_indexed.s(folder_id, assistant.id, request.type))
            else:
                group(tasks)()
    return responses


@router.delete(
    "/{id}/batch-delete/",
    status_code=status.HTTP_204_NO_CONTENT,
)
async def batch_delete_files(
    request: BatchDeleteRequest,
    sharepoint_token: str = Header(..., alias="X-Sharepoint-Token"),
    assistant=Depends(get_assistant_or_404),
    session: AsyncSession = Depends(get_session),
    user=Depends(check_user_permissions),
):
    await sharepoint_hook.set_access_token(sharepoint_token)
    await sharepoint_hook.initialize_redis()

    # validate doc_ids
    query = select(Document.id).where(
        and_(Document.id.in_(request.doc_ids), Document.assistant_id == assistant.id, Document.type == request.type)
    )
    result = await session.execute(query)
    doc_ids = result.scalars().all() or []
    if request.folders:
        for folder in request.folders:
            doc_ids.extend(
                await fetch_all_indexed_doc_ids(folder, user.id, assistant.id, folder.site_name, request.type, session)
            )
    delete_documents.delay(assistant.id, doc_ids)
    return {"message": "Document deletion started successfully"}


@router.get(
    "/{id}/sites/{site_id}/pages/",
    status_code=status.HTTP_200_OK,
    response_model=PageItemList,
)
async def list_sharepoint_pages(
    site_id: str,
    is_indexed: bool = Query(None, description="Filter by indexed status"),
    categories: list[str] = Query(None, description="Filter by categories"),
    hard_refresh: bool = Query(False, description="Hard refresh cache"),
    assistant: Assistant = Depends(get_assistant_or_404),
    user: User = Depends(is_not_general_user),
    pagination: tuple[int, int] = Depends(page_number_pagination),
    sharepoint_token: str = Header(..., alias="X-Sharepoint-Token"),
) -> PageItemList:
    page, size = pagination
    await sharepoint_hook.set_access_token(sharepoint_token)
    await sharepoint_hook.initialize_redis()
    pages = await sharepoint_hook.list_pages(user.id, assistant.id, categories, site_id, is_indexed, hard_refresh)
    start = (page - 1) * size
    end = start + size
    return {"count": len(pages), "data": pages[start:end]}


@router.get(
    "/{id}/sites/{site_id}/pages/{page_id}/",
    status_code=status.HTTP_200_OK,
    response_model=PageContentItem,
)
async def get_sharepoint_page(
    site_id: str,
    page_id: str,
    assistant: Assistant = Depends(get_assistant_or_404),
    categories: list[str] = Query(None, description="Filter by categories"),
    is_indexed: bool = Query(None, description="Filter by indexed status"),
    hard_refresh: bool = Query(False, description="Hard refresh cache"),
    user: User = Depends(is_not_general_user),
    sharepoint_token: str = Header(..., alias="X-Sharepoint-Token"),
) -> PageContentItem:
    await sharepoint_hook.set_access_token(sharepoint_token)
    await sharepoint_hook.initialize_redis()
    page = await sharepoint_hook.get_page_content(
        user.id,
        assistant.id,
        categories,
        site_id,
        page_id,
        is_indexed,
        hard_refresh,
    )
    if not page:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Page not found")
    return page


@router.post(
    "/{id}/batch-upload-pages/",
    status_code=status.HTTP_200_OK,
)
async def batch_upload_pages(
    request: BatchPageUploadRequest,
    assistant: Assistant = Depends(get_assistant_or_404),
    sharepoint_token: str = Header(..., alias="X-Sharepoint-Token"),
    user: User = Depends(is_not_general_user),
) -> list[dict]:
    responses = []
    user_info = {
        "user_id": str(user.id),
        "user_name": user.username,
        "email": user.email,
        "name": user.name,
    }
    categories = request.categories if request.categories else assistant.categories

    if not request.pages:
        logger.warning("No pages provided for batch processing")
        return responses

    tasks = []
    for page in request.pages:
        page_dict = page.model_dump()
        meta = {**page_dict, "user_info": user_info}
        subpages = page_dict.get("subpages", [])
        for sub_page in subpages:
            sub_page_meta = {**sub_page, "user_info": user_info}
            sub_page_task = chain(
                upload_and_index_page.s(
                    sub_page, assistant.id, sharepoint_token, user_info, categories, sub_page_meta
                ),
                process_document.s(),
            )
            tasks.append(sub_page_task)
            responses.append(
                {"page_id": sub_page["id"], "page_title": sub_page["title"], "message": "Subpage queued for indexing"}
            )

        task_chain = chain(
            upload_and_index_page.s(page_dict, assistant.id, sharepoint_token, user_info, categories, meta),
            process_document.s(),
        )
        tasks.append(task_chain)
        responses.append({"page_id": page.id, "page_title": page.title, "message": "Page queued for indexing"})

    if tasks:
        group(tasks)()

    return responses


@router.delete(
    "/{id}/batch-delete-pages/",
    status_code=status.HTTP_204_NO_CONTENT,
)
async def batch_delete_pages(
    request: BatchPageDeleteRequest,
    sharepoint_token: str = Header(..., alias="X-Sharepoint-Token"),
    assistant: Assistant = Depends(get_assistant_or_404),
    session: AsyncSession = Depends(get_session),
):
    await sharepoint_hook.set_access_token(sharepoint_token)
    await sharepoint_hook.initialize_redis()
    query = select(Document.id).where(
        and_(
            Document.id.in_(request.page_ids),
            Document.assistant_id == assistant.id,
            Document.type == DocumentTypes.SHAREPOINT_PAGE,
        )
    )
    result = await session.execute(query)
    doc_ids = result.scalars().all() or []
    delete_documents.delay(assistant.id, doc_ids)
    return {"message": "SharePoint page deletion started successfully"}
