from collections.abc import Sequence

from api.utils.division import get_division_or_404
from api.utils.pagination import page_number_pagination
from auth.cognito import get_or_create_auth_user, is_not_general_user, role_checker
from config import settings
from db.models import Assistant, AssistantAccess, Division, User
from db.session import get_session
from fastapi import APIRouter, Depends, HTTPException, Query, status
from hooks.openai_hook import GROUNDING_INSTRUCTIONS, oai_hook
from schema.assistant import (
    AssistantAccessCreate,
    AssistantAccessRead,
    AssistantAccessUpdate,
    AssistantCreate,
    AssistantRead,
    AssistantUpdate,
)
from schema.enums import AssistantProvider, AssistantTypes, RoleTypes
from schema.user import AssistantUserReadList, UserList
from sqlalchemy import and_, func, or_, select, update
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload
from tasks.indexing import remove_categories_from_related_models

router = APIRouter()


async def get_assistant_or_404(
    id: str,
    user: User = Depends(is_not_general_user),
    session: AsyncSession = Depends(get_session),
) -> Assistant:
    if user.role == RoleTypes.ADMIN:
        query = (
            select(Assistant)
            .where(Assistant.id == id)
            .options(selectinload(Assistant.users))
            .options(selectinload(Assistant.private_users))
        )
    elif user.role == RoleTypes.DIVISION_ADMIN:
        query = (
            select(Assistant)
            .where(Assistant.id == id)
            .where(Assistant.division_id == user.division_id)
            .options(selectinload(Assistant.users))
            .options(selectinload(Assistant.private_users))
        )
    elif user.role == RoleTypes.ASSISTANT_ADMIN:
        query = (
            select(Assistant)
            .where((Assistant.id == id) & (Assistant.users.any(User.id == user.id)))
            .where(Assistant.division_id == user.division_id)
            .options(selectinload(Assistant.users))
            .options(selectinload(Assistant.private_users))
        )
    else:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, detail="You are not permitted to access this assistant"
        )

    result = await session.execute(query)
    assistant = result.scalar_one_or_none()

    if assistant is None:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Assistant not found")

    return assistant


async def get_personal_assistant_or_404(id: str, session: AsyncSession = Depends(get_session)) -> Assistant:
    query = select(Assistant).where(Assistant.id == id, Assistant.type == AssistantTypes.PERSONAL)
    result = await session.execute(query)
    assistant = result.scalar_one_or_none()

    if assistant is None:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Personal assistant not found")

    return assistant


@router.get("/v2/", response_model=list[AssistantRead])
async def list_assistants(
    user: User = Depends(get_or_create_auth_user),
    division: Division | None = Depends(get_division_or_404),
    type: list[AssistantTypes] = Query(None, description="Filter by assistant type"),
    pagination: tuple[int, int] = Depends(page_number_pagination),
    session: AsyncSession = Depends(get_session),
) -> Sequence[Assistant]:
    """
    List assistants based on their publication status and the user's role.

    Retrieves a paginated list of assistants. The visibility of assistants is determined based on the user's role:
    - Admins can see all assistants.
    - Division Admins can see assistants associated with their division.
    - Assistant admins can see published assistants and those they are associated with.
    - Regular users can see public assistants and private assistants they are associated with.

    Args:
        user (User): The authenticated user object, provided by the `get_or_create_auth_user` dependency.
        division_id(optional): The ID of the division to filter assistants by.
        pagination (tuple[int, int]): A tuple containing pagination parameters (page, size).
        session (AsyncSession): The SQLAlchemy asynchronous session, provided by the `get_session` dependency.

    Returns:
        Sequence[Assistant]: A list of pydantic models representing the assistants.

    Raises:
        HTTPException: If there is a database error, an HTTP 500 error is raised.
    """
    page, size = pagination

    if user.role == RoleTypes.ADMIN:
        query = select(Assistant)
        if division:
            query = query.where(Assistant.division_id == division.id)

    elif user.role == RoleTypes.DIVISION_ADMIN:
        query = select(Assistant).where(Assistant.division_id == user.division_id)

    elif user.role == RoleTypes.ASSISTANT_ADMIN:
        query = (
            select(Assistant)
            .where(Assistant.division_id == user.division_id)
            .where(
                or_(
                    Assistant.is_published == True,  # noqa: E712
                    and_(
                        Assistant.is_published == False,  # noqa: E712
                        Assistant.users.any(User.id == user.id),
                    ),
                )
            )
        )
    else:
        query = (
            select(Assistant)
            .where(Assistant.division_id == user.division_id)
            .where(
                or_(
                    and_(Assistant.is_published == True, Assistant.is_private == False),  # noqa: E712
                    and_(
                        Assistant.is_published == True,  # noqa: E712
                        Assistant.is_private == True,  # noqa: E712
                        Assistant.private_users.any(User.id == user.id),
                    ),
                )
            )
        )

    if type is not None:
        query = query.where(Assistant.type.in_(type))

    query = query.order_by(Assistant.display_order).offset((page - 1) * size).limit(size)
    result = await session.execute(query)
    return result.scalars().all()


@router.get("/v2/{id}/", response_model=AssistantRead)
async def get_assistant(assistant: Assistant = Depends(get_assistant_or_404)) -> Assistant:
    """
    Retrieve a specific assistant by ID.

    Fetches the details of an assistant based on its ID.

    Args:
        assistant (Assistant): The assistant object, provided by the `get_assistant_or_404` dependency.

    Returns:
        AssistantRead: A pydantic model with the details of the assistant.

    Raises:
        HTTPException: If the assistant is not found, an HTTP 404 error is raised.
    """

    if hasattr(assistant, "categories") and isinstance(assistant.categories, list):
        # Strip leading/trailing whitespace from each category
        assistant.categories = [category.strip() for category in assistant.categories]
        # Sort categories case-insensitively
        assistant.categories.sort(key=str.lower)

    return assistant


@router.get("/v2/{id}/admin/", response_model=AssistantUserReadList, dependencies=[Depends(is_not_general_user)])
async def get_assistant_admins(
    assistant: Assistant = Depends(get_assistant_or_404),
    pagination: tuple[int, int] = Depends(page_number_pagination),
) -> AssistantUserReadList:
    """
    List administrators associated with a specific assistant.

    Retrieves the list of users with the role of assistant admin who are associated with the given assistant.

    Args:
        assistant (Assistant): The assistant object, provided by the `get_assistant_or_404` dependency.

    Returns:
        list[AssistantUserRead]: A list of pydantic models representing assistant admins.

    Raises:
        HTTPException: If the user does not have sufficient permissions, an HTTP 403 error is raised.
    """
    admin_users = [user for user in assistant.users if user.role == RoleTypes.ASSISTANT_ADMIN]

    page, size = pagination
    total_count = len(admin_users)
    start = (page - 1) * size
    end = start + size
    paginated_admins = admin_users[start:end]

    return {"count": total_count, "data": paginated_admins}


@router.get("/v2/{id}/users/", response_model=AssistantUserReadList, dependencies=[Depends(is_not_general_user)])
async def get_private_users(
    assistant: Assistant = Depends(get_assistant_or_404),
    pagination: tuple[int, int] = Depends(page_number_pagination),
) -> AssistantUserReadList:
    """
    List private users associated with a specific private assistant.

    Retrieves the list of users who are permitted to access a private assistant.
    Raises an error if the assistant is not private.

    Args:
        assistant (Assistant): The assistant object, provided by the `get_assistant_or_404` dependency.

    Returns:
        list[AssistantUserRead]: A list of pydantic models representing private users.

    Raises:
        HTTPException: If the assistant is not private, an HTTP 400 error is raised.
    """
    if not assistant.is_private:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Assistant is not private")

    private_users = [
        user for user in assistant.private_users if user.role in [RoleTypes.GENERAL, RoleTypes.ASSISTANT_ADMIN]
    ]
    page, size = pagination
    start = (page - 1) * size
    end = start + size
    paginated_users = private_users[start:end]

    return {"count": len(private_users), "data": paginated_users}


@router.get("/v2/{id}/users/all/", response_model=AssistantUserReadList, dependencies=[Depends(is_not_general_user)])
async def get_all_private_users(
    assistant: Assistant = Depends(get_assistant_or_404),
) -> AssistantUserReadList:
    """
    List of all private users associated with a specific private assistant.
    Args:
        assistant (Assistant): The assistant object, provided by the `get_assistant_or_404` dependency.

    Returns:
        list[AssistantUserRead]: A list of pydantic models representing private users.

    Raises:
        HTTPException: If the assistant is not private, an HTTP 400 error is raised.
    """
    if not assistant.is_private:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Assistant is not private")

    private_users = [
        user for user in assistant.private_users if user.role in [RoleTypes.GENERAL, RoleTypes.ASSISTANT_ADMIN]
    ]

    return {"count": len(private_users), "data": private_users}


@router.patch("/v2/{id}/assign/", status_code=status.HTTP_204_NO_CONTENT, dependencies=[Depends(is_not_general_user)])
async def assign_user_to_private_assistant(
    user_ids: list[str],
    assistant: Assistant = Depends(get_assistant_or_404),
    session: AsyncSession = Depends(get_session),
):
    """
    Assign users to a private assistant.

    Updates the list of users who can access a private assistant.
    Raises an error if the assistant is not private or if any of the users are not found.

    Args:
        user_ids (list[str]): A list of user IDs to assign to the assistant.
        assistant (Assistant): The assistant object, provided by the `get_assistant_or_404` dependency.
        session (AsyncSession): The SQLAlchemy asynchronous session, provided by the `get_session` dependency.

    Returns:
        None: No content is returned upon successful assignment.

    Raises:
        HTTPException: If the assistant is not private, or if any of the users are not found,
        HTTP 400 or 404 errors are raised.
    """
    if not assistant.is_private:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Assistant is not private")

    user = await session.execute(
        select(User)
        .where(User.division_id == assistant.division_id)
        .where(User.id.in_(user_ids), User.role.in_([RoleTypes.GENERAL, RoleTypes.ASSISTANT_ADMIN]))
    )
    users = user.scalars().all()
    if len(users) != len(user_ids):
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="General users/Assistant admins not found")

    assistant.private_users = users
    session.add(assistant)
    await session.commit()
    await session.refresh(assistant)


@router.delete(
    "/v2/{id}/delete/{user_id}/", status_code=status.HTTP_204_NO_CONTENT, dependencies=[Depends(is_not_general_user)]
)
async def unassign_user_from_private_assistant(
    user_id: str,
    assistant: Assistant = Depends(get_assistant_or_404),
    session: AsyncSession = Depends(get_session),
):
    if not assistant.is_private:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Assistant is not private")

    user = await session.execute(
        select(User)
        .where(User.division_id == assistant.division_id)
        .where(User.id == user_id, User.role.in_([RoleTypes.GENERAL, RoleTypes.ASSISTANT_ADMIN]))
    )
    user = user.scalar_one_or_none()
    if user is None:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="General user/Assistant admin not found")

    if user in assistant.private_users:
        assistant.private_users.remove(user)
    else:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="User not assigned to assistant")

    session.add(assistant)
    await session.commit()
    await session.refresh(assistant)


@router.get("/v2/{id}/manage-users/", response_model=UserList)
async def list_all_assistant_division_users(
    search: str = Query(None, description="Filter by name or email"),
    pagination: tuple[int, int] = Depends(page_number_pagination),
    session: AsyncSession = Depends(get_session),
    assistant: Assistant = Depends(get_assistant_or_404),
) -> Sequence[User]:
    """
    Retrieve a paginated list of users with optional filtering by name, email, and role.

    This endpoint is restricted to admin users only. It returns a paginated list of users,
    optionally filtered by name, email, or role.

    Args:
        pagination (tuple[int, int]): A tuple containing the page number and page size, provided by
        `page_number_pagination`.
        session (AsyncSession): The SQLAlchemy asynchronous session, provided by the `get_session` dependency.
        search (str, optional): A string to filter users by name or email.
        role (RoleTypes, optional): A string to filter users by role.
        division (Division): The division object, provided by the `get_division_or_404` dependency.
        logged_in_user (User): The currently authenticated user, provided by the `role_checker` dependency.

    Returns:
        Sequence[User]: A paginated list of users matching the filters.
    """
    page, size = pagination

    select_query = (
        select(User)
        .where(User.division_id == assistant.division_id)
        .where(User.role.in_([RoleTypes.GENERAL, RoleTypes.ASSISTANT_ADMIN]))
        .options(selectinload(User.assistants), selectinload(User.division))
    )
    if search:
        select_query = select_query.where(
            or_(
                User.search_tsvector.op("@@")(func.plainto_tsquery("english", f"{search}:*")),
                User.name.ilike(f"%{search}%"),
                User.email.ilike(f"%{search}%"),
            )
        )

    total_users = await session.execute(select(func.count()).select_from(select_query))
    count = total_users.scalar()

    # Sort by alphabetical order (User.name)
    select_query = select_query.order_by(User.name.asc())

    select_query = select_query.offset((page - 1) * size).limit(size)
    result = await session.execute(select_query)

    return {
        "count": count,
        "data": result.scalars().all(),
    }


@router.post(
    "/v2/",
    response_model=AssistantRead,
    status_code=status.HTTP_201_CREATED,
    dependencies=[Depends(role_checker([RoleTypes.ADMIN, RoleTypes.DIVISION_ADMIN]))],
)
async def create_assistant(
    assistant_create: AssistantCreate,
    division: Division | None = Depends(get_division_or_404),
    user: User = Depends(get_or_create_auth_user),
    session: AsyncSession = Depends(get_session),
) -> Assistant:
    """
    Create a new assistant.

    Creates a new assistant with the provided details. Checks if the assistant limit has been reached before creation.

    Args:
        assistant_create (AssistantCreate): A pydantic model containing the details for the new assistant.
        user (User): The authenticated user object, provided by the `get_or_create_auth_user` dependency.
        session (AsyncSession): The SQLAlchemy asynchronous session, provided by the `get_session` dependency.

    Returns:
        AssistantRead: A pydantic model with the details of the created assistant.

    Raises:
        HTTPException: If the assistant limit is reached or if there is a database error,
        HTTP 403 or 500 errors are raised.
    """
    # Count the number of Assistants
    result = await session.execute(select(func.count()).select_from(Assistant))
    total_assistants = result.scalar() or 0
    if settings.ASSISTANT_LIMIT is not None and total_assistants >= settings.ASSISTANT_LIMIT:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Assistants limit reached")

    # Get the current maximum display_order value
    result = await session.execute(select(func.max(Assistant.display_order)))
    max_display_order = result.scalar() or 0

    if assistant_create.type in [AssistantTypes.PERSONAL, AssistantTypes.CUSTOM]:
        asst_type = "personal" if assistant_create.type == AssistantTypes.PERSONAL else "custom"
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"You do not have permission to create a {asst_type} assistant",
        )
    grounding_instructions = assistant_create.grounding_instructions or GROUNDING_INSTRUCTIONS

    # include division name in assistant name to avoid similar names in openai platform.
    assistant_name = f"{division.name} {assistant_create.name}" if division else assistant_create.name
    assistant_id = await oai_hook.create_assistant(
        name=assistant_name,
        instructions=assistant_create.instructions,
        categories=assistant_create.categories,
        is_category_filtered=assistant_create.is_category_filtered,
        grounding_instructions=grounding_instructions,
    )

    # if division Admin is creating assistant, it should be assigned to the division where user is admin of
    division_id = user.division_id if user.role == RoleTypes.DIVISION_ADMIN else (division.id if division else None)

    assistant = Assistant(
        id=assistant_id,
        display_order=max_display_order + 1,
        grounding_instructions=grounding_instructions,
        division_id=division_id,
        **assistant_create.model_dump(exclude={"grounding_instructions", "division_id"}),
    )
    assistant.users.append(user)
    session.add(assistant)
    await session.commit()

    return assistant


async def adjust_display_order(assistant: Assistant, new_display_order: int, session: AsyncSession) -> None:
    """
    Adjust the display order of an assistant.

    Updates the display orders of assistants to accommodate the new display order of the given assistant.

    Args:
        assistant (Assistant): The assistant whose display order is being adjusted.
        new_display_order (int): The new display order value.
        session (AsyncSession): The SQLAlchemy asynchronous session.

    Returns:
        None: The operation is performed in the database.
    """
    if new_display_order < assistant.display_order:
        # move down
        await session.execute(
            update(Assistant)
            .where(Assistant.display_order >= new_display_order)
            .where(Assistant.display_order < assistant.display_order)
            .values(display_order=Assistant.display_order + 1)
        )
    elif new_display_order > assistant.display_order:
        # move up
        await session.execute(
            update(Assistant)
            .where(Assistant.display_order <= new_display_order)
            .where(Assistant.display_order > assistant.display_order)
            .values(display_order=Assistant.display_order - 1)
        )
    return None


@router.patch("/v2/{id}/", response_model=AssistantRead)
async def update_assistant(
    assistant_update: AssistantUpdate,
    assistant=Depends(get_assistant_or_404),
    session: AsyncSession = Depends(get_session),
) -> Assistant:
    """
    Update an existing assistant.

    Updates the details of an assistant based on the provided data. Adjusts the display order if necessary.

    Args:
        assistant_update (AssistantUpdate): A pydantic model with updated assistant details.
        assistant (Assistant): The assistant object to be updated, provided by the `get_assistant_or_404` dependency.
        session (AsyncSession): The SQLAlchemy asynchronous session, provided by the `get_session` dependency.

    Returns:
        AssistantRead: A pydantic model with the updated assistant details.

    Raises:
        HTTPException: If there is a database error, an HTTP 500 error is raised.
    """
    assistant_update_dict = assistant_update.model_dump(exclude_unset=True, exclude={"meta"})
    new_display_order = assistant_update_dict.get("display_order", assistant.display_order)

    if new_display_order != assistant.display_order:
        await adjust_display_order(assistant, new_display_order, session)

    is_private = assistant_update_dict.get("is_private", assistant.is_private)
    if not is_private:
        assistant.private_users = []

    grounding_instructions = assistant_update_dict.get("grounding_instructions", assistant.grounding_instructions)
    updated_categories = assistant_update_dict.get("categories", None)

    if updated_categories is not None:
        removed_categories = [category for category in assistant.categories if category not in updated_categories]
        if removed_categories:
            remove_categories_from_related_models.delay(assistant.id, removed_categories, updated_categories)

    for k, v in assistant_update_dict.items():
        setattr(assistant, k, v)

    instructions = assistant_update.instructions

    assistant_type = assistant_update_dict.get("type", assistant.type)

    if assistant.provider == AssistantProvider.OPENAI and assistant_type == AssistantTypes.GENERAL:
        await oai_hook.update_assistant(
            assistant.id,
            categories=assistant_update.categories,
            is_category_filtered=assistant_update.is_category_filtered,
            instructions=instructions if instructions is not None else assistant.instructions,
            grounding_instructions=grounding_instructions,
        )

    if assistant_update.meta:
        if assistant.meta is None:
            assistant.meta = {}
        assistant.meta = {**assistant.meta, **assistant_update.meta}

    session.add(assistant)
    await session.commit()
    await session.refresh(assistant)

    return assistant


@router.delete("/v2/{id}/", status_code=status.HTTP_204_NO_CONTENT)
async def delete_assistant(
    assistant=Depends(get_assistant_or_404),
    session: AsyncSession = Depends(get_session),
):
    """
    Delete an existing assistant.

    Removes an assistant from the database and performs any necessary cleanup.

    Args:
        assistant (Assistant): The assistant object to be deleted, provided by the `get_assistant_or_404` dependency.
        session (AsyncSession): The SQLAlchemy asynchronous session, provided by the `get_session` dependency.

    Returns:
        None: No content is returned upon successful deletion.

    Raises:
        HTTPException: If there is a database error, an HTTP 500 error is raised.
    """
    if assistant.provider == AssistantProvider.OPENAI:
        await oai_hook.delete_assistant(assistant.id)
    await session.delete(assistant)
    await session.commit()


async def get_assistant_access_or_404(id: str, session: AsyncSession = Depends(get_session)) -> AssistantAccess:
    query = select(AssistantAccess).where(AssistantAccess.id == id)
    result = await session.execute(query)
    assistant_access = result.scalar_one_or_none()

    if assistant_access is None:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Assistant access not found")

    return assistant_access


@router.get("/v2/{id}/access/", response_model=AssistantAccessRead)
async def get_assistant_access(
    assistant_access: AssistantAccess = Depends(get_assistant_access_or_404),
) -> AssistantAccess:
    """
    Retrieve access details for a specific assistant.

    Fetches the access details of an assistant based on its ID.

    Args:
        assistant_access (AssistantAccess): The assistant access object,
        provided by the `get_assistant_access_or_404` dependency.

    Returns:
        AssistantAccessRead: A pydantic model with the details of the assistant's access.

    Raises:
        HTTPException: If the assistant access is not found, an HTTP 404 error is raised.
    """

    return assistant_access


@router.post("/v2/{id}/access/", response_model=AssistantAccessRead, status_code=status.HTTP_201_CREATED)
async def create_assistant_access(
    assistant_id: str,
    assistant_access_create: AssistantAccessCreate,
    session: AsyncSession = Depends(get_session),
) -> AssistantAccess:
    """
    Create access details for a specific assistant.

    Adds a new access record for an assistant.

    Args:
        assistant_id (str): The ID of the assistant for which access is being created.
        assistant_access_create (AssistantAccessCreate): A pydantic model containing the details
        for the new access record.
        session (AsyncSession): The SQLAlchemy asynchronous session, provided by the `get_session` dependency.

    Returns:
        AssistantAccessRead: A pydantic model with the details of the created access record.

    Raises:
        HTTPException: If there is a database error, an HTTP 500 error is raised.
    """
    assistant_access = AssistantAccess(**assistant_access_create.model_dump())
    session.add(assistant_access)
    await session.commit()

    return assistant_access


@router.patch("/v2/{id}/access/", response_model=AssistantAccessRead)
async def update_assistant_access(
    assistant_access_update: AssistantAccessUpdate,
    assistant_access=Depends(get_assistant_access_or_404),
    session: AsyncSession = Depends(get_session),
) -> AssistantAccess:
    """
    Update access details for a specific assistant.

    Updates the access record of an assistant based on the provided data.

    Args:
        assistant_access_update (AssistantAccessUpdate): A pydantic model with updated access details.
        assistant_access (AssistantAccess): The assistant access object to be updated, provided by the
        `get_assistant_access_or_404` dependency.
        session (AsyncSession): The SQLAlchemy asynchronous session, provided by the `get_session` dependency.

    Returns:
        AssistantAccessRead: A pydantic model with the updated access details.

    Raises:
        HTTPException: If there is a database error, an HTTP 500 error is raised.
    """
    assistant_access_update_dict = assistant_access_update.model_dump(exclude_unset=True)
    for k, v in assistant_access_update_dict.items():
        setattr(assistant_access, k, v)

    session.add(assistant_access)
    await session.commit()

    return assistant_access


@router.delete("/v2/{id}/access/", status_code=status.HTTP_204_NO_CONTENT)
async def delete_assistant_access(
    assistant_access=Depends(get_assistant_access_or_404),
    session: AsyncSession = Depends(get_session),
):
    """
    Delete access details for a specific assistant.

    Removes an access record for an assistant.

    Args:
        assistant_access (AssistantAccess): The assistant access object to be deleted, provided by the
        `get_assistant_access_or_404` dependency.
        session (AsyncSession): The SQLAlchemy asynchronous session, provided by the `get_session` dependency.

    Returns:
        None: No content is returned upon successful deletion.

    Raises:
        HTTPException: If there is a database error, an HTTP 500 error is raised.
    """

    await session.delete(assistant_access)
    await session.commit()


@router.get("/", deprecated=True)
async def list_assistants_v1():
    """
    WARNING: Deprecated endpoint. Use /v2/ instead.
    List all assistants (v1 endpoint).

    Retrieves a static list of assistants from the settings configuration.

    Returns:
        list[AssistantRead]: A list of pydantic models representing the assistants.

    Raises:
        HTTPException: If there is a database error, an HTTP 500 error is raised.
    """
    return settings.ASSISTANTS


@router.get("/{id}/", deprecated=True)
async def get_assistant_v1(id: str):
    """
    WARNING: Deprecated endpoint. Use /v2/ instead.
    Retrieve a specific assistant by ID (v1 endpoint).

    Fetches the details of an assistant from the static settings based on its ID.

    Args:
        id (str): The ID of the assistant to retrieve.

    Returns:
        AssistantRead: A pydantic model with the details of the assistant.

    Raises:
        HTTPException: If the assistant is not found, an HTTP 404 error is raised.
    """
    assistant = next((a for a in settings.ASSISTANTS if a.id == id), None)
    if not assistant:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND)

    return assistant
