from auth.cognito import get_or_create_auth_user, is_admin_user, role_checker
from db.models import Division, DivisionCredentials, EncompassCredentials
from db.session import get_session
from fastapi import APIRouter, Depends, HTTPException, status
from schema.division import Division<PERSON><PERSON>, DivisionRead, DivisionUpdate
from schema.enums import RoleTypes
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

router = APIRouter()


async def get_division_or_404(id: str, session: AsyncSession = Depends(get_session)) -> Division:
    query = (
        select(Division)
        .where(Division.id == id)
        .options(
            selectinload(Division.users),
            selectinload(Division.assistants),
            selectinload(Division.credentials).selectinload(DivisionCredentials.encompass_credentials),
        )
    )
    result = await session.execute(query)
    division = result.scalar_one_or_none()

    if division is None:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Division not found")

    return division


@router.get("/", response_model=list[DivisionRead], dependencies=[Depends(is_admin_user)])
async def list_divisions(
    session: AsyncSession = Depends(get_session),
) -> list[Division]:
    query = select(Division)
    result = await session.execute(query)
    return result.scalars().all()


@router.get("/{id}/", response_model=DivisionRead, dependencies=[Depends(get_or_create_auth_user)])
async def get_division(division: Division = Depends(get_division_or_404)) -> Division:
    return division


@router.post(
    "/",
    response_model=DivisionRead,
    status_code=status.HTTP_201_CREATED,
    dependencies=[Depends(is_admin_user)],
)
async def create_division(
    division_create: DivisionCreate,
    session: AsyncSession = Depends(get_session),
) -> Division:
    # Check if division with the same domain already exists
    existing_division = await session.execute(select(Division).where(Division.domain == division_create.domain))
    if existing_division.scalar_one_or_none():
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Division with domain {division_create.domain} already exists.",
        )

    division = Division(**division_create.model_dump(exclude={"credentials"}))
    session.add(division)
    await session.flush()

    # Create DivisionCredentials
    credentials_data = division_create.credentials
    if credentials_data:
        division_credentials = DivisionCredentials(
            **credentials_data.model_dump(exclude={"encompass_credentials"}),
            division_id=division.id,
        )
        session.add(division_credentials)
        await session.flush()

        # Create EncompassCredentials if provided
        encompass_creds = credentials_data.encompass_credentials or []
        for enc in encompass_creds:
            encompass_cred = EncompassCredentials(
                **enc.model_dump(),
                division_credentials_id=division_credentials.id,
            )
            session.add(encompass_cred)

    await session.commit()
    await session.refresh(division)
    return division


@router.patch(
    "/{id}/",
    response_model=DivisionRead,
    dependencies=[Depends(role_checker([RoleTypes.ADMIN, RoleTypes.DIVISION_ADMIN]))],
)
async def update_division(
    division_update: DivisionUpdate,
    division: Division = Depends(get_division_or_404),
    session: AsyncSession = Depends(get_session),
) -> Division:
    update_data = division_update.model_dump(exclude_unset=True, exclude={"credentials"})
    for key, value in update_data.items():
        setattr(division, key, value)
    session.add(division)

    # Update credentials if provided
    if hasattr(division_update, "credentials") and division_update.credentials is not None:
        credentials_data = division_update.credentials
        if division.credentials:
            # Update existing credentials
            cred_update_data = credentials_data.model_dump(exclude_unset=True, exclude={"encompass_credentials"})
            for key, value in cred_update_data.items():
                setattr(division.credentials, key, value)
            session.add(division.credentials)

            if (
                hasattr(credentials_data, "encompass_credentials")
                and credentials_data.encompass_credentials is not None
            ):
                # remove old encompass credentials and add new ones
                for enc in list(division.credentials.encompass_credentials):
                    await session.delete(enc)
                await session.flush()  # Flush deletions before adding new ones
                for enc in credentials_data.encompass_credentials:
                    encompass_cred = EncompassCredentials(
                        **enc.model_dump(exclude_unset=True),
                        division_credentials_id=division.credentials.id,
                    )
                    session.add(encompass_cred)
        else:
            # Create new credentials if not exist
            division_credentials = DivisionCredentials(
                **credentials_data.model_dump(exclude={"encompass_credentials"}),
                division_id=division.id,
            )
            session.add(division_credentials)
            await session.flush()
            if credentials_data.encompass_credentials:
                for enc in credentials_data.encompass_credentials:
                    encompass_cred = EncompassCredentials(
                        **enc.model_dump(exclude_unset=True),
                        division_credentials_id=division_credentials.id,
                    )
                    session.add(encompass_cred)

    await session.commit()
    await session.refresh(division)
    return division


@router.delete(
    "/{id}/",
    status_code=status.HTTP_204_NO_CONTENT,
    dependencies=[Depends(is_admin_user)],
)
async def delete_division(
    division: Division = Depends(get_division_or_404),
    session: AsyncSession = Depends(get_session),
):
    await session.delete(division)
    await session.commit()
