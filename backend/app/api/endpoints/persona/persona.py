from auth.cognito import get_or_create_auth_user
from db.models import Persona, User
from db.session import get_session
from fastapi import APIRouter, Depends, HTTPException, status
from schema.persona.persona import Persona<PERSON><PERSON>, PersonaRead, PersonaUpdate
from sqlalchemy import and_, or_, select
from sqlalchemy.ext.asyncio import AsyncSession

router = APIRouter()


async def get_persona_or_404(
    persona_id: str,
    user: User = Depends(get_or_create_auth_user),
    session: AsyncSession = Depends(get_session),
) -> Persona:
    query = select(Persona).where(
        and_(Persona.id == persona_id, or_(Persona.owner_id == user.id, Persona.owner_id.is_(None)))
    )
    result = await session.execute(query)
    persona = result.scalar_one_or_none()

    if not persona:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Persona not found")

    return persona


@router.post(
    "/",
    response_model=PersonaRead,
    status_code=status.HTTP_201_CREATED,
)
async def create_custom_persona(
    persona_create: PersonaCreate,
    session: AsyncSession = Depends(get_session),
    user: User = Depends(get_or_create_auth_user),
) -> Persona:
    """
    Create a new persona record (trait).
    """
    persona = Persona(name=persona_create.name, description=persona_create.description, owner_id=user.id)
    session.add(persona)
    await session.commit()
    return persona


@router.get("/{persona_id}/", response_model=PersonaRead)
async def get_persona(
    persona: Persona = Depends(get_persona_or_404),
) -> Persona:
    """
    Retrieve a specific persona record (trait).
    """
    return persona


@router.get("/", response_model=list[PersonaRead])
async def list_personas(
    user: User = Depends(get_or_create_auth_user),
    session: AsyncSession = Depends(get_session),
) -> list[Persona]:
    """
    List all persona records (traits) for the current user.

    This includes both default personas and custom personas owned by the user.
    """
    query = select(Persona).where(or_(Persona.owner_id == user.id, Persona.owner_id.is_(None)))
    result = await session.execute(query)
    return result.scalars().all()


@router.patch(
    "/{persona_id}/",
    response_model=PersonaRead,
    dependencies=[Depends(get_or_create_auth_user)],
    status_code=status.HTTP_200_OK,
)
async def update_custom_persona(
    persona_update: PersonaUpdate,
    session: AsyncSession = Depends(get_session),
    persona: Persona = Depends(get_persona_or_404),
) -> Persona:
    """
    Update a persona record (trait).

    If the persona is predefined (default; owner_id is NULL), do not allow update.

    If the persona is already a custom persona owned by the user, it is updated directly.
    """
    if persona.owner_id is None:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Cannot update default persona")

    for field, value in persona_update.model_dump(exclude_unset=True).items():
        setattr(persona, field, value)

    session.add(persona)
    await session.commit()
    await session.refresh(persona)
    return persona


@router.delete(
    "/{persona_id}/",
    status_code=status.HTTP_204_NO_CONTENT,
    dependencies=[Depends(get_or_create_auth_user)],
)
async def delete_custom_persona(
    session: AsyncSession = Depends(get_session),
    persona: Persona = Depends(get_persona_or_404),
):
    """
    Delete a persona record (trait).

    If the persona is predefined (default; owner_id is NULL), do not allow deletion.
    If the persona is already a custom persona owned by the user, it is deleted directly.
    """
    if persona.owner_id is None:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Cannot delete default persona")

    await session.delete(persona)
    await session.commit()
