from collections.abc import Sequence
from datetime import datetime, timezone

from api.endpoints.assistant import get_assistant_or_404
from api.utils.pagination import page_number_pagination
from auth.cognito import check_user_permissions
from auth.cognito_sso import get_or_create_auth_user
from config import settings
from db.models import CustomLoanOfficerMapping, TotalExpertToken, User
from db.session import get_session
from fastapi import APIRouter, Depends, HTTPException, Query, status
from pydantic import EmailStr
from schema.custom_lo_mapping import (
    CustomLoanOfficerMappingCreate,
    CustomLoanOfficerMappingList,
    CustomLoanOfficerMappingRead,
    CustomLoanOfficerMappingUpdate,
)
from schema.enums import LoanOfficerMappingType, RoleTypes
from schema.total_expert import ListImpersonateUserResponse
from schema.user import UserList
from sqlalchemy import and_, func, select
from sqlalchemy.ext.asyncio import AsyncSession
from tools.utils.total_expert import total_expert_utils
from tools.utils.total_expert_token import TotalExpertTokenManager

from .user import list_users

router = APIRouter()


async def check_multi_impersonation(
    assistant_id,
    session: AsyncSession,
    email: str,
    mapping_type: LoanOfficerMappingType,
    update: bool = False,
    impersonation_id: str = "",
):
    # Defining Condition for Allowing Multiple Impersonation

    allow_multi_impersonation = settings.ENABLE_TOTAL_EXPERT_OAUTH and mapping_type in [
        LoanOfficerMappingType.TOTALEXPERT_GENERAL,
        LoanOfficerMappingType.TOTALEXPERT_ADMIN,
    ]

    # check if the user already has impersonation with same details
    if not allow_multi_impersonation:
        # Combination of assistant_id,email and mapping_type must be unique in case of single impersonation
        query = select(CustomLoanOfficerMapping).where(
            and_(
                CustomLoanOfficerMapping.assistant_id == assistant_id,
                func.lower(CustomLoanOfficerMapping.email) == email.lower(),
                CustomLoanOfficerMapping.mapping_type == mapping_type,
            )
        )
        result = await session.execute(query)
        map_res = result.scalars().all()
        if update:
            multi_res = len(map_res) > 1
            single_not_same = len(map_res) == 1 and map_res[0].id != impersonation_id
            if multi_res or single_not_same:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=(
                        "You've already set up impersonation for this account. To add a new one, please delete the"
                        " current impersonation first"
                    ),
                )
        else:
            if map_res:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=(
                        "You've already set up impersonation for this account. To add a new one, please delete the"
                        " current impersonation first"
                    ),
                )


async def get_assistant_custom_lo_mapping_or_404(
    id: str,
    impersonation_id: str,
    session: AsyncSession = Depends(get_session),
) -> CustomLoanOfficerMapping:
    query = select(CustomLoanOfficerMapping).where(
        CustomLoanOfficerMapping.id == impersonation_id, CustomLoanOfficerMapping.assistant_id == id
    )
    result = await session.execute(query)
    mapping = result.scalar_one_or_none()

    if not mapping:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Custom loan officer mapping not found")

    return mapping


async def te_oauth_enable_validation(
    lo_email: EmailStr,
    mapping_type: LoanOfficerMappingType,
    assistant=Depends(get_assistant_or_404),
    session: AsyncSession = Depends(get_session),
):
    valid_mapping_type = mapping_type in [
        LoanOfficerMappingType.TOTALEXPERT_GENERAL,
        LoanOfficerMappingType.TOTALEXPERT_ADMIN,
    ]
    if settings.ENABLE_TOTAL_EXPERT_OAUTH and valid_mapping_type:
        query = select(User.id).where(User.email == lo_email)
        result = await session.execute(query)
        user_id = result.scalar_one_or_none()

        if not user_id:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Impersonate user email does not exist.",
            )
        query = select(TotalExpertToken).where(
            TotalExpertToken.user_id == user_id, TotalExpertToken.assistant_id == assistant.id
        )
        result = await session.execute(query)
        token_record = result.scalar_one_or_none()
        if not token_record:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Impersonate user token does not exist.",
            )
        else:
            now = datetime.now(timezone.utc).replace(tzinfo=None)
            if token_record.refresh_token_expires_at < now:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Impersonate user token expired.",
                )


@router.get(
    "/v2/{id}/impersonation/users/",
    response_model=ListImpersonateUserResponse,
    dependencies=[Depends(check_user_permissions)],
)
async def list_custom_lo_mappings_for_current_user(
    user: User = Depends(get_or_create_auth_user),
    assistant=Depends(get_assistant_or_404),
    pagination: tuple[int, int] = Depends(page_number_pagination),
    session: AsyncSession = Depends(get_session),
    mapping_type: LoanOfficerMappingType = Query(
        LoanOfficerMappingType.TOTALEXPERT_GENERAL, description="Loan officer mapping type"
    ),
):
    """List custom loan officer mappings with optional filtering and pagination.

    This endpoint returns a paginated list of custom loan officer mappings. It can filter results
    based on the loan officer mapping type. It is accessible only to admin users.

    Args:
        pagination (tuple[int, int]): Pagination parameters including `page` and `size`.
        session (AsyncSession): The database session, automatically provided by the `get_session` dependency.
        mapping_type (LoanOfficerMappingType, optional): Filter results by the loan officer mapping type.

    Returns:
        CustomLoanOfficerMappingList: A paginated list of custom loan officer mappings with total count.

    Raises:
        HTTPException: If there is an error retrieving the mappings or if the user is not authorized.
    """
    page, size = pagination
    select_query = (
        select(User, CustomLoanOfficerMapping.active)
        .join(CustomLoanOfficerMapping, func.lower(CustomLoanOfficerMapping.lo_email) == func.lower(User.email))
        .where(CustomLoanOfficerMapping.assistant_id == assistant.id, CustomLoanOfficerMapping.email == user.email)
    )
    if mapping_type:
        select_query = select_query.where(CustomLoanOfficerMapping.mapping_type == mapping_type)

    total = await session.execute(select(func.count()).select_from(select_query))
    count = total.scalar()

    select_query = select_query.offset((page - 1) * size).limit(size)

    result = await session.execute(select_query)
    final_result = result.mappings().all()
    data = []
    user_ids = [res.get("User").id for res in final_result]
    has_active = False

    # Fetch all tokens for custom loan officer users and the assistant
    tokens_query = select(TotalExpertToken.user_id).where(
        TotalExpertToken.user_id.in_(user_ids),
        TotalExpertToken.assistant_id == assistant.id,
    )
    tokens_result = await session.execute(tokens_query)
    user_ids_with_token = set(tokens_result.scalars().all())

    for res in final_result:
        user_obj = res.get("User")
        is_user_active = res.get("active")
        entry = {"user": user_obj, "active": is_user_active, "oauth_exists": user_obj.id in user_ids_with_token}

        if is_user_active:
            has_active = True
        data.append(entry)

    # add current user if they are logged in to TE
    query = select(TotalExpertToken).where(
        TotalExpertToken.user_id == user.id,
        TotalExpertToken.assistant_id == assistant.id,
    )
    result = await session.execute(query)
    user_token = result.scalar()
    if user_token:
        current_user_entry = {"user": user, "active": not has_active, "oauth_exists": True}
        data = [current_user_entry] + data
        count_out = count + 1
    else:
        token_manager = TotalExpertTokenManager(session)
        await token_manager.initialize()
        auth_url = await token_manager.generate_auth_url(assistant.id)

        # send auth url and set current user inactive as they are nog logged in to the TE
        # and do not have a token
        current_user_entry = {"user": user, "active": False, "auth_url": auth_url, "oauth_exists": False}
        # But since we need atleast one active impersonation, so if impersonation exists,
        # use first impersonation as active
        if not has_active:
            if data:
                impersonated_user = data[0]
                impersonated_user["active"] = True
                await total_expert_utils.handle_multi_impersonation(user, impersonated_user["user"].id, assistant.id)
            else:
                return {"count": 0, "data": []}
        data = [current_user_entry] + data
        count_out = count + 1

    return {"count": count_out, "data": data}


@router.get(
    "/v2/{id}/impersonation/{impersonation_id}/",
    response_model=CustomLoanOfficerMappingRead,
    dependencies=[Depends(check_user_permissions), Depends(get_assistant_or_404)],
)
async def get_custom_lo_mapping(
    custom_lo_mapping: CustomLoanOfficerMapping = Depends(get_assistant_custom_lo_mapping_or_404),
) -> CustomLoanOfficerMappingRead:
    """
    Retrieve a specific custom loan officer mapping by ID.

    This endpoint fetches a custom loan officer mapping record based on the provided ID.
    It is accessible only to admin users.

    Args:
        custom_lo_mapping (CustomLoanOfficerMapping): The custom loan officer mapping object,
        automatically provided by the `get_custom_lo_mapping_or_404` dependency.

    Returns:
        CustomLoanOfficerMappingRead: The requested custom loan officer mapping.

    Raises:
        HTTPException: If the custom loan officer mapping with the specified ID does not exist or
        if the user is not authorized.
    """
    return custom_lo_mapping


@router.post(
    "/v2/{id}/impersonation/", response_model=CustomLoanOfficerMappingRead, status_code=status.HTTP_201_CREATED
)
async def create_custom_lo_mapping(
    custom_lo_mapping_create: CustomLoanOfficerMappingCreate,
    assistant=Depends(get_assistant_or_404),
    session: AsyncSession = Depends(get_session),
    user=Depends(check_user_permissions),
) -> CustomLoanOfficerMapping:
    """
    Create a new custom loan officer mapping.

    This endpoint creates a new custom loan officer mapping based on the provided data.
    It is accessible only to admin users.

    Args:
        custom_lo_mapping_create (CustomLoanOfficerMappingCreate): The data for creating
        the new custom loan officer mapping.
        session (AsyncSession): The database session, automatically provided by the `get_session` dependency.

    Returns:
        CustomLoanOfficerMappingRead: The newly created custom loan officer mapping.

    Raises:
        HTTPException: If there is an error creating the mapping or if the user is not authorized.
    """

    await te_oauth_enable_validation(
        custom_lo_mapping_create.lo_email, custom_lo_mapping_create.mapping_type, assistant, session
    )

    user_info = {
        "user_id": str(user.id),
        "user_name": user.username,
        "email": user.email,
        "name": user.name,
    }
    await check_multi_impersonation(
        assistant_id=assistant.id,
        session=session,
        email=custom_lo_mapping_create.email,
        mapping_type=custom_lo_mapping_create.mapping_type,
    )
    query = select(CustomLoanOfficerMapping).where(
        and_(
            CustomLoanOfficerMapping.assistant_id == assistant.id,
            func.lower(CustomLoanOfficerMapping.email) == custom_lo_mapping_create.email.lower(),
            func.lower(CustomLoanOfficerMapping.lo_email) == custom_lo_mapping_create.lo_email.lower(),
            CustomLoanOfficerMapping.mapping_type == custom_lo_mapping_create.mapping_type,
        )
    )
    result = await session.execute(query)
    existing_mapping = result.scalars().all()
    if existing_mapping:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="A mapping with the same details already exists.",
        )

    if custom_lo_mapping_create.meta:
        custom_lo_mapping_create.meta = {
            **custom_lo_mapping_create.meta,
            "user_info": {"created_by": user_info, "updated_by": user_info},
        }
    else:
        custom_lo_mapping_create.meta = {"user_info": {"created_by": user_info, "updated_by": user_info}}

    custom_lo_mapping_create.email = custom_lo_mapping_create.email.lower()
    custom_lo_mapping_create.lo_email = custom_lo_mapping_create.lo_email.lower()
    custom_lo_mapping = CustomLoanOfficerMapping(assistant_id=assistant.id, **custom_lo_mapping_create.model_dump())
    session.add(custom_lo_mapping)
    await session.commit()
    return custom_lo_mapping


@router.get(
    "/v2/{id}/impersonation/",
    response_model=CustomLoanOfficerMappingList,
    dependencies=[Depends(check_user_permissions)],
)
async def list_custom_lo_mappings(
    assistant=Depends(get_assistant_or_404),
    pagination: tuple[int, int] = Depends(page_number_pagination),
    session: AsyncSession = Depends(get_session),
    mapping_type: LoanOfficerMappingType = Query(None, description="Loan officer mapping type"),
) -> CustomLoanOfficerMappingList:
    """
    List custom loan officer mappings with optional filtering and pagination.

    This endpoint returns a paginated list of custom loan officer mappings. It can filter results
    based on the loan officer mapping type. It is accessible only to admin users.

    Args:
        pagination (tuple[int, int]): Pagination parameters including `page` and `size`.
        session (AsyncSession): The database session, automatically provided by the `get_session` dependency.
        mapping_type (LoanOfficerMappingType, optional): Filter results by the loan officer mapping type.

    Returns:
        CustomLoanOfficerMappingList: A paginated list of custom loan officer mappings with total count.

    Raises:
        HTTPException: If there is an error retrieving the mappings or if the user is not authorized.
    """
    page, size = pagination
    select_query = select(CustomLoanOfficerMapping).where(CustomLoanOfficerMapping.assistant_id == assistant.id)

    if mapping_type:
        select_query = select_query.where(CustomLoanOfficerMapping.mapping_type == mapping_type)

    total = await session.execute(select(func.count()).select_from(select_query))
    count = total.scalar()

    select_query = select_query.offset((page - 1) * size).limit(size)

    result = await session.execute(select_query)
    return {
        "count": count,
        "data": result.scalars().all(),
    }


@router.patch("/v2/{id}/impersonation/{impersonation_id}/", response_model=CustomLoanOfficerMappingRead)
async def update_custom_lo_mapping(
    custom_lo_mapping_update: CustomLoanOfficerMappingUpdate,
    assistant=Depends(get_assistant_or_404),
    custom_lo_mapping: CustomLoanOfficerMapping = Depends(get_assistant_custom_lo_mapping_or_404),
    user=Depends(check_user_permissions),
    session: AsyncSession = Depends(get_session),
) -> CustomLoanOfficerMappingRead:
    """
    Update an existing custom loan officer mapping.

    This endpoint updates a custom loan officer mapping based on the provided data. It is accessible
    only to admin users.

    Args:
        custom_lo_mapping_update (CustomLoanOfficerMappingUpdate): The data for updating the custom
        loan officer mapping.
        custom_lo_mapping (CustomLoanOfficerMapping): The existing custom loan officer mapping object,
        automatically provided by the `get_custom_lo_mapping_or_404` dependency.
        session (AsyncSession): The database session, automatically provided by the `get_session` dependency.

    Returns:
        CustomLoanOfficerMappingRead: The updated custom loan officer mapping.

    Raises:
        HTTPException: If the mapping to be updated does not exist or if the user is not authorized.
    """

    await te_oauth_enable_validation(
        custom_lo_mapping_update.lo_email, custom_lo_mapping_update.mapping_type, assistant, session
    )
    await check_multi_impersonation(
        assistant_id=assistant.id,
        session=session,
        email=custom_lo_mapping_update.email,
        mapping_type=custom_lo_mapping_update.mapping_type,
        update=True,
        impersonation_id=custom_lo_mapping.id,
    )
    user_info = {
        "user_id": str(user.id),
        "user_name": user.username,
        "email": user.email,
        "name": user.name,
    }

    query = select(CustomLoanOfficerMapping).where(
        and_(
            CustomLoanOfficerMapping.assistant_id == assistant.id,
            CustomLoanOfficerMapping.email == custom_lo_mapping_update.email,
            CustomLoanOfficerMapping.lo_email == custom_lo_mapping_update.lo_email,
            CustomLoanOfficerMapping.mapping_type == custom_lo_mapping_update.mapping_type,
        )
    )
    result = await session.execute(query)
    existing_mapping = result.scalars().all()
    if existing_mapping:
        # Allowing the same  impersonation to be updated with same data.
        if not (len(existing_mapping) == 1 and existing_mapping[0].id == custom_lo_mapping.id):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="A mapping with the same details already exists.",
            )
    custom_lo_mapping_update_dict = custom_lo_mapping_update.model_dump(exclude_unset=True)

    for k, v in custom_lo_mapping_update_dict.items():
        if k in ["email", "lo_email"]:
            v = v.lower()
        setattr(custom_lo_mapping, k, v)

    # check if the user already has impersonation with same details

    if custom_lo_mapping.meta:
        custom_lo_mapping.meta = {
            **custom_lo_mapping.meta,
            "user_info": {**custom_lo_mapping.meta["user_info"], "updated_by": user_info},
        }
    else:
        custom_lo_mapping.meta = {"user_info": {"updated_by": user_info}}

    if custom_lo_mapping.meta:
        custom_lo_mapping.meta.update(custom_lo_mapping.meta)

    session.add(custom_lo_mapping)
    await session.commit()
    await session.refresh(custom_lo_mapping)

    return custom_lo_mapping


@router.delete(
    "/v2/{id}/impersonation/{impersonation_id}/",
    status_code=status.HTTP_204_NO_CONTENT,
    dependencies=[Depends(check_user_permissions), Depends(get_assistant_or_404)],
)
async def delete_custom_lo_mapping(
    custom_lo_mapping: CustomLoanOfficerMapping = Depends(get_assistant_custom_lo_mapping_or_404),
    session: AsyncSession = Depends(get_session),
) -> None:
    await session.delete(custom_lo_mapping)
    await session.commit()


# Impersonation user list
@router.get("/v2/{id}/impersonation/users/list/", response_model=UserList)
async def get_all_users_for_impersonation(
    logged_in_user: User = Depends(check_user_permissions),
    _=Depends(get_assistant_or_404),
    pagination: tuple[int, int] = Depends(page_number_pagination),
    session: AsyncSession = Depends(get_session),
    search: str = Query(None, description="Filter by name or email"),
    division_id: str = Query(None, description="Filter by division id"),
) -> Sequence[User]:
    """
    Retrieve users for impersonation. Returns Admins if admin is requesting and
    even if admin is not in the same division.
    """
    if logged_in_user.role == RoleTypes.ASSISTANT_ADMIN and not search:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, detail="You cannot list all users. Please user search feature!"
        )
    return await list_users(
        logged_in_user=logged_in_user,
        pagination=pagination,
        session=session,
        search=search,
        division_id=division_id,
        impersonation=True,
    )
