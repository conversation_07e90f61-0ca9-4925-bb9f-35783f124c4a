import random
import uuid
from datetime import date, datetime, timedelta
from io import BytesIO

import numpy as np
import pandas as pd
from api.utils.utils import get_extension_and_mime_type, validate_assistant
from auth.cognito import get_or_create_auth_user
from db.models import User
from db.session import get_session
from docx import Document
from docx.shared import Inches
from fastapi import APIRouter, Depends, File, HTTPException, Query, UploadFile, status
from hooks.s3 import get_s3_hook
from loguru import logger
from schema.encompass import FindLendingOpportunity
from sqlalchemy.ext.asyncio import AsyncSession

router = APIRouter()


@router.post("/upload_file", dependencies=[Depends(get_or_create_auth_user)])
async def upload_file(
    file: UploadFile = File(...),
    file_type: str = Query(..., description="Type of the file"),
    assistant_id: str = Query(..., description="ID of the assistant"),
    session: AsyncSession = Depends(get_session),
    user: User = Depends(get_or_create_auth_user),
):
    if file_type == "excel":
        ALLOWED_EXTENSIONS = {"xlsx", "xls"}
        ALLOWED_MIME_TYPES = {
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            "application/vnd.ms-excel",
        }
    elif file_type == "image":
        ALLOWED_EXTENSIONS = {"jpg", "jpeg", "png"}
        ALLOWED_MIME_TYPES = {
            "image/jpeg",
            "image/png",
        }
    else:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid file type")

    MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB

    await validate_assistant(assistant_id, session)

    # Read file content once
    file_content = await file.read()

    # Check file size
    if len(file_content) > MAX_FILE_SIZE:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"File size exceeds maximum allowed size of {MAX_FILE_SIZE/1024/1024}MB",
        )

    extension, mime_type = get_extension_and_mime_type(file.filename, file_content)

    if extension not in ALLOWED_EXTENSIONS or mime_type not in ALLOWED_MIME_TYPES:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Only JPG, JPEG, PNG, XLSX and XLS files are allowed.",
        )

    try:
        # Generate unique filename
        unique_filename = f"{uuid.uuid4()}.{extension}"
        object_key = f"/{assistant_id}/{user.id}/{unique_filename}"

        # Upload to S3
        s3_hook = await get_s3_hook(assistant_id)
        file_url = s3_hook.upload_file(object_key, file_content, mime_type)
        return {"file_url": file_url}
    except Exception as e:
        logger.error(f"Error while uploading file: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e),
        )


@router.post("/find_lending_opportunity", response_model=FindLendingOpportunity)
async def find_lending_opportunity(
    request: FindLendingOpportunity,
    user: User = Depends(get_or_create_auth_user),
    assistant_id: str = Query(..., description="ID of the assistant"),
    session: AsyncSession = Depends(get_session),
):
    await validate_assistant(assistant_id, session)
    try:
        # Read input data from frontend file
        df_input = pd.read_excel(request.file_url)

        required_columns = {
            "EPO Date": "EPO expiration calculations",
            "Fund Released Date": "Time since loan calculations",
            "Appraised value": "Current value and renovation calculations",
            "Loan Amount": "Payment and equity calculations",
            "MoPymtPI": "Rate/term savings calculations",
            "First Name": "First Name",
            "Last Name": "Last Name",
        }

        # Check for missing columns
        missing_columns = [col for col in required_columns.keys() if col not in df_input.columns]

        if missing_columns:
            missing_fields_message = "The following required fields are missing from the input file:\n" + "\n".join(
                [f"- {col} (needed for {required_columns[col]})" for col in missing_columns]
            )
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=missing_fields_message)

        def parse_or_random_future(x):
            if pd.isna(x):
                future_days = random.randint(1, 365)
                return date.today() + timedelta(days=future_days)
            else:
                return datetime.strptime(x, "%m/%d/%Y").date()

        def calculate_monthly_payment(principal, annual_rate, num_payments):
            if pd.isna(principal) or principal <= 0:
                return np.nan
            monthly_rate = annual_rate / 12
            r = monthly_rate
            n = num_payments
            return principal * r * (1 + r) ** n / ((1 + r) ** n - 1)

        # Process Data
        output = df_input.copy()
        output["Parsed EPO Date"] = [parse_or_random_future(x) for x in output["EPO Date"]]
        output["EPO expired?"] = output["Parsed EPO Date"].apply(lambda x: "Yes" if x < date.today() else "No")

        output["Parsed Fund Released Date"] = pd.to_datetime(output["Fund Released Date"])
        output["Time Since Loan"] = datetime.today() - output["Parsed Fund Released Date"]
        output["Time Since Loan"] = (output["Time Since Loan"].dt.days / 365).round(2)

        output["Estimated Current Value(Int)"] = output["Appraised value"] * np.power(
            1 + 0.08, output["Time Since Loan"]
        )
        output["Estimated Current Value"] = output["Estimated Current Value(Int)"].apply(lambda x: f"${int(x):,}")

        output["New MoPymtPI (7%)"] = output["Loan Amount"].apply(
            lambda x: calculate_monthly_payment(x, annual_rate=0.07, num_payments=30 * 12)
        )
        output["Save $150 on Rate/Term?"] = np.where(
            (output["New MoPymtPI (7%)"] < output["MoPymtPI"]) | (output["New MoPymtPI (7%)"] == 150), "Yes", ""
        )
        output["Save $250 on Rate/Term?"] = np.where(
            (output["New MoPymtPI (7%)"] < output["MoPymtPI"]) | (output["New MoPymtPI (7%)"] == 250), "Yes", ""
        )
        output["Could pull $20k at 80%"] = np.where(
            ((output["Loan Amount"] + 25000) / output["Estimated Current Value(Int)"]) <= 0.8, "Yes", "No"
        )
        output["Could pull $30k at 80%"] = np.where(
            ((output["Loan Amount"] + 35000) / output["Estimated Current Value(Int)"]) <= 0.8, "Yes", "No"
        )
        output["Could pull $40k at 80%"] = np.where(
            ((output["Loan Amount"] + 45000) / output["Estimated Current Value(Int)"]) <= 0.8, "Yes", "No"
        )
        output["Could pull for Renovation(Int)"] = (output["Estimated Current Value(Int)"] * 0.95 - 5000) - output[
            "Loan Amount"
        ]
        output["Could pull for Renovation"] = output["Could pull for Renovation(Int)"].apply(lambda x: f"${int(x):,}")

        output = output.sort_values(
            by=[
                "EPO expired?",
                "Could pull $40k at 80%",
                "Could pull $30k at 80%",
                "Could pull $20k at 80%",
                "Save $250 on Rate/Term?",
                "Save $150 on Rate/Term?",
            ],
            ascending=[False, False, False, False, False, False],
        )

        all_cols = list(df_input.columns)
        all_cols.extend(
            [
                "EPO expired?",
                "Time Since Loan",
                "Estimated Current Value",
                "Save $150 on Rate/Term?",
                "Save $250 on Rate/Term?",
                "Could pull $20k at 80%",
                "Could pull $30k at 80%",
                "Could pull $40k at 80%",
                "Could pull for Renovation",
            ]
        )

        filtered_cols = [
            col
            for col in all_cols
            if col
            not in [
                "State",
                "Zip Code",
                "Purpose of Loan",
                "Loan Type",
                "Estimated Closing Date",
                "Borrower Email",
                "Borrower Phone",
                "Co-Borrower Phone",
                "Appraised value",
                "Down Payment Amount",
                "Buyers Agent Name",
                "Buyers Agent Phone",
                "Buyers Agent Email",
                "Current Milestone",
                "Lock Date",
                "Rate Lock Expiry Date",
                "Investor",
                "Servicing Company Name",
                "MoPymtPI",
                "Subject Property Occupancy Status",
                "Occupancy Type",
                "Loan Program",
                "Credit Score",
                "LTV",
                "Lien Position",
                "Fund Released Date",
                "Brokered or Banked Loan",
                "Address",
                "City",
                "Loan Officer",
                "Loan Officer Email",
                "Payroll Lead Type",
                "Last day to sign CD",
                "Application Date",
                "CD ordered Date",
                "Earliest Possible Closing Date",
                "Wire Ordered",
                "Fed Ref #",
                "EPO Date",
                "CD Sent",
                "Committed Investor",
                "Appraisal Expiration Date",
                "Loan selected for QC",
                "ITP",
                "Cash to Close",
                "Total PITI",
                "Loan Status",
                "Broker Name",
                "Sub-Servicer Loan Number",
                "Servicing Type",
                "Purchase Advice Date",
                "Loan First payment Date",
                "Customer Service Name",
                "Customer Service Phone Number",
                "Payment Due Date",
                "First Payment Due Investor",
                "Closer Name",
                "Referral Source",
                "Buyer's Agent",
                "Loan Processor",
                "Underwriter",
                "Loan Officer Login ID",
            ]
        ]

        # Write DataFrame to Excel buffer with multiple sheets
        excel_buffer = BytesIO()
        with pd.ExcelWriter(excel_buffer, engine="openpyxl") as writer:
            output[filtered_cols].to_excel(writer, sheet_name="Filtered Data", index=False)
            output[all_cols].to_excel(writer, sheet_name="All Data", index=False)
        excel_buffer.seek(0)

        # Upload Excel file to S3 and get URL
        s3_hook = await get_s3_hook(assistant_id)
        excel_url = s3_hook.upload_file("lending_opportunities/lending_opportunities.xlsx", excel_buffer)

        return {"file_url": excel_url}

    except Exception as e:
        logger.error(f"Error while finding lending opportunity: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e),
        )


@router.post("/create_call_list", response_model=FindLendingOpportunity)
async def create_call_list(
    request: FindLendingOpportunity,
    user: User = Depends(get_or_create_auth_user),
    assistant_id: str = Query(..., description="ID of the assistant"),
    session: AsyncSession = Depends(get_session),
):
    """
    Generate a Word document call list from an Excel file.

    Parameters:
    - excel_file: File object from frontend upload (BytesIO or file-like object)

    Returns:
    - BytesIO object containing the generated Word document
    """
    await validate_assistant(assistant_id, session)
    try:
        # Required columns that must be present in the Excel file
        required_columns = [
            "First Name",
            "Last Name",
            "Loan Officer",
            "Borrower Phone",
            "Co-Borrower Phone",
            "Borrower Email",
            "Could pull $40k at 80%",
            "Could pull $30k at 80%",
            "Could pull $20k at 80%",
            "Could pull for Renovation",
            "Save $250 on Rate/Term?",
            "Save $150 on Rate/Term?",
        ]

        # Load Excel data from uploaded file
        df = pd.read_excel(request.file_url, sheet_name="All Data")

        # Check for missing required columns
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Missing required columns in Excel file: {', '.join(missing_columns)}",
            )

        # Word document setup
        doc = Document()
        sections = doc.sections
        for section in sections:
            section.top_margin = Inches(0.25)
            section.bottom_margin = Inches(0.25)
            section.left_margin = Inches(0.25)
            section.right_margin = Inches(0.25)

        # Helper: Add Table Section
        def add_list_section(title, filtered_df, extra_col=None, extra_col_title=None):
            doc.add_heading(title, level=1)
            for officer in filtered_df["Loan Officer"].dropna().unique():
                doc.add_heading(officer, level=2)
                officer_df = filtered_df[filtered_df["Loan Officer"] == officer]
                cols = ["Name", "Phone Number (B)", "Phone Number (Co-B)", "Email"]
                if extra_col:
                    cols.append(extra_col_title)

                table = doc.add_table(rows=1, cols=len(cols))
                hdr_cells = table.rows[0].cells
                for i, col in enumerate(cols):
                    hdr_cells[i].text = col

                for _, row in officer_df.iterrows():
                    r = table.add_row().cells
                    r[0].text = f"{row['First Name']} {row['Last Name']}"
                    r[1].text = str(row["Borrower Phone"])
                    r[2].text = str(row["Co-Borrower Phone"])
                    r[3].text = str(row["Borrower Email"])
                    if extra_col:
                        r[4].text = f"${row[extra_col]:,.2f}"

                doc.add_paragraph()

        # ---- Filter & write groups ----
        def process_group(df, condition, group_name, included, extra_col=None, extra_title=None):
            filt_df = df[condition].copy()
            filt_df["Name"] = filt_df["First Name"] + " " + filt_df["Last Name"]
            # Only exclude names for debt consolidation groups
            if "debt consolidation" in group_name.lower():
                filt_df = filt_df[~filt_df["Name"].isin(included)]
                included.update(filt_df["Name"].tolist())
            add_list_section(group_name, filt_df, extra_col=extra_col, extra_col_title=extra_title)

        # Track who is already in higher-value debt consolidation groups
        included_names = set()

        # Debt Consolidation Groups
        process_group(
            df, df["Could pull $40k at 80%"] == "Yes", "Could do $40,000 of debt consolidation", included_names
        )

        process_group(
            df, df["Could pull $30k at 80%"] == "Yes", "Could do $30,000 of debt consolidation", included_names
        )

        process_group(
            df, df["Could pull $20k at 80%"] == "Yes", "Could do $20,000 of debt consolidation", included_names
        )

        # Renovation Group
        # Convert currency string to float, removing $ and , characters
        df["Renovation_Value"] = df["Could pull for Renovation"].replace(r"[\$,]", "", regex=True).astype(float)
        process_group(
            df,
            df["Renovation_Value"] > 25000,
            "Could do a Lot of Renovation",
            set(),
            extra_col="Renovation_Value",
            extra_title="Could Renovate",
        )

        # Rate/Term Groups
        process_group(df, df["Save $250 on Rate/Term?"] == "Yes", "Could save $250 on Rate/Term", set())

        process_group(df, df["Save $150 on Rate/Term?"] == "Yes", "Could save $150 on Rate/Term", set())

        # Save to BytesIO instead of file
        output = BytesIO()
        doc.save(output)
        output.seek(0)
        s3_hook = await get_s3_hook(assistant_id)
        word_url = s3_hook.upload_file("lending_opportunities/call_list.docx", output)
        return {"file_url": word_url}

    except Exception as e:
        logger.error(f"Error while creating call list: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e),
        )
