import json
from concurrent.futures import Thr<PERSON><PERSON><PERSON><PERSON>xecutor, as_completed
from datetime import datetime, timedelta
from threading import Thread

import pandas as pd
import redis.asyncio as aioredis
from api.dependencies import get_outlook_token
from api.utils.utils import validate_assistant
from auth.cognito import get_or_create_auth_user
from config import settings
from db.models import User
from db.session import get_session
from fastapi import APIRouter, Depends, HTTPException, Query, Request, status
from fastapi.responses import HTMLResponse, StreamingResponse
from hooks.twilio_call_hook import TwilioCallHook
from loguru import logger
from schema.twilio import CallCampaign, InitiateCampaign
from sqlalchemy.ext.asyncio import AsyncSession
from starlette.websockets import WebSocket
from tools.functions.twilio import TwilioUtils
from tools.templates import client_templates
from twilio.rest import Client
from twilio.twiml.voice_response import Connect, VoiceResponse

router = APIRouter()

client = Client(settings.TWILIO_ACCOUNT_SID, settings.TWILIO_AUTH_TOKEN)


def make_call(twiml, phone_number_to_call):
    call = client.calls.create(
        to=phone_number_to_call,
        from_=settings.TWILIO_PHONE_NUMBER,
        twiml=twiml,
        machine_detection="Enable",
        async_amd="true",
        async_amd_status_callback=f"{(settings.DOMAIN_URL).replace('wss://', 'https://')}/api/twilio-call/call-status",
    )
    return call


@router.websocket("/refinancing_option/outbound-call")
async def handle_outbound_call(websocket: WebSocket):
    """Handle WebSocket connections between Twilio and OpenAI."""
    await websocket.accept()
    twilio_utils = TwilioUtils()
    tools = [
        twilio_utils.get_end_call_tool(),
        twilio_utils.get_send_refinance_email_tool(),
        twilio_utils.get_send_message_to_user_tool(),
    ]
    system_prompt_path = "prompts/twilio/refinancing_option_outbound_call.md"
    start_conversation_message = "Greet the user with 'Hello there! and tell that you are the loan officer assistant for the user, Greet the user with the name(if available)' and tell them that you are calling on behalf of the loan officer to discuss refinancing."  # noqa: E501

    twilio_call_hook = TwilioCallHook(
        websocket,
        tools,
        system_prompt_path,
        start_conversation_message,
    )

    await twilio_call_hook.handle_twilio_call()


@router.post("/loan_inquiry_call")
async def handle_loan_inquiry_call(request: Request, division: str = None):
    """Handle incoming call and return TwiML response to connect to Media Stream."""
    form = await request.form()
    from_number = form.get("From")
    formatted_number = f"{from_number[-10:-7]}-{from_number[-7:-4]}-{from_number[-4:]}"
    current_phone_number = form.get("To")
    response = VoiceResponse()
    response.say("Please wait while we connect your call to the loan assistant")
    connect = Connect()
    stream = connect.stream(url=f"{settings.DOMAIN_URL}/api/twilio-call/loan_inquiry_call")
    stream.parameter(name="division", value=division)
    stream.parameter(name="phone_number", value=formatted_number)
    stream.parameter(name="current_phone_number", value=current_phone_number)
    response.append(connect)
    return HTMLResponse(content=str(response), media_type="application/xml")


@router.websocket("/loan_inquiry_call")
async def handle_inquiry_call(websocket: WebSocket):
    """Handle WebSocket connections between Twilio and OpenAI."""
    await websocket.accept()
    twilio_utils = TwilioUtils()
    tools = [
        twilio_utils.get_end_call_tool(),
        twilio_utils.get_send_message_to_user_tool(),
        twilio_utils.get_transfer_call_tool(),
        twilio_utils.get_interest_rates_tool(),
    ]
    system_prompt_path = "prompts/twilio/encompass_inquiry_inbound_call.md"
    start_conversation_message = "Thank the user for the patience. Greet the user with 'Hello there! and tell that you are the loan officer assistant for the user, Greet the user with the name(if available) of the user and ask them 'How can I help you today?'"  # noqa: E501

    twilio_call_hook = TwilioCallHook(
        websocket,
        tools,
        system_prompt_path,
        start_conversation_message,
    )

    await twilio_call_hook.handle_twilio_call()


@router.post("/initiate_campaign", response_model=CallCampaign)
async def initiate_campaign(
    request: InitiateCampaign,
    outlook_token: str | None = Depends(get_outlook_token),
    user: User = Depends(get_or_create_auth_user),
    assistant_id: str = Query(..., description="ID of the assistant"),
    session: AsyncSession = Depends(get_session),
):
    await validate_assistant(assistant_id, session)

    if outlook_token:
        redis = await aioredis.from_url(settings.REDIS_URL)
        await redis.setex(f"outlook_token-{user.email}", timedelta(minutes=30), outlook_token)

    try:
        # Read input data from frontend file
        df_input = pd.read_excel(request.file_url)

        required_columns = {}
        if request.campaign_name == "Probate Campaign":
            required_columns = {
                "County Name": "County where the probate is filed",
                "State": "State of the probate",
                "First Name (Deceased)": "Deceased's first name",
                "Last Name": "Deceased's last name",
                "Document Type": "Type of probate document",
                "Property Address": "Property address of the estate",
                "Property City": "City of the property",
                "Property State": "State of the property",
                "Property Zip": "Zip code of the property",
                "Survivor First Name": "Personal Representative (PR) first name",
                "Survivor Last Name": "PR last name",
                "Survivor Address": "PR mailing address",
                "Survivor City": "PR city",
                "Survivor State": "PR state",
                "Survivor Zip": "PR zip code",
                "Wireless 1": "Primary wireless phone number for PR",
                "Email ID 1": "Primary email address for PR",
            }

        # Check for missing columns
        missing_columns = [col for col in required_columns.keys() if col not in df_input.columns]

        if missing_columns:
            missing_fields_message = "The following required fields are missing from the input file:\n" + "\n".join(
                [f"- {col} (needed for {required_columns[col]})" for col in missing_columns]
            )
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=missing_fields_message)

        def initiate_calls():
            with ThreadPoolExecutor(max_workers=3) as executor:
                futures = {
                    executor.submit(
                        make_call,
                        '<?xml version="1.0" encoding="UTF-8"?><Response><Connect><Stream url="{domain}/api/twilio-call/campaign/outbound-call"><Parameter name="current_phone_number" value="{current_phone_number}"/><Parameter name="phone_number" value="{phone_number}"/><Parameter name="probate_details" value=\'{probate_details}\'/><Parameter name="user_email" value="{user_email}"/></Stream></Connect></Response>'.format(  # noqa: E501
                            domain=settings.DOMAIN_URL,
                            current_phone_number=settings.TWILIO_PHONE_NUMBER,
                            phone_number=row["Wireless 1"],
                            user_email=user.email,
                            probate_details=json.dumps({col: row[col] for col in required_columns.keys()})
                            .replace("'", "&apos;")
                            .replace('"', "&quot;"),
                        ),
                        row["Wireless 1"],
                    ): idx
                    for idx, row in df_input.iterrows()
                }
                for future in as_completed(futures):
                    idx = futures[future]
                    try:
                        call = future.result()
                        logger.info(f"Row {idx} call initiated, SID = {call.sid}")
                    except Exception as e:
                        logger.error(f"Row {idx} failed: {e}")

        Thread(target=initiate_calls, daemon=True).start()

        return {"message": "Probate campaign has been initiated. Calls are being placed in the background."}

    except Exception as e:
        logger.error(f"Error while initiating campaign: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e),
        )


@router.websocket("/campaign/outbound-call")
async def handle_campaign_outbound_call(websocket: WebSocket):
    """Handle WebSocket connections between Twilio and OpenAI."""
    await websocket.accept()
    twilio_utils = TwilioUtils()
    tools = [
        twilio_utils.get_transfer_call_tool(),
        twilio_utils.get_send_message_to_user_tool(),
        twilio_utils.get_send_probate_mail_tool(),
    ]
    system_prompt_path = "prompts/twilio/probate_campaign.md"
    start_conversation_message = "Say 'Hello there!I’m calling because I noticed you’re the personal representative of an estate.  I have put together a team of professionals just to help families who are going through the probate process so we pull records from the courthouse and reach out to see if we can help the families.  There are lots of ways we can help ranging from selling properties as-is for cash to helping sell and donate personal property and even renovations so you can sell for top dollar.  For us it starts with understanding the family’s goals then offering you some options to choose from.  I haven’t asked, is there property in the estate that you could use some help with and do you have a plan for it?'"  # noqa: E501

    twilio_call_hook = TwilioCallHook(
        websocket,
        tools,
        system_prompt_path,
        start_conversation_message,
        session_init_args={
            "current_date": datetime.now().strftime("%Y-%m-%d"),
            "probate_outreach_number": client_templates.PROBATE_OUTREACH_NUMBER,
        },
    )

    await twilio_call_hook.handle_twilio_call()


@router.post("/call-status")
async def handle_call_status(request: Request):
    form = await request.form()
    call_sid = form.get("CallSid")
    answered_by = form.get("AnsweredBy")

    if answered_by == "machine_start":
        twiml = VoiceResponse()
        twiml.play(f"{(settings.DOMAIN_URL).replace('wss://', 'https://')}/api/twilio-call/get-probate-voicemail")

        client = Client(settings.TWILIO_ACCOUNT_SID, settings.TWILIO_AUTH_TOKEN)
        try:
            client.calls(call_sid).update(twiml=str(twiml))
            logger.info("Voicemail TwiML sent successfully")
        except Exception as e:
            logger.error(f"Failed to update call with voicemail TwiML: {e}")

    return {"message": "processed"}


@router.get("/get-probate-voicemail")
async def get_probate_voicemail(request: Request):
    from hooks.openai_hook import oai_hook

    response = oai_hook.stream_audio(
        "Hi, this is brenda calling i'm reaching out because i noticed you're the personal representative of an estate and we have a team of professionals ready to help families going through probate we assist with everything from selling property as is for cash to helping with personal property and been doing renovations for top dollar sales i'd love to understand your family's goals and see how we can best support you if you have any property in the estate and could use some help or if you already have a plan feel free to give me a call back you can reach me at 863-457-3784 thanks so much and i look forward to connecting",  # noqa: E501
        response_format="mp3",
        voice="sage",
    )
    return StreamingResponse(response, media_type="audio/mpeg")
