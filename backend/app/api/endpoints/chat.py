import os
from datetime import datetime, timezone

from api.dependencies import get_outlook_token
from db.models import Assistant, Assistant<PERSON><PERSON><PERSON>, Conversation, Message
from db.session import get_session
from fastapi import APIRouter, Body, Depends, File, HTTPException, UploadFile, status
from fastapi.responses import J<PERSON>NResponse
from hooks.gemini_hook import gemini_hook
from hooks.openai_hook import oai_hook
from hooks.s3 import get_s3_hook
from loguru import logger
from schema.chat import ChatInput, ChatOutput, Transcription
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession
from sse_starlette.sse import EventSourceResponse
from tools.utils.document_understanding import get_temp_file_paths
from tools.utils.total_expert import total_expert_utils

from .conversation import get_conversation_or_404
from .user import get_user_or_404

router = APIRouter()


@router.post("/transcribe/")
async def transcribe(file: UploadFile = File(...)) -> Transcription:
    """
    Transcribe an audio file.

    Args:
        file (UploadFile): The audio file to transcribe.

    Returns:
        Transcription: The transcription result.
    """
    transcript = await oai_hook.transcribe(file=file)
    return transcript


async def update_user_last_activity(user_id: int, session: AsyncSession) -> None:
    """
    Update the last activity timestamp for a user.

    Args:
        user_id (int): The ID of the user.
        session (AsyncSession): The database session, automatically provided by the `get_session` dependency.

    Returns:
        None
    """
    user = await get_user_or_404(id=user_id, session=session)
    if user:
        if user.meta:
            user.meta = {
                **user.meta,
                "user_info": {
                    **user.meta["user_info"],
                    "last_activity": datetime.now(timezone.utc).isoformat(),
                },
            }
        else:
            user.meta = {"user_info": {"last_activity": datetime.now(timezone.utc).isoformat()}}

        session.add(user)
        await session.commit()

    return None


@router.post("/{id}/", response_model=ChatOutput)
async def chat(
    chat_input: ChatInput = Body(...),
    image_file: UploadFile = File(None, description="Image file"),
    conversation: Conversation = Depends(get_conversation_or_404),
    session: AsyncSession = Depends(get_session),
    outlook_token: str | None = Depends(get_outlook_token),
) -> Message:
    """
    Send a chat message and receive a response from the assistant.

    Args:
        chat_input (ChatInput): The input data for the chat.
        conversation (Conversation): The conversation object, provided by the `get_conversation_or_404` dependency.
        session (AsyncSession): The database session, automatically provided by the `get_session` dependency.

    Returns:
        ChatOutput: A pydantic model containing the conversation, user message, and assistant message.
    """
    # change updated date to current time
    conversation.updated_at = datetime.now()
    if not conversation.title:
        title = await oai_hook.generate_title(chat_input.message)
        conversation.title = title
    await session.commit()
    await session.refresh(conversation)

    content = [{"type": "text", "text": chat_input.message}]
    s3_hook = await get_s3_hook(chat_input.assistant_id)
    if chat_input.image_url:
        image_url = s3_hook.get_presigned_url_from_link(chat_input.image_url)
        content.append({"type": "image_url", "image_url": {"url": image_url, "detail": "high"}})

    if image_file:
        file = await oai_hook.upload_file_thread(file=image_file, purpose="vision")
        content.append({"type": "image_file", "image_file": {"file_id": file.id, "detail": "high"}})

    attachments = []
    files = chat_input.file_urls or []
    for link in files:
        file = await oai_hook.upload_file_thread_from_s3(s3_hook=s3_hook, link=str(link), purpose="assistants")
        attachments.append({"file_id": file.id, "tools": [{"type": "code_interpreter"}]})

    message_id = await oai_hook.create_message(thread_id=conversation.id, content=content, attachments=attachments)
    user_message = Message(
        id=message_id,
        conversation_id=conversation.id,
        content=content,
        attachments=attachments,
        role=chat_input.role,
    )
    session.add(user_message)
    await session.commit()

    user = conversation.user
    # await update_user_last_activity(user.id, session)

    query = select(Assistant.enable_hard_filter).where(Assistant.id == chat_input.assistant_id)
    result = await session.execute(query)
    enable_hard_filter = result.scalar_one_or_none() or False

    user_query = user_message.content[0]["text"]

    # get AssistantPersona for the user
    query = select(AssistantPersona).where(
        AssistantPersona.user_id == user.id,
        AssistantPersona.assistant_id == chat_input.assistant_id,
        AssistantPersona.enabled == True,  # noqa: E712
    )
    result = await session.execute(query)
    persona = result.scalar_one_or_none()

    response = await oai_hook.ask_assistant(
        thread_id=conversation.id,
        assistant_id=chat_input.assistant_id,
        message_id=message_id,
        user=user,
        persona=persona,
        user_query=user_query,
        enable_hard_filter=enable_hard_filter,
        outlook_token=outlook_token,
    )
    components = response.get("components", None)
    payloads = response.get("payloads", None)
    attachments = response.get("attachments", None)
    meta = {}
    if components:
        meta.update({"component": components})
    if payloads:
        meta.update({"payload": payloads})
    assistant_message = Message(
        id=response["message_id"],
        conversation_id=conversation.id,
        content=response["content"],
        attachments=attachments,
        role="assistant",
        meta=meta if meta else None,
    )
    session.add(assistant_message)
    await session.commit()

    return ChatOutput(conversation=conversation, user_message=user_message, assistant_message=assistant_message)


@router.post("/{id}/stream/")
async def chat_stream(
    chat_input: ChatInput = Body(...),
    image_file: UploadFile = File(None),
    conversation: Conversation = Depends(get_conversation_or_404),
    session: AsyncSession = Depends(get_session),
    outlook_token: str | None = Depends(get_outlook_token),
    selected_user_id: str | None = None,
):
    """
    Stream a chat message and receive real-time responses from the assistant.

    Args:
        chat_input (ChatInput): The input data for the chat.
        conversation (Conversation): The conversation object, provided by the `get_conversation_or_404` dependency.
        session (AsyncSession): The database session, automatically provided by the `get_session` dependency.

    Returns:
        EventSourceResponse: An event stream containing the chat output.
    """
    await total_expert_utils.handle_multi_impersonation(
        user=conversation.user, selected_user_id=selected_user_id, assistant_id=chat_input.assistant_id
    )
    # change updated date to current time
    conversation.updated_at = datetime.now()
    if not conversation.title:
        title = await oai_hook.generate_title(chat_input.message)
        conversation.title = title
    await session.commit()
    await session.refresh(conversation)

    content = [{"type": "text", "text": chat_input.message}]
    s3_hook = await get_s3_hook(chat_input.assistant_id)
    if chat_input.image_url:
        image_url = s3_hook.get_presigned_url_from_link(chat_input.image_url)
        content.append({"type": "image_url", "image_url": {"url": image_url, "detail": "high"}})

    if image_file:
        file = await oai_hook.upload_file_thread(file=image_file, purpose="vision")
        content.append({"type": "image_file", "image_file": {"file_id": file.id, "detail": "high"}})

    attachments = []
    files = chat_input.file_urls or []
    for link in files:
        file = await oai_hook.upload_file_thread_from_s3(s3_hook=s3_hook, link=str(link), purpose="assistants")
        attachments.append({"file_id": file.id, "tools": [{"type": "code_interpreter"}]})

    message_id = await oai_hook.create_message(thread_id=conversation.id, content=content, attachments=attachments)
    user_message = Message(
        id=message_id,
        conversation_id=conversation.id,
        content=content,
        attachments=attachments,
        role=chat_input.role,
    )
    session.add(user_message)
    await session.commit()

    user = conversation.user
    await update_user_last_activity(user.id, session)

    query = select(Assistant.enable_hard_filter).where(Assistant.id == chat_input.assistant_id)
    result = await session.execute(query)
    enable_hard_filter = result.scalar_one_or_none() or False

    user_query = user_message.content[0]["text"]

    # get AssistantPersona for the user
    query = select(AssistantPersona).where(
        AssistantPersona.user_id == user.id,
        AssistantPersona.assistant_id == chat_input.assistant_id,
        AssistantPersona.enabled == True,  # noqa: E712
    )
    result = await session.execute(query)
    persona = result.scalar_one_or_none()
    logger.info(f"Initial User's Query: {user_query}")
    event_generator = oai_hook.ask_assistant_streaming(
        thread_id=conversation.id,
        assistant_id=chat_input.assistant_id,
        message_id=message_id,
        user=user,
        persona=persona,
        user_query=user_query,
        enable_hard_filter=enable_hard_filter,
        outlook_token=outlook_token,
        web_search=chat_input.web_search,
    )

    async def stream_generator():
        generated_message = ""
        result = []
        components = []
        payloads = []
        status = []
        attachments = []
        assistant_message_id = None

        async for event in event_generator:
            if event.event == "text":
                generated_message += event.data
            elif event.event == "component":
                components = event.data
            elif event.event == "payload":
                payloads = event.data
            elif event.event == "status":
                status = event.data
            elif event.event == "complete":
                result = event.data["content"]
                attachments = event.data["attachments"]
            elif event.event == "hint":
                if event.data and isinstance(event.data, dict) and event.data.get("message_id"):
                    assistant_message_id = event.data.get("message_id")
            yield event.model_dump()

        meta = {}
        if components:
            meta.update({"component": components})
        if payloads:
            meta.update({"payload": payloads})
        if status:
            meta.update({"status": status})

        assistant_message = Message(
            id=assistant_message_id,
            conversation_id=conversation.id,
            content=result,
            attachments=attachments,
            role="assistant",
            meta=meta if meta else None,
        )
        session.add(assistant_message)
        await session.commit()

        chat_output = ChatOutput(
            conversation=conversation, user_message=user_message, assistant_message=assistant_message
        )
        yield {"event": "chat_output", "data": chat_output.model_dump_json()}

    return EventSourceResponse(stream_generator(), media_type="text/event-stream")


@router.get("/{id}/cancel/")
async def cancel_chat(
    conversation: Conversation = Depends(get_conversation_or_404),
    session: AsyncSession = Depends(get_session),
):
    """
    Cancel an ongoing chat session.

    Args:
        conversation (Conversation): The conversation object, provided by the `get_conversation_or_404` dependency.
        session (AsyncSession): The database session, automatically provided by the `get_session` dependency.

    Returns:
        JSONResponse: A JSON response with a success message if cancellation is successful.

    Raises:
        HTTPException: 500 Internal Server Error if the chat could not be cancelled.
    """
    if await oai_hook.cancel_runs(conversation.id):
        return JSONResponse(content="Cancelled!", status_code=status.HTTP_200_OK)
    raise HTTPException(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        detail="Couldn't cancel chat. Some unexpected error occured!",
    )


@router.post("/{id}/document_understanding/stream/", response_model=None)
async def personal_chat(
    chat_input: ChatInput = Body(...),
    image_file: UploadFile = File(None, description="Image file"),
    files: list[UploadFile] = File(None, description="List of attachments"),
    conversation: Conversation = Depends(get_conversation_or_404),
    session: AsyncSession = Depends(get_session),
):
    """
    Send a chat message and receive a response from the assistant in a streaming format.
    """
    if not conversation.title:
        title = await oai_hook.generate_title(chat_input.message)
        conversation.title = title
        await session.commit()
        await session.refresh(conversation)

    # Prepare user message
    content = [{"type": "text", "text": chat_input.message}]
    if chat_input.image_url:
        content.append({"type": "image_url", "image_url": {"url": chat_input.image_url, "detail": "high"}})

    if image_file:
        file = await gemini_hook.upload_and_cache_file(file=image_file, purpose="vision")
        content.append({"type": "image_file", "image_file": {"file_id": file.id, "detail": "high"}})

    attachments = []
    if files:
        for input_file in files:
            file = await gemini_hook.upload_and_cache_file(file=input_file, purpose="assistants")
            attachments.append({"file_id": file.id})

    message_id = await gemini_hook.create_message()
    user_message = Message(
        id=message_id,
        conversation_id=conversation.id,
        content=content,
        attachments=attachments,
        role=chat_input.role,
    )
    session.add(user_message)
    await session.commit()

    links = chat_input.file_urls or []

    s3_hook = await get_s3_hook(chat_input.assistant_id)
    temp_file_paths = await get_temp_file_paths(links, s3_hook=s3_hook)

    # conversation history retrieving for history context need to verify
    # conversation_history = await gemini_hook.get_conversation_history(session=session, thread_id=conversation.id)

    conversation_history = []
    conversation_history.append(f"User: {chat_input.message}")

    async def stream_generator():
        assistant_response = ""
        try:
            async for stream_response in gemini_hook.query_model(conversation_history, temp_file_paths):
                assistant_response += stream_response
                yield {"event": "text", "data": stream_response}
            assistant_message_id = await gemini_hook.create_message()
            assistant_content = [{"type": "text", "text": assistant_response}]
            assistant_message = Message(
                id=assistant_message_id,
                conversation_id=conversation.id,
                content=assistant_content,
                attachments=attachments,
                role="assistant",
            )
            session.add(assistant_message)
            await session.commit()
            chat_output = ChatOutput(
                conversation=conversation, user_message=user_message, assistant_message=assistant_message
            )
            yield {"event": "chat_output", "data": chat_output.model_dump_json()}

        finally:
            for temp_file_path in temp_file_paths:
                if os.path.exists(temp_file_path):
                    os.remove(temp_file_path)

    return EventSourceResponse(stream_generator(), media_type="text/event-stream")
