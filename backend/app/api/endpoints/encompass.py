import json
from hashlib import sha256

from api.utils.pagination import page_number_pagination
from api.utils.utils import generate_msg_id
from auth.cognito import check_user_permissions, get_or_create_auth_user, is_admin_user, is_not_general_user
from clients import client_config
from db.models import Conversation, LoanOfficer, Message, User
from db.session import get_session
from fastapi import APIRouter, Depends, HTTPException, Query, status
from fastapi.encoders import jsonable_encoder
from loguru import logger
from schema.chat import ChatOutput
from schema.encompass import (
    AppraisedValueCalculationInput,
    AppraisedValueCalculationOutput,
    ListLoanOfficers,
    LoanDetailsByRefiTypeInput,
    LoanDetailsByRefiTypeOutput,
    MaximumCashoutCalculationInput,
    MaximumCashoutCalculationOutput,
    MonthlyPaymentCalculationInput,
    MonthlyPaymentCalculationOutput,
    OpenHouseFlyerInput,
    PreQualificationInput,
    PreQualificationOutput,
    PropertyLoanAmountInput,
    PropertyLoanAmountOutput,
    RemainingCashoutCalclationInput,
    RemainingCashoutCalclationOutput,
)
from schema.enums import RefinanceType
from schema.polly import PricingRequestBody, PricingRequestResponse, PricingRequestResponseModel
from sqlalchemy import func, select
from sqlalchemy.ext.asyncio import AsyncSession
from starlette.status import HTTP_422_UNPROCESSABLE_ENTITY
from tools.functions.encompass.loan import loan_function
from tools.functions.encompass.sales import sales_function
from tools.functions.polly.polly import polly_function
from tools.schema import ToolResponse
from tools.utils.encompass.loan import loan_utils
from tools.utils.encompass.sales import sales_utils

from .conversation import get_conversation_or_404

router = APIRouter()


@router.post(
    "/monthly_payment", response_model=MonthlyPaymentCalculationOutput, dependencies=[Depends(get_or_create_auth_user)]
)
async def calculate_monthly_payment(request: MonthlyPaymentCalculationInput):
    try:
        monthly_pi = await loan_utils.calculate_monthly_payment(
            request.loan_amount, rate=request.interest_rate, term_months=request.loan_term
        )
        return {"monthly_payment": round(monthly_pi, 2)}
    except Exception as e:
        logger.error(f"Error while calculating monthy payment: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e),
        )


@router.get("/loan-officer/sync/", status_code=status.HTTP_204_NO_CONTENT)
async def sync_loan_officer_data(user: User = Depends(is_not_general_user)) -> None:
    from tasks.loid import a_sync_loan_officer_data

    await a_sync_loan_officer_data(user)


@router.get("/loan-officers/", response_model=ListLoanOfficers, dependencies=[Depends(check_user_permissions)])
async def fetch_all_loan_officers(
    id: str,
    q: str = Query(None, description="Filter by name, email or user_id"),
    session=Depends(get_session),
    pagination: tuple[int, int] = Depends(page_number_pagination),
):
    """id: assistant id"""
    page, size = pagination
    query = select(LoanOfficer).where(LoanOfficer.assistant_id == id)
    if q:
        query = query.where(
            LoanOfficer.first_name.ilike(f"%{q}%")
            | LoanOfficer.last_name.ilike(f"%{q}%")
            | LoanOfficer.email.ilike(f"%{q}%")
            | LoanOfficer.loan_id.ilike(f"%{q}%")
        )

    total_data_fields = await session.execute(select(func.count()).select_from(query))
    count = total_data_fields.scalar()
    query = query.offset((page - 1) * size).limit(size)
    result = await session.execute(query)
    return {"count": count, "data": result.scalars().all()}


@router.post(
    "/appraised_value", response_model=AppraisedValueCalculationOutput, dependencies=[Depends(get_or_create_auth_user)]
)
async def calculate_appraised_value(request: AppraisedValueCalculationInput):
    try:
        property_location = {
            "Address": request.street_address,
            "City": request.city,
            "State": request.state,
            "Zip Code": request.zip_code,
        }
        appraised_value = await sales_utils.get_property_estimated_value(property_location)
        return {"appraised_value": round(appraised_value, 2) or "Please verify the addresses and try again."}
    except Exception as e:
        logger.error(f"Error while calculating appraised value: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e),
        )


@router.post(
    "/maximum_cashout", response_model=MaximumCashoutCalculationOutput, dependencies=[Depends(get_or_create_auth_user)]
)
async def calculate_maximum_cashout(request: MaximumCashoutCalculationInput):
    try:
        loan_details = {
            "Loan Amount": request.loan_amount,
            "Interest Rate": request.interest_rate,
            "MoPymtPI": request.monthly_payment,
            "Fund Released Date": request.closed_date,
        }
        estimated_payoff = await sales_utils.get_estimated_payoff(loan_details)
        max_cashout = (request.appraised_value * 0.8) - estimated_payoff
        return {"max_cashout": round(max_cashout, 2)}
    except Exception as e:
        logger.error(f"Error while calculating maximum cashout: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e),
        )


@router.post("/loan_details_by_refi_type", response_model=LoanDetailsByRefiTypeOutput)
async def get_loan_details_by_refi_type(
    request: LoanDetailsByRefiTypeInput, user: User = Depends(get_or_create_auth_user)
):
    try:
        # Fetch loan details
        loan_details_response = await loan_function.get_loan_details_by_loan_number(
            user=user, loan_number=request.loan_number, assistant_id=request.assistant_id
        )

        if not loan_details_response.component:
            return {"loan_details": "Please verify the loan number and try again."}

        # Extract loan details from the component
        default_loan_details = loan_details_response.component.component_props.get("data")[0]
        loan_details = {
            "First Name": default_loan_details.get("First Name"),
            "Last Name": default_loan_details.get("Last Name"),
        }
        if request.refi_type == RefinanceType.RATE_TERM:
            loan_details.update(
                {
                    "Loan Amount": default_loan_details.get("Loan Amount"),
                    "Interest Rate": default_loan_details.get("Interest Rate"),
                    "Loan Term": default_loan_details.get("Loan Term", 360),
                    "MoPymtPI": default_loan_details.get("MoPymtPI"),
                    "Fund Released Date": default_loan_details.get("Fund Released Date"),
                }
            )
            return {"loan_details": loan_details}

        if request.refi_type == RefinanceType.CASHOUT:
            # Compute total revolving balance with float conversion
            total_revolving_balance = sum(
                float(default_loan_details.get(balance_type, 0))
                for balance_type in [
                    "Borrower Revolving Balance",
                    "Coborrower Revolving Balance",
                    "Borrower Pair Revolving Balance",
                ]
            )

            loan_details.update(
                {
                    "Loan Amount": default_loan_details.get("Loan Amount"),
                    "Interest Rate": default_loan_details.get("Interest Rate"),
                    "Loan Term": default_loan_details.get("Loan Term", 360),
                    "MoPymtPI": default_loan_details.get("MoPymtPI"),
                    "Fund Released Date": default_loan_details.get("Fund Released Date"),
                    "Address": default_loan_details.get("Address"),
                    "City": default_loan_details.get("City"),
                    "State": default_loan_details.get("State"),
                    "Zip Code": default_loan_details.get("Zip Code"),
                    "Total Revolving Balance": round(total_revolving_balance, 2),
                    "FICO": default_loan_details.get("Credit Score"),
                    "Citizenship": default_loan_details.get("Borr Declarations"),
                    "Loan Purpose": default_loan_details.get("Purpose of Loan"),
                    "LTV": default_loan_details.get("LTV"),
                    "Impounds": default_loan_details.get("Request Impound Type"),
                    "AUS": default_loan_details.get(
                        "AUS Tracking - Latest Submission - Underwriting Risk Assess Type"
                    ),
                    "Property Type": default_loan_details.get("Subject Property Type"),
                    "Property Occupancy": default_loan_details.get("Subject Property Occupancy Status"),
                    "County": default_loan_details.get("Subject Property County"),
                    "Branch": default_loan_details.get("Branch ID"),
                }
            )

            # Fetch property appraised value
            appraised_value = await sales_utils.get_property_estimated_value(loan_details)
            if not appraised_value:
                return {
                    "appraised_value": "Please verify the address and try again.",
                    "loan_details": loan_details,
                }

            # Calculate cashout information
            estimated_payoff = await sales_utils.get_estimated_payoff(loan_details)
            max_cashout = (appraised_value * 0.8) - estimated_payoff

            return {
                "max_cashout": round(max_cashout, 2),
                "appraised_value": round(appraised_value, 2),
                "loan_details": loan_details,
            }

        if request.refi_type == RefinanceType.PURCHASE:
            loan_details.update(
                {
                    "Address": default_loan_details.get("Address"),
                    "City": default_loan_details.get("City"),
                    "State": default_loan_details.get("State"),
                    "Zip Code": default_loan_details.get("Zip Code"),
                }
            )
            return {"loan_details": loan_details}

        return {"loan_details": "Please confirm the refinance option."}

    except Exception as e:
        logger.error(f"Error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e),
        )


@router.post(
    "/remaining_cashout",
    response_model=RemainingCashoutCalclationOutput,
    dependencies=[Depends(get_or_create_auth_user)],
)
async def calculate_remaining_cashout(request: RemainingCashoutCalclationInput):
    try:
        remaining_cashout = request.max_cashout - request.debt_amount
        return {"remaining_cashout": round(remaining_cashout, 2)}
    except Exception as e:
        logger.error(f"Error while calculating remaining cashout: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e),
        )


@router.post("/get_open_house_flyer", dependencies=[Depends(get_or_create_auth_user)])
async def get_open_house_flyer(
    flyer_data: OpenHouseFlyerInput,
    conversation: Conversation = Depends(get_conversation_or_404),
    session: AsyncSession = Depends(get_session),
):
    try:
        user_message_id = await generate_msg_id("msg")
        user_content = [{"type": "text", "text": "Generate Open House Flyer based on the above details."}]
        user_message = Message(
            id=user_message_id,
            conversation_id=conversation.id,
            content=user_content,
            role="user",
        )
        session.add(user_message)
        await session.commit()

        open_house_response = await sales_function.generate_open_house_flyer(**flyer_data.dict())
        flyer_link = open_house_response.component.component_props.get("link")
        if flyer_link:
            assistant_message_id = await generate_msg_id("msg")
            assistant_content = [{"type": "text", "text": open_house_response.message}]
            meta = {
                "component": [
                    {
                        "component_name": open_house_response.component.component_name.value,
                        "component_props": open_house_response.component.component_props,
                    }
                ]
            }
            assistant_message = Message(
                id=assistant_message_id,
                conversation_id=conversation.id,
                content=assistant_content,
                role="assistant",
                meta=meta,
            )
            session.add(assistant_message)
            await session.commit()

            chat_output = ChatOutput(
                conversation=conversation, user_message=user_message, assistant_message=assistant_message
            )
            return {"event": "chat_output", "data": chat_output.model_dump_json(), "flyer_link": flyer_link}
    except Exception as e:
        logger.error(f"Error while generating open house flyer: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e),
        )


@router.post("/get_prequalification_details", response_model=PreQualificationOutput)
async def get_prequalification_details(request: PreQualificationInput, user: User = Depends(get_or_create_auth_user)):
    try:
        loan_details = await loan_function.get_loan_details_by_loan_number(
            user=user,
            loan_number=request.loan_number,
            assistant_id=request.assistant_id,
            data_only=True,
        )
        if isinstance(loan_details, ToolResponse):
            return {"error": "Couldn't fetch the loan details"}

        max_allowed = None
        maxprequal_amount = loan_details.get("Max to be approved for", 0)
        loan_amount = loan_details.get("Loan Amount")

        if maxprequal_amount > 0:
            max_allowed = maxprequal_amount
        elif loan_amount:
            max_allowed = loan_amount

        prequalification_details = {
            "First Name": loan_details.get("First Name"),
            "Last Name": loan_details.get("Last Name"),
            "Loan Type": loan_details.get("Loan Type"),
            "Address": loan_details.get("Address"),
            "City": loan_details.get("City"),
            "State": loan_details.get("State"),
            "Zip Code": loan_details.get("Zip Code"),
            "Max Allowed": max_allowed,
        }

        return {"prequalification_details": prequalification_details}

    except Exception as e:
        logger.error(f"Error while fetching prequalification details: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e),
        )


@router.post(
    "/property_loan_amount", response_model=PropertyLoanAmountOutput, dependencies=[Depends(get_or_create_auth_user)]
)
async def calculate_property_loan_amount(request: PropertyLoanAmountInput):
    try:
        down_payment_amount = (request.down_payment_percent / 100) * request.purchase_price
        property_loan_amount = request.purchase_price - down_payment_amount
        return {"property_loan_amount": round(property_loan_amount, 2)}
    except Exception as e:
        logger.error(f"Error while calculating property loan amount: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e),
        )


@router.get("/config/impersonation-access/", dependencies=[Depends(is_admin_user)])
async def get_impersonation_config():
    return {"data": client_config.ENCOMPASS_LOAN_IMPERSONATION_RESTRICTION}


@router.post("/request_pricing/")
async def get_pricing(
    assistant_id: str,
    pricing_req_body: PricingRequestBody,
    user: User = Depends(get_or_create_auth_user),
) -> PricingRequestResponseModel:

    await polly_function.initialize_redis()
    if not pricing_req_body.branch:
        branch = client_config.POLLY_DEFAULT_BRANCH or await loan_utils.fetch_loan_officer_branch(
            user=user, assistant_id=assistant_id
        )

        if not branch:
            raise HTTPException(
                status_code=HTTP_422_UNPROCESSABLE_ENTITY, detail="Branch info not found for the current user"
            )
        pricing_req_body.branch = branch

    if not await polly_function.validate_branch(pricing_req_body.branch):
        raise HTTPException(status_code=HTTP_422_UNPROCESSABLE_ENTITY, detail="User's branch is not valid")
    loan_request_body = polly_function.loan_details_to_pricing_request_body(pricing_req_body)
    request_hash = sha256(json.dumps(loan_request_body, sort_keys=True).encode()).hexdigest()
    cache_key = f"pricing{request_hash}"

    cached_response = await polly_function.redis.get(cache_key)
    if cached_response:
        full_result = [PricingRequestResponse.model_validate(item) for item in json.loads(cached_response)]
    else:
        data = await polly_function.request_pricing(loan_request_body)
        full_result = polly_function.parse_price_request_result(data)  # should return List[PricingRequestResponse]
        serialized = json.dumps(jsonable_encoder(full_result))
        await polly_function.redis.set(cache_key, serialized, ex=60)

    # Extra Filter out to filter  rates with prices > 100 - point
    max_price = 100 - pricing_req_body.search.desiredPrice
    full_result = [res for res in full_result if res.prices.price < max_price]

    final_result = full_result[:10]
    final_result.sort(key=lambda x: x.prices.price, reverse=True)
    return PricingRequestResponseModel(result=final_result, count=len(full_result))
