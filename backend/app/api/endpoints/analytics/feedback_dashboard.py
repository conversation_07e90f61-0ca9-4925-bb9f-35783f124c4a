import json
from datetime import datetime, timedelta

from api.endpoints.feedback import get_feedback_or_404
from api.utils.division import get_division_or_404
from api.utils.pagination import page_number_pagination
from api.utils.utils import extract_text, generate_and_upload_excel
from auth.cognito import get_or_create_auth_user, is_not_general_user
from dateutil.relativedelta import relativedelta
from db.models import Assistant, Conversation, Division, Feedback, Message, QuestionAnswer, User
from db.session import get_session
from fastapi import APIRouter, Depends, HTTPException, Query, status
from fastapi.responses import JSONResponse
from hooks.openai_hook import oai_hook
from schema.analytics.feedback_dashboard import FeedbackCorrection, FeedbackDetailsList
from schema.enums import RoleTypes
from sqlalchemy import String, and_, cast, func, or_, select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import aliased
from sqlalchemy.sql.functions import coalesce

router = APIRouter()


@router.get("/", response_model=FeedbackDetailsList, dependencies=[Depends(is_not_general_user)])
async def get_feedback_details(
    pagination: tuple[int, int] = Depends(page_number_pagination),
    rating: bool | None = Query(None, description="Filter by user rating"),
    assistant_id: str | None = Query(None, description="Filter by assistant id"),
    is_indexed: bool | None = Query(None, description="Filter by indexed status"),
    session: AsyncSession = Depends(get_session),
    user: User = Depends(get_or_create_auth_user),
    search: str = Query(None, description="Filter by name or email"),
    start_date: datetime | None = Query(None, description="Start date for message filter"),
    end_date: datetime | None = Query(None, description="End date for message filter"),
    filter_type: str = Query(None, description="Preset filter for date range: 'last_month', 'last_3_months', etc."),
    export_to_s3: bool = Query(False, description="If true, export the result to an Excel file in S3"),
    division: Division | None = Depends(get_division_or_404),
) -> FeedbackDetailsList:
    """
    Retrieve a paginated list of feedback details with optional filters and export functionality.

    Fetches feedback details, including user names, conversation information, feedback content, and user ratings,
    from the database. The results can be filtered by user rating, assistant ID, indexed status, and are paginated
    based on the provided page number and size. Optionally, the feedback details can be exported to an Excel file
    and uploaded to an S3 bucket.

    Args:
        pagination (tuple[int, int]): A tuple containing the page number and page size for pagination.
        rating (bool | None): Optional filter to include only feedback with the specified user rating.
        assistant_id (str | None): Optional filter to include only feedback associated with a specific assistant.
        is_indexed (bool | None): Optional filter to include only feedback marked as indexed.
        sort_order (Literal["asc", "desc"]): Sort order for the feedback records (ascending or descending).
        session (AsyncSession): The SQLAlchemy asynchronous session, provided by the `get_session` dependency.
        user (User): The authenticated user, provided by the `get_or_create_auth_user` dependency.
        start_date (datetime | None): Start date filter for messages created after this date.
        end_date (datetime | None): End date filter for messages created before this date.
        filter_type (str | None): Preset filter for date ranges (e.g., 'last_month', 'last_3_months').
        export_to_s3 (bool): If `True`, exports the result to an Excel file and uploads it to S3.
        division (Division | None): The division to filter by, if divisions exist in the system.

    Returns:
        FeedbackDetailsList: A Pydantic model containing the total count of feedback records and the paginated data.
        If `export_to_s3` is `True`, returns a JSON response with the S3 URL of the uploaded file.

    Raises:
        HTTPException: If the user does not have the appropriate role or permissions, an HTTP 403 error is raised.
        HTTPException: If an error occurs during file export or upload to S3, an HTTP 500 error is raised.
    """
    page, size = pagination
    AssistantMessage = aliased(Message)

    user_message_subquery = (
        select(AssistantMessage.content.label("user_query"))
        .where(
            and_(
                AssistantMessage.conversation_id == Message.conversation_id,
                AssistantMessage.role == "user",
                AssistantMessage.created_at < Message.created_at,
            )
        )
        .order_by(AssistantMessage.created_at.desc())
        .limit(1)
        .lateral()
    )
    query = (
        select(
            User.name,
            User.email,
            Message.created_at.label("message_created_at"),
            Conversation.meta["assistant"].label("assistant_name"),
            Assistant.name.label("assistant"),
            Conversation.id.label("conversation_id"),
            Feedback.feedback_text,
            Message.content.label("message_content"),
            user_message_subquery.c.user_query,
            Feedback.user_rating,
            Feedback.created_at.label("feedback_created_at"),
            Feedback.id.label("feedback_id"),
            coalesce(QuestionAnswer.is_indexed, False).label("is_indexed"),
            QuestionAnswer.id.label("question_answer_id"),
        )
        .select_from(User)
        .outerjoin(Conversation, User.id == Conversation.user_id)
        .outerjoin(Message, Conversation.id == Message.conversation_id)
        .outerjoin(Feedback, Message.id == Feedback.message_id)
        .outerjoin(QuestionAnswer, cast(Feedback.id, String) == QuestionAnswer.meta["feedback_id"].astext)
        .outerjoin(user_message_subquery, user_message_subquery.c.user_query.isnot(None))
        .outerjoin(Assistant, Assistant.id == Conversation.meta["assistant"].astext)
        .where(Feedback.user_rating.isnot(None))
        .order_by(Message.created_at.desc())
    )

    # Add division filter if divisions exist
    if division is not None:
        if user.role == RoleTypes.ADMIN:
            # Admin can see any division's data
            query = query.where(User.division_id == division.id)
        else:
            # Non-admin users can only see their division's data
            if user.division_id != division.id:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="You do not have permission to view feedback from other divisions.",
                )
            query = query.where(User.division_id == division.id)

    # Apply date range filters based on filter_type
    if filter_type:
        now = datetime.now()
        if filter_type == "today":
            start_date = now.replace(hour=0, minute=0, second=0, microsecond=0)
            end_date = now
        elif filter_type == "last_week":
            start_date = now - timedelta(days=7)
        elif filter_type == "last_month":
            start_date = now - relativedelta(months=1)
            end_date = now
        elif filter_type == "last_3_months":
            start_date = now - relativedelta(months=3)
            end_date = now
        elif filter_type == "last_6_months":
            start_date = now - relativedelta(months=6)
            end_date = now
        elif filter_type == "last_year":
            start_date = now - relativedelta(years=1)
            end_date = now

    if user.role == RoleTypes.ASSISTANT_ADMIN:
        assistant_ids = [assistant.id for assistant in user.assistants]
        query = query.where(Conversation.meta["assistant"].astext.in_(assistant_ids))

    if rating is not None:
        query = query.where(Feedback.user_rating == rating)

    if is_indexed is not None:
        query = query.where(coalesce(QuestionAnswer.is_indexed, False) == is_indexed)

    if assistant_id is not None:
        query = query.where(Conversation.meta["assistant"].astext == assistant_id)

    if search:
        query = query.where(
            or_(
                User.search_tsvector.op("@@")(func.plainto_tsquery("english", f"{search}:*")),
                User.name.ilike(f"%{search}%"),
                User.email.ilike(f"%{search}%"),
            )
        )

    # Apply date range filters
    if start_date:
        query = query.where(Message.created_at >= start_date)
    if end_date:
        query = query.where(Message.created_at <= end_date)

    total_feedback = await session.execute(select(func.count()).select_from(query))
    count = total_feedback.scalar()

    # Export to S3 if requested
    if export_to_s3:
        result = await session.execute(query)
        messages = result.fetchall()
        report_data = [
            {
                "Name": msg.name,
                "Assistant": msg.assistant,
                "Conversation ID": msg.conversation_id,
                "User Query": extract_text(msg.user_query),
                "Message Content": extract_text(msg.message_content),
                "Feedback Text": msg.feedback_text,
                "User Rating": msg.user_rating,
                "Feedback Created At": msg.feedback_created_at,
                "Is Indexed": msg.is_indexed,
            }
            for msg in messages
        ]

        folder = "reports/"  # S3 folder name
        prefix = "feedback_messages"
        excel_url = generate_and_upload_excel(folder, prefix, report_data)

        if excel_url:
            return JSONResponse(content={"message": "File uploaded to S3", "url": excel_url})
        else:
            return JSONResponse(content={"message": "Failed to upload Excel file."}, status_code=500)

    # add pagination
    query = query.offset((page - 1) * size).limit(size)
    result = await session.execute(query)

    return {
        "count": count,
        "data": result.all(),
    }


@router.get(
    "/suggestive-correction/{id}/", response_model=FeedbackCorrection, dependencies=[Depends(is_not_general_user)]
)
async def get_suggestive_correction(
    feedback: Feedback = Depends(get_feedback_or_404),
    session: AsyncSession = Depends(get_session),
) -> FeedbackCorrection:
    """
    Generate a suggestive correction for feedback based on conversation history and user feedback.

    Retrieves recent messages from the conversation associated with the feedback, and constructs a prompt to generate a
    corrected question and suggested answer based on the conversation and feedback provided.

    Args:
        feedback (Feedback): The feedback object, provided by the `get_feedback_or_404` dependency.
        session (AsyncSession): The SQLAlchemy asynchronous session, provided by the `get_session` dependency.

    Returns:
        FeedbackCorrection: A pydantic model containing the corrected question and suggested answer.

    Raises:
        HTTPException: If the user does not have the appropriate role or permissions, an HTTP 403 error is raised.
    """
    query = (
        select(Message)
        .where(Message.conversation_id == feedback.message.conversation_id)
        .where(Message.created_at < feedback.created_at)
        .order_by(Message.created_at.desc())
        .limit(6)
    )
    result = await session.execute(query)
    messages = result.scalars().all()

    # Construct the conversation string
    conversation = "\n".join([f"{message.role.upper()}: {message.content}" for message in messages[::-1]])

    # Construct the feedback string
    feedback_text = (
        f'{feedback.feedback_text} (Rating: {"positive" if feedback.user_rating is True else "negative"})'
        if feedback.feedback_text
        else "No feedback provided."
    )

    prompt = (
        "Generate a corrected question and suggested answer based on the following conversation and user feedback. "
        "If the user provided a suggested answer in the feedback, include it; otherwise, "
        "leave the answer field empty for further review.\n\n"
        f"**Conversation:**\n{conversation}\n\n"
        f"**Feedback:**\n{feedback_text}\n\n"
        "Please ensure the corrected question and answer are clear, concise, and aligned with the feedback provided. "
        "Return the corrected question and suggested answer in JSON format with the keys 'question' and 'answer'."
    )

    response = await oai_hook.create_completion(
        messages=[{"role": "user", "content": prompt}],
        model="gpt-4o",
        temperature=0,
        response_format={"type": "json_object"},
    )

    return json.loads(response)
