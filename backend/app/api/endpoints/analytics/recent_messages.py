from datetime import datetime, timedelta

from api.utils.division import get_division_or_404
from api.utils.pagination import page_number_pagination
from api.utils.utils import extract_text, generate_and_upload_excel
from auth.cognito import get_or_create_auth_user, is_not_general_user
from config import settings
from dateutil.relativedelta import relativedelta
from db.models import Assistant, Conversation, Division, Feedback, Message, User
from db.session import get_session
from fastapi import APIRouter, Depends, Header, HTTPException, Query, status
from fastapi.responses import J<PERSON>NResponse
from limits import parse, storage, strategies
from schema.analytics.recent_messages import RecentMessagesResponse
from schema.enums import AssistantTypes, RoleTypes
from sqlalchemy import func, or_, select
from sqlalchemy.ext.asyncio import AsyncSession

limits_storage = storage.RedisStorage(settings.REDIS_URL)
rate_limiter = strategies.FixedWindowRateLimiter(limits_storage)
rate_limit_string = parse("10/day")


router = APIRouter()


async def verify_password(user: User, password: str):
    user_id = str(user.id)
    # Always check if user is already rate-limited, check rate limit for user_id
    if not rate_limiter.test(rate_limit_string, user_id):
        raise HTTPException(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail=(
                "You have entered the wrong password too many times and are temporarily blocked. Please try again"
                " later."
            ),
        )

    # If password is wrong, increment the counter (already done above)
    if password != settings.PERSONAL_MESSAGES_ACCESS_PASSWORD:
        # add one wrong hit for rate limit; check rate limit for user_id
        rate_limiter.hit(rate_limit_string, user_id)
        reset_time, remaining_count = rate_limiter.get_window_stats(rate_limit_string, user_id)
        if remaining_count == 0:
            message = (
                "You have entered the wrong password too many times and are temporarily blocked. Please try again"
                " later."
            )
        else:
            message = f"The password is incorrect. Please try again. Remaining Attempts: {remaining_count}"
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=message,
        )

    return True


async def get_recent_messages_by_assistant_types(
    assistant_types: list[AssistantTypes],
    pagination: tuple[int, int],
    session: AsyncSession,
    user: User,
    search: str | None,
    assistant_id: str | None,
    start_date: datetime | None,
    end_date: datetime | None,
    filter_type: str | None,
    export_to_s3: bool,
    division: Division | None = None,
) -> RecentMessagesResponse:
    page, size = pagination

    # Apply date range filters based on filter_type
    if filter_type:
        now = datetime.now()
        if filter_type == "today":
            start_date = now.replace(hour=0, minute=0, second=0, microsecond=0)
            end_date = now
        elif filter_type == "last_week":
            start_date = now - timedelta(days=7)
        elif filter_type == "last_month":
            start_date = now - relativedelta(months=1)
            end_date = now
        elif filter_type == "last_3_months":
            start_date = now - relativedelta(months=3)
            end_date = now
        elif filter_type == "last_6_months":
            start_date = now - relativedelta(months=6)
            end_date = now
        elif filter_type == "last_year":
            start_date = now - relativedelta(years=1)
            end_date = now

    query = (
        select(
            User.name,
            User.email,
            Conversation.title,
            Message.role,
            Message.content.label("message"),
            Message.meta.label("message_meta"),
            Conversation.id.label("conversation_id"),
            Message.id.label("message_id"),
            Conversation.meta["assistant"].label("assistant_name"),
            Assistant.name.label("assistant"),
            Message.created_at.label("message_created_at"),
        )
        .select_from(User)
        .outerjoin(Conversation, User.id == Conversation.user_id)
        .outerjoin(Message, Conversation.id == Message.conversation_id)
        .outerjoin(Feedback, Message.id == Feedback.message_id)
        .outerjoin(Assistant, Conversation.meta["assistant"].astext == Assistant.id)
        .where(Message.content.isnot(None))
        .where(Message.created_at.isnot(None))
        .where(Assistant.type.in_(assistant_types))
    )

    # Add division filter if divisions exist
    if division is not None:
        if user.role == RoleTypes.ADMIN:
            # Admin can see any division's data
            query = query.where(User.division_id == division.id)
        else:
            # Non-admin users can only see their division's data
            if user.division_id != division.id:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="You do not have permission to view messages from other divisions.",
                )
            query = query.where(User.division_id == division.id)

    # Apply other filters
    if user.role == RoleTypes.ASSISTANT_ADMIN:
        assistant_ids = [assistant.id for assistant in user.assistants]
        query = query.where(Conversation.meta["assistant"].astext.in_(assistant_ids))

    if search:
        query = query.where(
            or_(
                User.search_tsvector.op("@@")(func.plainto_tsquery("english", f"{search}:*")),
                User.name.ilike(f"%{search}%"),
                User.email.ilike(f"%{search}%"),
            )
        )

    if assistant_id:
        query = query.where(Conversation.meta["assistant"].astext == assistant_id)

    # Apply date range filters
    if start_date:
        query = query.where(func.date(Message.created_at) >= start_date)
    if end_date:
        query = query.where(func.date(Message.created_at) <= end_date)

    # count total number of messages
    total_messages = await session.execute(select(func.count()).select_from(query))
    count = total_messages.scalar()

    # Export to S3 if requested
    if export_to_s3:
        result = await session.execute(query.order_by(Message.created_at.asc()))
        messages = result.fetchall()
        report_data = []
        for msg in messages:
            # Process the message metadata for SourceCitation component
            sources = []
            if msg.message_meta and "component" in msg.message_meta:
                for component in msg.message_meta["component"]:
                    if component.get("component_name") == "SourceCitation":
                        for prop in component.get("component_props", []):
                            filename = prop.get("filename")
                            page_number = prop.get("page_number")
                            sourcetext = prop.get("text")[0:100] if prop.get("text") else ""
                            sources.append(f"[{filename} - Page No: {page_number}] (Source Text: {sourcetext})")
            report_data.append(
                {
                    "Name": msg.name,
                    "Email": msg.email,
                    "Conversation Title": msg.title,
                    "Role": msg.role,
                    "Message": extract_text(msg.message),
                    "Assistant": msg.assistant,
                    "Created At": msg.message_created_at,
                    "Sources": "\n".join([f"{idx + 1}. {s}" for idx, s in enumerate(sources)]),
                }
            )

        folder = "reports/"  # S3 folder name
        prefix = "recent_messages"
        excel_url = generate_and_upload_excel(folder, prefix, report_data)

        if excel_url:
            return JSONResponse(content={"message": "File uploaded to S3", "url": excel_url})
        else:
            return JSONResponse(content={"message": "Failed to upload Excel file."}, status_code=500)

    # add pagination
    query = query.order_by(Message.created_at.desc()).offset((page - 1) * size).limit(size)
    result = await session.execute(query)

    return {
        "count": count,
        "data": result,
    }


@router.get("/", response_model=RecentMessagesResponse, dependencies=[Depends(is_not_general_user)])
async def get_recent_messages(
    pagination: tuple[int, int] = Depends(page_number_pagination),
    session: AsyncSession = Depends(get_session),
    user: User = Depends(get_or_create_auth_user),
    search: str = Query(None, description="Filter by name or email"),
    assistant_id: str | None = Query(None, description="Filter by assistant id"),
    start_date: datetime | None = Query(None, description="Start date for message filter"),
    end_date: datetime | None = Query(None, description="End date for message filter"),
    filter_type: str = Query(None, description="Preset filter for date range: 'last_month', 'last_3_months', etc."),
    export_to_s3: bool = Query(False, description="If true, export the result to an Excel file in S3"),
    division: Division | None = Depends(get_division_or_404),
) -> RecentMessagesResponse:
    """
    Retrieve recent messages with optional filters and pagination.
    If divisions exist in the system, filters messages by division.
    If no divisions exist, returns all messages without division filtering.

    The response includes messages with details such as user name, email, conversation title,
    message role, content, and creation timestamp. The result is filtered based on the user's role,
    optional search terms, assistant ID, and date range filters.

    Args:
        pagination (tuple[int, int]): Pagination parameters including `page` and `size`.
        session (AsyncSession): The database session.
        user (User): The authenticated user object, automatically provided by the `get_or_create_auth_user` dependency.
        search (str, optional): Search term to filter messages by user name or email.
        assistant_id (str, optional): Filter messages by assistant ID.
        start_date (datetime, optional): Start date for filtering messages based on creation date.
        end_date (datetime, optional): End date for filtering messages based on creation date.
        filter_type (str, optional): Preset filter for date ranges like `last_month`, `last_3_months`, etc.
        export_to_s3 (bool, optional): If `True`, the filtered result will be exported to an Excel file and uploaded to S3. # noqa: E501
        division (Division | None): The division to filter by, if divisions exist in the system.

    Returns:
        RecentMessagesResponse: A response object containing the total count of messages and the paginated data.
        If `export_to_s3` is `True`, the response includes a URL to the Excel file uploaded to S3.

    Raises:
        HTTPException: If the user is not authorized to access this information or if there is a database error.
        HTTPException: If a non-admin user tries to filter by a division other than their own.
    """
    return await get_recent_messages_by_assistant_types(
        assistant_types=[AssistantTypes.GENERAL, AssistantTypes.CUSTOM],
        pagination=pagination,
        session=session,
        user=user,
        search=search,
        assistant_id=assistant_id,
        start_date=start_date,
        end_date=end_date,
        filter_type=filter_type,
        export_to_s3=export_to_s3,
        division=division,
    )


@router.get(
    "/personal/",
    response_model=RecentMessagesResponse,
    dependencies=[Depends(is_not_general_user)],
)
async def get_personal_recent_messages(
    pagination: tuple[int, int] = Depends(page_number_pagination),
    session: AsyncSession = Depends(get_session),
    user: User = Depends(get_or_create_auth_user),
    search: str = Query(None, description="Filter by name or email"),
    assistant_id: str | None = Query(None, description="Filter by assistant id"),
    start_date: datetime | None = Query(None, description="Start date for message filter"),
    end_date: datetime | None = Query(None, description="End date for message filter"),
    filter_type: str = Query(None, description="Preset filter for date range: 'last_month', 'last_3_months', etc."),
    export_to_s3: bool = Query(False, description="If true, export the result to an Excel file in S3"),
    password: str = Header(..., alias="X-Access-Password"),
    division: Division | None = Depends(get_division_or_404),
) -> RecentMessagesResponse:
    await verify_password(user, password)

    return await get_recent_messages_by_assistant_types(
        assistant_types=[AssistantTypes.PERSONAL],
        pagination=pagination,
        session=session,
        user=user,
        search=search,
        assistant_id=assistant_id,
        start_date=start_date,
        end_date=end_date,
        filter_type=filter_type,
        export_to_s3=export_to_s3,
        division=division,
    )
