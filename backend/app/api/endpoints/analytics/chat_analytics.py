from datetime import datetime, timedelta

from api.utils.assistant import get_assistants_id
from api.utils.pagination import datetime_pagination, page_number_pagination
from auth.cognito import get_or_create_auth_user, is_not_general_user
from dateutil.parser import parse
from db.models import Assistant, Conversation, Feedback, Message, User
from db.session import get_session
from fastapi import APIRouter, Depends, Query
from schema.analytics.chat_analytics import FeedbackSummary, MessageOverTime, UserCount, UserEngagementFeedbackSummary
from schema.enums import RoleTypes
from sqlalchemy import and_, distinct, func, select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

router = APIRouter()


@router.get("/total-messages/", response_model=int, dependencies=[Depends(is_not_general_user)])
async def get_total_messages(
    session: AsyncSession = Depends(get_session),
    user: User = Depends(get_or_create_auth_user),
    division_id: str | None = Query(None, description="Filter by division id"),
) -> int:
    """
    Retrieve the total number of messages in the database.

    The count of messages varies based on the user's role. Admin users can access all messages,
    while Assistant Admins have a filtered view based on their assistants.

    Args:
        session (AsyncSession): The database session.
        user (User): The authenticated user object, automatically provided by the `get_or_create_auth_user` dependency.

    Returns:
        int: The total number of messages.

    Raises:
        HTTPException: If the user is not authorized to access this information.
    """
    if user.role == RoleTypes.ADMIN:
        query = select(func.count()).select_from(Message)
        if division_id:
            assistant_ids = await get_assistants_id(
                session,
                and_filters=[Assistant.division_id == division_id],
            )
            query = query.join(Conversation, Message.conversation_id == Conversation.id).where(
                Conversation.meta["assistant"].astext.in_(assistant_ids)
            )
    elif user.role == RoleTypes.DIVISION_ADMIN:
        assistant_ids = await get_assistants_id(
            session,
            and_filters=[Assistant.division_id == user.division_id],
        )
        query = (
            select(func.count())
            .select_from(Message)
            .join(Conversation, Message.conversation_id == Conversation.id)
            .where(Conversation.meta["assistant"].astext.in_(assistant_ids))
        )
    elif user.role == RoleTypes.ASSISTANT_ADMIN:
        assistant_ids = [assistant.id for assistant in user.assistants]
        query = (
            select(func.count()).select_from(Message).join(Conversation, Message.conversation_id == Conversation.id)
        ).where(Conversation.meta["assistant"].astext.in_(assistant_ids))
    result = await session.execute(query)
    return result.scalar()


@router.get("/average-conversation-per-user/", response_model=float, dependencies=[Depends(is_not_general_user)])
async def get_average_conversation_per_user(
    session: AsyncSession = Depends(get_session),
    user: User = Depends(get_or_create_auth_user),
    division_id: str | None = Query(None, description="Filter by division id"),
) -> float:
    """
    Calculate the average number of conversations per user.

    The average is computed based on the user's role. Admins get a full average, while Assistant Admins
    receive an average based on their assistants' conversations.

    Args:
        session (AsyncSession): The database session.
        user (User): The authenticated user object, automatically provided by the `get_or_create_auth_user` dependency.

    Returns:
        float: The average number of conversations per user.

    Raises:
        HTTPException: If the user is not authorized to access this information.
    """
    if user.role == RoleTypes.ADMIN:
        subquery = select(func.count()).select_from(Conversation).group_by(Conversation.user_id).subquery()
        if division_id:
            query = select(Assistant).where(Assistant.division_id == division_id)
            assistans = await session.execute(query)
            assistant_ids = [assistant.id for assistant in assistans.scalars()]
            subquery = (
                select(func.count())
                .select_from(Conversation)
                .where(Conversation.meta["assistant"].astext.in_(assistant_ids))
                .group_by(Conversation.user_id)
                .subquery()
            )
    elif user.role == RoleTypes.DIVISION_ADMIN:
        query_assistant = select(Assistant).where(Assistant.division_id == user.division_id)
        assistans = await session.execute(query_assistant)
        assistant_ids = [assistant.id for assistant in assistans.scalars()]
        subquery = (
            select(func.count())
            .select_from(Conversation)
            .where(Conversation.meta["assistant"].astext.in_(assistant_ids))
            .group_by(Conversation.user_id)
            .subquery()
        )
    elif user.role == RoleTypes.ASSISTANT_ADMIN:
        assistant_ids = [assistant.id for assistant in user.assistants]
        subquery = (
            select(func.count())
            .select_from(Conversation)
            .where(Conversation.meta["assistant"].astext.in_(assistant_ids))
            .group_by(Conversation.user_id)
            .subquery()
        )

    query = select(func.avg(subquery.c.count)).select_from(subquery)
    result = await session.execute(query)
    final_data = result.scalar() or 0
    return final_data


@router.get("/feedback-summary/", response_model=list[FeedbackSummary], dependencies=[Depends(is_not_general_user)])
async def get_feedback_summary(
    session: AsyncSession = Depends(get_session),
    user: User = Depends(get_or_create_auth_user),
    division_id: str | None = Query(None, description="Filter by division id"),
) -> list[FeedbackSummary]:
    """
    Get a summary of feedback ratings.

    The summary is based on the user's role. Admins get a summary of all feedback ratings,
    while Assistant Admins receive a filtered summary based on their assistants' feedback.

    Args:
        session (AsyncSession): The database session.
        user (User): The authenticated user object, automatically provided by the `get_or_create_auth_user` dependency.

    Returns:
        list[FeedbackSummary]: A list of feedback summaries grouped by rating.

    Raises:
        HTTPException: If the user is not authorized to access this information.
    """
    assistant_ids = []

    if user.role == RoleTypes.ADMIN:
        if division_id:
            assistant_ids = await get_assistants_id(
                session,
                and_filters=[Assistant.division_id == division_id],
            )
        else:
            query = select(Feedback.user_rating, func.count()).group_by(Feedback.user_rating)
            result = await session.execute(query)
            return result
    elif user.role == RoleTypes.DIVISION_ADMIN:
        assistant_ids = await get_assistants_id(
            session,
            and_filters=[Assistant.division_id == user.division_id],
        )
    elif user.role == RoleTypes.ASSISTANT_ADMIN:
        assistant_ids = [assistant.id for assistant in user.assistants]

    query = (
        select(Feedback.user_rating, func.count())
        .select_from(Feedback)
        .join(Message, Feedback.message_id == Message.id)
        .join(Conversation, Message.conversation_id == Conversation.id)
        .where(Conversation.meta["assistant"].astext.in_(list(map(str, assistant_ids))))
        .group_by(Feedback.user_rating)
    )

    result = await session.execute(query)
    return result


@router.get("/messages-over-time/", response_model=list[MessageOverTime], dependencies=[Depends(is_not_general_user)])
async def get_messages_over_time(
    pagination: tuple[str, str, int] = Depends(datetime_pagination),
    assistant_id: str | None = Query(None, description="Filter by assistant id"),
    session: AsyncSession = Depends(get_session),
    user: User = Depends(get_or_create_auth_user),
    division_id: str | None = Query(None, description="Filter by division id"),
) -> list[MessageOverTime]:
    """
    Retrieve the number of messages over time with pagination.

    The messages are filtered based on the user's role and the specified date range.

    Args:
        pagination (tuple[str, str, int]): Pagination parameters including `before`, `after`, `page`, and `size`.
        session (AsyncSession): The database session.
        user (User): The authenticated user object, automatically provided by the `get_or_create_auth_user` dependency.

    Returns:
        list[MessageOverTime]: A list of messages grouped by date.

    Raises:
        HTTPException: If the user is not authorized to access this information.
    """
    before, after, page, size = pagination
    date_created = func.date(Message.created_at)
    assistant_ids = []

    query = (
        select(date_created, func.count())
        .select_from(Message)
        .join(Conversation, Message.conversation_id == Conversation.id)
    )
    if user.role == RoleTypes.ADMIN and division_id:
        assistant_ids = await get_assistants_id(
            session,
            and_filters=[Assistant.division_id == division_id],
        )
    elif user.role == RoleTypes.ASSISTANT_ADMIN:
        assistant_ids = [assistant.id for assistant in user.assistants]
    elif user.role == RoleTypes.DIVISION_ADMIN:
        assistant_ids = await get_assistants_id(
            session,
            and_filters=[Assistant.division_id == user.division_id],
        )

    if assistant_ids:
        query = query.where(Conversation.meta["assistant"].astext.in_(assistant_ids))

    if assistant_id:
        query = query.where(Conversation.meta["assistant"].astext == assistant_id)

    query = (
        query.group_by(date_created)
        .having(
            and_(
                date_created <= before if before else True,
                date_created >= after if after else True,
            )
        )
        .order_by(date_created.desc())
        .offset((page - 1) * size)
        .limit(size)
    )
    result = await session.execute(query)
    return result


@router.get(
    "/user-engagement-feedback-summary/",
    response_model=list[UserEngagementFeedbackSummary],
    dependencies=[Depends(is_not_general_user)],
)
async def get_user_engagement_feedback_summary(
    pagination: tuple[int, int] = Depends(page_number_pagination),
    session: AsyncSession = Depends(get_session),
    user: User = Depends(get_or_create_auth_user),
    division_id: str | None = Query(None, description="Filter by division id"),
    assistant_id: str | None = Query(None, description="Filter by assistant id"),
) -> list[UserEngagementFeedbackSummary]:
    """
    Retrieve a summary of user engagement metrics including conversations, messages, and feedback.

    The summary is filtered based on the user's role. Assistant Admins get a filtered view based on their assistants.

    Args:
        pagination (tuple[int, int]): Pagination parameters including `page` and `size`.
        session (AsyncSession): The database session.
        user (User): The authenticated user object, automatically provided by the `get_or_create_auth_user` dependency.

    Returns:
        list[UserEngagementFeedbackSummary]: A list of user engagement summaries.

    Raises:
        HTTPException: If the user is not authorized to access this information.
    """
    page, size = pagination
    conversation_count = func.count(distinct(Conversation.id))
    message_count = func.count(Message.id)
    feedback_count = func.count(Feedback.id)
    assistant_ids = []

    query = (
        select(
            User.name,
            User.email,
            conversation_count.label("conversation_count"),
            message_count.label("message_count"),
            feedback_count.label("feedback_count"),
        )
        .select_from(User)
        .outerjoin(Conversation, User.id == Conversation.user_id)
        .outerjoin(Message, Conversation.id == Message.conversation_id)
        .outerjoin(Feedback, Message.id == Feedback.message_id)
    )

    if user.role == RoleTypes.ADMIN and division_id:
        assistant_ids = await get_assistants_id(
            session,
            and_filters=[Assistant.division_id == division_id],
        )
    elif user.role == RoleTypes.ASSISTANT_ADMIN:
        assistant_ids = [assistant.id for assistant in user.assistants]
    elif user.role == RoleTypes.DIVISION_ADMIN:
        assistant_ids = await get_assistants_id(
            session,
            and_filters=[Assistant.division_id == user.division_id],
        )

    if assistant_ids:
        query = query.where(Conversation.meta["assistant"].astext.in_(assistant_ids))

    if assistant_id:
        query = query.where(Conversation.meta["assistant"].astext == assistant_id)

    query = (
        query.group_by(User.id, User.name)
        .order_by(conversation_count.desc(), message_count.desc(), feedback_count.desc())
        .offset((page - 1) * size)
        .limit(size)
    )
    result = await session.execute(query)
    return result


async def calculate_average_messages(
    session: AsyncSession,
    user: User,
    day_range: list[int],
    after: str | None,
    before: str | None,
    division_id: str | None = None,
) -> float:
    after = parse(after).replace(tzinfo=None) if after else None
    before = parse(before).replace(tzinfo=None)
    sql_day_range = [(day + 1) % 7 for day in day_range]
    query = select(func.count()).select_from(Message).where(func.extract("dow", Message.created_at).in_(sql_day_range))
    first_message_query = select(func.min(Message.created_at)).select_from(Message)
    assistant_ids = []

    if user.role == RoleTypes.ADMIN and division_id:
        assistant_ids = await get_assistants_id(
            session,
            and_filters=[Assistant.division_id == division_id],
        )
    elif user.role == RoleTypes.ASSISTANT_ADMIN:
        assistant_ids = [assistant.id for assistant in user.assistants]
    elif user.role == RoleTypes.DIVISION_ADMIN:
        assistant_ids = await get_assistants_id(
            session,
            and_filters=[Assistant.division_id == user.division_id],
        )

    if assistant_ids:
        query = query.join(Conversation, Message.conversation_id == Conversation.id).where(
            Conversation.meta["assistant"].astext.in_(assistant_ids)
        )
        first_message_query = first_message_query.join(Conversation, Message.conversation_id == Conversation.id).where(
            Conversation.meta["assistant"].astext.in_(assistant_ids)
        )

    query = query.where(
        and_(
            Message.created_at >= after if after else True,
            Message.created_at <= before if before else True,
        )
    )

    result = await session.execute(query)
    total_messages = result.scalar()

    # Query to get the first and last message timestamps
    first_message_result = await session.execute(first_message_query)
    first_message_date = first_message_result.scalar()
    if not first_message_date:
        return 0.0

    first_message_date = after if after else first_message_date
    last_message_date = before if before else datetime.now()
    total_days = (last_message_date - first_message_date).days + 1
    total_relevant_days = sum(
        1 for day in range(total_days) if (first_message_date + timedelta(days=day)).weekday() in day_range
    )
    if total_relevant_days == 0:
        return 0.0
    return total_messages / total_relevant_days


@router.get("/weekday-average-messages/", response_model=float, dependencies=[Depends(is_not_general_user)])
async def get_weekday_average_messages(
    session: AsyncSession = Depends(get_session),
    user: User = Depends(get_or_create_auth_user),
    after: str = Query(None, description="Return results after this date (ISO 8601 format)"),
    before: str = Query(
        default_factory=lambda: datetime.now().isoformat(), description="Return results before this date"
    ),
    division_id: str | None = Query(None, description="Filter by division id"),
) -> float:
    """
    Calculate the average number of messages sent on weekdays.

    The average is computed based on the specified date range and the user's role.

    Args:
        session (AsyncSession): The database session.
        user (User): The authenticated user object, automatically provided by the `get_or_create_auth_user` dependency.
        after (str): Optional date to filter results after this date.
        before (str): Optional date to filter results before this date.

    Returns:
        float: The average number of messages sent on weekdays.

    Raises:
        HTTPException: If the user is not authorized to access this information.
    """
    return await calculate_average_messages(
        session, user, day_range=[0, 1, 2, 3, 4], after=after, before=before, division_id=division_id
    )


@router.get("/weekend-average-messages/", response_model=float, dependencies=[Depends(is_not_general_user)])
async def get_weekend_average_messages(
    session: AsyncSession = Depends(get_session),
    user: User = Depends(get_or_create_auth_user),
    after: str = Query(None, description="Return results after this date (ISO 8601 format)"),
    before: str = Query(
        default_factory=lambda: datetime.now().isoformat(), description="Return results before this date"
    ),
    division_id: str | None = Query(None, description="Filter by division id"),
) -> float:
    """
    Calculate the average number of messages sent on weekends.

    The average is computed based on the specified date range and the user's role.

    Args:
        session (AsyncSession): The database session.
        user (User): The authenticated user object, automatically provided by the `get_or_create_auth_user` dependency.
        after (str): Optional date to filter results after this date.
        before (str): Optional date to filter results before this date.

    Returns:
        float: The average number of messages sent on weekends.

    Raises:
        HTTPException: If the user is not authorized to access this information.
    """
    return await calculate_average_messages(
        session, user, day_range=[5, 6], after=after, before=before, division_id=division_id
    )


@router.get("/active-users/", response_model=UserCount, dependencies=[Depends(is_not_general_user)])
async def get_active_users(
    session: AsyncSession = Depends(get_session),
    user: User = Depends(get_or_create_auth_user),
    after: str = Query(None, description="Return results after this date (ISO 8601 format)"),
    before: str = Query(
        default_factory=lambda: datetime.now().isoformat(), description="Return results before this date"
    ),
    division_id: str | None = Query(None, description="Filter by division id"),
) -> UserCount:
    """
    Get the count of active users based on message activity within a specified date range.

    The count is filtered based on the user's role and the specified date range.

    Args:
        session (AsyncSession): The database session.
        user (User): The authenticated user object, automatically provided by the `get_or_create_auth_user` dependency.
        after (str): Optional date to filter results after this date.
        before (str): Optional date to filter results before this date.

    Returns:
        dict: A dictionary containing the count of active users.

    Raises:
        HTTPException: If the user is not authorized to access this information.
    """
    after = parse(after).replace(tzinfo=None) if after else None
    before = parse(before).replace(tzinfo=None)
    assistant_ids = []

    query = (
        select(User)
        .distinct(User.id)
        .options(selectinload(User.assistants))
        .outerjoin(Conversation, User.id == Conversation.user_id)
        .outerjoin(Message, Conversation.id == Message.conversation_id)
    )

    if user.role == RoleTypes.ADMIN and division_id:
        assistant_ids = await get_assistants_id(
            session,
            and_filters=[Assistant.division_id == division_id],
        )
    elif user.role == RoleTypes.ASSISTANT_ADMIN:
        assistant_ids = [assistant.id for assistant in user.assistants]
    elif user.role == RoleTypes.DIVISION_ADMIN:
        assistant_ids = await get_assistants_id(
            session,
            and_filters=[Assistant.division_id == user.division_id],
        )

    if assistant_ids:
        query = query.where(Conversation.meta["assistant"].astext.in_(assistant_ids))

    active_users_query = query.where(
        and_(
            Message.created_at >= after if after else True,
            Message.created_at <= before if before else True,
        )
    )

    total_users = await session.execute(select(func.count()).select_from(query))
    active_users = await session.execute(select(func.count()).select_from(active_users_query))

    return {"total": total_users.scalar(), "active": active_users.scalar()}
