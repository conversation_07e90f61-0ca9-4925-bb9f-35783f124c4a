from collections import Counter

from api.utils.assistant import get_assistants_id
from assistants import assistants
from auth.cognito import get_or_create_auth_user, is_not_general_user
from db.models import Assistant, AssistantAnalytics, Conversation, Message, User
from db.session import get_session
from fastapi import APIRouter, Depends, Query
from fastapi.responses import JSONResponse
from schema.enums import RoleTypes
from sqlalchemy import func, select
from sqlalchemy.ext.asyncio import AsyncSession
from tasks.message_analytics import get_word_cloud

router = APIRouter()


@router.get("/message_analytics", dependencies=[Depends(is_not_general_user)])
async def get_assistant_messages(
    assistant_id: str = Query(..., description="Filter by assistant ID"),
    session: AsyncSession = Depends(get_session),
    user: User = Depends(get_or_create_auth_user),
    division_id: str | None = Query(None, description="Filter by division ID"),
):
    try:
        division_assistants = []
        if user.role == RoleTypes.ADMIN and division_id:
            division_assistants = await get_assistants_id(
                session,
                and_filters=[Assistant.division_id == division_id],
            )
        elif user.role == RoleTypes.DIVISION_ADMIN or (user.role == RoleTypes.ASSISTANT_ADMIN and user.division_id):
            division_assistants = await get_assistants_id(
                session,
                and_filters=[Assistant.division_id == user.division_id],
            )

        if assistant_id not in division_assistants and division_id:
            return JSONResponse(
                content={"error": "You have selected an assistant that does not belong to provided division."},
                status_code=403,
            )
        elif assistant_id not in division_assistants and user.division_id:
            return JSONResponse(
                content={"error": "You do not have access to this assistant."},
                status_code=403,
            )

        analytics_query = (
            select(AssistantAnalytics)
            .where(AssistantAnalytics.assistant_id == assistant_id)
            .order_by(AssistantAnalytics.message_count.desc())
        )
        chunk_analytics = (await session.execute(analytics_query)).scalars().all()

        message_query = (
            select(
                Conversation.title,
                Message.content.label("message"),
                Message.created_at.label("message_created_at"),
            )
            .select_from(Conversation)
            .join(Message, Conversation.id == Message.conversation_id)
            .join(User, User.id == Conversation.user_id)
            .where(Message.role == "user")
            .where(Conversation.meta["assistant"].astext == assistant_id)
            .where(Message.content.isnot(None))
            .where(Message.created_at.isnot(None))
            .order_by(Message.created_at.desc())
        )
        messages = await session.execute(message_query)
        messages_data = messages.fetchall()

        concatenated_message = " ".join(item["text"] for entry in messages_data for item in entry[1] if "text" in item)

        uni_word_cloud, bi_word_cloud = get_word_cloud(concatenated_message)

        title_counts = Counter(msg.title for msg in messages_data)
        top_10_titles = title_counts.most_common(10)

        data = {
            "conversation_titles": top_10_titles,
            "uni_word_cloud": uni_word_cloud,
            "bi_word_cloud": bi_word_cloud,
            "chunk_analytics": chunk_analytics,
        }
        return data

    except Exception as e:
        return JSONResponse(content={"error": str(e)}, status_code=500)


@router.get("/message_per_assistant", response_model=dict[str, int], dependencies=[Depends(is_not_general_user)])
async def get_assistant_message_counts(
    session: AsyncSession = Depends(get_session),
    user: User = Depends(get_or_create_auth_user),
    division_id: str | None = Query(None, description="Filter by division ID"),
) -> dict[str, int]:
    """
    Fetches the count of messages per assistant for the authenticated user.

    Args:
        session (AsyncSession): The SQLAlchemy session dependency.
        user (User): The authenticated user.

    Returns:
        Dict[str, int]: A dictionary mapping assistant display names to message counts, including "Others."
    """
    try:
        division_assistants = []
        if user.role == RoleTypes.ADMIN and division_id:
            division_assistants = await get_assistants_id(
                session,
                and_filters=[Assistant.division_id == division_id],
            )
        elif user.role == RoleTypes.DIVISION_ADMIN:
            division_assistants = await get_assistants_id(
                session,
                and_filters=[Assistant.division_id == user.division_id],
            )

        assistant_id_expr = Conversation.meta["assistant"].astext
        query = (
            select(
                assistant_id_expr.label("assistant_id"),
                func.count(Message.id).label("message_count"),
            )
            .select_from(Message)
            .join(Conversation, Message.conversation_id == Conversation.id)
            .where(assistant_id_expr.isnot(None))
            .group_by(assistant_id_expr)
        )
        if division_assistants:
            query = query.where(assistant_id_expr.in_(division_assistants))

        result = await session.execute(query)
        data = result.mappings().all()
        assistant_message_counts = {row["assistant_id"]: row["message_count"] for row in data}

        assistant_query = select(Assistant.id.label("assistant_id"), Assistant.display_name.label("display_name"))
        result = await session.execute(assistant_query)
        assistant_mapping = {row.assistant_id: row.display_name for row in result.fetchall()}

        for assistant in assistants:
            assistant_mapping[assistant.id] = assistant.display_name

        filtered_counts = {key: value for key, value in assistant_message_counts.items() if key in assistant_mapping}
        others_count = sum(value for key, value in assistant_message_counts.items() if key not in assistant_mapping)

        filtered_counts["Others"] = others_count

        assistants_analytics = {
            assistant_mapping.get(assistant_id, assistant_id): count for assistant_id, count in filtered_counts.items()
        }

        return assistants_analytics

    except Exception as e:
        return JSONResponse(content={"error": str(e)}, status_code=500)
