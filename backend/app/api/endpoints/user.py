from collections.abc import Sequence
from uuid import UUID

from api.utils.pagination import page_number_pagination
from auth.cognito import get_or_create_auth_user, is_admin_user, is_not_general_user, role_checker
from db.models import Assistant, AssistantAccess, User
from db.session import get_session
from fastapi import APIRouter, Depends, HTTPException, Query, status
from loguru import logger
from schema.enums import RoleTypes
from schema.user import User<PERSON>cc<PERSON>, User<PERSON>ist, UserRead, UserSelfUpdate, UserUpdate
from sqlalchemy import func, or_, select
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

router = APIRouter()


@router.get("/me/", response_model=UserRead)
async def get_self(user: User = Depends(get_or_create_auth_user)):
    """
    Retrieve the currently authenticated user's details.

    The user is identified through the `get_or_create_auth_user` dependency.

    Args:
        user (User): The authenticated user object, automatically provided by the `get_or_create_auth_user` dependency.

    Returns:
        UserRead: A pydantic model containing the details of the user.
    """

    return user


@router.put("/me/", response_model=UserRead)
async def update_self(
    user_update: UserSelfUpdate,
    user: User = Depends(get_or_create_auth_user),
    session: AsyncSession = Depends(get_session),
) -> UserRead:
    """
    Update the current user's information.

    The changes are applied to the fields provided in the `user_update` model.

    Args:
        user_update (UserSelfUpdate): A pydantic model containing the fields to update.
        user (User): The authenticated user object, provided by the `get_or_create_auth_user` dependency.
        session (AsyncSession): The SQLAlchemy asynchronous session, provided by the `get_session` dependency.

    Returns:
        UserRead: A pydantic model with the updated user details.

    Raises:
        HTTPException: If there is a database error, an HTTP 500 error is raised.
    """
    try:
        updates = user_update.model_dump(exclude_unset=True)

        for k, v in updates.items():
            setattr(user, k, v)

        session.add(user)
        await session.commit()

        return UserRead.model_validate(user, from_attributes=True)
    except SQLAlchemyError as e:
        logger.error(f"Database error: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Database error")


@router.get("/access/", response_model=UserAccess)
async def get_user_access(
    user: User = Depends(get_or_create_auth_user),
    session: AsyncSession = Depends(get_session),
) -> UserAccess:
    """
    Retrieve the access levels of the authenticated user.

    Admin users have access to all assistants, while general users have limited access.

    Args:
        user (User): The authenticated user object, provided by the `get_or_create_auth_user` dependency.
        session (AsyncSession): The SQLAlchemy asynchronous session, provided by the `get_session` dependency.

    Returns:
        UserAccess: A pydantic model containing the user's role and access permissions.
    """
    if user.role == "general":
        return UserAccess(role=RoleTypes.GENERAL, admin_access=False, access=[])

    assistant_access_query = select(AssistantAccess).where(AssistantAccess.role == user.role)
    assistant_access = await session.execute(assistant_access_query)
    assistant_ids = (
        [access.assistant_id for access in assistant_access.scalar_one_or_none()]
        if assistant_access.scalar_one_or_none()
        else []
    )

    return UserAccess(role=user.role, admin_access=True, access=assistant_ids)


async def get_user_or_404(id: UUID, session: AsyncSession = Depends(get_session)) -> User:
    """
    Retrieve a user by ID or raise a 404 error if not found.

    Args:
        id (UUID): The unique identifier of the user.
        session (AsyncSession): The SQLAlchemy asynchronous session, provided by the `get_session` dependency.

    Returns:
        User: The user object corresponding to the given ID.

    Raises:
        HTTPException: If no user is found with the provided ID, a 404 error is raised.
    """

    query = select(User).where(User.id == id).options(selectinload(User.assistants), selectinload(User.division))
    result = await session.execute(query)
    user = result.scalar_one_or_none()

    if user is None:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="User not found")

    return user


@router.get("/assistant/", response_model=UserList, dependencies=[Depends(is_not_general_user)])
async def assistant_list_users(
    pagination: tuple[int, int] = Depends(page_number_pagination),
    session: AsyncSession = Depends(get_session),
    search: str = Query(None, description="Filter by name or email"),
    role: list[RoleTypes] = Query(None, description="Filter by multiple roles"),
) -> Sequence[User]:
    """
    Retrieve a paginated list of users with optional filtering by name, email, and role.

    This endpoint is restricted to admin users only. It returns a paginated list of users,
    optionally filtered by name, email, or role.

    Args:
        pagination (tuple[int, int]): A tuple containing the page number and page size, provided by
        `page_number_pagination`.
        session (AsyncSession): The SQLAlchemy asynchronous session, provided by the `get_session` dependency.
        search (str, optional): A string to filter users by name or email.
        role (RoleTypes, optional): A string to filter users by role.

    Returns:
        Sequence[User]: A paginated list of users matching the filters.
    """
    page, size = pagination
    select_query = select(User).options(selectinload(User.assistants)).options(selectinload(User.division))
    if search:
        select_query = select_query.where(
            or_(
                User.search_tsvector.op("@@")(func.plainto_tsquery("english", f"{search}:*")),
                User.name.ilike(f"%{search}%"),
                User.email.ilike(f"%{search}%"),
            )
        )
    if role:
        for r in role:
            if r is RoleTypes.ADMIN:
                raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="query role cannot contain admin")
    else:
        role = [RoleTypes.GENERAL, RoleTypes.ASSISTANT_ADMIN]
    select_query = select_query.where(User.role.in_(role))
    total_users = await session.execute(select(func.count()).select_from(select_query))
    count = total_users.scalar()

    # Sort by alphabetical order (User.name)
    select_query = select_query.order_by(User.name.asc())

    select_query = select_query.offset((page - 1) * size).limit(size)
    result = await session.execute(select_query)

    return {
        "count": count,
        "data": result.scalars().all(),
    }


@router.get("/{id}/", response_model=UserRead)
async def get_user(
    _: User = Depends(role_checker([RoleTypes.ADMIN, RoleTypes.DIVISION_ADMIN])), user: User = Depends(get_user_or_404)
) -> User:
    """
    Retrieve a specific user's details by ID.

    This endpoint is restricted to admin users only. It returns the information of a user identified by their ID.

    Args:
        user (User): The user object, provided by the `get_user_or_404` dependency.

    Returns:
        UserRead: A pydantic model containing the user's details.
    """
    return user


async def list_users(
    logged_in_user: User,
    pagination: tuple[int, int],
    session: AsyncSession,
    search: str = None,
    role: list[RoleTypes] = None,
    division_id: str | None = None,
    impersonation: bool = False,
) -> Sequence[User]:
    """
    Retrieve a paginated list of users with optional filtering by name, email, and role.

    This endpoint is restricted to admin users only. It returns a paginated list of users,
    optionally filtered by name, email, or role.

    Args:
        pagination (tuple[int, int]): A tuple containing the page number and page size, provided by
        `page_number_pagination`.
        session (AsyncSession): The SQLAlchemy asynchronous session, provided by the `get_session` dependency.
        search (str, optional): A string to filter users by name or email.
        role (RoleTypes, optional): A string to filter users by role.
        division (Division): The division object, provided by the `get_division_or_404` dependency.
        logged_in_user (User): The currently authenticated user, provided by the `role_checker` dependency.

    Returns:
        Sequence[User]: A paginated list of users matching the filters.
    """
    page, size = pagination

    select_query = select(User).options(selectinload(User.assistants), selectinload(User.division))
    if search:
        select_query = select_query.where(
            or_(
                User.search_tsvector.op("@@")(func.plainto_tsquery("english", f"{search}:*")),
                User.name.ilike(f"%{search}%"),
                User.email.ilike(f"%{search}%"),
            )
        )
    if role:
        select_query = select_query.where(User.role.in_(role))

    if impersonation and logged_in_user.role == RoleTypes.ASSISTANT_ADMIN and logged_in_user.division:
        select_query = select_query.where(User.division == logged_in_user.division)

    if logged_in_user.role == RoleTypes.DIVISION_ADMIN:
        if logged_in_user.division:
            select_query = select_query.where(User.division == logged_in_user.division).where(
                User.role != RoleTypes.ADMIN
            )
        else:
            select_query = select_query.where(User.id == logged_in_user.id)

    if logged_in_user.role == RoleTypes.ADMIN and division_id:
        if impersonation:
            # Include users in the division OR admins
            select_query = select_query.where(or_(User.division_id == division_id, User.role == RoleTypes.ADMIN))
        else:
            select_query = select_query.where(User.division_id == division_id)

    total_users = await session.execute(select(func.count()).select_from(select_query))
    count = total_users.scalar()

    # Sort by alphabetical order (User.name)
    select_query = select_query.order_by(User.name.asc())

    select_query = select_query.offset((page - 1) * size).limit(size)
    result = await session.execute(select_query)

    return {
        "count": count,
        "data": result.scalars().all(),
    }


@router.get("/", response_model=UserList)
async def get_all_users(
    logged_in_user: User = Depends(role_checker([RoleTypes.ADMIN, RoleTypes.DIVISION_ADMIN])),
    pagination: tuple[int, int] = Depends(page_number_pagination),
    session: AsyncSession = Depends(get_session),
    search: str = Query(None, description="Filter by name or email"),
    role: list[RoleTypes] = Query(None, description="Filter by multiple roles"),
    division_id: str = Query(None, description="Filter by division id"),
) -> Sequence[User]:
    """
    Retrieve a paginated list of users with optional filtering by name, email, and role.

    This endpoint is restricted to admin users only. It returns a paginated list of users,
    optionally filtered by name, email, or role.

    Args:
        pagination (tuple[int, int]): A tuple containing the page number and page size, provided by
        `page_number_pagination`.
        session (AsyncSession): The SQLAlchemy asynchronous session, provided by the `get_session` dependency.
        search (str, optional): A string to filter users by name or email.
        role (RoleTypes, optional): A string to filter users by role.
        division (Division): The division object, provided by the `get_division_or_404` dependency.
        logged_in_user (User): The currently authenticated user, provided by the `role_checker` dependency.

    Returns:
        Sequence[User]: A paginated list of users matching the filters.
    """
    return await list_users(logged_in_user, pagination, session, search, role, division_id)


@router.patch("/", response_model=UserRead)
async def update_user(
    user_update: UserUpdate,
    logged_in_user: User = Depends(role_checker([RoleTypes.ADMIN, RoleTypes.DIVISION_ADMIN])),
    user: User = Depends(get_user_or_404),
    session: AsyncSession = Depends(get_session),
) -> User:
    """
    Update a specific user's details.

    This endpoint is restricted to admin users only. It allows updating user details,
    including their role and associated assistants.

    Args:
        user_update (UserUpdate): A pydantic model containing the fields to update.
        user (User): The user object, provided by the `get_user_or_404` dependency.
        session (AsyncSession): The SQLAlchemy asynchronous session, provided by the `get_session` dependency.
        logged_in_user (User): The currently authenticated user, provided by the `role_checker` dependency.

    Returns:
        UserRead: A pydantic model with the updated user details.

    Raises:
        HTTPException: If specified assistants are not found, or if there are other validation issues.
    """
    user_update_dict = user_update.model_dump(exclude_unset=True)
    assistant_ids = user_update_dict.pop("assistant_ids", None)
    user_update_role = user_update_dict.pop("role", None)
    division_admin_dict = user_update_dict.pop("division", None)  # noqa: F841

    if logged_in_user.role == RoleTypes.ADMIN:
        if user_update_role == RoleTypes.ADMIN:
            setattr(user, "role", RoleTypes.ADMIN)
            result = await session.execute(select(Assistant))
            user.assistants = result.scalars().all()

        elif user_update_role == RoleTypes.DIVISION_ADMIN:
            setattr(user, "role", RoleTypes.DIVISION_ADMIN)
            user_division = user.division_id
            # if division_admin_dict:
            #     setattr(user, "division_id", division_admin_dict)
            # TODO: Need to add user assistant according to division selection

            if user_division:
                division_assistants = await session.execute(
                    select(Assistant).where(Assistant.division_id == user_division)
                )
                user.assistants = division_assistants.scalars().all()
            else:
                division_assistants = await session.execute(
                    select(Assistant).where(Assistant.division_id == logged_in_user.division_id)
                )
                user.assistants = division_assistants.scalars().all()

        elif user_update_role == RoleTypes.GENERAL:
            setattr(user, "role", RoleTypes.GENERAL)
            # if division_admin_dict:
            #     setattr(user, "division_id", division_admin_dict)
            user.assistants = []

    elif logged_in_user.role == RoleTypes.DIVISION_ADMIN:
        if user_update_role == RoleTypes.GENERAL:
            setattr(user, "role", RoleTypes.GENERAL)
            setattr(user, "division_id", logged_in_user.division_id)
            user.assistants = []

    if assistant_ids and user.role not in [RoleTypes.ADMIN, RoleTypes.DIVISION_ADMIN]:
        query = select(Assistant).where(Assistant.id.in_(assistant_ids))
        result = await session.execute(query)
        assistants = result.scalars().all()
        if len(assistants) != len(assistant_ids):
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Assistants not found")

        user.assistants = assistants
        setattr(user, "role", RoleTypes.ASSISTANT_ADMIN)

        if user.role == RoleTypes.ASSISTANT_ADMIN and len(assistants) == 0:
            setattr(user, "role", RoleTypes.GENERAL)
    elif assistant_ids is not None and len(assistant_ids) == 0 and user.role == RoleTypes.ASSISTANT_ADMIN:
        setattr(user, "role", RoleTypes.GENERAL)
        user.assistants = []

    for k, v in user_update_dict.items():
        setattr(user, k, v)

    session.add(user)
    await session.commit()
    await session.refresh(user)

    return user


@router.delete("/{id}/", status_code=status.HTTP_204_NO_CONTENT, dependencies=[Depends(is_admin_user)])
async def delete_user(user: User = Depends(get_user_or_404), session: AsyncSession = Depends(get_session)):
    """
    Delete a specific user by ID.

    This endpoint is restricted to admin users only. It deletes the user identified by the given ID.

    Args:
        user (User): The user object, provided by the `get_user_or_404` dependency.
        session (AsyncSession): The SQLAlchemy asynchronous session, provided by the `get_session` dependency.

    Returns:
        None
    """
    await session.delete(user)
    await session.commit()
