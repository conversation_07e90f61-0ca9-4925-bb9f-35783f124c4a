import logging
import sys
from contextlib import asynccontextmanager

import sentry_sdk
from api import api_router
from api.endpoints.auth import router as auth_router
from api.startup import run_startup_events
from config import settings
from fastapi import FastAPI, HTTPException
from fastapi.exceptions import RequestValidationError
from loguru import logger
from middleware import RateLimitMiddleware, SelectiveGZipMiddleware
from sentry_sdk.integrations.fastapi import FastApiIntegration
from sentry_sdk.integrations.logging import LoggingIntegration
from starlette.middleware.cors import CORSMiddleware

logger.remove(1)
logger.add(
    sink=sys.stdout,
    level=settings.LOG_LEVEL,
)


@asynccontextmanager
async def lifespan(app: FastAPI):
    logger.info("Application Starting Up.. Running Automated Scripts...")
    await run_startup_events(app)
    yield
    logger.info("Application shutting down...")


# Disable docs in production
if settings.APP_ENV in ["local", "dev", "division"]:
    docs_url = f"{settings.API_STR}/docs"
    redoc_url = f"{settings.API_STR}/redoc"
    openapi_url = f"{settings.API_STR}/openapi.json"
else:
    docs_url, redoc_url, openapi_url = None, None, None

app = FastAPI(
    title=settings.PROJECT_NAME,
    docs_url=docs_url,
    redoc_url=redoc_url,
    openapi_url=openapi_url,
    openapi_version="3.0.0",
    lifespan=lifespan,
)

# Register rate limiting middleware before other middlewares for early rejection
app.add_middleware(RateLimitMiddleware)

if not settings.BACKEND_CORS_ORIGINS:
    allowed_origins = ["*"]
else:
    allowed_origins = [str(origin).rstrip("/") for origin in settings.BACKEND_CORS_ORIGINS]
app.add_middleware(
    CORSMiddleware,
    allow_origins=allowed_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
app.add_middleware(SelectiveGZipMiddleware)

if settings.APP_ENV != "local":

    def before_send(event, hint):
        if "exc_info" in hint:
            exc_type, exc_value, tb = hint["exc_info"]
            if isinstance(exc_value, HTTPException) and exc_value.status_code == 404:
                return None
            # Ignore RequestValidationError
            if isinstance(exc_value, RequestValidationError):
                return None
        return event

    sentry_sdk.init(
        dsn=settings.SENTRY_DSN,
        integrations=[
            FastApiIntegration(),
            LoggingIntegration(level=settings.LOG_LEVEL, event_level=logging.ERROR),
        ],
        environment=settings.SENTRY_ENVIRONMENT,
        traces_sample_rate=settings.SENTRY_TRACES_SAMPLE_RATE,
        before_send=before_send,
    )


@app.get("/", include_in_schema=False)
async def root():
    return {"app": settings.PROJECT_NAME}


app.include_router(api_router, prefix=settings.API_STR)
app.include_router(auth_router, prefix="/auth", tags=["Auth"])
