from enum import Enum


class RoleTypes(str, Enum):
    GENERAL = "general"
    ADMIN = "admin"
    DIVISION_ADMIN = "division_admin"
    ASSISTANT_ADMIN = "assistant_admin"


class DocumentTypes(str, Enum):
    FILE = "file"
    LINK = "link"
    TEXT = "text"
    SHAREPOINT = "sharepoint"
    IMAGE = "image"
    SHAREPOINT_IMAGE = "sharepoint_image"
    SHAREPOINT_PAGE = "sharepoint_page"


class AssistantTypes(str, Enum):
    GENERAL = "general"
    PERSONAL = "personal"
    CUSTOM = "custom"


class AssistantSubTypes(str, Enum):
    TOTALEXPERT = "totalexpert"
    ENCOMPASS = "encompass"
    SIDEKICK = "sidekick"
    ENCOMPASS_SALES = "encompass_sales"
    DOCUMENT_UNDERSTANDING = "document_understanding"
    RETRIEVAL = "retrieval"
    ANALYSIS = "analysis"
    MANN_ENCOMPASS = "mann_encompass"
    CAR_GURU = "car_guru"
    OUTLOOK = "outlook"
    IMAGE = "image"
    CALL_CAMPAIGN = "call_campaign"


class AssistantProvider(str, Enum):
    OPENAI = "openai"
    GEMINI = "gemini"
    ANTHROPIC = "anthropic"


class DataFieldFolder(str, Enum):
    LOAN = "loan_fields"
    CONTACT = "contact_fields"
    ACTIVITY = "activity_fields"
    CONTACT_NOTES = "contact_notes_fields"


class DocumentIndexStatus(str, Enum):
    PENDING = "pending"
    INDEXED = "indexed"
    FAILED = "failed"


class LoanOfficerMappingType(str, Enum):
    REFI_REPORT = "refi_report"
    OTHER_REPORT = "other_report"
    COMMON = "common"
    TOTALEXPERT_ADMIN = "totalexpert_admin"
    TOTALEXPERT_GENERAL = "totalexpert_general"


class WebsiteContentTypes(str, Enum):
    FULL_CONTENT = "full_content"
    SUMMARY = "summary"


class QuestionAnswerTypes(str, Enum):
    FAQ = "faq"
    FEEDBACK = "feedback"


class DataFieldTypes(str, Enum):
    TEXT = "text"
    DECIMALNUMBER = "decimalnumber"
    INTEGERNUMBER = "integernumber"
    DATE = "date"


class ContactNoteTypeIds(str, Enum):
    GENERAL_NOTE = "general note"
    TASK = "task"
    EMAIL_OPENED = "email opened"
    EMAIL_SENT = "email sent"
    PREQUALIFICATION = "prequalification"
    MESSAGE_RECEIVED = "message received"
    CONTACTED = "contacted"
    WELCOME_EMAIL_SENT = "welcome email sent"
    SMS_SENT = "sms sent"
    SMS_RECEIVED = "sms received"
    EMAIL_DELIVERED = "email delivered"
    EMAIL_UNSUBSCRIBED = "email unsubscribed"
    EMAIL_DELIVERY_FAILURE = "email delivery failure"
    EMAIL_LINK_CLICKED = "email link clicked"
    PRINT_MATERIAL_SENT = "print material sent"
    OPEN_HOUSE_REGISTRATION = "open house registration"
    EVENT_REGISTRATION = "event registration"
    CONTACT_ASSIGNED = "contact assigned"
    CONTACT_PASSED = "contact passed"
    CONTACT_SHARED = "contact shared"
    DRIP_CAMPAIGN_ENDED = "drip campaign ended"
    SPAM_COMPLAINT = "spam complaint"
    DRIP_CAMPAIGN_ADDED = "drip campaign added"
    CONTACT_ACCEPTED = "contact accepted"
    APPOINTMENT_SCHEDULED = "appointment scheduled"
    TRANSACTION_SCHEDULED = "transaction scheduled"
    DRIP_CAMPAIGN_DELETED = "drip campaign deleted"
    LISTING_ALERT_SENT = "listing alert sent"
    DRIP_CAMPAIGN_EMAIL_SENT = "drip campaign email sent"
    CONNECTION_INVITE_SENT = "connection invite sent"
    AUTO_CAMPAIGN_EMAIL_SENT = "auto campaign email sent"
    MOVEMENT_REGISTRATION = "movement registration"
    AUTO_CAMPAIGN_PRINT_ORDER_SENT = "auto campaign print order sent"
    TASK_COMPLETE_EMAIL_SENT = "task complete email sent"


class TotalExpertActivityType(str, Enum):
    EMAIL_PROCESSED = "ACTIVITY.EMAIL_PROCESSED"
    EMAIL_DELIVERED = "ACTIVITY.EMAIL_DELIVERED"
    JOURNEY_TRIGGERED = "ACTIVITY.LEAD_JOURNEY_TRIGGERED"


class ReadStatus(str, Enum):
    READ = "read"
    UNREAD = "unread"


class RefinanceType(str, Enum):
    CASHOUT = "cashout"
    RATE_TERM = "rate_term"
    PURCHASE = "purchase"


# TE Journey
class TEJourneyTypes(str, Enum):
    STANDARD = "Standard"
    RATE_TERM_REFI = "Rate Term Refi"
    CASHOUT_REFI = "Cashout Refi"


class EncompassTypes(str, Enum):
    PROD = "prod"
    DEV = "dev"
