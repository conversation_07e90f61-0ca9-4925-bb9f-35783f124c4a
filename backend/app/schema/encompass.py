from pydantic import BaseModel, Field

from .enums import RefinanceType


class Credentials(BaseModel):
    client_id: str
    client_secret: str
    username: str
    password: str
    instance_id: str


class MonthlyPaymentCalculationInput(BaseModel):
    loan_amount: float
    interest_rate: float
    loan_term: int


class MonthlyPaymentCalculationOutput(BaseModel):
    monthly_payment: float


class LoanOfficerBase(BaseModel):
    first_name: str
    last_name: str
    email: str
    loan_id: str


class LoanOfficersRead(LoanOfficerBase):
    pass


class ListLoanOfficers(BaseModel):
    count: int
    data: list[LoanOfficersRead]


class SalesReportSummary(BaseModel):
    loan_officer: str = Field(alias="Loan Officer")
    loan_officer_email: str = Field(alias="Loan Officer Email")
    number_of_loans: int = Field(alias="Number of Loans")
    loan_volume: str = Field(alias="Loan Volume")

    class Config:
        populate_by_name = True
        from_attributes = True


class AppraisedValueCalculationInput(BaseModel):
    street_address: str
    city: str
    state: str
    zip_code: str


class AppraisedValueCalculationOutput(BaseModel):
    appraised_value: float | str


class MaximumCashoutCalculationInput(BaseModel):
    loan_amount: float
    interest_rate: float
    monthly_payment: float
    closed_date: str
    appraised_value: float


class MaximumCashoutCalculationOutput(BaseModel):
    max_cashout: float


class LoanDetailsByRefiTypeInput(BaseModel):
    loan_number: str
    assistant_id: str
    refi_type: RefinanceType


class LoanDetailsByRefiTypeOutput(BaseModel):
    max_cashout: float | str = "N/A"
    appraised_value: float | str = "N/A"
    loan_details: dict | str = {}


class RemainingCashoutCalclationInput(BaseModel):
    max_cashout: float
    debt_amount: float


class RemainingCashoutCalclationOutput(BaseModel):
    remaining_cashout: float


class OpenHouseFlyerInput(BaseModel):
    property_image: str
    street_address: str
    city: str
    state: str
    zip_code: str
    purchase_price: float
    property_tax_rate: float | None = 1.0
    annual_hoi: float | None = 1500.0
    interest_rate: float
    point: float
    bedrooms_number: int
    bathrooms_number: int
    amenities: list[str]
    status: list[str]
    agent_name: str
    agent_cell_phone: str
    company_name: str
    company_office_phone: str
    source: str
    mls_number: str


class PreQualificationInput(BaseModel):
    loan_number: str
    assistant_id: str


class PreQualificationOutput(BaseModel):
    prequalification_details: dict


class PropertyLoanAmountInput(BaseModel):
    purchase_price: float
    down_payment_percent: float


class PropertyLoanAmountOutput(BaseModel):
    property_loan_amount: float


class FindLendingOpportunity(BaseModel):
    file_url: str


class DataPulseSummaryRequest(BaseModel):
    loid: str | None = None


class UpdateDataPulseReport(BaseModel):
    name: str | None = None
    description: str | None = None
    payload: dict | None = None
    primary_display_fields: list | None = None
    secondary_display_fields: list | None = None
    order: int | None = None
    ttl: int | None = None
    is_visible: bool | None = None


class DataPulseReport(UpdateDataPulseReport):
    id: str
    requires_condition_pending: bool
    requires_custom_date: bool
    assistant_id: str


class DataPulseUpdateResponse(DataPulseReport):
    updated_fields: list[str]
