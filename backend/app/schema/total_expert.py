from datetime import datetime
from uuid import UUID

from config import settings
from pydantic import BaseModel
from schema.enums import ContactNoteTypeIds, TEJourneyTypes


class ContactGroupBase(BaseModel):
    id: str | int
    group_name: str


class ContactGroupUpdate(BaseModel):
    group_name: str


class ContactGroupCreate(BaseModel):
    group_name: str


class ContactGroupCreateResponse(BaseModel):
    id: int
    created: str


class ContactGroupRead(ContactGroupBase):
    pass


class ListContactGroup(BaseModel):
    data: list[ContactGroupRead]


class CustomFieldBase(BaseModel):
    field_name: str | None = None
    value: str | None = None
    description: str | None = None


class CustomFieldUpdate(BaseModel):
    field_name: str | None = None
    value: str | None = None


class ContactBase(BaseModel):
    source: str = settings.CLIENT_NAME
    title: str | None = None
    first_name: str
    last_name: str
    email: str | None = None
    phone_cell: str | None = None
    email_work: str | None = None
    address: str | None = None
    address_2: str | None = None
    city: str | None = None
    state: str | None = None
    zip_code: str | None = None
    phone_office: str | None = None
    employer_name: str | None = None
    employer_address: str | None = None
    employer_address_2: str | None = None
    employer_city: str | None = None
    employer_state: str | None = None
    employer_zip: str | None = None
    license_number: str | None = None
    close_date: str | None = None
    external_id: str | None = None
    birthday: str | None = None
    contact_groups: list[ContactGroupBase] | None = None
    custom: list[CustomFieldBase] | None = None


class CustomFieldRead(BaseModel):
    fields: list[str]


class ContactUpdate(BaseModel):
    contact_id: list[str | int] | str | int
    source: str | None = None
    title: str | None = None
    first_name: str | None = None
    last_name: str | None = None
    email: str | None = None
    phone_cell: str | None = None
    email_work: str | None = None
    address: str | None = None
    address_2: str | None = None
    city: str | None = None
    state: str | None = None
    zip_code: str | None = None
    phone_office: str | None = None
    employer_name: str | None = None
    employer_address: str | None = None
    employer_address_2: str | None = None
    employer_city: str | None = None
    employer_state: str | None = None
    employer_zip: str | None = None
    license_number: str | None = None
    close_date: str | None = None
    external_id: str | None = None
    birthday: str | None = None
    ok_to_mail: bool | None = None
    ok_to_email: bool | None = None
    ok_to_call: bool | None = None
    remove_contact_groups: list[ContactGroupUpdate] | None = None
    contact_groups: list[ContactGroupUpdate] | None = None
    custom_fields: list[CustomFieldUpdate] | None = None


class ContactAdd(ContactBase):
    ok_to_mail: bool = True
    ok_to_email: bool = True
    ok_to_call: bool = True


class ContactRead(ContactBase):
    id: str | int


class ListContacts(BaseModel):
    count: int
    data: list[ContactRead]


class ContactNoteBase(BaseModel):
    title: str
    note: str
    note_type: ContactNoteTypeIds = ContactNoteTypeIds.GENERAL_NOTE


class ContactNoteAdd(ContactNoteBase):
    pass


class ContactNoteUpdate(BaseModel):
    title: str | None = None
    note: str | None = None
    note_type: ContactNoteTypeIds | None = None


class InsightContactRead(BaseModel):
    id: str | int
    first_name: str
    last_name: str
    email: str | None = None
    phone_cell: str | None = None


class InsightAttributes(BaseModel):
    field_name: str
    field_value: str | float | int


class InsightsBase(BaseModel):
    id: str | int
    attributes: list[InsightAttributes] | None = None
    description: str
    expires_at: str
    insight_date: str
    contact: InsightContactRead
    insight_type: str | None = None


class InsightsRead(InsightsBase):
    pass


class ListInsights(BaseModel):
    count: int
    data: list[InsightsRead]


class InsightTypeBase(BaseModel):
    id: str
    name: str
    type: str
    is_published: bool
    created_at: datetime
    updated_at: datetime
    meta: dict | None = None


class InsightTypeRead(BaseModel):
    data: list[InsightTypeBase]


class InsightTypeUpdate(BaseModel):
    is_published: bool | None = None


# Journeys
class BaseJourney(BaseModel):
    name: str
    description: str | None
    fields: list[str] | None
    blacklisted_words: list[str] | None
    type: TEJourneyTypes
    is_published: bool = False
    custom_field_mapping: dict | None
    is_custom: bool = False


class JourneyRead(BaseJourney):
    id: str
    assistant_id: str
    created_at: datetime
    updated_at: datetime
    meta: dict | None = None


class ListJourneys(BaseModel):
    data: list[JourneyRead]
    count: int


class JourneyUpdate(BaseModel):
    description: str | None = None
    fields: list[str] | None = None
    blacklisted_words: list[str] | None = None
    type: TEJourneyTypes | None = None
    custom_field_mapping: dict | None = None
    is_published: bool | None = None


class OauthCallback(BaseModel):
    code: str


class ImpersonateUserResponse(BaseModel):
    name: str
    email: str
    id: UUID


class ImpersonatedUserWithStatus(BaseModel):
    user: ImpersonateUserResponse
    active: bool
    oauth_exists: bool = False
    auth_url: str | None = None


class ListImpersonateUserResponse(BaseModel):
    count: int
    data: list[ImpersonatedUserWithStatus]


class ListUserForImpersonation(BaseModel):
    count: int
    data: list[ImpersonateUserResponse]
