import json
from datetime import datetime

from pydantic import BaseModel, ConfigDict, model_validator


class Transcription(BaseModel):
    text: str


class ChatInput(BaseModel):
    message: str
    image_url: str | None = None
    file_urls: list[str] | None = None
    assistant_id: str
    role: str = "user"
    web_search: bool | None = False

    @model_validator(mode="before")
    @classmethod
    def validate_to_json(cls, value):
        if isinstance(value, str):
            return cls(**json.loads(value))
        return value


class MessageOutput(BaseModel):
    id: str
    content: list[dict]
    attachments: list[dict] | None = None
    role: str = "assistant"
    created_at: datetime
    meta: dict | None = None

    model_config = ConfigDict(from_attributes=True)


class ConversationOutput(BaseModel):
    id: str
    title: str | None = None
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)


class ChatOutput(BaseModel):
    conversation: ConversationOutput
    user_message: MessageOutput
    assistant_message: MessageOutput
