from datetime import datetime

from pydantic import BaseModel, ConfigDict, EmailStr, field_validator
from utils.sanitize import sanitize_html

from .enums import <PERSON><PERSON><PERSON><PERSON>, AssistantSubTypes, AssistantTypes, RoleTypes


class AssistantBase(BaseModel):
    name: str
    display_name: str
    description: str | None = None
    instructions: str | None = None
    grounding_instructions: str | None = None
    disclaimer: str | None = None
    conversation_starters: list[str] | None = None
    is_published: bool = False
    categories: list[str] | None = None
    is_category_filtered: bool = False
    enable_hard_filter: bool = False
    enable_web_search: bool = False
    is_private: bool = False
    type: str = AssistantTypes.GENERAL
    sub_type: AssistantSubTypes | None = None
    provider: str = AssistantProvider.OPENAI
    prompts: list[str] | None = None
    contact_emails: list[EmailStr] | None = None
    enable_help_button: bool = False
    division_id: str | None = None

    @field_validator("description", "instructions", "grounding_instructions", "disclaimer", mode="before")
    @classmethod
    def sanitize_text_fields(cls, value: str | None) -> str | None:
        return sanitize_html(value)

    model_config = ConfigDict(from_attributes=True)


class AssistantCreate(AssistantBase):
    encompass_credential_id: str | None = None


class AssistantRead(AssistantBase):
    id: str
    created_at: datetime
    updated_at: datetime
    display_order: int
    meta: dict | None


class AssistantUpdate(BaseModel):
    name: str | None = None
    display_name: str | None = None
    description: str | None = None
    instructions: str | None = None
    grounding_instructions: str | None = None
    disclaimer: str | None = None
    conversation_starters: list[str] | None = None
    is_published: bool | None = None
    categories: list[str] | None = None
    is_category_filtered: bool | None = None
    enable_hard_filter: bool | None = None
    enable_web_search: bool | None = None
    display_order: int | None = None
    is_private: bool | None = None
    meta: dict | None = None
    type: str | None = None
    sub_type: str | None = None
    provider: str | None = None
    prompts: list[str] | None = None
    contact_emails: list[EmailStr] | None = None
    enable_help_button: bool | None = None

    @field_validator("description", "instructions", "grounding_instructions", "disclaimer", mode="before")
    @classmethod
    def sanitize_text_fields(cls, value: str | None) -> str | None:
        return sanitize_html(value)


class AssistantFileUpload(BaseModel):
    file_name: str


class AssistantAccessBase(BaseModel):
    role: RoleTypes
    assistant_id: str


class AssistantAccessCreate(AssistantAccessBase):
    pass


class AssistantAccessRead(AssistantAccessBase):
    id: str
    created_at: datetime
    updated_at: datetime


class AssistantAccessUpdate(BaseModel):
    role: RoleTypes | None = None
    assistant_id: str | None = None
