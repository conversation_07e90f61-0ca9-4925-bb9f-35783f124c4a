from datetime import datetime
from uuid import UUID

from pydantic import BaseModel, ConfigDict


class FeedbackBase(BaseModel):
    user_rating: bool
    feedback_text: str | None = None


class FeedbackCreate(FeedbackBase):
    message_id: str


class FeedbackUpdate(FeedbackBase):
    pass


class FeedbackRead(FeedbackBase):
    id: UUID
    message_id: str
    created_at: datetime
    updated_at: datetime
    meta: dict | None = None

    model_config = ConfigDict(from_attributes=True)
