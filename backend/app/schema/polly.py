from enum import Enum

from pydantic import BaseModel


class Citizenship(Enum):
    NONE = "None"
    FOREIGN_NATIONAL = "ForeignNational"
    NON_PERMANENT_RESIDENT_ALIEN = "NonPermanentResidentAlien"
    PERMANENT_RESIDENT_ALIEN = "PermanentResidentAlien"
    US_CITIZEN = "USCitizen"
    US_CITIZEN_ABROAD = "USCitizenAbroad"


class Purpose(Enum):
    PURCHASE = "Purchase"
    REFINANCE = "Refinance"
    CONSTRUCTION = "Construction"
    CONSTRUCTION_PERM = "ConstructionPerm"
    OTHER = "Other"
    NO_CASH_OUT_REFINANCE = "NoCashOutRefinance"
    CASH_OUT_REFINANCE = "CashOutRefinance"


class Impounds(Enum):
    NONE = "None"
    PARTIAL = "Partial"
    FULL = "Full"


class AUS(Enum):
    NONE = "None"
    MANUAL = "Manual"
    DU = "DU"
    LP = "LP"
    OTHER = "Other"
    NOT_SPECIFIED = "NotSpecified"


class PropertyType(Enum):
    SFR = "SFR"
    CONDOMINIUM = "Condominium"
    PUD = "PUD"
    MOBILE = "Mobile"
    TWO_FOUR_UNIT = "TwoFourUnit"
    COOPERATIVE = "Cooperative"
    TOWNHOME = "Townhome"
    MULTIFAMILY = "Multifamily"
    COMMERCIAL = "Commercial"
    MIXED_USE = "MixedUse"
    FARM = "Farm"
    HOME_AND_BUSINESS = "HomeAndBusiness"
    LAND = "Land"
    MANUFACTURED_SINGLE_WIDE = "ManufacturedSingleWide"
    MANUFACTURED_DOUBLE_WIDE = "ManufacturedDoubleWide"


class Occupancy(Enum):
    PRIMARY_RESIDENCE = "PrimaryResidence"
    SECOND_HOME = "SecondHome"
    INVESTMENT_PROPERTY = "InvestmentProperty"


class LoanType(Enum):
    NONE = "None"
    CONVENTIONAL = "Conventional"
    FHA = "FHA"
    VA = "VA"
    USDA = "USDA"
    JUMBO = "Jumbo"
    NON_QM = "NonQM"
    HELOC = "HELOC"


class Amortization(Enum):
    FIXED = "Fixed"
    ARM = "ARM"
    BALLOON = "Balloon"


class Borrower(BaseModel):
    fico: int
    citizenship: str


class Loan(BaseModel):
    amount: float
    purpose: str | None = "Purchase"
    ltv: float
    impounds: str
    aus: str


class Property(BaseModel):
    propertyType: str
    occupancy: str
    state: str
    county: str
    stateFipsCode: str = ""
    countyFipsCode: str = ""


class Search(BaseModel):
    desiredPrice: float = 0
    desiredLockPeriod: int | None = 30
    loanType: str = None
    loanTerms: int = None
    armType: str = "Fixed"


class PricingRequestBody(BaseModel):
    loan: Loan
    property: Property
    borrower: Borrower
    search: Search
    branch: str | None = None


class Price(BaseModel):
    rate: float
    price: float
    apr: float
    lockPeriod: int
    investor: str
    discount: float
    principalAndInterest: float


class PricingRequestResponse(BaseModel):
    id: str
    code: str
    amortizationTermMonths: int
    amortization: Amortization
    loanType: LoanType
    ltv: float
    name: str
    prices: Price


class PricingRequestResponseModel(BaseModel):
    result: list[PricingRequestResponse]
    count: int
