from pydantic import BaseModel, field_validator
from schema.enums import EncompassTypes
from utils.sanitize import sanitize_html


class DivisionBase(BaseModel):
    name: str
    domain: str
    description: str | None = None
    logo: str | None = None
    avatar: str | None = None
    primary_color: str | None = None
    secondary_color: str | None = None

    @field_validator(
        "name", "domain", "description", "logo", "avatar", "primary_color", "secondary_color", mode="before"
    )
    @classmethod
    def sanitize_text_fields(cls, value: str | None) -> str | None:
        return sanitize_html(value)


class DivisionRead(DivisionBase):
    id: str


class EncompassCredentialsCreate(BaseModel):
    name: str
    encompass_client_id: str | None = None
    encompass_client_secret: str | None = None
    encompass_password: str | None = None
    encompass_username: str | None = None
    encompass_instance_id: str | None = None
    encompass_type: str = EncompassTypes.PROD
    enabled: bool = False


class EncompassCredentialsUpdate(BaseModel):
    name: str | None = None
    encompass_client_id: str | None = None
    encompass_client_secret: str | None = None
    encompass_password: str | None = None
    encompass_username: str | None = None
    encompass_instance_id: str | None = None
    encompass_type: str | None = None
    enabled: bool | None = None


class DivisionCredentialsCreate(BaseModel):
    pinecone_index: str
    pinecone_host: str
    aws_storage_bucket_name: str
    total_expert_client_id: str | None = None
    total_expert_client_secret: str | None = None
    encompass_credentials: list[EncompassCredentialsCreate] | None = None
    enabled_total_expert: bool = False
    enabled_personal_assistant: bool = False
    enabled_sales_assistant: bool = False


class DivisionCredentialsUpdate(BaseModel):
    pinecone_index: str | None = None
    pinecone_host: str | None = None
    aws_storage_bucket_name: str | None = None
    total_expert_client_id: str | None = None
    total_expert_client_secret: str | None = None
    enabled_total_expert: bool | None = None
    enabled_personal_assistant: bool | None = None
    enabled_sales_assistant: bool | None = None
    encompass_credentials: list[EncompassCredentialsCreate] | None = None


class DivisionUpdate(DivisionBase):
    name: str | None = None
    domain: str | None = None
    description: str | None = None
    logo: str | None = None
    avatar: str | None = None
    primary_color: str | None = None
    secondary_color: str | None = None
    credentials: DivisionCredentialsUpdate | None = None

    @field_validator(
        "name", "domain", "description", "logo", "avatar", "primary_color", "secondary_color", mode="before"
    )
    @classmethod
    def sanitize_text_fields(cls, value: str | None) -> str | None:
        return sanitize_html(value)


class DivisionCreate(DivisionBase):
    credentials: DivisionCredentialsCreate | None = None
