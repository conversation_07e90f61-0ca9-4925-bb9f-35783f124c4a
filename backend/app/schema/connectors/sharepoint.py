from typing import Literal

from pydantic import BaseModel
from schema.enums import DocumentTypes
from schema.resources.document import DocumentRead, SharepointFolderRead


class FolderItem(BaseModel):
    id: str
    name: str
    type: Literal["folder", "file"]
    createdBy: dict | None = None
    createdDateTime: str | None = None
    lastModifiedBy: dict | None = None
    lastModifiedDateTime: str | None = None
    mimeType: str | None = None
    uri: str | None = None
    fullpath: str | None = None
    is_indexed: bool = False
    is_modified: bool = False
    site_id: str | None = None
    drive_id: str | None = None
    children: list["FolderItem"] | None = []
    document: DocumentRead | SharepointFolderRead | None = None
    size: int | None = None


class FileItem(BaseModel):
    id: str
    name: str
    fullpath: str
    mimeType: str
    createdDateTime: str
    lastModifiedDateTime: str
    site_id: str
    site_name: str
    drive_id: str
    folder_id: str | None = None


class FolderInput(BaseModel):
    id: str
    site_id: str
    site_name: str
    drive_id: str
    size: int | None = None
    createdDateTime: str | None = None
    lastModifiedDateTime: str | None = None


class BatchUploadRequest(BaseModel):
    files: list[FileItem] | None = None
    folders: list[FolderInput] | None = None
    categories: list[str] | None
    type: DocumentTypes = DocumentTypes.SHAREPOINT


class BatchDeleteRequest(BaseModel):
    folders: list[FolderInput] | None = None
    doc_ids: list[str] | None = None
    type: DocumentTypes = DocumentTypes.SHAREPOINT


class SiteItem(BaseModel):
    id: str
    displayName: str
    name: str
    url: str


class SiteItemList(BaseModel):
    count: int
    data: list[SiteItem]


class DriveItem(BaseModel):
    id: str
    name: str


class DriveItemList(BaseModel):
    count: int
    data: list[DriveItem]


class PageItem(BaseModel):
    id: str
    name: str
    title: str
    webUrl: str
    site_id: str
    createdDateTime: str
    lastModifiedDateTime: str
    createdBy: dict | None = None
    lastModifiedBy: dict | None = None
    is_indexed: bool = False
    is_modified: bool = False
    document: DocumentRead | None = None


class PageItemList(BaseModel):
    count: int
    data: list[PageItem]


class PageUploadRequest(BaseModel):
    id: str
    name: str
    title: str
    webUrl: str
    site_id: str
    createdDateTime: str
    lastModifiedDateTime: str
    createdBy: dict | None = None
    lastModifiedBy: dict | None = None
    subpages: list["PageUploadRequest"] | None = []


class BatchPageUploadRequest(BaseModel):
    pages: list[PageUploadRequest] | None = None
    categories: list[str] | None


class PageContent(BaseModel):
    text: str
    documents: list[dict] | None = None
    links: list[PageItem] | None = None
    external_links: list[str] | None = None


class PageContentItem(BaseModel):
    id: str
    site_id: str
    page_content: PageContent


class BatchPageDeleteRequest(BaseModel):
    page_ids: list[str] | None = None
