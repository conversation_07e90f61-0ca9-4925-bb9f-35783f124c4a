from enum import Enum

from pydantic import BaseModel, EmailStr


class TranscriptStatus(Enum):
    AVAILABLE = "Transcript Available"
    PREV_AVAILABLE = "Previous Transcript Available"
    UNAVAILABLE = "Transcript Unavailable"


class EmailRequest(BaseModel):
    recipients: list[EmailStr]
    subject: str
    body: str


class ReplyEmail(BaseModel):
    recipients: list[str]
    body: str
    messageId: str


class ForwardEmail(BaseModel):
    comment: str
    messageId: str
    recipients: list[str]


class EmailAddress(BaseModel):
    address: str | None
    name: str | None


class Attendee(BaseModel):
    emailAddress: EmailAddress


class EventDateTime(BaseModel):
    dateTime: str
    timeZone: str


class Body(BaseModel):
    contentType: str
    content: str


class Event(BaseModel):
    id: str
    subject: str
    body: Body
    start: EventDateTime
    end: EventDateTime
    attendees: list[Attendee] | None = []
    organizer: Attendee
    webLink: str
    transcript_available: TranscriptStatus | None = TranscriptStatus.UNAVAILABLE


class CalendarOwner(BaseModel):
    name: str
    address: EmailStr


class CalendarEvent(BaseModel):
    Calendar_owner: CalendarOwner
    Calendar_id: str
    events: list[Event]


class EventSummary(BaseModel):
    event_id: str
    summary: str
