from typing import Literal
from uuid import UUID

from pydantic import BaseModel, ConfigDict, EmailStr
from schema.assistant import AssistantRead
from schema.division import DivisionRead

from .enums import RoleTypes


class UserInfo(BaseModel):
    given_name: str = ""


class UserMeta(BaseModel):
    user_info: UserInfo | None = {}


class UserBase(BaseModel):
    email: EmailStr | None = None
    phone_number: str | None = None
    name: str | None = None
    username: str | None = None
    display_picture: str | None = None
    role: RoleTypes = RoleTypes.GENERAL
    gender: Literal["male", "female", "other"] | None = None

    meta: UserMeta | None = {}

    model_config = ConfigDict(from_attributes=True)


class UserSelfUpdate(BaseModel):
    phone_number: str | None = None
    name: str | None = None
    display_picture: str | None = None
    gender: Literal["male", "female", "other"] | None = None

    model_config = ConfigDict(from_attributes=True)


class UserUpdate(BaseModel):
    # email: EmailStr | None = None
    phone_number: str | None = None
    assistant_ids: list[str] | None = None
    role: RoleTypes | None = None
    division: str | None = None
    encompass_impersonation_access: bool | None = None

    model_config = ConfigDict(from_attributes=True)


class UserRead(UserBase):
    id: UUID
    division: DivisionRead | None
    encompass_impersonation_access: bool
    assistants: list[AssistantRead]


class UserList(BaseModel):
    count: int
    data: list[UserRead]


class UserAccess(BaseModel):
    role: RoleTypes
    admin_access: bool
    access: list[str]


class AssistantUserRead(UserBase):
    id: UUID


class AssistantUserReadList(BaseModel):
    count: int
    data: list[AssistantUserRead]
