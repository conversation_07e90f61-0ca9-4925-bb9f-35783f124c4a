from datetime import datetime

from pydantic import BaseModel, ConfigDict, field_validator
from schema.enums import DataFieldFolder, DataFieldTypes


class DataFieldBase(BaseModel):
    field_id: str
    name: str
    type: DataFieldTypes
    folder: DataFieldFolder | None = None
    is_searchable: bool
    display_in_ui: bool
    meta: dict | None = None

    model_config = ConfigDict(from_attributes=True)


class DataFieldRead(DataFieldBase):
    id: str
    is_merged: bool
    merged_name: str | None
    display_order: int
    created_at: datetime
    updated_at: datetime


class DataFieldReadList(BaseModel):
    count: int
    data: list[DataFieldRead]


class DataFieldCreate(DataFieldBase):
    @field_validator("field_id", "name")
    @classmethod
    def validate_non_empty_string(cls, v: str) -> str:
        if v is None or v.strip() == "":
            raise ValueError("Field cannot be empty or null")
        return v


class DataFieldUpdate(BaseModel):
    field_id: str | None = None
    name: str | None = None
    type: DataFieldTypes | None = None
    is_searchable: bool | None = None
    display_in_ui: bool | None = None
    display_order: int | None = None
    meta: dict | None = None

    @field_validator("field_id", "name")
    @classmethod
    def validate_non_empty_string(cls, v: str | None) -> str | None:
        if v is not None and v.strip() == "":
            raise ValueError("Field cannot be an empty string")
        return v


class MergedDataField(BaseModel):
    name: str
    fields: list[DataFieldRead]


class MergedDataFieldInput(BaseModel):
    name: str
    field_ids: list[str]


class MergedDataFieldDelete(BaseModel):
    field_ids: list


class MergedDataFieldRead(MergedDataField):
    pass


class ListMergedDateField(BaseModel):
    count: int
    data: list[MergedDataFieldRead]
