from datetime import datetime

from pydantic import BaseModel, ConfigDict


class DocumentationBase(BaseModel):
    title: str
    content: str
    is_indexed: bool = False
    categories: list[str] | None = None
    expiry_date: datetime | None = None

    model_config = ConfigDict(from_attributes=True)


class DocumentationCreate(DocumentationBase):
    pass


class DocumentationRead(DocumentationBase):
    id: str
    assistant_id: str
    created_at: datetime
    updated_at: datetime
    meta: dict | None = None


class DocumentationUpdate(BaseModel):
    title: str | None = None
    content: str | None = None
    categories: list[str] | None = None
    expiry_date: datetime | None = None
