from pydantic import BaseModel
from schema.enums import AssistantSubTypes
from schema.resources.data_field import DataFieldCreate, DataFieldRead, DataFieldUpdate


class DivisionDataFieldStatus(BaseModel):
    division_id: str
    division_name: str
    is_enabled: bool


class AdminDataFieldCreate(DataFieldCreate):
    division_ids: list[str]
    assistant_subtype: AssistantSubTypes


class AdminDataFieldRead(DataFieldRead):
    divisions: list[DivisionDataFieldStatus] = []


class AdminDataFieldReadList(BaseModel):
    count: int
    data: list[AdminDataFieldRead]


class AdminDataFieldUpdate(DataFieldUpdate):
    division_ids: list[str] | None = None
    assistant_subtype: AssistantSubTypes
