from datetime import datetime

from croniter import croniter
from pydantic import AnyU<PERSON>, BaseModel, ConfigDict, field_validator
from schema.enums import WebsiteContentTypes


class WebsiteContentBase(BaseModel):
    id: str
    assistant_id: str
    created_at: datetime
    updated_at: datetime
    meta: dict | None = None
    title: str | None = None
    content: str | None = None
    content_type: WebsiteContentTypes = WebsiteContentTypes.FULL_CONTENT
    domain: str | None = None
    url: AnyUrl
    include_links: bool = False
    is_indexed: bool = False
    categories: list[str] | None = None
    cron_expression: str | None = None
    count: int | None = None

    model_config = ConfigDict(from_attributes=True)


class WebsiteLinks(BaseModel):
    title: str
    url: AnyUrl

    @field_validator("url")
    def validate_url(cls, v):
        return str(v)


class WebsiteLinksList(BaseModel):
    count: int
    data: list[WebsiteLinks]


class WebsiteContentCreate(BaseModel):
    url: AnyUrl
    content_type: WebsiteContentTypes = WebsiteContentTypes.FULL_CONTENT
    include_links: bool = False
    is_indexed: bool = False
    categories: list[str] | None = None
    cron_expression: str | None = None
    links: list["WebsiteLinks"] | None = None

    model_config = ConfigDict(from_attributes=True)

    @field_validator("url")
    def validate_url(cls, v):
        return str(v)

    @field_validator("cron_expression")
    def validate_cron_expression(cls, v):
        if v is not None:
            if not croniter.is_valid(v):
                raise ValueError("Invalid cron expression")
        return v


class WebsiteContentRead(WebsiteContentBase):
    children: list["WebsiteContentBase"] | None = None


class WebsiteContentReadList(BaseModel):
    count: int
    data: list["WebsiteContentRead"] | None = None
    page: int


class WebsiteContentUpdate(BaseModel):
    categories: list[str] | None = None
    cron_expression: str | None = None

    @field_validator("cron_expression")
    def validate_cron_expression(cls, v):
        if v is not None:
            if not croniter.is_valid(v):
                raise ValueError("Invalid cron expression")
        return v
