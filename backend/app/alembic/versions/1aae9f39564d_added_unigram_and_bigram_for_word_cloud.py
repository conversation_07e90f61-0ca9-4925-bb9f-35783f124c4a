"""Added unigram and bigram for word cloud

Revision ID: 1aae9f39564d
Revises: c4db71e9089f
Create Date: 2024-12-02 11:26:03.158110

"""

from collections.abc import Sequence

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = "1aae9f39564d"
down_revision: str | None = "c4db71e9089f"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "assistant_analytics", sa.Column("unigram_word_cloud", postgresql.JSON(astext_type=sa.Text()), nullable=True)
    )
    op.add_column(
        "assistant_analytics", sa.Column("bigram_word_cloud", postgresql.JSON(astext_type=sa.Text()), nullable=True)
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("assistant_analytics", "bigram_word_cloud")
    op.drop_column("assistant_analytics", "unigram_word_cloud")
    # ### end Alembic commands ###
