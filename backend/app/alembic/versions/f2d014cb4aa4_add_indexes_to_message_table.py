"""Add indexes to Message table

Revision ID: f2d014cb4aa4
Revises: afd59e3ff0ec
Create Date: 2025-02-12 18:51:22.840067

"""

from collections.abc import Sequence

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "f2d014cb4aa4"
down_revision: str | None = "afd59e3ff0ec"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    op.create_index(
        "idx_message_conversation_created_at_role",
        "messages",
        ["conversation_id", "created_at", "role"],
    )

    # Creating index on created_at column
    op.create_index(
        "idx_message_created_at",
        "messages",
        ["created_at"],
    )


def downgrade() -> None:
    op.drop_index("idx_message_conversation_created_at_role", table_name="message")
    op.drop_index("idx_message_created_at", table_name="message")
