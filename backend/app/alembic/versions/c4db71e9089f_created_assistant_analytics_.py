"""created assistant analytics table

Revision ID: c4db71e9089f
Revises: 090ea92cdf66
Create Date: 2024-11-14 12:14:03.966396

"""

from collections.abc import Sequence

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = "c4db71e9089f"
down_revision: str | None = "6ddcf2964b06"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "assistant_analytics",
        sa.Column("assistant_id", sa.String(length=255), nullable=False),
        sa.Column("cluster_name", sa.String(length=255), nullable=False),
        sa.Column("message_count", sa.Integer(), nullable=False),
        sa.Column("percentage", sa.Float(), nullable=False),
        sa.Column("meta", postgresql.JSON(astext_type=sa.Text()), nullable=True),
        sa.Column("id", sa.String(), nullable=False),
        sa.Column("created_at", sa.DateTime(), server_default=sa.text("now()"), nullable=False),
        sa.Column("updated_at", sa.DateTime(), nullable=False),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("assistant_id", "cluster_name", name="unique_assistant_cluster"),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("assistant_analytics")
    # ### end Alembic commands ###
