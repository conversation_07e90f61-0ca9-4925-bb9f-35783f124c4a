"""add encompass impersonation access column on user model

Revision ID: f67aef7203fe
Revises: f19a5556ba56
Create Date: 2025-05-22 19:11:39.303042

"""

from collections.abc import Sequence

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "f67aef7203fe"
down_revision: str | None = "f19a5556ba56"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "users",
        sa.Column("encompass_impersonation_access", sa.<PERSON>(), nullable=False, server_default=sa.text("false")),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("users", "encompass_impersonation_access")
    # ### end Alembic commands ###
