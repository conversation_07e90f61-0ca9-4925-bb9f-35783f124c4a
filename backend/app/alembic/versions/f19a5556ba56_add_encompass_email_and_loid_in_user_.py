"""add encompass email and loid in user table

Revision ID: f19a5556ba56
Revises: 3d4add1ecfbf
Create Date: 2025-05-19 17:49:38.749658

"""

from collections.abc import Sequence

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "f19a5556ba56"
down_revision: str | None = "3d4add1ecfbf"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("users", sa.Column("encompass_email", sa.String(length=255), nullable=True))
    op.add_column("users", sa.Column("encompass_loid", sa.String(length=100), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("users", "encompass_loid")
    op.drop_column("users", "encompass_email")
    # ### end Alembic commands ###
