"""add datapulse_reports table

Revision ID: b936f43d6020
Revises: 4974a28af933
Create Date: 2025-06-18 17:22:26.776616

"""

from collections.abc import Sequence

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = "b936f43d6020"
down_revision: str | None = "4974a28af933"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "datapulse_reports",
        sa.Column("id", sa.String(length=255), nullable=False),
        sa.Column("name", sa.String(length=255), nullable=False),
        sa.Column("description", sa.Text(), nullable=False),
        sa.Column("payload", postgresql.JSON(astext_type=sa.Text()), nullable=False),
        sa.Column("primary_display_fields", postgresql.JSON(astext_type=sa.Text()), nullable=False),
        sa.Column("secondary_display_fields", postgresql.JSON(astext_type=sa.Text()), nullable=False),
        sa.Column("is_visible", sa.Boolean(), nullable=False),
        sa.Column("order", sa.Integer(), nullable=False),
        sa.Column("requires_condition_pending", sa.Boolean(), nullable=False),
        sa.Column("requires_custom_date", sa.Boolean(), nullable=False),
        sa.Column("ttl", sa.Integer(), nullable=False),
        sa.Column("assistant_id", sa.String(), nullable=False),
        sa.Column("created_at", sa.DateTime(), server_default=sa.text("now()"), nullable=False),
        sa.Column("updated_at", sa.DateTime(), nullable=False),
        sa.Column("meta", postgresql.JSON(astext_type=sa.Text()), nullable=True),
        sa.ForeignKeyConstraint(
            ["assistant_id"], ["assistants.id"], name="fk_datapulse_reports_assistant_id", ondelete="CASCADE"
        ),
        sa.PrimaryKeyConstraint("id", "assistant_id", name="pk_datapulse_reports"),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("datapulse_reports")
    # ### end Alembic commands ###
