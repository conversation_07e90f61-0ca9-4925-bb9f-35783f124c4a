"""add enable_web_search to assistants

Revision ID: 86d4f99f8283
Revises: b936f43d6020
Create Date: 2025-06-30 11:28:17.350563

"""

from collections.abc import Sequence

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "86d4f99f8283"
down_revision: str | None = "b936f43d6020"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    # Step 1: Add column allowing NULLs temporarily
    op.add_column(
        "assistants",
        sa.Column("enable_web_search", sa.<PERSON>(), nullable=True),
    )

    # Step 2: Set existing rows to False
    op.execute("UPDATE assistants SET enable_web_search = FALSE")

    # Step 3: Make column NOT NULL with default False
    op.alter_column(
        "assistants",
        "enable_web_search",
        existing_type=sa.<PERSON>(),
        nullable=False,
        server_default=sa.text("FALSE"),
    )


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("assistants", "enable_web_search")
    # ### end Alembic commands ###
