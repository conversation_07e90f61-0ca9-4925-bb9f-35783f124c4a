"""add totalexpert journeys table

Revision ID: a3eb484b9041
Revises: 9cfeddc59970
Create Date: 2025-03-11 15:55:34.059077

"""

from collections.abc import Sequence

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = "a3eb484b9041"
down_revision: str | None = "9cfeddc59970"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "total_expert_journeys",
        sa.Column("type", sa.String(length=50), nullable=False),
        sa.Column("name", sa.String(length=255), nullable=False),
        sa.Column("description", sa.Text(), nullable=True),
        sa.Column("fields", postgresql.JSON(astext_type=sa.Text()), nullable=True),
        sa.Column("is_published", sa.<PERSON>(), nullable=False),
        sa.Column("is_custom", sa.<PERSON>(), nullable=False),
        sa.Column("blacklisted_words", postgresql.JSON(astext_type=sa.Text()), nullable=True),
        sa.Column("id", sa.String(), nullable=False),
        sa.Column("created_at", sa.DateTime(), server_default=sa.text("now()"), nullable=False),
        sa.Column("updated_at", sa.DateTime(), nullable=False),
        sa.Column("meta", postgresql.JSON(astext_type=sa.Text()), nullable=True),
        sa.PrimaryKeyConstraint("id"),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("total_expert_journeys")
    # ### end Alembic commands ###
