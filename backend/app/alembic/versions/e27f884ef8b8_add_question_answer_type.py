"""add question answer type

Revision ID: e27f884ef8b8
Revises: 20f93f46cd0a
Create Date: 2024-08-28 15:12:54.413138

"""

from collections.abc import Sequence

import sqlalchemy as sa
from alembic import op
from schema.enums import QuestionAnswerTypes

# revision identifiers, used by Alembic.
revision: str = "e27f884ef8b8"
down_revision: str | None = "20f93f46cd0a"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "question_answers",
        sa.Column("type", sa.String(length=50), nullable=False, server_default=QuestionAnswerTypes.FAQ),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("question_answers", "type")
    # ### end Alembic commands ###
