"""Add flag to enable assistant in division credentials

Revision ID: b90e7ae0e822
Revises: c15110a774c0
Create Date: 2025-05-27 15:20:41.132438

"""

from collections.abc import Sequence

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "b90e7ae0e822"
down_revision: str | None = "c15110a774c0"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "division_credentials",
        sa.Column("enabled_personal_assistant", sa.<PERSON>(), nullable=False, server_default=sa.text("false")),
    )
    op.add_column(
        "division_credentials",
        sa.Column("enabled_sales_assistant", sa.<PERSON>(), nullable=False, server_default=sa.text("false")),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("division_credentials", "enabled_sales_assistant")
    op.drop_column("division_credentials", "enabled_personal_assistant")
    # ### end Alembic commands ###
