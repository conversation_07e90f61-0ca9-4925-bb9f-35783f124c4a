"""add assistant relationship

Revision ID: 3543495f5701
Revises: a3eb484b9041
Create Date: 2025-03-11 16:23:06.425739

"""

from collections.abc import Sequence

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "3543495f5701"
down_revision: str | None = "a3eb484b9041"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("total_expert_journeys", sa.Column("assistant_id", sa.String(), nullable=False))
    op.create_unique_constraint("unique_assistant_journey", "total_expert_journeys", ["assistant_id", "id"])
    op.create_foreign_key(
        "total_expert_journeys_assistant_id_fkey",
        "total_expert_journeys",
        "assistants",
        ["assistant_id"],
        ["id"],
        ondelete="CASCADE",
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint("total_expert_journeys_assistant_id_fkey", "total_expert_journeys", type_="foreignkey")
    op.drop_constraint("unique_assistant_journey", "total_expert_journeys", type_="unique")
    op.drop_column("total_expert_journeys", "assistant_id")
    # ### end Alembic commands ###
