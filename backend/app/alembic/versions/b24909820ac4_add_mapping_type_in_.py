"""Add mapping_type in CustomLoanOfficerMapping

Revision ID: b24909820ac4
Revises: 51042668dab3
Create Date: 2024-07-17 17:12:30.880012

"""

from collections.abc import Sequence

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects.postgresql import ENUM

# revision identifiers, used by Alembic.
revision: str = "b24909820ac4"
down_revision: str | None = "51042668dab3"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    # Create the enum type
    loan_officer_mapping_type = ENUM("REFI_REPORT", "OTHER_REPORT", "COMMON", name="loanofficermappingtype")
    loan_officer_mapping_type.create(op.get_bind())

    op.add_column(
        "custom_loan_officer_mappings",
        sa.Column(
            "mapping_type",
            sa.Enum("REFI_REPORT", "OTHER_REPORT", "COMMON", name="loanofficermappingtype"),
            nullable=False,
            server_default="OTHER_REPORT",
        ),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    loan_officer_mapping_type = ENUM("REFI_REPORT", "OTHER_REPORT", "COMMON", name="loanofficermappingtype")
    loan_officer_mapping_type.drop(op.get_bind())

    op.drop_column("custom_loan_officer_mappings", "mapping_type")
    # ### end Alembic commands ###
