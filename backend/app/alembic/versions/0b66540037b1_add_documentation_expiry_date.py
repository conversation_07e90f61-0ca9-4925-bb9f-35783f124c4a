"""add documentation expiry date

Revision ID: 0b66540037b1
Revises: e27f884ef8b8
Create Date: 2024-09-09 16:19:11.237590

"""

from collections.abc import Sequence

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "0b66540037b1"
down_revision: str | None = "e27f884ef8b8"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("documentations", sa.Column("expiry_date", sa.DateTime(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("documentations", "expiry_date")
    # ### end Alembic commands ###
