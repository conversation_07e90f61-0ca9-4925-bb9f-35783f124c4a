"""add remaining total_expert migrations

Revision ID: 4974a28af933
Revises: f4cf598894ec
Create Date: 2025-06-18 17:15:37.220554

"""

from collections.abc import Sequence

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "4974a28af933"
down_revision: str | None = "f4cf598894ec"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint("total_expert_tokens_assistant_id_fkey", "total_expert_tokens", type_="foreignkey")
    op.drop_constraint("total_expert_tokens_user_id_fkey", "total_expert_tokens", type_="foreignkey")
    op.create_foreign_key("total_expert_tokens_user_id_fkey", "total_expert_tokens", "users", ["user_id"], ["id"])
    op.create_foreign_key(
        "total_expert_tokens_assistant_id_fkey", "total_expert_tokens", "assistants", ["assistant_id"], ["id"]
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint("total_expert_tokens_user_id_fkey", "total_expert_tokens", type_="foreignkey")
    op.drop_constraint("total_expert_tokens_assistant_id_fkey", "total_expert_tokens", type_="foreignkey")
    op.create_foreign_key(
        "total_expert_tokens_user_id_fkey", "total_expert_tokens", "users", ["user_id"], ["id"], ondelete="CASCADE"
    )
    op.create_foreign_key(
        "total_expert_tokens_assistant_id_fkey",
        "total_expert_tokens",
        "assistants",
        ["assistant_id"],
        ["id"],
        ondelete="CASCADE",
    )
    # ### end Alembic commands ###
