"""create total expert tokens table

Revision ID: 4e526b9d1d21
Revises: 81f57753e60e
Create Date: 2025-06-06 23:40:38.309558

"""

from collections.abc import Sequence

import sqlalchemy as sa
from alembic import op
from config import settings
from sqlalchemy.dialects import postgresql
from sqlalchemy_utils.types.encrypted.encrypted_type import AesEngine, EncryptedType

# revision identifiers, used by Alembic.
revision: str = "4e526b9d1d21"
down_revision: str | None = "81f57753e60e"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "total_expert_tokens",
        sa.Column("user_id", sa.UUID(), nullable=False),
        sa.Column("assistant_id", sa.String(), nullable=False),
        sa.Column(
            "access_token",
            EncryptedType(sa.String, settings.ENCRYPTION_KEY, AesEngine, padding="pkcs5"),
            nullable=False,
        ),
        sa.Column(
            "refresh_token",
            EncryptedType(sa.String, settings.ENCRYPTION_KEY, AesEngine, padding="pkcs5"),
            nullable=False,
        ),
        sa.Column("token_type", sa.String(), nullable=False),
        sa.Column("expires_at", sa.DateTime(), nullable=False),
        sa.Column("refresh_token_expires_at", sa.DateTime(), nullable=False),
        sa.Column("id", sa.String(), nullable=False),
        sa.Column("created_at", sa.DateTime(), server_default=sa.text("now()"), nullable=False),
        sa.Column("updated_at", sa.DateTime(), nullable=False),
        sa.Column("meta", postgresql.JSON(astext_type=sa.Text()), nullable=True),
        sa.ForeignKeyConstraint(
            ["assistant_id"],
            ["assistants.id"],
            name="total_expert_tokens_assistant_id_fkey",
            ondelete="CASCADE",
        ),
        sa.ForeignKeyConstraint(
            ["user_id"],
            ["users.id"],
            name="total_expert_tokens_user_id_fkey",
            ondelete="CASCADE",
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("total_expert_tokens")
    # ### end Alembic commands ###
