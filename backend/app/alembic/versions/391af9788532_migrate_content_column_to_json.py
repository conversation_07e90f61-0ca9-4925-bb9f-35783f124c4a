"""Migrate content column to JSON

Revision ID: 391af9788532
Revises: 1aae9f39564d
Create Date: 2024-11-19 15:57:12.270867

"""

import json
from collections.abc import Sequence

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = "391af9788532"
down_revision: str | None = "1aae9f39564d"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("messages", sa.Column("attachments", postgresql.JSON(astext_type=sa.Text()), nullable=True))

    # Step 1: Add a temporary JSON column
    op.add_column("messages", sa.Column("content_temp", postgresql.JSON(astext_type=sa.Text()), nullable=True))

    # Step 2: Migrate existing data to the new JSON column
    connection = op.get_bind()
    messages = connection.execute(sa.text("SELECT id, content FROM messages")).fetchall()
    for message in messages:
        old_content = message.content
        new_content = [{"type": "text", "text": old_content}]
        connection.execute(
            sa.text("UPDATE messages SET content_temp = :new_content WHERE id = :id").params(
                new_content=json.dumps(new_content), id=message.id
            )
        )

    # Step 3: Drop the old column and rename the new one
    op.drop_column("messages", "content")
    op.alter_column("messages", "content_temp", new_column_name="content")
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###

    # Step 1: Add a temporary Text column
    op.add_column("messages", sa.Column("content_temp", sa.Text, nullable=True))

    # Step 2: Migrate data back to the Text column
    connection = op.get_bind()
    messages = connection.execute(sa.text("SELECT id, content FROM messages")).fetchall()

    for message in messages:
        content_list = message.content
        if content_list and isinstance(content_list, list) and len(content_list) > 0:
            old_content = content_list[0].get("text", "")
            connection.execute(
                sa.text("UPDATE messages SET content_temp = :old_content WHERE id = :id").params(
                    old_content=old_content, id=message.id
                )
            )

    # Step 3: Drop the JSON column and rename the Text column
    op.drop_column("messages", "content")
    op.alter_column("messages", "content_temp", new_column_name="content")

    op.drop_column("messages", "attachments")
    # ### end Alembic commands ###
