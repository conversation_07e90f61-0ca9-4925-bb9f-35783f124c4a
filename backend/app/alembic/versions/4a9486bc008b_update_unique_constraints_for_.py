"""update unique constraints for sharepoint folder

Revision ID: 4a9486bc008b
Revises: f2d014cb4aa4
Create Date: 2025-02-14 18:14:26.032259

"""

from collections.abc import Sequence

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "4a9486bc008b"
down_revision: str | None = "f2d014cb4aa4"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint("unique_site_drive_folder", "sharepoint_folders", type_="unique")
    op.create_unique_constraint(
        "unique_site_drive_assistant_folder",
        "sharepoint_folders",
        ["site_id", "drive_id", "folder_id", "assistant_id"],
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint("unique_site_drive_assistant_folder", "sharepoint_folders", type_="unique")
    op.create_unique_constraint("unique_site_drive_folder", "sharepoint_folders", ["site_id", "drive_id", "folder_id"])
    # ### end Alembic commands ###
