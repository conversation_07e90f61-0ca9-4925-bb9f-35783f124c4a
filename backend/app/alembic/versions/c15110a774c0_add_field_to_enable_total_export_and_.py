"""Add field to enable total export and encompass

Revision ID: c15110a774c0
Revises: 64b4889f7cdd
Create Date: 2025-05-22 16:42:02.741731

"""

from collections.abc import Sequence

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "c15110a774c0"
down_revision: str | None = "64b4889f7cdd"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "division_credentials",
        sa.Column("enabled_total_expert", sa.<PERSON>(), nullable=False, server_default=sa.text("false")),
    )
    op.add_column(
        "encompass_credentials", sa.Column("enabled", sa.<PERSON>(), nullable=False, server_default=sa.text("false"))
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("encompass_credentials", "enabled")
    op.drop_column("division_credentials", "enabled_total_expert")
    # ### end Alembic commands ###
