"""active_cusotm_lo_mapping

Revision ID: f4cf598894ec
Revises: 4e526b9d1d21
Create Date: 2025-06-16 17:51:32.065092

"""

from collections.abc import Sequence

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "f4cf598894ec"
down_revision: str | None = "4e526b9d1d21"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "custom_loan_officer_mappings", sa.Column("active", sa.<PERSON>(), server_default=sa.false(), nullable=False)
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("custom_loan_officer_mappings", "active")
    # ### end Alembic commands ###
