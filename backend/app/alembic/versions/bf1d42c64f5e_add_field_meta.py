"""add field meta

Revision ID: bf1d42c64f5e
Revises: b22be601eb23
Create Date: 2024-12-09 17:19:32.922684

"""

from collections.abc import Sequence

# revision identifiers, used by Alembic.
revision: str = "bf1d42c64f5e"
down_revision: str | None = "b22be601eb23"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    pass


def downgrade() -> None:
    pass
