"""add display_order column

Revision ID: 7565179dbc1a
Revises: 4f85945021a0
Create Date: 2024-12-17 17:21:35.907480

"""

from collections.abc import Sequence

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "7565179dbc1a"
down_revision: str | None = "4f85945021a0"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("data_fields", sa.Column("display_order", sa.Integer(), nullable=True))

    # Initialize display_order for existing records
    conn = op.get_bind()
    data_fields = conn.execute(sa.text("SELECT id FROM data_fields ORDER BY created_at")).fetchall()
    for index, field in enumerate(data_fields):
        conn.execute(
            sa.text("UPDATE data_fields SET display_order = :display_order WHERE id = :id"),
            {"display_order": index, "id": field.id},
        )

    # Set the display_order column to NOT NULL after it has been initialized
    op.alter_column("data_fields", "display_order", nullable=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("data_fields", "display_order")
    # ### end Alembic commands ###
