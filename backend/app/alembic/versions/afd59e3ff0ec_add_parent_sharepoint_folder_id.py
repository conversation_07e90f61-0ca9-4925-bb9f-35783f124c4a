"""add parent sharepoint folder id

Revision ID: afd59e3ff0ec
Revises: 36c8d5f306a4
Create Date: 2025-02-09 17:29:19.893609

"""

from collections.abc import Sequence

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "afd59e3ff0ec"
down_revision: str | None = "36c8d5f306a4"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("sharepoint_folders", sa.Column("is_indexed", sa.<PERSON>(), nullable=False, server_default="false"))
    op.add_column("sharepoint_folders", sa.Column("index_status", sa.String(length=50), nullable=True))
    op.add_column("sharepoint_folders", sa.Column("parent_id", sa.String(), nullable=True))
    op.create_foreign_key(None, "sharepoint_folders", "sharepoint_folders", ["parent_id"], ["id"], ondelete="CASCADE")
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, "sharepoint_folders", type_="foreignkey")
    op.drop_column("sharepoint_folders", "parent_id")
    op.drop_column("sharepoint_folders", "index_status")
    op.drop_column("sharepoint_folders", "is_indexed")
    # ### end Alembic commands ###
