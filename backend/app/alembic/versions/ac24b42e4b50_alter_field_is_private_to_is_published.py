"""alter field is_private to is_published

Revision ID: ac24b42e4b50
Revises: 779c9b239698
Create Date: 2024-06-03 17:59:13.264348

"""

from collections.abc import Sequence

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "ac24b42e4b50"
down_revision: str | None = "779c9b239698"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "assistants",
        "is_private",
        new_column_name="is_published",
        nullable=False,
        server_default=sa.false(),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "assistants",
        "is_published",
        new_column_name="is_private",
        nullable=False,
        server_default=sa.false(),
    )
    # ### end Alembic commands ###
