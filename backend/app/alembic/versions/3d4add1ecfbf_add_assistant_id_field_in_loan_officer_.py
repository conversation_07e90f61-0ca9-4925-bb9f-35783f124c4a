"""add assistant_id field in loan officer table

Revision ID: 3d4add1ecfbf
Revises: 80f883cd8a47
Create Date: 2025-04-23 17:45:26.517641

"""

from collections.abc import Sequence

import sqlalchemy as sa
from alembic import op
from schema.enums import AssistantSubTypes

# revision identifiers, used by Alembic.
revision: str = "3d4add1ecfbf"
down_revision: str | None = "80f883cd8a47"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("loan_officers", sa.Column("assistant_id", sa.String(), nullable=True))
    # now handle for current loan officers
    # fetch assistants with type ENCOMPASS
    conn = op.get_bind()
    encompass_assistants = conn.execute(
        sa.text("SELECT id FROM assistants WHERE sub_type = :subtype"), {"subtype": AssistantSubTypes.ENCOMPASS}
    ).fetchall()

    if encompass_assistants:
        encompass_assistant_id = encompass_assistants[0].id
        conn.execute(
            sa.text("UPDATE loan_officers SET assistant_id = :assistant_id"),
            {"assistant_id": encompass_assistant_id},
        )

    # now alter table to make assistant_id not nullable
    op.alter_column("loan_officers", "assistant_id", nullable=False)
    op.create_foreign_key(
        "loan_officers_assistant_id_fkey", "loan_officers", "assistants", ["assistant_id"], ["id"], ondelete="CASCADE"
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint("loan_officers_assistant_id_fkey", "loan_officers", type_="foreignkey")
    op.drop_column("loan_officers", "assistant_id")
    # ### end Alembic commands ###
