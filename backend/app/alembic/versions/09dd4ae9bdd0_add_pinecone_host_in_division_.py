"""add pinecone_host in division credentials

Revision ID: 09dd4ae9bdd0
Revises: d06a896ccafe
Create Date: 2025-05-14 14:24:14.502440

"""

from collections.abc import Sequence

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "09dd4ae9bdd0"
down_revision: str | None = "d06a896ccafe"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "division_credentials",
        sa.Column("pinecone_host", sa.String(length=512), nullable=False, server_default="https://api.pinecone.io"),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("division_credentials", "pinecone_host")
    # ### end Alembic commands ###
