"""add unique constraints to assistantpersona

Revision ID: bb08dcb2a7f5
Revises: 61340fe2436f
Create Date: 2025-03-19 18:00:21.879634

"""

from collections.abc import Sequence

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "bb08dcb2a7f5"
down_revision: str | None = "61340fe2436f"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_unique_constraint("unique_assistant_user_persona", "assistant_personas", ["assistant_id", "user_id"])
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint("unique_assistant_user_persona", "assistant_personas", type_="unique")
    # ### end Alembic commands ###
