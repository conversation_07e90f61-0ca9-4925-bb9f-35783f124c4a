"""Add field to store name and type in encompass cred

Revision ID: dd855f55047d
Revises: b90e7ae0e822
Create Date: 2025-05-27 16:13:31.124518

"""

from collections.abc import Sequence

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "dd855f55047d"
down_revision: str | None = "b90e7ae0e822"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "encompass_credentials",
        sa.Column("name", sa.String(length=255), nullable=False, server_default="Encompass Assistant"),
    )
    op.add_column(
        "encompass_credentials",
        sa.Column("encompass_type", sa.String(length=255), nullable=False, server_default="prod"),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("encompass_credentials", "encompass_type")
    op.drop_column("encompass_credentials", "name")
    # ### end Alembic commands ###
