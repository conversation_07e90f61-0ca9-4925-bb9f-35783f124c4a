"""add encompass_credential_id in assistants

Revision ID: 64b4889f7cdd
Revises: 09dd4ae9bdd0
Create Date: 2025-05-21 16:52:20.238514

"""

from collections.abc import Sequence

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "64b4889f7cdd"
down_revision: str | None = "09dd4ae9bdd0"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("assistants", sa.Column("encompass_credential_id", sa.String(), nullable=True))
    op.create_foreign_key(
        "assistants_encompass_credential_id_fkey",
        "assistants",
        "encompass_credentials",
        ["encompass_credential_id"],
        ["id"],
        ondelete="CASCADE",
    )
    op.create_foreign_key(
        None, "assistants", "encompass_credentials", ["encompass_credential_id"], ["id"], ondelete="CASCADE"
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by <PERSON>embic - please adjust! ###
    op.drop_constraint("assistants_encompass_credential_id_fkey", "assistants", type_="foreignkey")
    op.drop_column("assistants", "encompass_credential_id")
    # ### end Alembic commands ###
