"""add avatar in division model

Revision ID: 81f57753e60e
Revises: dd855f55047d
Create Date: 2025-06-06 17:02:20.329129

"""

from collections.abc import Sequence

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "81f57753e60e"
down_revision: str | None = "dd855f55047d"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("divisions", sa.Column("avatar", sa.Text(), nullable=True, server_default=None))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("divisions", "avatar")
    # ### end Alembic commands ###
