"""add assistant_field_folder unique constraint

Revision ID: 35d2e120affc
Revises: 70edf7f6d244
Create Date: 2024-12-31 17:36:22.348365

"""

from collections.abc import Sequence

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "35d2e120affc"
down_revision: str | None = "70edf7f6d244"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint("unique_data_field", "data_fields", type_="unique")
    op.create_index(
        "unique_assistant_field_folder",
        "data_fields",
        ["assistant_id", "field_id", sa.text("COALESCE(folder, '')")],
        unique=True,
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index("unique_assistant_field_folder", table_name="data_fields")
    op.create_unique_constraint("unique_data_field", "data_fields", ["assistant_id", "field_id"])
    # ### end Alembic commands ###
