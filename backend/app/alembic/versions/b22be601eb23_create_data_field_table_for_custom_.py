"""create data_field table for custom assistant fields

Revision ID: b22be601eb23
Revises: 8f794703ab02
Create Date: 2024-12-06 17:33:23.326383

"""

from collections.abc import Sequence

# revision identifiers, used by Alembic.
revision: str = "b22be601eb23"
down_revision: str | None = "8f794703ab02"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    pass


def downgrade() -> None:
    pass
