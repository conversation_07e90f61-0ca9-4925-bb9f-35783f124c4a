"""add cron in website

Revision ID: c11ec515abb7
Revises: 0b66540037b1
Create Date: 2024-09-10 16:30:11.029480

"""

from collections.abc import Sequence

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "c11ec515abb7"
down_revision: str | None = "0b66540037b1"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("website_contents", sa.Column("cron_expression", sa.String(length=255), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("website_contents", "cron_expression")
    # ### end Alembic commands ###
