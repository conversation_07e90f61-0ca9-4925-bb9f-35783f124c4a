"""add user_id column on insights table

Revision ID: ed18f2413dc9
Revises: 9627d4c0c0a1
Create Date: 2025-02-04 16:39:30.688860

"""

from collections.abc import Sequence

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "ed18f2413dc9"
down_revision: str | None = "9627d4c0c0a1"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    # manage already existing data
    conn = op.get_bind()
    insp = sa.inspect(conn)
    if "user_id" not in [col["name"] for col in insp.get_columns("totalexpert_insights")]:
        conn.execute(sa.text("DELETE FROM totalexpert_insights;"))
    else:
        conn.execute(sa.text("DELETE FROM totalexpert_insights WHERE user_id IS NULL;"))

    # Add upgrades
    op.add_column("totalexpert_insights", sa.Column("user_id", sa.UUID(), nullable=False))
    op.create_foreign_key(
        "fk_totalexpert_insights_user_id", "totalexpert_insights", "users", ["user_id"], ["id"], ondelete="CASCADE"
    )
    op.drop_constraint("totalexpert_insights_pkey", "totalexpert_insights", type_="primary")
    op.create_primary_key("pk_totalexpert_insights", "totalexpert_insights", ["id", "user_id"])
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint("pk_totalexpert_insights", "totalexpert_insights", type_="primary")
    op.create_primary_key("totalexpert_insights_pkey", "totalexpert_insights", ["id"])
    op.drop_constraint("fk_totalexpert_insights_user_id", "totalexpert_insights", type_="foreignkey")
    op.drop_column("totalexpert_insights", "user_id")
