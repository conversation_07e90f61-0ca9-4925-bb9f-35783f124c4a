"""add ts vector for search

Revision ID: 41026a27244d
Revises: ac24b42e4b50
Create Date: 2024-06-10 16:53:32.971410

"""

from collections.abc import Sequence

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = "41026a27244d"
down_revision: str | None = "ac24b42e4b50"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("users", sa.Column("search_tsvector", postgresql.TSVECTOR(), nullable=True))

    # Create the tsvector update function
    op.execute(
        """
    CREATE FUNCTION users_tsvector_trigger() RETURNS trigger AS $$
    begin
      new.search_tsvector :=
        to_tsvector('english', coalesce(new.name, '') || ' ' || coalesce(new.email, ''));
      return new;
    end
    $$ LANGUAGE plpgsql;
    """
    )

    # Create the trigger
    op.execute(
        """
    CREATE TRIGGER tsvectorupdate BEFORE INSERT OR UPDATE
    ON users FOR EACH ROW EXECUTE FUNCTION users_tsvector_trigger();
    """
    )

    # Update existing rows
    op.execute(
        """
    UPDATE users SET search_tsvector =
        to_tsvector('english', coalesce(name, '') || ' ' || coalesce(email, ''));
    """
    )
    op.create_index(op.f("ix_users_search_tsvector"), "users", ["search_tsvector"], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute("DROP TRIGGER IF EXISTS tsvectorupdate ON users;")
    op.execute("DROP FUNCTION IF EXISTS users_tsvector_trigger;")
    op.drop_index(op.f("ix_users_search_tsvector"), table_name="users")

    op.drop_column("users", "search_tsvector")
    # ### end Alembic commands ###
