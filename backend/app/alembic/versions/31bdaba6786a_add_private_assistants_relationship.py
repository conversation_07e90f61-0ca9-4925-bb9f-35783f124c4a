"""add private assistants relationship

Revision ID: 31bdaba6786a
Revises: 343cf7d0db3c
Create Date: 2024-08-06 17:15:55.472127

"""

from collections.abc import Sequence

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "31bdaba6786a"
down_revision: str | None = "343cf7d0db3c"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "private_assistant_association",
        sa.Column("user_id", sa.UUID(), nullable=False),
        sa.Column("assistant_id", sa.String(), nullable=False),
        sa.ForeignKeyConstraint(["assistant_id"], ["assistants.id"], ondelete="CASCADE"),
        sa.ForeignKeyConstraint(["user_id"], ["users.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("user_id", "assistant_id"),
    )
    op.add_column("assistants", sa.Column("is_private", sa.<PERSON>(), nullable=False, server_default=sa.false()))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("assistants", "is_private")
    op.drop_table("private_assistant_association")
    # ### end Alembic commands ###
