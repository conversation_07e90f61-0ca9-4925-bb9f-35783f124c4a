"""Add categories to documents

Revision ID: 075133425360
Revises: ee96f28d30eb
Create Date: 2024-05-17 16:53:48.386069

"""

from collections.abc import Sequence

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = "075133425360"
down_revision: str | None = "ee96f28d30eb"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("assistants", sa.Column("categories", postgresql.JSON(astext_type=sa.Text()), nullable=True))
    op.add_column(
        "assistants", sa.Column("is_category_filtered", sa.<PERSON>(), nullable=False, server_default=sa.false())
    )
    op.add_column("documentations", sa.Column("categories", postgresql.JSON(astext_type=sa.Text()), nullable=True))
    op.add_column("documents", sa.Column("categories", postgresql.JSON(astext_type=sa.Text()), nullable=True))
    op.add_column("question_answers", sa.Column("categories", postgresql.JSON(astext_type=sa.Text()), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("question_answers", "categories")
    op.drop_column("documents", "categories")
    op.drop_column("documentations", "categories")
    op.drop_column("assistants", "is_category_filtered")
    op.drop_column("assistants", "categories")
    # ### end Alembic commands ###
