"""add division and division credentials

Revision ID: d06a896ccafe
Revises: 3d4add1ecfbf
Create Date: 2025-05-07 14:33:07.713564

"""

from collections.abc import Sequence

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql
from sqlalchemy_utils import EncryptedType

# revision identifiers, used by Alembic.
revision: str = "d06a896ccafe"
down_revision: str | None = "f67aef7203fe"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "divisions",
        sa.Column("name", sa.String(length=255), nullable=False),
        sa.Column("domain", sa.String(length=255), nullable=False),
        sa.Column("description", sa.Text(), nullable=True),
        sa.Column("logo", sa.Text(), nullable=True),
        sa.Column("primary_color", sa.String(length=50), nullable=True),
        sa.Column("secondary_color", sa.String(length=50), nullable=True),
        sa.Column("id", sa.String(), nullable=False),
        sa.Column("created_at", sa.DateTime(), server_default=sa.text("now()"), nullable=False),
        sa.Column("updated_at", sa.DateTime(), nullable=False),
        sa.Column("meta", postgresql.JSON(astext_type=sa.Text()), nullable=True),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("domain"),
    )
    op.create_table(
        "division_credentials",
        sa.Column("pinecone_index", sa.String(length=255), nullable=False),
        sa.Column("aws_storage_bucket_name", sa.String(length=255), nullable=False),
        sa.Column("total_expert_client_id", EncryptedType(sa.String(length=255)), nullable=True),
        sa.Column(
            "total_expert_client_secret",
            EncryptedType(sa.String(length=255)),
            nullable=True,
        ),
        sa.Column("division_id", sa.String(), nullable=False),
        sa.Column("id", sa.String(), nullable=False),
        sa.Column("created_at", sa.DateTime(), server_default=sa.text("now()"), nullable=False),
        sa.Column("updated_at", sa.DateTime(), nullable=False),
        sa.Column("meta", postgresql.JSON(astext_type=sa.Text()), nullable=True),
        sa.ForeignKeyConstraint(["division_id"], ["divisions.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("division_id"),
    )
    op.create_table(
        "encompass_credentials",
        sa.Column("encompass_client_id", EncryptedType(sa.String(length=255)), nullable=True),
        sa.Column("encompass_client_secret", EncryptedType(sa.String(length=255)), nullable=True),
        sa.Column("encompass_password", EncryptedType(sa.String(length=255)), nullable=True),
        sa.Column("encompass_username", EncryptedType(sa.String(length=255)), nullable=True),
        sa.Column("encompass_instance_id", EncryptedType(sa.String(length=255)), nullable=True),
        sa.Column("division_credentials_id", sa.String(), nullable=False),
        sa.Column("id", sa.String(), nullable=False),
        sa.Column("created_at", sa.DateTime(), server_default=sa.text("now()"), nullable=False),
        sa.Column("updated_at", sa.DateTime(), nullable=False),
        sa.Column("meta", postgresql.JSON(astext_type=sa.Text()), nullable=True),
        sa.ForeignKeyConstraint(["division_credentials_id"], ["division_credentials.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
    )
    op.add_column("assistants", sa.Column("division_id", sa.String(), nullable=True))
    op.create_foreign_key(
        "assistants_division_id_fkey", "assistants", "divisions", ["division_id"], ["id"], ondelete="CASCADE"
    )
    op.add_column("users", sa.Column("division_id", sa.String(), nullable=True))
    op.create_foreign_key("users_division_id_fkey", "users", "divisions", ["division_id"], ["id"], ondelete="CASCADE")
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint("users_division_id_fkey", "users", type_="foreignkey")
    op.drop_column("users", "division_id")
    op.drop_constraint("assistants_division_id_fkey", "assistants", type_="foreignkey")
    op.drop_column("assistants", "division_id")
    op.drop_table("encompass_credentials")
    op.drop_table("division_credentials")
    op.drop_table("divisions")
    # ### end Alembic commands ###
