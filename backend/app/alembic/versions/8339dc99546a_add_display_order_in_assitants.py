"""Add display order in assitants

Revision ID: 8339dc99546a
Revises: 41026a27244d
Create Date: 2024-07-02 16:04:03.523844

"""

from collections.abc import Sequence

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "8339dc99546a"
down_revision: str | None = "41026a27244d"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("assistants", sa.Column("display_order", sa.Integer(), nullable=False, server_default="0"))

    # Update existing assistants to have a sequential order
    conn = op.get_bind()
    assistants = conn.execute(sa.text("SELECT id FROM assistants ORDER BY created_at")).fetchall()
    for index, assistant in enumerate(assistants):
        conn.execute(
            sa.text("UPDATE assistants SET display_order = :display_order WHERE id = :id"),
            {"display_order": index, "id": assistant.id},
        )

    # Remove the server_default to avoid issues with future insertions
    op.alter_column("assistants", "display_order", server_default=None)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("assistants", "display_order")
    # ### end Alembic commands ###
