import uuid
from datetime import datetime

import sqlalchemy as sa
from schema.enums import <PERSON><PERSON><PERSON><PERSON>, AssistantTypes, DataFieldTypes, LoanOfficerMappingType
from sqlalchemy import (
    Boolean,
    Column,
    DateTime,
    Enum,
    Float,
    <PERSON><PERSON>ey,
    Integer,
    PrimaryKeyConstraint,
    String,
    Table,
    Text,
    UniqueConstraint,
)
from sqlalchemy.dialects.postgresql import <PERSON>SO<PERSON>, TSVECTOR, UUID
from sqlalchemy.ext.mutable import Mutable<PERSON>ict, MutableList
from sqlalchemy.orm import DeclarativeBase, Mapped, backref, declarative_mixin, mapped_column, relationship
from sqlalchemy.sql import func
from sqlalchemy_utils import EncryptedType
from sqlalchemy_utils.types.encrypted.encrypted_type import AesEngine


class Base(DeclarativeBase):
    pass


@declarative_mixin
class PrimaryUUIDTimestamped(Base):
    __abstract__ = True

    id: Mapped[str] = mapped_column(primary_key=True, default=lambda: str(uuid.uuid4()))
    created_at: Mapped[datetime] = mapped_column(
        DateTime, default=datetime.now, server_default=func.now(), nullable=False
    )
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.now, onupdate=func.now(), nullable=False)
    meta: Mapped[dict | None] = mapped_column(MutableDict.as_mutable(JSON), nullable=True)


class User(PrimaryUUIDTimestamped):
    __tablename__ = "users"

    id: Mapped[UUID] = mapped_column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    email: Mapped[str] = mapped_column(String(255), nullable=True)
    phone_number: Mapped[str] = mapped_column(String(20), nullable=True, unique=True)
    name: Mapped[str] = mapped_column(String(255), nullable=True)
    username: Mapped[str] = mapped_column(String(255), nullable=True, unique=True)
    display_picture: Mapped[str] = mapped_column(Text, nullable=True)
    role: Mapped[str] = mapped_column(String(50), nullable=True)
    gender: Mapped[str] = mapped_column(String(50), nullable=True)
    encompass_email: Mapped[str] = mapped_column(String(255), nullable=True)
    encompass_loid: Mapped[str] = mapped_column(String(100), nullable=True)

    # if enabled, provides access based on impersonation token, otherwise only shows loans that they are assigned into
    encompass_impersonation_access: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False)

    assistants: Mapped[list["Assistant"]] = relationship(
        "Assistant", secondary="user_assistant_association", back_populates="users"
    )
    private_assistants: Mapped[list["Assistant"]] = relationship(
        "Assistant", secondary="private_assistant_association", back_populates="private_users"
    )
    user_documents: Mapped[list["UserDocument"]] = relationship(
        "UserDocument", back_populates="user", cascade="all, delete"
    )
    sharepoint_folders: Mapped[list["SharepointFolder"]] = relationship(
        "SharepointFolder", back_populates="user", cascade="all, delete"
    )

    search_tsvector: Mapped[TSVECTOR] = mapped_column(TSVECTOR, nullable=True, index=True)

    division_id: Mapped[str] = mapped_column(ForeignKey("divisions.id", ondelete="CASCADE"), nullable=True)
    division: Mapped["Division"] = relationship("Division", back_populates="users")
    conversations: Mapped[list["Conversation"]] = relationship("Conversation", cascade="all, delete")
    assistant_personas: Mapped[list["AssistantPersona"]] = relationship("AssistantPersona", cascade="all, delete")
    custom_personas: Mapped[list["Persona"]] = relationship("Persona", back_populates="owner")

    total_expert_tokens: Mapped[list["TotalExpertToken"]] = relationship("TotalExpertToken", back_populates="user")


class Conversation(PrimaryUUIDTimestamped):
    __tablename__ = "conversations"

    user_id: Mapped[UUID] = mapped_column(UUID(as_uuid=True), ForeignKey("users.id", ondelete="CASCADE"))
    title: Mapped[str] = mapped_column(String(255), nullable=True)
    is_pinned: Mapped[bool] = mapped_column(Boolean, default=False)

    user: Mapped[User] = relationship("User", back_populates="conversations")
    messages: Mapped[list["Message"]] = relationship("Message", cascade="all, delete-orphan")


class Message(PrimaryUUIDTimestamped):
    __tablename__ = "messages"

    conversation_id: Mapped[str] = mapped_column(ForeignKey("conversations.id", ondelete="CASCADE"), nullable=False)
    role: Mapped[str] = mapped_column(Enum("user", "assistant", "system", name="role_types"), nullable=False)
    content: Mapped[list] = mapped_column(MutableList.as_mutable(JSON), nullable=False)
    attachments: Mapped[list] = mapped_column(MutableList.as_mutable(JSON), nullable=True)

    conversation: Mapped[Conversation] = relationship("Conversation", back_populates="messages")
    feedback: Mapped["Feedback"] = relationship("Feedback", uselist=False, cascade="all, delete-orphan")

    __table_args__ = (
        sa.Index("idx_message_created_at", "created_at", unique=False),
        sa.Index("idx_message_conversation_created_at_role", "conversation_id", "created_at", "role", unique=False),
    )


class Feedback(PrimaryUUIDTimestamped):
    __tablename__ = "feedbacks"

    id: Mapped[UUID] = mapped_column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    message_id: Mapped[str] = mapped_column(ForeignKey("messages.id", ondelete="CASCADE"))
    user_rating: Mapped[bool] = mapped_column(Boolean, nullable=True)
    feedback_text: Mapped[str | None] = mapped_column(Text, nullable=True)

    message: Mapped[Message] = relationship("Message", back_populates="feedback")


class Assistant(PrimaryUUIDTimestamped):
    __tablename__ = "assistants"

    name: Mapped[str] = mapped_column(String(255), nullable=False)
    display_name: Mapped[str] = mapped_column(String(255), nullable=False)
    description: Mapped[str] = mapped_column(Text, nullable=True)
    instructions: Mapped[str] = mapped_column(Text, nullable=True)
    grounding_instructions: Mapped[str] = mapped_column(Text, default="")
    disclaimer: Mapped[str] = mapped_column(Text, nullable=True)
    conversation_starters: Mapped[list[str]] = mapped_column(MutableList.as_mutable(JSON), nullable=True)
    prompts: Mapped[list[str]] = mapped_column(MutableList.as_mutable(JSON), nullable=True)
    contact_emails: Mapped[list[str]] = mapped_column(MutableList.as_mutable(JSON), nullable=True)
    enable_help_button: Mapped[bool] = mapped_column(Boolean, default=False)
    is_published: Mapped[bool] = mapped_column(Boolean, default=False)
    users: Mapped[list["User"]] = relationship(
        "User", secondary="user_assistant_association", back_populates="assistants"
    )
    enable_hard_filter: Mapped[bool] = mapped_column(Boolean, default=False)
    enable_web_search: Mapped[bool] = mapped_column(Boolean, default=False)
    is_private: Mapped[bool] = mapped_column(Boolean, default=False)
    private_users: Mapped[list["User"]] = relationship(
        "User", secondary="private_assistant_association", back_populates="private_assistants"
    )

    categories: Mapped[list[str]] = mapped_column(MutableList.as_mutable(JSON), nullable=True)
    is_category_filtered: Mapped[bool] = mapped_column(Boolean, default=False)
    display_order: Mapped[int] = mapped_column(Integer, nullable=False, default=0)
    type: Mapped[str] = mapped_column(String(50), nullable=False, default=AssistantTypes.GENERAL)
    sub_type: Mapped[str] = mapped_column(String(50), nullable=True)
    provider: Mapped[str] = mapped_column(String(255), nullable=False, default=AssistantProvider.OPENAI)
    encompass_credential_id: Mapped[str] = mapped_column(
        ForeignKey("encompass_credentials.id", ondelete="CASCADE"), nullable=True
    )

    division_id: Mapped[str] = mapped_column(ForeignKey("divisions.id", ondelete="CASCADE"), nullable=True)
    division: Mapped["Division"] = relationship("Division", back_populates="assistants")
    assistant_accesses: Mapped[list["AssistantAccess"]] = relationship("AssistantAccess", cascade="all, delete")
    documents: Mapped[list["Document"]] = relationship("Document", cascade="all, delete")
    question_answers: Mapped[list["QuestionAnswer"]] = relationship("QuestionAnswer", cascade="all, delete")
    documentations: Mapped[list["Documentation"]] = relationship("Documentation", cascade="all, delete")
    website_contents: Mapped[list["WebsiteContent"]] = relationship("WebsiteContent", cascade="all, delete")
    data_fields: Mapped[list["DataField"]] = relationship("DataField", cascade="all, delete")
    custom_loan_officer_mappings: Mapped[list["CustomLoanOfficerMapping"]] = relationship(
        "CustomLoanOfficerMapping", cascade="all, delete"
    )
    sharepoint_folders: Mapped[list["SharepointFolder"]] = relationship("SharepointFolder", cascade="all, delete")
    total_expert_journeys: Mapped[list["TotalExpertJourney"]] = relationship(
        "TotalExpertJourney", back_populates="assistant", cascade="all, delete"
    )
    assistant_personas: Mapped[list["AssistantPersona"]] = relationship("AssistantPersona", cascade="all, delete")
    loan_officers: Mapped[list["LoanOfficer"]] = relationship(
        "LoanOfficer", back_populates="assistant", cascade="all, delete"
    )

    total_expert_tokens: Mapped[list["TotalExpertToken"]] = relationship(
        "TotalExpertToken", back_populates="assistant"
    )


user_assistant_association = Table(
    "user_assistant_association",
    Base.metadata,
    Column("user_id", ForeignKey("users.id", ondelete="CASCADE"), primary_key=True),
    Column("assistant_id", ForeignKey("assistants.id", ondelete="CASCADE"), primary_key=True),
)


private_assistant_association = Table(
    "private_assistant_association",
    Base.metadata,
    Column("user_id", ForeignKey("users.id", ondelete="CASCADE"), primary_key=True),
    Column("assistant_id", ForeignKey("assistants.id", ondelete="CASCADE"), primary_key=True),
)


class UserDocument(PrimaryUUIDTimestamped):
    __tablename__ = "user_documents"

    user_id: Mapped[UUID] = mapped_column(ForeignKey("users.id", ondelete="CASCADE"), nullable=False)
    document_id: Mapped[UUID] = mapped_column(
        ForeignKey("documents.id", ondelete="CASCADE"), nullable=False, unique=True
    )

    user: Mapped["User"] = relationship("User", back_populates="user_documents")
    document: Mapped["Document"] = relationship("Document", back_populates="user_document")


class AssistantAccess(PrimaryUUIDTimestamped):
    __tablename__ = "assistant_accesses"

    role: Mapped[str] = mapped_column(String(50), nullable=False)
    assistant_id: Mapped[str] = mapped_column(ForeignKey("assistants.id", ondelete="CASCADE"), nullable=False)

    assistant: Mapped[Assistant] = relationship("Assistant", back_populates="assistant_accesses")


class Document(PrimaryUUIDTimestamped):
    __tablename__ = "documents"

    title: Mapped[str] = mapped_column(String(255), nullable=False)
    description: Mapped[str] = mapped_column(Text, nullable=True)
    type: Mapped[str] = mapped_column(String(50), nullable=False)
    tags: Mapped[list[str]] = mapped_column(MutableList.as_mutable(JSON), nullable=True)

    link: Mapped[str] = mapped_column(Text, nullable=False)
    text: Mapped[str] = mapped_column(Text, nullable=True)

    is_indexed: Mapped[bool] = mapped_column(Boolean, default=False)
    index_status: Mapped[str] = mapped_column(String(50), nullable=True)
    categories: Mapped[list[str]] = mapped_column(MutableList.as_mutable(JSON), nullable=True)

    assistant_id: Mapped[str] = mapped_column(ForeignKey("assistants.id", ondelete="CASCADE"), nullable=False)
    assistant: Mapped[Assistant] = relationship("Assistant", back_populates="documents")
    user_document: Mapped["UserDocument"] = relationship(
        "UserDocument", back_populates="document", uselist=False, cascade="all, delete-orphan"
    )


class QuestionAnswer(PrimaryUUIDTimestamped):
    __tablename__ = "question_answers"

    question: Mapped[str] = mapped_column(Text, nullable=False)
    answer: Mapped[str] = mapped_column(Text, nullable=False)
    type: Mapped[str] = mapped_column(String(50), nullable=False)
    is_indexed: Mapped[bool] = mapped_column(Boolean, default=False)
    categories: Mapped[list[str]] = mapped_column(MutableList.as_mutable(JSON), nullable=True)

    assistant_id: Mapped[str] = mapped_column(ForeignKey("assistants.id", ondelete="CASCADE"), nullable=False)
    assistant: Mapped[Assistant] = relationship("Assistant", back_populates="question_answers")


class Documentation(PrimaryUUIDTimestamped):
    __tablename__ = "documentations"

    title: Mapped[str] = mapped_column(Text, nullable=False)
    content: Mapped[str] = mapped_column(Text, nullable=False)
    is_indexed: Mapped[bool] = mapped_column(Boolean, default=False)
    categories: Mapped[list[str]] = mapped_column(MutableList.as_mutable(JSON), nullable=True)
    expiry_date: Mapped[datetime] = mapped_column(DateTime, nullable=True)

    assistant_id: Mapped[str] = mapped_column(ForeignKey("assistants.id", ondelete="CASCADE"), nullable=False)
    assistant: Mapped[Assistant] = relationship("Assistant", back_populates="documentations")


class LoanOfficer(PrimaryUUIDTimestamped):
    __tablename__ = "loan_officers"

    loan_id: Mapped[str] = mapped_column(String(255), nullable=False)
    email: Mapped[str] = mapped_column(String(255), nullable=False, index=True)
    last_name: Mapped[str] = mapped_column(String(255), nullable=True)
    first_name: Mapped[str] = mapped_column(String(255), nullable=True)
    assistant_id: Mapped[str] = mapped_column(ForeignKey("assistants.id", ondelete="CASCADE"), nullable=False)
    assistant: Mapped[Assistant] = relationship("Assistant", back_populates="loan_officers")


class CustomLoanOfficerMapping(PrimaryUUIDTimestamped):
    __tablename__ = "custom_loan_officer_mappings"

    loan_id: Mapped[str] = mapped_column(String(255), nullable=True)
    last_name: Mapped[str] = mapped_column(String(255), nullable=True)
    first_name: Mapped[str] = mapped_column(String(255), nullable=True)
    email: Mapped[str] = mapped_column(String(255), nullable=False, index=True)
    lo_email: Mapped[str] = mapped_column(String(255), nullable=False)
    mapping_type: Mapped[str] = mapped_column(
        Enum(LoanOfficerMappingType), nullable=False, default=LoanOfficerMappingType.COMMON
    )

    assistant_id: Mapped[str] = mapped_column(ForeignKey("assistants.id", ondelete="CASCADE"), nullable=False)
    assistant: Mapped[Assistant] = relationship("Assistant", back_populates="custom_loan_officer_mappings")
    meta: Mapped[dict] = mapped_column(JSON, nullable=True)
    active: Mapped[bool] = mapped_column(Boolean, default=False, server_default=sa.false())


class WebsiteContent(PrimaryUUIDTimestamped):
    __tablename__ = "website_contents"

    title: Mapped[str] = mapped_column(Text, nullable=True)
    content: Mapped[str] = mapped_column(Text, nullable=True)
    content_type: Mapped[str] = mapped_column(String(50), nullable=False)  # summary or full_content

    domain: Mapped[str] = mapped_column(String(255), nullable=True)
    url: Mapped[str] = mapped_column(Text, nullable=False)

    include_links: Mapped[bool] = mapped_column(Boolean, default=False)
    is_indexed: Mapped[bool] = mapped_column(Boolean, default=False)
    categories: Mapped[list[str]] = mapped_column(MutableList.as_mutable(JSON), nullable=True)

    assistant_id: Mapped[str] = mapped_column(ForeignKey("assistants.id", ondelete="CASCADE"), nullable=False)
    assistant: Mapped[Assistant] = relationship("Assistant", back_populates="website_contents")

    parent_id: Mapped[str] = mapped_column(ForeignKey("website_contents.id", ondelete="CASCADE"), nullable=True)
    parent: Mapped["WebsiteContent"] = relationship(
        "WebsiteContent", remote_side="WebsiteContent.id", backref=backref("children", cascade="all, delete-orphan")
    )
    cron_expression: Mapped[str] = mapped_column(String(255), nullable=True)

    __table_args__ = (UniqueConstraint("assistant_id", "url", name="unique_assistant_url"),)


class AssistantAnalytics(PrimaryUUIDTimestamped):
    __tablename__ = "assistant_analytics"

    assistant_id: Mapped[str] = mapped_column(String(255), nullable=False)
    cluster_name: Mapped[str] = mapped_column(String(255), nullable=False)
    message_count: Mapped[int] = mapped_column(Integer, nullable=False)
    percentage: Mapped[float] = mapped_column(Float, nullable=False)
    meta: Mapped[dict] = mapped_column(JSON, nullable=True)
    unigram_word_cloud: Mapped[list] = mapped_column(JSON, nullable=True)
    bigram_word_cloud: Mapped[list] = mapped_column(JSON, nullable=True)

    __table_args__ = (UniqueConstraint("assistant_id", "cluster_name", name="unique_assistant_cluster"),)


class DataField(PrimaryUUIDTimestamped):
    __tablename__ = "data_fields"

    field_id: Mapped[str] = mapped_column(String(255), nullable=False)
    name: Mapped[str] = mapped_column(String(255), nullable=False)
    type: Mapped[str] = mapped_column(String(50), nullable=False, default=DataFieldTypes.TEXT)
    is_searchable: Mapped[bool] = mapped_column(Boolean, default=False)
    display_in_ui: Mapped[bool] = mapped_column(Boolean, default=False)
    display_order: Mapped[int] = mapped_column(Integer, nullable=False, default=0)
    is_merged: Mapped[bool] = mapped_column(Boolean, default=False)
    merged_name: Mapped[str] = mapped_column(String(255), nullable=True)
    folder: Mapped[str] = mapped_column(String(100), nullable=True)

    assistant_id: Mapped[str] = mapped_column(ForeignKey("assistants.id", ondelete="CASCADE"), nullable=False)
    assistant: Mapped[Assistant] = relationship("Assistant", back_populates="data_fields")

    meta: Mapped[dict] = mapped_column(JSON, nullable=True)

    __table_args__ = (
        sa.Index(
            "unique_assistant_field_folder", "assistant_id", "field_id", sa.text("COALESCE(folder, '')"), unique=True
        ),
    )


class TotalExpertInsight(PrimaryUUIDTimestamped):
    __tablename__ = "totalexpert_insights"

    id: Mapped[str] = mapped_column(String(255))  # Override the id from PrimaryUUIDTimestamped
    is_read: Mapped[bool] = mapped_column(Boolean, default=False)
    user_id: Mapped[UUID] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("users.id", ondelete="CASCADE", name="fk_totalexpert_insights_user_id"),
    )

    __table_args__ = (PrimaryKeyConstraint("id", "user_id", name="pk_totalexpert_insights"),)


class SharepointFolder(PrimaryUUIDTimestamped):
    __tablename__ = "sharepoint_folders"

    site_id: Mapped[str] = mapped_column(String(255), nullable=False)
    drive_id: Mapped[str] = mapped_column(String(255), nullable=False)
    folder_id: Mapped[str] = mapped_column(String(255), nullable=False)
    site_name: Mapped[str] = mapped_column(String(255), nullable=False)
    type: Mapped[str] = mapped_column(String(50), nullable=False)

    is_indexed: Mapped[bool] = mapped_column(Boolean, default=False)
    index_status: Mapped[str] = mapped_column(String(50), nullable=True)
    categories: Mapped[list[str]] = mapped_column(MutableList.as_mutable(JSON), nullable=True)

    assistant_id: Mapped[str] = mapped_column(ForeignKey("assistants.id", ondelete="CASCADE"), nullable=False)
    user_id: Mapped[str] = mapped_column(ForeignKey("users.id", ondelete="CASCADE"), nullable=True)

    assistant: Mapped[Assistant] = relationship("Assistant", back_populates="sharepoint_folders")
    user: Mapped[User] = relationship("User", back_populates="sharepoint_folders")

    parent_id: Mapped[str] = mapped_column(ForeignKey("sharepoint_folders.id", ondelete="CASCADE"), nullable=True)
    parent: Mapped["SharepointFolder"] = relationship(
        "SharepointFolder",
        remote_side="SharepointFolder.id",
        backref=backref("children", cascade="all, delete-orphan"),
    )

    __table_args__ = (
        UniqueConstraint(
            "site_id", "drive_id", "folder_id", "assistant_id", "type", name="unique_site_drive_assistant_folder"
        ),
    )


class TotalExpertJourney(PrimaryUUIDTimestamped):
    __tablename__ = "total_expert_journeys"

    type: Mapped[str] = mapped_column(String(50), nullable=False)
    name: Mapped[str] = mapped_column(String(255), nullable=False)
    description: Mapped[str] = mapped_column(Text, nullable=True)
    fields: Mapped[list[str]] = mapped_column(
        MutableList.as_mutable(JSON), nullable=True
    )  # TODO:  might need to make it foreign key for datafields

    custom_field_mapping: Mapped[dict | None] = mapped_column(MutableDict.as_mutable(JSON), nullable=True)
    is_published: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False)
    is_custom: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False)
    blacklisted_words: Mapped[list[str]] = mapped_column(MutableList.as_mutable(JSON), nullable=True)

    assistant_id: Mapped[str] = mapped_column(
        ForeignKey("assistants.id", name="total_expert_journeys_assistant_id_fkey", ondelete="CASCADE"), nullable=False
    )
    assistant: Mapped["Assistant"] = relationship("Assistant", back_populates="total_expert_journeys")
    __table_args__ = (UniqueConstraint("assistant_id", "id", name="unique_assistant_journey"),)


class Persona(PrimaryUUIDTimestamped):
    __tablename__ = "personas"

    name: Mapped[str] = mapped_column(String(255), nullable=False)
    description: Mapped[str] = mapped_column(Text, nullable=True)

    owner_id: Mapped[UUID | None] = mapped_column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)
    owner: Mapped[User | None] = relationship("User", back_populates="custom_personas")


class AssistantPersona(PrimaryUUIDTimestamped):
    __tablename__ = "assistant_personas"

    profession: Mapped[str] = mapped_column(Text, nullable=True)
    enabled: Mapped[bool] = mapped_column(Boolean, default=False)
    description: Mapped[str] = mapped_column(Text, nullable=True)

    assistant_id: Mapped[str] = mapped_column(ForeignKey("assistants.id", ondelete="CASCADE"), nullable=False)
    user_id: Mapped[UUID] = mapped_column(
        UUID(as_uuid=True), ForeignKey("users.id", ondelete="CASCADE"), nullable=False
    )

    assistant: Mapped[Assistant] = relationship("Assistant", back_populates="assistant_personas")
    user: Mapped[User] = relationship("User", back_populates="assistant_personas")

    __table_args__ = (UniqueConstraint("assistant_id", "user_id", name="unique_assistant_user_persona"),)


class TotalExpertInsightType(PrimaryUUIDTimestamped):
    __tablename__ = "total_expert_insight_types"

    id: Mapped[str] = mapped_column(String(255), nullable=False)  # override to make unique primary key
    name: Mapped[str] = mapped_column(Text, nullable=False)
    type: Mapped[str] = mapped_column(String(255), nullable=False)
    is_published: Mapped[bool] = mapped_column(Boolean, default=False)
    assistant_id: Mapped[str] = mapped_column(
        ForeignKey("assistants.id", ondelete="CASCADE", name="total_expert_insight_types_assistant_id_fkey"),
        nullable=False,
    )

    __table_args__ = (PrimaryKeyConstraint("id", "assistant_id", name="pk_total_expert_insight_types"),)


class Division(PrimaryUUIDTimestamped):
    __tablename__ = "divisions"

    name: Mapped[str] = mapped_column(String(255), nullable=False)
    domain: Mapped[str] = mapped_column(String(255), nullable=False, unique=True)
    description: Mapped[str] = mapped_column(Text, nullable=True)
    logo: Mapped[str] = mapped_column(Text, nullable=True)
    avatar: Mapped[str] = mapped_column(Text, nullable=True)
    primary_color: Mapped[str] = mapped_column(String(50), nullable=True)
    secondary_color: Mapped[str] = mapped_column(String(50), nullable=True)

    users: Mapped[list["User"]] = relationship("User", back_populates="division", cascade="all, delete")
    assistants: Mapped[list["Assistant"]] = relationship("Assistant", back_populates="division", cascade="all, delete")
    credentials: Mapped["DivisionCredentials"] = relationship(
        "DivisionCredentials", back_populates="division", uselist=False, cascade="all, delete-orphan"
    )


class DivisionCredentials(PrimaryUUIDTimestamped):
    from config import settings

    __tablename__ = "division_credentials"

    pinecone_index: Mapped[str] = mapped_column(String(255), nullable=False)
    pinecone_host: Mapped[str] = mapped_column(String(512), nullable=False)
    aws_storage_bucket_name: Mapped[str] = mapped_column(String(255), nullable=False)

    total_expert_client_id: Mapped[str] = mapped_column(
        EncryptedType(
            String(255),
            settings.ENCRYPTION_KEY,
            AesEngine,
            padding="pkcs5",
        ),
        nullable=True,
    )
    total_expert_client_secret: Mapped[str] = mapped_column(
        EncryptedType(
            String(255),
            settings.ENCRYPTION_KEY,
            AesEngine,
            padding="pkcs5",
        ),
        nullable=True,
    )

    division_id: Mapped[str] = mapped_column(
        ForeignKey("divisions.id", ondelete="CASCADE"), nullable=False, unique=True
    )
    division: Mapped[Division] = relationship("Division", back_populates="credentials")
    encompass_credentials: Mapped[list["EncompassCredentials"]] = relationship(
        "EncompassCredentials", uselist=True, back_populates="division_credentials", cascade="all, delete-orphan"
    )
    enabled_total_expert: Mapped[bool] = mapped_column(Boolean, default=False)
    enabled_personal_assistant: Mapped[bool] = mapped_column(Boolean, default=False)
    enabled_sales_assistant: Mapped[bool] = mapped_column(Boolean, default=False)


class EncompassCredentials(PrimaryUUIDTimestamped):
    from config import settings

    __tablename__ = "encompass_credentials"

    name: Mapped[str] = mapped_column(String(255), nullable=False)
    encompass_client_id: Mapped[str] = mapped_column(
        EncryptedType(
            String(255),
            settings.ENCRYPTION_KEY,
            AesEngine,
            padding="pkcs5",
        ),
        nullable=True,
    )
    encompass_client_secret: Mapped[str] = mapped_column(
        EncryptedType(
            String(255),
            settings.ENCRYPTION_KEY,
            AesEngine,
            padding="pkcs5",
        ),
        nullable=True,
    )
    encompass_password: Mapped[str] = mapped_column(
        EncryptedType(
            String(255),
            settings.ENCRYPTION_KEY,
            AesEngine,
            padding="pkcs5",
        ),
        nullable=True,
    )
    encompass_username: Mapped[str] = mapped_column(
        EncryptedType(
            String(255),
            settings.ENCRYPTION_KEY,
            AesEngine,
            padding="pkcs5",
        ),
        nullable=True,
    )
    encompass_instance_id: Mapped[str] = mapped_column(
        EncryptedType(
            String(255),
            settings.ENCRYPTION_KEY,
            AesEngine,
            padding="pkcs5",
        ),
        nullable=True,
    )

    division_credentials_id: Mapped[str] = mapped_column(
        ForeignKey("division_credentials.id", ondelete="CASCADE"), nullable=False
    )
    division_credentials: Mapped[DivisionCredentials] = relationship(
        "DivisionCredentials", back_populates="encompass_credentials"
    )
    encompass_type: Mapped[str] = mapped_column(String(255), nullable=False, default="prod")
    enabled: Mapped[bool] = mapped_column(Boolean, default=False)


class TotalExpertToken(PrimaryUUIDTimestamped):
    from config import settings

    __tablename__ = "total_expert_tokens"

    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    assistant_id = Column(String, ForeignKey("assistants.id"), nullable=False)
    access_token = Column(
        EncryptedType(
            String,
            settings.ENCRYPTION_KEY,
            AesEngine,
            padding="pkcs5",
        ),
        nullable=False,
    )
    refresh_token = Column(
        EncryptedType(
            String,
            settings.ENCRYPTION_KEY,
            AesEngine,
            padding="pkcs5",
        ),
        nullable=False,
    )
    token_type = Column(String, nullable=False, default="Bearer")
    expires_at = Column(DateTime, nullable=False)
    refresh_token_expires_at = Column(DateTime, nullable=False)

    # Relationships
    user = relationship("User", back_populates="total_expert_tokens")
    assistant = relationship("Assistant", back_populates="total_expert_tokens")

    class Config:
        orm_mode = True


class DataPulseReport(PrimaryUUIDTimestamped):
    __tablename__ = "datapulse_reports"

    id: Mapped[str] = mapped_column(String(255))  # Override the id from PrimaryUUIDTimestamped

    name: Mapped[str] = mapped_column(String(255), nullable=False)
    description: Mapped[str] = mapped_column(Text, nullable=False)

    payload: Mapped[dict] = mapped_column(MutableDict.as_mutable(JSON), nullable=False)
    primary_display_fields: Mapped[list[str]] = mapped_column(MutableList.as_mutable(JSON), nullable=False)
    secondary_display_fields: Mapped[list[str]] = mapped_column(MutableList.as_mutable(JSON), nullable=False)

    is_visible: Mapped[bool] = mapped_column(Boolean, default=True)
    order: Mapped[int] = mapped_column(Integer, nullable=False)
    requires_condition_pending: Mapped[bool] = mapped_column(Boolean, default=False)
    requires_custom_date: Mapped[bool] = mapped_column(Boolean, default=False)
    ttl: Mapped[int] = mapped_column(Integer, nullable=False, default=86400)

    assistant_id: Mapped[str] = mapped_column(ForeignKey("assistants.id", ondelete="CASCADE"), nullable=False)

    __table_args__ = (PrimaryKeyConstraint("id", "assistant_id", name="pk_datapulse_reports"),)
