# OpenAI Credentials
OPENAI_API_KEY=${OPENAI_API_KEY}
OPENAI_ORG_ID=${OPENAI_ORG_ID}
OPENAI_PROJECT_ID=${OPENAI_PROJECT_ID}

# Pinecone Credentials
PINECONE_SERVERLESS_API_KEY=${PINECONE_SERVERLESS_API_KEY}
PINECONE_ENV=${PINECONE_ENV}
PINECONE_INDEX=${PINECONE_INDEX}
PINECONE_HOST=${PINECONE_HOST}

# Postgres Credentials
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres
POSTGRES_DB=insydeai
POSTGRES_HOST=db
POSTGRES_PORT=5432

DATABASE_URL="postgresql+asyncpg://${POSTGRES_USER}:${POSTGRES_PASSWORD}@${POSTGRES_HOST}:${POSTGRES_PORT}/${POSTGRES_DB}"

# AWS Credentials
AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}
AWS_STORAGE_BUCKET_NAME=${AWS_STORAGE_BUCKET_NAME}
AWS_S3_REGION_NAME=${AWS_S3_REGION_NAME}
AWS_CDN_BASE_URL=${AWS_CDN_BASE_URL}
AWS_CDN_BUCKET_NAME=${AWS_CDN_BUCKET_NAME}

# SSO Credentials
AZURE_CLIENT_ID=${AZURE_CLIENT_ID}
AZURE_TENANT_ID=${AZURE_TENANT_ID}
AZURE_CLIENT_SECRET=${AZURE_CLIENT_SECRET}
REDIRECT_URI=${REDIRECT_URI}

# Celery
CELERY_BROKER_URL=${CELERY_BROKER_URL}
CELERY_RESULT_BACKEND=${CELERY_RESULT_BACKEND}
CELERY_FLOWER_USER=${CELERY_FLOWER_USER}
CELERY_FLOWER_PASSWORD=${CELERY_FLOWER_PASSWORD}

# Unstructured
UNSTRUCTURED_API_URL=${UNSTRUCTURED_API_URL}
UNSTRUCTURED_API_KEY=${UNSTRUCTURED_API_KEY}

# ENCOMPASS Credentials
ENCOMPASS_CLIENT_ID=${ENCOMPASS_CLIENT_ID}
ENCOMPASS_CLIENT_SECRET=${ENCOMPASS_CLIENT_SECRET}
ENCOMPASS_PASSWORD=${ENCOMPASS_PASSWORD}
ENCOMPASS_USERNAME=${ENCOMPASS_USERNAME}
ENCOMPASS_INSTANCE_ID=${ENCOMPASS_INSTANCE_ID}

# OPTIONALLY, if you want to use other encompass assistants with separate credentials
# EXTRA_ENCOMPASS_ASSISTANTS_ENVIRONMENTS=["DEV"] (EXAMPLE ONLY)
# DEV from above variable value
# DEV_ENCOMPASS_CLIENT_ID=${DEV_ENCOMPASS_CLIENT_ID}
# DEV_ENCOMPASS_CLIENT_SECRET=${DEV_ENCOMPASS_CLIENT_SECRET}
# DEV_ENCOMPASS_PASSWORD=${DEV_ENCOMPASS_PASSWORD}
# DEV_ENCOMPASS_USERNAME=${DEV_ENCOMPASS_USERNAME}
# DEV_ENCOMPASS_INSTANCE_ID=${DEV_ENCOMPASS_INSTANCE_ID}

# ATTOM Data credentials
ATTOM_API_URL=${ATTOM_API_URL}
ATTOM_API_KEY=${ATTOM_API_KEY}

# Cognito
AWS_DEFAULT_REGION=${AWS_DEFAULT_REGION}
AWS_COGNITO_USERPOOL_ID=${AWS_COGNITO_USERPOOL_ID}
WEB_CLIENT_ID=${WEB_CLIENT_ID}

# Sidekick
SIDEKICK_ASSISTANT_ID=${SIDEKICK_ASSISTANT_ID}
LOAN_ASSISTANT_ID=${LOAN_ASSISTANT_ID}
SIDEKICK_DISPLAY_NAME=${SIDEKICK_DISPLAY_NAME}
TOTAL_EXPERT_ASSISTANT_ID=${TOTAL_EXPERT_ASSISTANT_ID}

# Sentry Configuration
SENTRY_DSN=${SENTRY_DSN}
SENTRY_ENVIRONMENT=${SENTRY_ENVIRONMENT}
SENTRY_TRACES_SAMPLE_RATE=${SENTRY_TRACES_SAMPLE_RATE}

ASSISTANT_LIMIT=${ASSISTANT_LIMIT}

# Sharepoint
AZURE_SHAREPOINT_TENANT_ID=${AZURE_SHAREPOINT_TENANT_ID}
AZURE_SHAREPOINT_CLIENT_ID=${AZURE_SHAREPOINT_CLIENT_ID}
AZURE_SHAREPOINT_CLIENT_SECRET=${AZURE_SHAREPOINT_CLIENT_SECRET}

# Jina Reader
JINA_READER_API_KEY=${JINA_READER_API_KEY}

# TOTAL EXPERT
TOTAL_EXPERT_CLIENT_ID=${TOTAL_EXPERT_CLIENT_ID}
TOTAL_EXPERT_CLIENT_SECRET=${TOTAL_EXPERT_CLIENT_SECRET}

VOYAGE_API_KEY=${VOYAGE_API_KEY}

# MANN ASSISTANT for Synergy
MANN_ASSISTANT_FILE_LOC=${MANN_ASSISTANT_FILE_LOC}
GEMINI_API_KEY=${GEMINI_API_KEY}
GEMINI_MODEL_NAME=${GEMINI_MODEL_NAME}


#OPENAI MODELS
OPENAI_IMAGE_GEN_MODEL=${OPENAI_IMAGE_GEN_MODEL}


ENCOMPASS_ASSISTANT_ENABLED=${ENCOMPASS_ASSISTANT_ENABLED}
ENCOMPASS_SALES_ASSISTANT_ENABLED=${ENCOMPASS_SALES_ASSISTANT_ENABLED}
TOTALEXPERT_ASSISTANT_ENABLED=${TOTALEXPERT_ASSISTANT_ENABLED}
PERSONAL_ASSISTANTS_ENABLED=${PERSONAL_ASSISTANTS_ENABLED}

EXTRA_ENCOMPASS_ASSISTANTS_ENVIRONMENTS=${EXTRA_ENCOMPASS_ASSISTANTS_ENVIRONMENTS}
EXTRA_ENCOMPASS_SALES_ASSISTANTS_ENVIRONMENTS=${EXTRA_ENCOMPASS_SALES_ASSISTANTS_ENVIRONMENTS}
