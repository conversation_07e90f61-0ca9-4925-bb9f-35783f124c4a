version: "3"
services:
  db:
    image: postgres:16
    ports:
      - "5432:5432"
    volumes:
      - db-data:/var/lib/postgresql/data
      - ./initdb:/docker-entrypoint-initdb.d
    env_file:
      - ${ENV_FILE_PATH:-.env}

  backend: &backend
    ports:
      - "8000:8000"
    build:
      context: ./backend
      dockerfile: Dockerfile
    environment:
      PORT: 8000
    depends_on:
      - db
    env_file:
      - ${ENV_FILE_PATH:-.env}
    command: ['/start-reload.sh']
    volumes:
      - ./backend/app:/app

  redis:
    image: redis:6

  worker:
    <<: *backend
    image: worker
    depends_on:
      - db
      - redis
    ports: []
    command: /start-celeryworker

  flower:
    <<: *backend
    image: flower
    ports:
      - "5555:5555"
    env_file:
      - ${ENV_FILE_PATH:-.env}
    command: /start-flower

  beat:
    <<: *backend
    image: beat
    depends_on:
      - db
      - redis
    ports: []
    command: /start-celerybeat

volumes:
  db-data:
